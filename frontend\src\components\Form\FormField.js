/**
 * 可重用的表單欄位組件
 */

import React from 'react';
import { Form, Input, TextArea } from 'antd-mobile';
import { validateField, getFieldDisplayName } from '../../utils/validation';

const FormField = ({
  name,
  value,
  onChange,
  label,
  placeholder,
  type = 'text',
  required = false,
  disabled = false,
  multiline = false,
  maxLength,
  showError = true,
  style = {},
  className = ''
}) => {
  // 獲取顯示標籤
  const displayLabel = label || getFieldDisplayName(name);
  
  // 驗證欄位
  const validation = validateField(name, value);
  const hasError = showError && !validation.isValid;

  // 處理值變更
  const handleChange = (newValue) => {
    if (onChange) {
      onChange(name, newValue);
    }
  };

  // 生成placeholder
  const fieldPlaceholder = placeholder || `請輸入${displayLabel}`;

  return (
    <Form.Item
      name={name}
      label={displayLabel}
      className={`form-field ${className} ${hasError ? 'has-error' : ''}`}
      style={style}
      help={hasError ? validation.error : undefined}
      validateStatus={hasError ? 'error' : undefined}
    >
      {multiline ? (
        <TextArea
          value={value || ''}
          onChange={handleChange}
          placeholder={fieldPlaceholder}
          disabled={disabled}
          maxLength={maxLength}
          rows={3}
          showCount={!!maxLength}
          autoSize={{ minRows: 2, maxRows: 6 }}
        />
      ) : (
        <Input
          value={value || ''}
          onChange={handleChange}
          placeholder={fieldPlaceholder}
          disabled={disabled}
          maxLength={maxLength}
          type={type}
          clearable
        />
      )}
    </Form.Item>
  );
};

export default FormField;
