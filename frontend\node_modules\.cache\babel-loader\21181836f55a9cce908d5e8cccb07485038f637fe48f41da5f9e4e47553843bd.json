{"ast": null, "code": "import { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useMemoizedFn(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMemoizedFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useRef(fn);\n  // why not write `fnRef.current = fn`?\n  // https://github.com/alibaba/hooks/issues/728\n  fnRef.current = useMemo(function () {\n    return fn;\n  }, [fn]);\n  var memoizedFn = useRef();\n  if (!memoizedFn.current) {\n    memoizedFn.current = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(this, args);\n    };\n  }\n  return memoizedFn.current;\n}\nexport default useMemoizedFn;", "map": {"version": 3, "names": ["useMemo", "useRef", "isFunction", "isDev", "useMemoizedFn", "fn", "console", "error", "concat", "fnRef", "current", "memoizedFn", "args", "_i", "arguments", "length", "apply"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useMemoizedFn/index.js"], "sourcesContent": ["import { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useMemoizedFn(fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMemoizedFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useRef(fn);\n  // why not write `fnRef.current = fn`?\n  // https://github.com/alibaba/hooks/issues/728\n  fnRef.current = useMemo(function () {\n    return fn;\n  }, [fn]);\n  var memoizedFn = useRef();\n  if (!memoizedFn.current) {\n    memoizedFn.current = function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(this, args);\n    };\n  }\n  return memoizedFn.current;\n}\nexport default useMemoizedFn;"], "mappings": "AAAA,SAASA,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,KAAK,MAAM,gBAAgB;AAClC,SAASC,aAAaA,CAACC,EAAE,EAAE;EACzB,IAAIF,KAAK,EAAE;IACT,IAAI,CAACD,UAAU,CAACG,EAAE,CAAC,EAAE;MACnBC,OAAO,CAACC,KAAK,CAAC,sDAAsD,CAACC,MAAM,CAAC,OAAOH,EAAE,CAAC,CAAC;IACzF;EACF;EACA,IAAII,KAAK,GAAGR,MAAM,CAACI,EAAE,CAAC;EACtB;EACA;EACAI,KAAK,CAACC,OAAO,GAAGV,OAAO,CAAC,YAAY;IAClC,OAAOK,EAAE;EACX,CAAC,EAAE,CAACA,EAAE,CAAC,CAAC;EACR,IAAIM,UAAU,GAAGV,MAAM,CAAC,CAAC;EACzB,IAAI,CAACU,UAAU,CAACD,OAAO,EAAE;IACvBC,UAAU,CAACD,OAAO,GAAG,YAAY;MAC/B,IAAIE,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC1B;MACA,OAAOJ,KAAK,CAACC,OAAO,CAACM,KAAK,CAAC,IAAI,EAAEJ,IAAI,CAAC;IACxC,CAAC;EACH;EACA,OAAOD,UAAU,CAACD,OAAO;AAC3B;AACA,eAAeN,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}