{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * 相機狀態管理Hook\n * 管理相機拍攝、圖片選擇和預覽功能\n */\n\nimport { useState, useCallback, useRef, useEffect } from 'react';\nimport { Toast } from 'antd-mobile';\nimport { getCameraManager } from '../utils/cameraManager';\nimport { getDeviceType } from '../utils/deviceDetector';\nimport { validateImageFile, getImagePreviewUrl, revokeImagePreviewUrl } from '../utils/imageUtils';\nimport { CAMERA_TARGET, CAMERA_STATUS, ERROR_MESSAGES } from '../utils/constants';\nexport const useCameraState = () => {\n  _s();\n  const [images, setImages] = useState({\n    front: {\n      file: null,\n      preview: null,\n      ocrText: '',\n      parseStatus: null\n    },\n    back: {\n      file: null,\n      preview: null,\n      ocrText: '',\n      parseStatus: null\n    }\n  });\n  const [currentTarget, setCurrentTarget] = useState(CAMERA_TARGET.FRONT);\n  const [cameraStatus, setCameraStatus] = useState(CAMERA_STATUS.INACTIVE);\n  const [cameraModalVisible, setCameraModalVisible] = useState(false);\n  const [deviceType, setDeviceType] = useState('desktop');\n  const cameraManagerRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  /**\n   * 初始化相機管理器\n   */\n  const initializeCameraManager = useCallback(async () => {\n    try {\n      if (!cameraManagerRef.current) {\n        cameraManagerRef.current = getCameraManager();\n        await cameraManagerRef.current.initialize();\n      }\n      const type = getDeviceType();\n      setDeviceType(type);\n      console.log('📱 設備類型:', type);\n      console.log('📷 相機管理器初始化完成');\n    } catch (error) {\n      console.error('❌ 相機管理器初始化失敗:', error);\n      Toast.show({\n        content: '相機初始化失敗',\n        position: 'center'\n      });\n    }\n  }, []);\n\n  /**\n   * 更新圖片狀態\n   */\n  const updateImageState = useCallback((target, updates) => {\n    setImages(prev => ({\n      ...prev,\n      [target]: {\n        ...prev[target],\n        ...updates\n      }\n    }));\n  }, []);\n\n  /**\n   * 處理圖片文件\n   */\n  const handleImageFile = useCallback((file, target) => {\n    // 驗證圖片文件\n    const validation = validateImageFile(file);\n    if (!validation.isValid) {\n      Toast.show({\n        content: validation.error,\n        position: 'center'\n      });\n      return null;\n    }\n\n    // 釋放舊的預覽URL\n    const oldPreview = images[target].preview;\n    if (oldPreview) {\n      revokeImagePreviewUrl(oldPreview);\n    }\n\n    // 創建新的預覽URL\n    const preview = getImagePreviewUrl(file);\n\n    // 更新狀態\n    updateImageState(target, {\n      file,\n      preview,\n      ocrText: '',\n      parseStatus: null\n    });\n    console.log(`📸 圖片已設置 - ${target}面:`, file.name);\n    return {\n      file,\n      preview\n    };\n  }, [images, updateImageState]);\n\n  /**\n   * 啟動相機\n   */\n  const startCamera = useCallback(async (target = currentTarget) => {\n    if (!cameraManagerRef.current) {\n      await initializeCameraManager();\n    }\n    setCurrentTarget(target);\n    setCameraStatus(CAMERA_STATUS.STARTING);\n    setCameraModalVisible(true);\n    try {\n      console.log(`📷 啟動相機 - ${target}面`);\n      setCameraStatus(CAMERA_STATUS.ACTIVE);\n    } catch (error) {\n      console.error('❌ 啟動相機失敗:', error);\n      setCameraStatus(CAMERA_STATUS.ERROR);\n      setCameraModalVisible(false);\n      let message = ERROR_MESSAGES.CAMERA_PERMISSION_DENIED;\n      if (error.name === 'NotFoundError') {\n        message = ERROR_MESSAGES.CAMERA_NOT_FOUND;\n      } else if (error.name === 'NotReadableError') {\n        message = ERROR_MESSAGES.CAMERA_OCCUPIED;\n      }\n      Toast.show({\n        content: message,\n        position: 'center'\n      });\n    }\n  }, [currentTarget, initializeCameraManager]);\n\n  /**\n   * 停止相機\n   */\n  const stopCamera = useCallback(() => {\n    if (cameraManagerRef.current) {\n      cameraManagerRef.current.stopCamera();\n    }\n    setCameraStatus(CAMERA_STATUS.INACTIVE);\n    setCameraModalVisible(false);\n    console.log('📷 相機已停止');\n  }, []);\n\n  /**\n   * 拍照\n   */\n  const capturePhoto = useCallback(async (target = currentTarget) => {\n    if (!cameraManagerRef.current) {\n      Toast.show({\n        content: '相機未初始化',\n        position: 'center'\n      });\n      return null;\n    }\n    try {\n      const photoData = await cameraManagerRef.current.takePhoto();\n      if (photoData && photoData.file) {\n        const result = handleImageFile(photoData.file, target);\n        stopCamera();\n        Toast.show({\n          content: `${target === 'front' ? '正面' : '反面'}拍照完成`,\n          position: 'center'\n        });\n        return result;\n      }\n    } catch (error) {\n      console.error('❌ 拍照失敗:', error);\n      Toast.show({\n        content: '拍照失敗，請重試',\n        position: 'center'\n      });\n    }\n    return null;\n  }, [currentTarget, handleImageFile, stopCamera]);\n\n  /**\n   * 從相冊選擇圖片\n   */\n  const selectFromGallery = useCallback((target = currentTarget) => {\n    setCurrentTarget(target);\n\n    // 創建文件輸入元素\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = 'image/*';\n    input.capture = 'environment'; // 優先使用後置攝像頭\n\n    input.onchange = event => {\n      const file = event.target.files[0];\n      if (file) {\n        const result = handleImageFile(file, target);\n        if (result) {\n          Toast.show({\n            content: `${target === 'front' ? '正面' : '反面'}圖片已選擇`,\n            position: 'center'\n          });\n        }\n      }\n    };\n    input.click();\n  }, [currentTarget, handleImageFile]);\n\n  /**\n   * 移除圖片\n   */\n  const removeImage = useCallback(target => {\n    const oldPreview = images[target].preview;\n    if (oldPreview) {\n      revokeImagePreviewUrl(oldPreview);\n    }\n    updateImageState(target, {\n      file: null,\n      preview: null,\n      ocrText: '',\n      parseStatus: null\n    });\n    console.log(`🗑️ 移除圖片 - ${target}面`);\n  }, [images, updateImageState]);\n\n  /**\n   * 切換拍攝目標\n   */\n  const switchTarget = useCallback(target => {\n    setCurrentTarget(target);\n    console.log(`🔄 切換拍攝目標: ${target}面`);\n  }, []);\n\n  /**\n   * 更新OCR結果\n   */\n  const updateOCRResult = useCallback((target, ocrText, parseStatus = null) => {\n    updateImageState(target, {\n      ocrText,\n      parseStatus\n    });\n  }, [updateImageState]);\n\n  /**\n   * 清空所有圖片\n   */\n  const clearAllImages = useCallback(() => {\n    // 釋放預覽URL\n    Object.keys(images).forEach(target => {\n      const preview = images[target].preview;\n      if (preview) {\n        revokeImagePreviewUrl(preview);\n      }\n    });\n    setImages({\n      front: {\n        file: null,\n        preview: null,\n        ocrText: '',\n        parseStatus: null\n      },\n      back: {\n        file: null,\n        preview: null,\n        ocrText: '',\n        parseStatus: null\n      }\n    });\n    console.log('🧹 清空所有圖片');\n  }, [images]);\n\n  /**\n   * 檢查是否有圖片\n   */\n  const hasImages = useCallback((target = 'any') => {\n    if (target === 'any') {\n      return !!(images.front.file || images.back.file);\n    }\n    return !!images[target].file;\n  }, [images]);\n\n  /**\n   * 獲取圖片數據用於提交\n   */\n  const getImagesForSubmit = useCallback(() => {\n    const result = {};\n    if (images.front.file) {\n      result.front = {\n        file: images.front.file,\n        ocrText: images.front.ocrText\n      };\n    }\n    if (images.back.file) {\n      result.back = {\n        file: images.back.file,\n        ocrText: images.back.ocrText\n      };\n    }\n    return result;\n  }, [images]);\n\n  // 初始化相機管理器\n  useEffect(() => {\n    initializeCameraManager();\n  }, [initializeCameraManager]);\n\n  // 清理預覽URL\n  useEffect(() => {\n    return () => {\n      Object.keys(images).forEach(target => {\n        const preview = images[target].preview;\n        if (preview) {\n          revokeImagePreviewUrl(preview);\n        }\n      });\n    };\n  }, []);\n  return {\n    // 狀態\n    images,\n    currentTarget,\n    cameraStatus,\n    cameraModalVisible,\n    deviceType,\n    cameraManager: cameraManagerRef.current,\n    // 操作方法\n    startCamera,\n    stopCamera,\n    capturePhoto,\n    selectFromGallery,\n    removeImage,\n    switchTarget,\n    updateOCRResult,\n    clearAllImages,\n    // 工具方法\n    hasImages,\n    getImagesForSubmit,\n    // 便捷屬性\n    isMobile: deviceType === 'mobile',\n    isTablet: deviceType === 'tablet',\n    isCameraActive: cameraStatus === CAMERA_STATUS.ACTIVE,\n    isCameraStarting: cameraStatus === CAMERA_STATUS.STARTING,\n    hasFrontImage: !!images.front.file,\n    hasBackImage: !!images.back.file,\n    hasAnyImage: hasImages('any')\n  };\n};\n_s(useCameraState, \"m4SPpSvHbO4WVG3LRNNvmUP6iJ0=\");\nexport default useCameraState;", "map": {"version": 3, "names": ["useState", "useCallback", "useRef", "useEffect", "Toast", "getCameraManager", "getDeviceType", "validateImageFile", "getImagePreviewUrl", "revokeImagePreviewUrl", "CAMERA_TARGET", "CAMERA_STATUS", "ERROR_MESSAGES", "useCameraState", "_s", "images", "setImages", "front", "file", "preview", "ocrText", "parseStatus", "back", "currentTarget", "set<PERSON><PERSON><PERSON><PERSON>arget", "FRONT", "cameraStatus", "setCameraStatus", "INACTIVE", "cameraModalVisible", "setCameraModalVisible", "deviceType", "setDeviceType", "cameraManagerRef", "fileInputRef", "initializeCameraManager", "current", "initialize", "type", "console", "log", "error", "show", "content", "position", "updateImageState", "target", "updates", "prev", "handleImageFile", "validation", "<PERSON><PERSON><PERSON><PERSON>", "oldPreview", "name", "startCamera", "STARTING", "ACTIVE", "ERROR", "message", "CAMERA_PERMISSION_DENIED", "CAMERA_NOT_FOUND", "CAMERA_OCCUPIED", "stopCamera", "capturePhoto", "photoData", "<PERSON><PERSON><PERSON><PERSON>", "result", "selectFromGallery", "input", "document", "createElement", "accept", "capture", "onchange", "event", "files", "click", "removeImage", "switchTarget", "updateOCRResult", "clearAllImages", "Object", "keys", "for<PERSON>ach", "hasImages", "getImagesForSubmit", "cameraManager", "isMobile", "isTablet", "isCameraActive", "isCameraStarting", "hasFrontImage", "hasBackImage", "hasAnyImage"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/hooks/useCameraState.js"], "sourcesContent": ["/**\n * 相機狀態管理Hook\n * 管理相機拍攝、圖片選擇和預覽功能\n */\n\nimport { useState, useCallback, useRef, useEffect } from 'react';\nimport { Toast } from 'antd-mobile';\nimport { getCameraManager } from '../utils/cameraManager';\nimport { getDeviceType } from '../utils/deviceDetector';\nimport { validateImageFile, getImagePreviewUrl, revokeImagePreviewUrl } from '../utils/imageUtils';\nimport { CAMERA_TARGET, CAMERA_STATUS, ERROR_MESSAGES } from '../utils/constants';\n\nexport const useCameraState = () => {\n  const [images, setImages] = useState({\n    front: { file: null, preview: null, ocrText: '', parseStatus: null },\n    back: { file: null, preview: null, ocrText: '', parseStatus: null }\n  });\n  \n  const [currentTarget, setCurrentTarget] = useState(CAMERA_TARGET.FRONT);\n  const [cameraStatus, setCameraStatus] = useState(CAMERA_STATUS.INACTIVE);\n  const [cameraModalVisible, setCameraModalVisible] = useState(false);\n  const [deviceType, setDeviceType] = useState('desktop');\n  \n  const cameraManagerRef = useRef(null);\n  const fileInputRef = useRef(null);\n\n  /**\n   * 初始化相機管理器\n   */\n  const initializeCameraManager = useCallback(async () => {\n    try {\n      if (!cameraManagerRef.current) {\n        cameraManagerRef.current = getCameraManager();\n        await cameraManagerRef.current.initialize();\n      }\n      \n      const type = getDeviceType();\n      setDeviceType(type);\n      \n      console.log('📱 設備類型:', type);\n      console.log('📷 相機管理器初始化完成');\n    } catch (error) {\n      console.error('❌ 相機管理器初始化失敗:', error);\n      Toast.show({\n        content: '相機初始化失敗',\n        position: 'center',\n      });\n    }\n  }, []);\n\n  /**\n   * 更新圖片狀態\n   */\n  const updateImageState = useCallback((target, updates) => {\n    setImages(prev => ({\n      ...prev,\n      [target]: { ...prev[target], ...updates }\n    }));\n  }, []);\n\n  /**\n   * 處理圖片文件\n   */\n  const handleImageFile = useCallback((file, target) => {\n    // 驗證圖片文件\n    const validation = validateImageFile(file);\n    if (!validation.isValid) {\n      Toast.show({\n        content: validation.error,\n        position: 'center',\n      });\n      return null;\n    }\n\n    // 釋放舊的預覽URL\n    const oldPreview = images[target].preview;\n    if (oldPreview) {\n      revokeImagePreviewUrl(oldPreview);\n    }\n\n    // 創建新的預覽URL\n    const preview = getImagePreviewUrl(file);\n    \n    // 更新狀態\n    updateImageState(target, {\n      file,\n      preview,\n      ocrText: '',\n      parseStatus: null\n    });\n\n    console.log(`📸 圖片已設置 - ${target}面:`, file.name);\n    return { file, preview };\n  }, [images, updateImageState]);\n\n  /**\n   * 啟動相機\n   */\n  const startCamera = useCallback(async (target = currentTarget) => {\n    if (!cameraManagerRef.current) {\n      await initializeCameraManager();\n    }\n\n    setCurrentTarget(target);\n    setCameraStatus(CAMERA_STATUS.STARTING);\n    setCameraModalVisible(true);\n\n    try {\n      console.log(`📷 啟動相機 - ${target}面`);\n      setCameraStatus(CAMERA_STATUS.ACTIVE);\n    } catch (error) {\n      console.error('❌ 啟動相機失敗:', error);\n      setCameraStatus(CAMERA_STATUS.ERROR);\n      setCameraModalVisible(false);\n      \n      let message = ERROR_MESSAGES.CAMERA_PERMISSION_DENIED;\n      if (error.name === 'NotFoundError') {\n        message = ERROR_MESSAGES.CAMERA_NOT_FOUND;\n      } else if (error.name === 'NotReadableError') {\n        message = ERROR_MESSAGES.CAMERA_OCCUPIED;\n      }\n      \n      Toast.show({\n        content: message,\n        position: 'center',\n      });\n    }\n  }, [currentTarget, initializeCameraManager]);\n\n  /**\n   * 停止相機\n   */\n  const stopCamera = useCallback(() => {\n    if (cameraManagerRef.current) {\n      cameraManagerRef.current.stopCamera();\n    }\n    setCameraStatus(CAMERA_STATUS.INACTIVE);\n    setCameraModalVisible(false);\n    console.log('📷 相機已停止');\n  }, []);\n\n  /**\n   * 拍照\n   */\n  const capturePhoto = useCallback(async (target = currentTarget) => {\n    if (!cameraManagerRef.current) {\n      Toast.show({\n        content: '相機未初始化',\n        position: 'center',\n      });\n      return null;\n    }\n\n    try {\n      const photoData = await cameraManagerRef.current.takePhoto();\n      if (photoData && photoData.file) {\n        const result = handleImageFile(photoData.file, target);\n        stopCamera();\n        \n        Toast.show({\n          content: `${target === 'front' ? '正面' : '反面'}拍照完成`,\n          position: 'center',\n        });\n        \n        return result;\n      }\n    } catch (error) {\n      console.error('❌ 拍照失敗:', error);\n      Toast.show({\n        content: '拍照失敗，請重試',\n        position: 'center',\n      });\n    }\n    \n    return null;\n  }, [currentTarget, handleImageFile, stopCamera]);\n\n  /**\n   * 從相冊選擇圖片\n   */\n  const selectFromGallery = useCallback((target = currentTarget) => {\n    setCurrentTarget(target);\n    \n    // 創建文件輸入元素\n    const input = document.createElement('input');\n    input.type = 'file';\n    input.accept = 'image/*';\n    input.capture = 'environment'; // 優先使用後置攝像頭\n    \n    input.onchange = (event) => {\n      const file = event.target.files[0];\n      if (file) {\n        const result = handleImageFile(file, target);\n        if (result) {\n          Toast.show({\n            content: `${target === 'front' ? '正面' : '反面'}圖片已選擇`,\n            position: 'center',\n          });\n        }\n      }\n    };\n    \n    input.click();\n  }, [currentTarget, handleImageFile]);\n\n  /**\n   * 移除圖片\n   */\n  const removeImage = useCallback((target) => {\n    const oldPreview = images[target].preview;\n    if (oldPreview) {\n      revokeImagePreviewUrl(oldPreview);\n    }\n    \n    updateImageState(target, {\n      file: null,\n      preview: null,\n      ocrText: '',\n      parseStatus: null\n    });\n    \n    console.log(`🗑️ 移除圖片 - ${target}面`);\n  }, [images, updateImageState]);\n\n  /**\n   * 切換拍攝目標\n   */\n  const switchTarget = useCallback((target) => {\n    setCurrentTarget(target);\n    console.log(`🔄 切換拍攝目標: ${target}面`);\n  }, []);\n\n  /**\n   * 更新OCR結果\n   */\n  const updateOCRResult = useCallback((target, ocrText, parseStatus = null) => {\n    updateImageState(target, {\n      ocrText,\n      parseStatus\n    });\n  }, [updateImageState]);\n\n  /**\n   * 清空所有圖片\n   */\n  const clearAllImages = useCallback(() => {\n    // 釋放預覽URL\n    Object.keys(images).forEach(target => {\n      const preview = images[target].preview;\n      if (preview) {\n        revokeImagePreviewUrl(preview);\n      }\n    });\n    \n    setImages({\n      front: { file: null, preview: null, ocrText: '', parseStatus: null },\n      back: { file: null, preview: null, ocrText: '', parseStatus: null }\n    });\n    \n    console.log('🧹 清空所有圖片');\n  }, [images]);\n\n  /**\n   * 檢查是否有圖片\n   */\n  const hasImages = useCallback((target = 'any') => {\n    if (target === 'any') {\n      return !!(images.front.file || images.back.file);\n    }\n    return !!images[target].file;\n  }, [images]);\n\n  /**\n   * 獲取圖片數據用於提交\n   */\n  const getImagesForSubmit = useCallback(() => {\n    const result = {};\n    \n    if (images.front.file) {\n      result.front = {\n        file: images.front.file,\n        ocrText: images.front.ocrText\n      };\n    }\n    \n    if (images.back.file) {\n      result.back = {\n        file: images.back.file,\n        ocrText: images.back.ocrText\n      };\n    }\n    \n    return result;\n  }, [images]);\n\n  // 初始化相機管理器\n  useEffect(() => {\n    initializeCameraManager();\n  }, [initializeCameraManager]);\n\n  // 清理預覽URL\n  useEffect(() => {\n    return () => {\n      Object.keys(images).forEach(target => {\n        const preview = images[target].preview;\n        if (preview) {\n          revokeImagePreviewUrl(preview);\n        }\n      });\n    };\n  }, []);\n\n  return {\n    // 狀態\n    images,\n    currentTarget,\n    cameraStatus,\n    cameraModalVisible,\n    deviceType,\n    cameraManager: cameraManagerRef.current,\n    \n    // 操作方法\n    startCamera,\n    stopCamera,\n    capturePhoto,\n    selectFromGallery,\n    removeImage,\n    switchTarget,\n    updateOCRResult,\n    clearAllImages,\n    \n    // 工具方法\n    hasImages,\n    getImagesForSubmit,\n    \n    // 便捷屬性\n    isMobile: deviceType === 'mobile',\n    isTablet: deviceType === 'tablet',\n    isCameraActive: cameraStatus === CAMERA_STATUS.ACTIVE,\n    isCameraStarting: cameraStatus === CAMERA_STATUS.STARTING,\n    hasFrontImage: !!images.front.file,\n    hasBackImage: !!images.back.file,\n    hasAnyImage: hasImages('any')\n  };\n};\n\nexport default useCameraState;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,WAAW,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAChE,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,gBAAgB,QAAQ,wBAAwB;AACzD,SAASC,aAAa,QAAQ,yBAAyB;AACvD,SAASC,iBAAiB,EAAEC,kBAAkB,EAAEC,qBAAqB,QAAQ,qBAAqB;AAClG,SAASC,aAAa,EAAEC,aAAa,EAAEC,cAAc,QAAQ,oBAAoB;AAEjF,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAClC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGhB,QAAQ,CAAC;IACnCiB,KAAK,EAAE;MAAEC,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAK,CAAC;IACpEC,IAAI,EAAE;MAAEJ,IAAI,EAAE,IAAI;MAAEC,OAAO,EAAE,IAAI;MAAEC,OAAO,EAAE,EAAE;MAAEC,WAAW,EAAE;IAAK;EACpE,CAAC,CAAC;EAEF,MAAM,CAACE,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAACU,aAAa,CAACe,KAAK,CAAC;EACvE,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAACW,aAAa,CAACiB,QAAQ,CAAC;EACxE,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAG9B,QAAQ,CAAC,KAAK,CAAC;EACnE,MAAM,CAAC+B,UAAU,EAAEC,aAAa,CAAC,GAAGhC,QAAQ,CAAC,SAAS,CAAC;EAEvD,MAAMiC,gBAAgB,GAAG/B,MAAM,CAAC,IAAI,CAAC;EACrC,MAAMgC,YAAY,GAAGhC,MAAM,CAAC,IAAI,CAAC;;EAEjC;AACF;AACA;EACE,MAAMiC,uBAAuB,GAAGlC,WAAW,CAAC,YAAY;IACtD,IAAI;MACF,IAAI,CAACgC,gBAAgB,CAACG,OAAO,EAAE;QAC7BH,gBAAgB,CAACG,OAAO,GAAG/B,gBAAgB,CAAC,CAAC;QAC7C,MAAM4B,gBAAgB,CAACG,OAAO,CAACC,UAAU,CAAC,CAAC;MAC7C;MAEA,MAAMC,IAAI,GAAGhC,aAAa,CAAC,CAAC;MAC5B0B,aAAa,CAACM,IAAI,CAAC;MAEnBC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAEF,IAAI,CAAC;MAC7BC,OAAO,CAACC,GAAG,CAAC,eAAe,CAAC;IAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;MACrCrC,KAAK,CAACsC,IAAI,CAAC;QACTC,OAAO,EAAE,SAAS;QAClBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMC,gBAAgB,GAAG5C,WAAW,CAAC,CAAC6C,MAAM,EAAEC,OAAO,KAAK;IACxD/B,SAAS,CAACgC,IAAI,KAAK;MACjB,GAAGA,IAAI;MACP,CAACF,MAAM,GAAG;QAAE,GAAGE,IAAI,CAACF,MAAM,CAAC;QAAE,GAAGC;MAAQ;IAC1C,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAME,eAAe,GAAGhD,WAAW,CAAC,CAACiB,IAAI,EAAE4B,MAAM,KAAK;IACpD;IACA,MAAMI,UAAU,GAAG3C,iBAAiB,CAACW,IAAI,CAAC;IAC1C,IAAI,CAACgC,UAAU,CAACC,OAAO,EAAE;MACvB/C,KAAK,CAACsC,IAAI,CAAC;QACTC,OAAO,EAAEO,UAAU,CAACT,KAAK;QACzBG,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,OAAO,IAAI;IACb;;IAEA;IACA,MAAMQ,UAAU,GAAGrC,MAAM,CAAC+B,MAAM,CAAC,CAAC3B,OAAO;IACzC,IAAIiC,UAAU,EAAE;MACd3C,qBAAqB,CAAC2C,UAAU,CAAC;IACnC;;IAEA;IACA,MAAMjC,OAAO,GAAGX,kBAAkB,CAACU,IAAI,CAAC;;IAExC;IACA2B,gBAAgB,CAACC,MAAM,EAAE;MACvB5B,IAAI;MACJC,OAAO;MACPC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFkB,OAAO,CAACC,GAAG,CAAC,cAAcM,MAAM,IAAI,EAAE5B,IAAI,CAACmC,IAAI,CAAC;IAChD,OAAO;MAAEnC,IAAI;MAAEC;IAAQ,CAAC;EAC1B,CAAC,EAAE,CAACJ,MAAM,EAAE8B,gBAAgB,CAAC,CAAC;;EAE9B;AACF;AACA;EACE,MAAMS,WAAW,GAAGrD,WAAW,CAAC,OAAO6C,MAAM,GAAGvB,aAAa,KAAK;IAChE,IAAI,CAACU,gBAAgB,CAACG,OAAO,EAAE;MAC7B,MAAMD,uBAAuB,CAAC,CAAC;IACjC;IAEAX,gBAAgB,CAACsB,MAAM,CAAC;IACxBnB,eAAe,CAAChB,aAAa,CAAC4C,QAAQ,CAAC;IACvCzB,qBAAqB,CAAC,IAAI,CAAC;IAE3B,IAAI;MACFS,OAAO,CAACC,GAAG,CAAC,aAAaM,MAAM,GAAG,CAAC;MACnCnB,eAAe,CAAChB,aAAa,CAAC6C,MAAM,CAAC;IACvC,CAAC,CAAC,OAAOf,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjCd,eAAe,CAAChB,aAAa,CAAC8C,KAAK,CAAC;MACpC3B,qBAAqB,CAAC,KAAK,CAAC;MAE5B,IAAI4B,OAAO,GAAG9C,cAAc,CAAC+C,wBAAwB;MACrD,IAAIlB,KAAK,CAACY,IAAI,KAAK,eAAe,EAAE;QAClCK,OAAO,GAAG9C,cAAc,CAACgD,gBAAgB;MAC3C,CAAC,MAAM,IAAInB,KAAK,CAACY,IAAI,KAAK,kBAAkB,EAAE;QAC5CK,OAAO,GAAG9C,cAAc,CAACiD,eAAe;MAC1C;MAEAzD,KAAK,CAACsC,IAAI,CAAC;QACTC,OAAO,EAAEe,OAAO;QAChBd,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACrB,aAAa,EAAEY,uBAAuB,CAAC,CAAC;;EAE5C;AACF;AACA;EACE,MAAM2B,UAAU,GAAG7D,WAAW,CAAC,MAAM;IACnC,IAAIgC,gBAAgB,CAACG,OAAO,EAAE;MAC5BH,gBAAgB,CAACG,OAAO,CAAC0B,UAAU,CAAC,CAAC;IACvC;IACAnC,eAAe,CAAChB,aAAa,CAACiB,QAAQ,CAAC;IACvCE,qBAAqB,CAAC,KAAK,CAAC;IAC5BS,OAAO,CAACC,GAAG,CAAC,UAAU,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMuB,YAAY,GAAG9D,WAAW,CAAC,OAAO6C,MAAM,GAAGvB,aAAa,KAAK;IACjE,IAAI,CAACU,gBAAgB,CAACG,OAAO,EAAE;MAC7BhC,KAAK,CAACsC,IAAI,CAAC;QACTC,OAAO,EAAE,QAAQ;QACjBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,OAAO,IAAI;IACb;IAEA,IAAI;MACF,MAAMoB,SAAS,GAAG,MAAM/B,gBAAgB,CAACG,OAAO,CAAC6B,SAAS,CAAC,CAAC;MAC5D,IAAID,SAAS,IAAIA,SAAS,CAAC9C,IAAI,EAAE;QAC/B,MAAMgD,MAAM,GAAGjB,eAAe,CAACe,SAAS,CAAC9C,IAAI,EAAE4B,MAAM,CAAC;QACtDgB,UAAU,CAAC,CAAC;QAEZ1D,KAAK,CAACsC,IAAI,CAAC;UACTC,OAAO,EAAE,GAAGG,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,MAAM;UAClDF,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,OAAOsB,MAAM;MACf;IACF,CAAC,CAAC,OAAOzB,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BrC,KAAK,CAACsC,IAAI,CAAC;QACTC,OAAO,EAAE,UAAU;QACnBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI;EACb,CAAC,EAAE,CAACrB,aAAa,EAAE0B,eAAe,EAAEa,UAAU,CAAC,CAAC;;EAEhD;AACF;AACA;EACE,MAAMK,iBAAiB,GAAGlE,WAAW,CAAC,CAAC6C,MAAM,GAAGvB,aAAa,KAAK;IAChEC,gBAAgB,CAACsB,MAAM,CAAC;;IAExB;IACA,MAAMsB,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,OAAO,CAAC;IAC7CF,KAAK,CAAC9B,IAAI,GAAG,MAAM;IACnB8B,KAAK,CAACG,MAAM,GAAG,SAAS;IACxBH,KAAK,CAACI,OAAO,GAAG,aAAa,CAAC,CAAC;;IAE/BJ,KAAK,CAACK,QAAQ,GAAIC,KAAK,IAAK;MAC1B,MAAMxD,IAAI,GAAGwD,KAAK,CAAC5B,MAAM,CAAC6B,KAAK,CAAC,CAAC,CAAC;MAClC,IAAIzD,IAAI,EAAE;QACR,MAAMgD,MAAM,GAAGjB,eAAe,CAAC/B,IAAI,EAAE4B,MAAM,CAAC;QAC5C,IAAIoB,MAAM,EAAE;UACV9D,KAAK,CAACsC,IAAI,CAAC;YACTC,OAAO,EAAE,GAAGG,MAAM,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,OAAO;YACnDF,QAAQ,EAAE;UACZ,CAAC,CAAC;QACJ;MACF;IACF,CAAC;IAEDwB,KAAK,CAACQ,KAAK,CAAC,CAAC;EACf,CAAC,EAAE,CAACrD,aAAa,EAAE0B,eAAe,CAAC,CAAC;;EAEpC;AACF;AACA;EACE,MAAM4B,WAAW,GAAG5E,WAAW,CAAE6C,MAAM,IAAK;IAC1C,MAAMM,UAAU,GAAGrC,MAAM,CAAC+B,MAAM,CAAC,CAAC3B,OAAO;IACzC,IAAIiC,UAAU,EAAE;MACd3C,qBAAqB,CAAC2C,UAAU,CAAC;IACnC;IAEAP,gBAAgB,CAACC,MAAM,EAAE;MACvB5B,IAAI,EAAE,IAAI;MACVC,OAAO,EAAE,IAAI;MACbC,OAAO,EAAE,EAAE;MACXC,WAAW,EAAE;IACf,CAAC,CAAC;IAEFkB,OAAO,CAACC,GAAG,CAAC,cAAcM,MAAM,GAAG,CAAC;EACtC,CAAC,EAAE,CAAC/B,MAAM,EAAE8B,gBAAgB,CAAC,CAAC;;EAE9B;AACF;AACA;EACE,MAAMiC,YAAY,GAAG7E,WAAW,CAAE6C,MAAM,IAAK;IAC3CtB,gBAAgB,CAACsB,MAAM,CAAC;IACxBP,OAAO,CAACC,GAAG,CAAC,cAAcM,MAAM,GAAG,CAAC;EACtC,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMiC,eAAe,GAAG9E,WAAW,CAAC,CAAC6C,MAAM,EAAE1B,OAAO,EAAEC,WAAW,GAAG,IAAI,KAAK;IAC3EwB,gBAAgB,CAACC,MAAM,EAAE;MACvB1B,OAAO;MACPC;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACwB,gBAAgB,CAAC,CAAC;;EAEtB;AACF;AACA;EACE,MAAMmC,cAAc,GAAG/E,WAAW,CAAC,MAAM;IACvC;IACAgF,MAAM,CAACC,IAAI,CAACnE,MAAM,CAAC,CAACoE,OAAO,CAACrC,MAAM,IAAI;MACpC,MAAM3B,OAAO,GAAGJ,MAAM,CAAC+B,MAAM,CAAC,CAAC3B,OAAO;MACtC,IAAIA,OAAO,EAAE;QACXV,qBAAqB,CAACU,OAAO,CAAC;MAChC;IACF,CAAC,CAAC;IAEFH,SAAS,CAAC;MACRC,KAAK,EAAE;QAAEC,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE,EAAE;QAAEC,WAAW,EAAE;MAAK,CAAC;MACpEC,IAAI,EAAE;QAAEJ,IAAI,EAAE,IAAI;QAAEC,OAAO,EAAE,IAAI;QAAEC,OAAO,EAAE,EAAE;QAAEC,WAAW,EAAE;MAAK;IACpE,CAAC,CAAC;IAEFkB,OAAO,CAACC,GAAG,CAAC,WAAW,CAAC;EAC1B,CAAC,EAAE,CAACzB,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMqE,SAAS,GAAGnF,WAAW,CAAC,CAAC6C,MAAM,GAAG,KAAK,KAAK;IAChD,IAAIA,MAAM,KAAK,KAAK,EAAE;MACpB,OAAO,CAAC,EAAE/B,MAAM,CAACE,KAAK,CAACC,IAAI,IAAIH,MAAM,CAACO,IAAI,CAACJ,IAAI,CAAC;IAClD;IACA,OAAO,CAAC,CAACH,MAAM,CAAC+B,MAAM,CAAC,CAAC5B,IAAI;EAC9B,CAAC,EAAE,CAACH,MAAM,CAAC,CAAC;;EAEZ;AACF;AACA;EACE,MAAMsE,kBAAkB,GAAGpF,WAAW,CAAC,MAAM;IAC3C,MAAMiE,MAAM,GAAG,CAAC,CAAC;IAEjB,IAAInD,MAAM,CAACE,KAAK,CAACC,IAAI,EAAE;MACrBgD,MAAM,CAACjD,KAAK,GAAG;QACbC,IAAI,EAAEH,MAAM,CAACE,KAAK,CAACC,IAAI;QACvBE,OAAO,EAAEL,MAAM,CAACE,KAAK,CAACG;MACxB,CAAC;IACH;IAEA,IAAIL,MAAM,CAACO,IAAI,CAACJ,IAAI,EAAE;MACpBgD,MAAM,CAAC5C,IAAI,GAAG;QACZJ,IAAI,EAAEH,MAAM,CAACO,IAAI,CAACJ,IAAI;QACtBE,OAAO,EAAEL,MAAM,CAACO,IAAI,CAACF;MACvB,CAAC;IACH;IAEA,OAAO8C,MAAM;EACf,CAAC,EAAE,CAACnD,MAAM,CAAC,CAAC;;EAEZ;EACAZ,SAAS,CAAC,MAAM;IACdgC,uBAAuB,CAAC,CAAC;EAC3B,CAAC,EAAE,CAACA,uBAAuB,CAAC,CAAC;;EAE7B;EACAhC,SAAS,CAAC,MAAM;IACd,OAAO,MAAM;MACX8E,MAAM,CAACC,IAAI,CAACnE,MAAM,CAAC,CAACoE,OAAO,CAACrC,MAAM,IAAI;QACpC,MAAM3B,OAAO,GAAGJ,MAAM,CAAC+B,MAAM,CAAC,CAAC3B,OAAO;QACtC,IAAIA,OAAO,EAAE;UACXV,qBAAqB,CAACU,OAAO,CAAC;QAChC;MACF,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,OAAO;IACL;IACAJ,MAAM;IACNQ,aAAa;IACbG,YAAY;IACZG,kBAAkB;IAClBE,UAAU;IACVuD,aAAa,EAAErD,gBAAgB,CAACG,OAAO;IAEvC;IACAkB,WAAW;IACXQ,UAAU;IACVC,YAAY;IACZI,iBAAiB;IACjBU,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,cAAc;IAEd;IACAI,SAAS;IACTC,kBAAkB;IAElB;IACAE,QAAQ,EAAExD,UAAU,KAAK,QAAQ;IACjCyD,QAAQ,EAAEzD,UAAU,KAAK,QAAQ;IACjC0D,cAAc,EAAE/D,YAAY,KAAKf,aAAa,CAAC6C,MAAM;IACrDkC,gBAAgB,EAAEhE,YAAY,KAAKf,aAAa,CAAC4C,QAAQ;IACzDoC,aAAa,EAAE,CAAC,CAAC5E,MAAM,CAACE,KAAK,CAACC,IAAI;IAClC0E,YAAY,EAAE,CAAC,CAAC7E,MAAM,CAACO,IAAI,CAACJ,IAAI;IAChC2E,WAAW,EAAET,SAAS,CAAC,KAAK;EAC9B,CAAC;AACH,CAAC;AAACtE,EAAA,CA5UWD,cAAc;AA8U3B,eAAeA,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}