{"ast": null, "code": "import React from 'react';\nexport const defaultImage = React.createElement(\"svg\", {\n  viewBox: '0 0 200 200',\n  xmlns: 'http://www.w3.org/2000/svg',\n  xmlnsXlink: 'http://www.w3.org/1999/xlink'\n}, React.createElement(\"defs\", null, React.createElement(\"linearGradient\", {\n  x1: '50%',\n  y1: '-116.862%',\n  x2: '50%',\n  y2: '90.764%',\n  id: 'error-block-image-default-a'\n}, React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.207,\n  offset: '0%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.115,\n  offset: '80.072%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0,\n  offset: '100%'\n})), React.createElement(\"circle\", {\n  id: 'error-block-image-default-d',\n  cx: 18.823,\n  cy: 18.823,\n  r: 18.823\n}), React.createElement(\"rect\", {\n  id: 'error-block-image-default-b',\n  x: 3.5,\n  y: 9,\n  width: 51.429,\n  height: 88,\n  rx: 4.571\n})), React.createElement(\"g\", {\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M73.557.004c19.435-.311 38.696 17.016 51.523 35.287 8.708-10.822 17.127-16.233 25.255-16.233 13.333 0 28.35 14.274 45.053 42.822 1.769 3.024-3.582 7.435-16.054 13.231l-41.322 1.37c-7.343 5.872-31.225.626-69.152 1.234-27.79.445-45.759-1.234-53.908-5.037C3.2 71.143-1.625 68.686.48 65.308 27.371 22.12 51.73.353 73.557.003Zm93.098 49.53a1.125 1.125 0 0 0-.401.072l-.058.023-.07.03-.028.014-.02.01c-.03.015-.059.032-.088.049a2.543 2.543 0 0 0-.568.477l-.067.074c-1.686 1.931-2.904 7.062-2.904 8.985 0 2.283 1.719 4.153 3.898 4.314l.026.001v3.805c0 .39.25.705.56.705.31 0 .56-.316.56-.705l.001-3.88c1.92-.402 3.363-2.148 3.363-4.24 0-2.39-1.882-9.734-4.204-9.734Zm-100-5a1.125 1.125 0 0 0-.331.05l-.035.01-.035.012-.058.023-.07.03-.028.014-.02.01c-.03.015-.059.032-.088.049a2.543 2.543 0 0 0-.568.477l-.067.074c-1.686 1.931-2.904 7.062-2.904 8.985 0 2.212 1.613 4.036 3.695 4.294l.203.02.026.001v3.805c0 .39.25.705.56.705.282 0 .515-.26.555-.6l.006-.105v-3.88c1.92-.402 3.363-2.148 3.363-4.24 0-2.39-1.882-9.734-4.204-9.734ZM52.64 38.348l-.15.008-.149.023-.032.007-.032.008-.078.022-.045.015-.045.016-.06.023-.038.017-.038.017-.058.028-.022.011a2.201 2.201 0 0 0-.323.204l-.05.038-.05.04-.025.02-.025.021a3.742 3.742 0 0 0-.31.294l-.036.04c-.035.037-.07.076-.105.116-.01.012-.02.025-.031.036a3.275 3.275 0 0 0-.081.098l-.063.078c-2.031 2.583-3.48 8.692-3.48 11.027 0 2.636 1.846 4.832 4.292 5.323l.224.04-.064-.012.105.018.103.014v4.618c0 .47.299.85.667.85.337 0 .615-.32.659-.735l.006-.115v-4.618c.18-.023.355-.054.527-.094l.256-.067.196-.06c2.136-.706 3.68-2.75 3.68-5.162 0-2.996-2.383-12.207-5.325-12.207Z',\n  transform: 'translate(2.286 22.286)',\n  fill: 'url(#error-block-image-default-a)'\n}), React.createElement(\"g\", {\n  transform: 'rotate(-90 102.429 55.357)'\n}, React.createElement(\"path\", {\n  d: 'M6.857 0H52a6.857 6.857 0 0 1 6.857 6.857v92A6.857 6.857 0 0 1 52 105.714H6.857A6.857 6.857 0 0 1 0 98.857v-92A6.857 6.857 0 0 1 6.857 0Z',\n  fill: '#7EACFF'\n}), React.createElement(\"mask\", {\n  id: 'error-block-image-default-c',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-default-b'\n})), React.createElement(\"use\", {\n  fill: '#377EFF',\n  xlinkHref: '#error-block-image-default-b'\n}), React.createElement(\"path\", {\n  d: 'M11.838 91.8a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.628-.652c0-.36.281-.651.628-.651Zm-2.858 0a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.652.64.64 0 0 1-.627-.652c0-.36.281-.651.627-.651Zm2.16-2.305a.64.64 0 0 1 .628.652.64.64 0 0 1-.627.651.64.64 0 0 1-.627-.651c0-.36.28-.652.627-.652Zm-2.982-.04a.64.64 0 0 1 .627.651.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.651.627-.651Zm5.268-.531a.64.64 0 0 1 .628.651.64.64 0 0 1-.628.652.64.64 0 0 1-.627-.652c0-.36.281-.651.627-.651Zm2.858-1.143a.64.64 0 0 1 .627.651.64.64 0 0 1-.627.652.64.64 0 0 1-.628-.652c0-.36.281-.651.628-.651Zm-6.37-.917c.209 0 .377.175.377.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm3.512-.798.093.007a.644.644 0 0 1 .535.645.64.64 0 0 1-.628.652.64.64 0 0 1-.627-.652c0-.36.281-.652.627-.652Zm5.715 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.652.627-.652Zm-11.429 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.652.627-.652Zm-3.261.241c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.377-.391c0-.216.169-.391.377-.391Zm11.833-.812a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.651.64.64 0 0 1-.628-.651c0-.36.281-.652.628-.652Zm-4.851.399c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.377-.391c0-.216.169-.391.377-.391Zm10.313-2.056a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.651.64.64 0 0 1-.627-.651c0-.36.281-.652.627-.652Zm-2.354-.128a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.628-.652c0-.36.281-.652.628-.652Zm-13.798.311c.207 0 .376.175.376.391a.384.384 0 0 1-.376.391.384.384 0 0 1-.377-.39c0-.217.169-.392.377-.392Zm11.832-.812a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.651.64.64 0 0 1-.627-.651c0-.36.281-.652.627-.652Zm-6.285 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.651.64.64 0 0 1-.627-.651c0-.36.28-.652.627-.652Zm3.428 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.651.64.64 0 0 1-.627-.651c0-.36.28-.652.627-.652Zm-6.118.24c.208 0 .376.176.376.392a.384.384 0 0 1-.376.39.384.384 0 0 1-.377-.39c0-.216.169-.391.377-.391Zm11.261-2.525a.64.64 0 0 1 .627.651.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.651.627-.651Zm-3.557.484c.208 0 .376.175.376.391a.384.384 0 0 1-.376.391.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-2.478-.555a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.652.627-.652Zm-3.512-.26c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-2.857 0c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-4.571 0c.207 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.377-.391c0-.216.169-.391.377-.391Zm14.898-1.835a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.651.64.64 0 0 1-.627-.651c0-.36.281-.652.627-.652Zm-8.027-.245c.208 0 .377.175.377.39a.384.384 0 0 1-.377.392.384.384 0 0 1-.376-.391c0-.216.169-.391.376-.391Zm6.271-1.349c.208 0 .377.175.377.391a.384.384 0 0 1-.377.391.384.384 0 0 1-.376-.39c0-.217.169-.392.376-.392Zm-11.484-.481c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm15.103-.972c.208 0 .376.175.376.391a.384.384 0 0 1-.376.391.384.384 0 0 1-.376-.39c0-.217.168-.392.376-.392Zm-9.333-1.404c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-6.819-.405c.208 0 .377.175.377.39a.384.384 0 0 1-.377.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Z',\n  fill: '#003CFF',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-c)',\n  transform: 'rotate(116 12.367 83.503)'\n}), React.createElement(\"path\", {\n  stroke: '#FFF',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n  d: 'M36.774 5.474H21.523'\n}), React.createElement(\"path\", {\n  d: 'm67.818 94.025-4.996 3.913m4.996 11.91-4.996-3.912m-1.142 9.145-1.143-6.288m10.71-6.768h-7.262',\n  stroke: '#4486FE',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n})), React.createElement(\"circle\", {\n  cx: 8.571,\n  cy: 8.571,\n  r: 8.571,\n  transform: 'translate(22.857 142)',\n  fill: '#FFCD6B',\n  fillRule: 'nonzero'\n}), React.createElement(\"g\", {\n  transform: 'translate(132.857 124)'\n}, React.createElement(\"mask\", {\n  id: 'error-block-image-default-e',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-default-d'\n})), React.createElement(\"use\", {\n  fill: '#FBBE47',\n  fillRule: 'nonzero',\n  xlinkHref: '#error-block-image-default-d'\n}), React.createElement(\"circle\", {\n  fill: '#FFCD6B',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 13.886,\n  cy: 15.12,\n  r: 18.823\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 23.4,\n  cy: 29.057,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 30.343,\n  cy: 29.829,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 18.771,\n  cy: 32.657,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 29.571,\n  cy: 25.971,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 19.286,\n  cy: 7.971,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 26.486,\n  cy: 5.914,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 11.057,\n  cy: 6.943,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 30.086,\n  cy: 15.686,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 22.886,\n  cy: 14.657,\n  r: 1\n})), React.createElement(\"path\", {\n  d: 'm87.429 135.123 6.591-9.378v-.08h-5.99v-2.559h10.038v1.787l-6.44 9.254v.082h6.56v2.557h-10.76v-1.663Zm12.185-5.889 4.948-7.047v-.056h-4.498v-1.917h7.536v1.34l-4.849 6.942v.059h4.923v1.92h-8.06v-1.24Zm10.345.702 3.708-5.274v-.045h-3.372v-1.437h5.648v1.003l-3.628 5.206v.045H116v1.438h-6.041v-.936Z',\n  fill: '#FFF',\n  fillRule: 'nonzero'\n})));", "map": {"version": 3, "names": ["React", "defaultImage", "createElement", "viewBox", "xmlns", "xmlnsXlink", "x1", "y1", "x2", "y2", "id", "stopColor", "stopOpacity", "offset", "cx", "cy", "r", "x", "y", "width", "height", "rx", "fill", "fillRule", "d", "transform", "xlinkHref", "mask", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/error-block/images/default.js"], "sourcesContent": ["import React from 'react';\nexport const defaultImage = React.createElement(\"svg\", {\n  viewBox: '0 0 200 200',\n  xmlns: 'http://www.w3.org/2000/svg',\n  xmlnsXlink: 'http://www.w3.org/1999/xlink'\n}, React.createElement(\"defs\", null, React.createElement(\"linearGradient\", {\n  x1: '50%',\n  y1: '-116.862%',\n  x2: '50%',\n  y2: '90.764%',\n  id: 'error-block-image-default-a'\n}, React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.207,\n  offset: '0%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.115,\n  offset: '80.072%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0,\n  offset: '100%'\n})), React.createElement(\"circle\", {\n  id: 'error-block-image-default-d',\n  cx: 18.823,\n  cy: 18.823,\n  r: 18.823\n}), React.createElement(\"rect\", {\n  id: 'error-block-image-default-b',\n  x: 3.5,\n  y: 9,\n  width: 51.429,\n  height: 88,\n  rx: 4.571\n})), React.createElement(\"g\", {\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M73.557.004c19.435-.311 38.696 17.016 51.523 35.287 8.708-10.822 17.127-16.233 25.255-16.233 13.333 0 28.35 14.274 45.053 42.822 1.769 3.024-3.582 7.435-16.054 13.231l-41.322 1.37c-7.343 5.872-31.225.626-69.152 1.234-27.79.445-45.759-1.234-53.908-5.037C3.2 71.143-1.625 68.686.48 65.308 27.371 22.12 51.73.353 73.557.003Zm93.098 49.53a1.125 1.125 0 0 0-.401.072l-.058.023-.07.03-.028.014-.02.01c-.03.015-.059.032-.088.049a2.543 2.543 0 0 0-.568.477l-.067.074c-1.686 1.931-2.904 7.062-2.904 8.985 0 2.283 1.719 4.153 3.898 4.314l.026.001v3.805c0 .39.25.705.56.705.31 0 .56-.316.56-.705l.001-3.88c1.92-.402 3.363-2.148 3.363-4.24 0-2.39-1.882-9.734-4.204-9.734Zm-100-5a1.125 1.125 0 0 0-.331.05l-.035.01-.035.012-.058.023-.07.03-.028.014-.02.01c-.03.015-.059.032-.088.049a2.543 2.543 0 0 0-.568.477l-.067.074c-1.686 1.931-2.904 7.062-2.904 8.985 0 2.212 1.613 4.036 3.695 4.294l.203.02.026.001v3.805c0 .39.25.705.56.705.282 0 .515-.26.555-.6l.006-.105v-3.88c1.92-.402 3.363-2.148 3.363-4.24 0-2.39-1.882-9.734-4.204-9.734ZM52.64 38.348l-.15.008-.149.023-.032.007-.032.008-.078.022-.045.015-.045.016-.06.023-.038.017-.038.017-.058.028-.022.011a2.201 2.201 0 0 0-.323.204l-.05.038-.05.04-.025.02-.025.021a3.742 3.742 0 0 0-.31.294l-.036.04c-.035.037-.07.076-.105.116-.01.012-.02.025-.031.036a3.275 3.275 0 0 0-.081.098l-.063.078c-2.031 2.583-3.48 8.692-3.48 11.027 0 2.636 1.846 4.832 4.292 5.323l.224.04-.064-.012.105.018.103.014v4.618c0 .47.299.85.667.85.337 0 .615-.32.659-.735l.006-.115v-4.618c.18-.023.355-.054.527-.094l.256-.067.196-.06c2.136-.706 3.68-2.75 3.68-5.162 0-2.996-2.383-12.207-5.325-12.207Z',\n  transform: 'translate(2.286 22.286)',\n  fill: 'url(#error-block-image-default-a)'\n}), React.createElement(\"g\", {\n  transform: 'rotate(-90 102.429 55.357)'\n}, React.createElement(\"path\", {\n  d: 'M6.857 0H52a6.857 6.857 0 0 1 6.857 6.857v92A6.857 6.857 0 0 1 52 105.714H6.857A6.857 6.857 0 0 1 0 98.857v-92A6.857 6.857 0 0 1 6.857 0Z',\n  fill: '#7EACFF'\n}), React.createElement(\"mask\", {\n  id: 'error-block-image-default-c',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-default-b'\n})), React.createElement(\"use\", {\n  fill: '#377EFF',\n  xlinkHref: '#error-block-image-default-b'\n}), React.createElement(\"path\", {\n  d: 'M11.838 91.8a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.628-.652c0-.36.281-.651.628-.651Zm-2.858 0a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.652.64.64 0 0 1-.627-.652c0-.36.281-.651.627-.651Zm2.16-2.305a.64.64 0 0 1 .628.652.64.64 0 0 1-.627.651.64.64 0 0 1-.627-.651c0-.36.28-.652.627-.652Zm-2.982-.04a.64.64 0 0 1 .627.651.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.651.627-.651Zm5.268-.531a.64.64 0 0 1 .628.651.64.64 0 0 1-.628.652.64.64 0 0 1-.627-.652c0-.36.281-.651.627-.651Zm2.858-1.143a.64.64 0 0 1 .627.651.64.64 0 0 1-.627.652.64.64 0 0 1-.628-.652c0-.36.281-.651.628-.651Zm-6.37-.917c.209 0 .377.175.377.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm3.512-.798.093.007a.644.644 0 0 1 .535.645.64.64 0 0 1-.628.652.64.64 0 0 1-.627-.652c0-.36.281-.652.627-.652Zm5.715 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.652.627-.652Zm-11.429 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.652.627-.652Zm-3.261.241c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.377-.391c0-.216.169-.391.377-.391Zm11.833-.812a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.651.64.64 0 0 1-.628-.651c0-.36.281-.652.628-.652Zm-4.851.399c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.377-.391c0-.216.169-.391.377-.391Zm10.313-2.056a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.651.64.64 0 0 1-.627-.651c0-.36.281-.652.627-.652Zm-2.354-.128a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.628-.652c0-.36.281-.652.628-.652Zm-13.798.311c.207 0 .376.175.376.391a.384.384 0 0 1-.376.391.384.384 0 0 1-.377-.39c0-.217.169-.392.377-.392Zm11.832-.812a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.651.64.64 0 0 1-.627-.651c0-.36.281-.652.627-.652Zm-6.285 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.651.64.64 0 0 1-.627-.651c0-.36.28-.652.627-.652Zm3.428 0a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.651.64.64 0 0 1-.627-.651c0-.36.28-.652.627-.652Zm-6.118.24c.208 0 .376.176.376.392a.384.384 0 0 1-.376.39.384.384 0 0 1-.377-.39c0-.216.169-.391.377-.391Zm11.261-2.525a.64.64 0 0 1 .627.651.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.651.627-.651Zm-3.557.484c.208 0 .376.175.376.391a.384.384 0 0 1-.376.391.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-2.478-.555a.64.64 0 0 1 .627.652.64.64 0 0 1-.627.652.64.64 0 0 1-.627-.652c0-.36.28-.652.627-.652Zm-3.512-.26c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-2.857 0c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-4.571 0c.207 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.377-.391c0-.216.169-.391.377-.391Zm14.898-1.835a.64.64 0 0 1 .628.652.64.64 0 0 1-.628.651.64.64 0 0 1-.627-.651c0-.36.281-.652.627-.652Zm-8.027-.245c.208 0 .377.175.377.39a.384.384 0 0 1-.377.392.384.384 0 0 1-.376-.391c0-.216.169-.391.376-.391Zm6.271-1.349c.208 0 .377.175.377.391a.384.384 0 0 1-.377.391.384.384 0 0 1-.376-.39c0-.217.169-.392.376-.392Zm-11.484-.481c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm15.103-.972c.208 0 .376.175.376.391a.384.384 0 0 1-.376.391.384.384 0 0 1-.376-.39c0-.217.168-.392.376-.392Zm-9.333-1.404c.208 0 .376.175.376.39a.384.384 0 0 1-.376.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Zm-6.819-.405c.208 0 .377.175.377.39a.384.384 0 0 1-.377.392.384.384 0 0 1-.376-.391c0-.216.168-.391.376-.391Z',\n  fill: '#003CFF',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-c)',\n  transform: 'rotate(116 12.367 83.503)'\n}), React.createElement(\"path\", {\n  stroke: '#FFF',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round',\n  d: 'M36.774 5.474H21.523'\n}), React.createElement(\"path\", {\n  d: 'm67.818 94.025-4.996 3.913m4.996 11.91-4.996-3.912m-1.142 9.145-1.143-6.288m10.71-6.768h-7.262',\n  stroke: '#4486FE',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n})), React.createElement(\"circle\", {\n  cx: 8.571,\n  cy: 8.571,\n  r: 8.571,\n  transform: 'translate(22.857 142)',\n  fill: '#FFCD6B',\n  fillRule: 'nonzero'\n}), React.createElement(\"g\", {\n  transform: 'translate(132.857 124)'\n}, React.createElement(\"mask\", {\n  id: 'error-block-image-default-e',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-default-d'\n})), React.createElement(\"use\", {\n  fill: '#FBBE47',\n  fillRule: 'nonzero',\n  xlinkHref: '#error-block-image-default-d'\n}), React.createElement(\"circle\", {\n  fill: '#FFCD6B',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 13.886,\n  cy: 15.12,\n  r: 18.823\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 23.4,\n  cy: 29.057,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 30.343,\n  cy: 29.829,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 18.771,\n  cy: 32.657,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 29.571,\n  cy: 25.971,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 19.286,\n  cy: 7.971,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 26.486,\n  cy: 5.914,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 11.057,\n  cy: 6.943,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 30.086,\n  cy: 15.686,\n  r: 1.286\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-default-e)',\n  cx: 22.886,\n  cy: 14.657,\n  r: 1\n})), React.createElement(\"path\", {\n  d: 'm87.429 135.123 6.591-9.378v-.08h-5.99v-2.559h10.038v1.787l-6.44 9.254v.082h6.56v2.557h-10.76v-1.663Zm12.185-5.889 4.948-7.047v-.056h-4.498v-1.917h7.536v1.34l-4.849 6.942v.059h4.923v1.92h-8.06v-1.24Zm10.345.702 3.708-5.274v-.045h-3.372v-1.437h5.648v1.003l-3.628 5.206v.045H116v1.438h-6.041v-.936Z',\n  fill: '#FFF',\n  fillRule: 'nonzero'\n})));"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,YAAY,GAAGD,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EACrDC,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,4BAA4B;EACnCC,UAAU,EAAE;AACd,CAAC,EAAEL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEF,KAAK,CAACE,aAAa,CAAC,gBAAgB,EAAE;EACzEI,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,WAAW;EACfC,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE;AACN,CAAC,EAAEV,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BS,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE;AACV,CAAC,CAAC,EAAEb,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BS,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE;AACV,CAAC,CAAC,EAAEb,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BS,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,CAAC;EACdC,MAAM,EAAE;AACV,CAAC,CAAC,CAAC,EAAEb,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EACjCQ,EAAE,EAAE,6BAA6B;EACjCI,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BQ,EAAE,EAAE,6BAA6B;EACjCO,CAAC,EAAE,GAAG;EACNC,CAAC,EAAE,CAAC;EACJC,KAAK,EAAE,MAAM;EACbC,MAAM,EAAE,EAAE;EACVC,EAAE,EAAE;AACN,CAAC,CAAC,CAAC,EAAErB,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;EAC5BoB,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC,EAAEvB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BsB,CAAC,EAAE,ukDAAukD;EAC1kDC,SAAS,EAAE,yBAAyB;EACpCH,IAAI,EAAE;AACR,CAAC,CAAC,EAAEtB,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;EAC3BuB,SAAS,EAAE;AACb,CAAC,EAAEzB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BsB,CAAC,EAAE,2IAA2I;EAC9IF,IAAI,EAAE;AACR,CAAC,CAAC,EAAEtB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BQ,EAAE,EAAE,6BAA6B;EACjCY,IAAI,EAAE;AACR,CAAC,EAAEtB,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC5BwB,SAAS,EAAE;AACb,CAAC,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC9BoB,IAAI,EAAE,SAAS;EACfI,SAAS,EAAE;AACb,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BsB,CAAC,EAAE,u3GAAu3G;EAC13GF,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCF,SAAS,EAAE;AACb,CAAC,CAAC,EAAEzB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9B0B,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,OAAO;EACtBC,cAAc,EAAE,OAAO;EACvBP,CAAC,EAAE;AACL,CAAC,CAAC,EAAExB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BsB,CAAC,EAAE,gGAAgG;EACnGI,MAAM,EAAE,SAAS;EACjBC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,OAAO;EACtBC,cAAc,EAAE;AAClB,CAAC,CAAC,CAAC,EAAE/B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EACjCY,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE,KAAK;EACRS,SAAS,EAAE,uBAAuB;EAClCH,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE;AACZ,CAAC,CAAC,EAAEvB,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;EAC3BuB,SAAS,EAAE;AACb,CAAC,EAAEzB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BQ,EAAE,EAAE,6BAA6B;EACjCY,IAAI,EAAE;AACR,CAAC,EAAEtB,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC5BwB,SAAS,EAAE;AACb,CAAC,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC9BoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBG,SAAS,EAAE;AACb,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCoB,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBI,IAAI,EAAE,mCAAmC;EACzCb,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC/BsB,CAAC,EAAE,0SAA0S;EAC7SF,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}