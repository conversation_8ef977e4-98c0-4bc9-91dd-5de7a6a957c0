{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport { isObject } from '../utils';\nvar NetworkEventType;\n(function (NetworkEventType) {\n  NetworkEventType[\"ONLINE\"] = \"online\";\n  NetworkEventType[\"OFFLINE\"] = \"offline\";\n  NetworkEventType[\"CHANGE\"] = \"change\";\n})(NetworkEventType || (NetworkEventType = {}));\nfunction getConnection() {\n  var nav = navigator;\n  if (!isObject(nav)) return null;\n  return nav.connection || nav.mozConnection || nav.webkitConnection;\n}\nfunction getConnectionProperty() {\n  var c = getConnection();\n  if (!c) return {};\n  return {\n    rtt: c.rtt,\n    type: c.type,\n    saveData: c.saveData,\n    downlink: c.downlink,\n    downlinkMax: c.downlinkMax,\n    effectiveType: c.effectiveType\n  };\n}\nfunction useNetwork() {\n  var _a = __read(useState(function () {\n      return __assign({\n        since: undefined,\n        online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine\n      }, getConnectionProperty());\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    var onOnline = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: true,\n          since: new Date()\n        });\n      });\n    };\n    var onOffline = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: false,\n          since: new Date()\n        });\n      });\n    };\n    var onConnectionChange = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), getConnectionProperty());\n      });\n    };\n    window.addEventListener(NetworkEventType.ONLINE, onOnline);\n    window.addEventListener(NetworkEventType.OFFLINE, onOffline);\n    var connection = getConnection();\n    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    return function () {\n      window.removeEventListener(NetworkEventType.ONLINE, onOnline);\n      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);\n      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    };\n  }, []);\n  return state;\n}\nexport default useNetwork;", "map": {"version": 3, "names": ["__assign", "__read", "useEffect", "useState", "isObject", "NetworkEventType", "getConnection", "nav", "navigator", "connection", "mozConnection", "webkitConnection", "getConnectionProperty", "c", "rtt", "type", "saveData", "downlink", "downlinkMax", "effectiveType", "useNetwork", "_a", "since", "undefined", "online", "onLine", "state", "setState", "onOnline", "prevState", "Date", "onOffline", "onConnectionChange", "window", "addEventListener", "ONLINE", "OFFLINE", "CHANGE", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useNetwork/index.js"], "sourcesContent": ["import { __assign, __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport { isObject } from '../utils';\nvar NetworkEventType;\n(function (NetworkEventType) {\n  NetworkEventType[\"ONLINE\"] = \"online\";\n  NetworkEventType[\"OFFLINE\"] = \"offline\";\n  NetworkEventType[\"CHANGE\"] = \"change\";\n})(NetworkEventType || (NetworkEventType = {}));\nfunction getConnection() {\n  var nav = navigator;\n  if (!isObject(nav)) return null;\n  return nav.connection || nav.mozConnection || nav.webkitConnection;\n}\nfunction getConnectionProperty() {\n  var c = getConnection();\n  if (!c) return {};\n  return {\n    rtt: c.rtt,\n    type: c.type,\n    saveData: c.saveData,\n    downlink: c.downlink,\n    downlinkMax: c.downlinkMax,\n    effectiveType: c.effectiveType\n  };\n}\nfunction useNetwork() {\n  var _a = __read(useState(function () {\n      return __assign({\n        since: undefined,\n        online: navigator === null || navigator === void 0 ? void 0 : navigator.onLine\n      }, getConnectionProperty());\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    var onOnline = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: true,\n          since: new Date()\n        });\n      });\n    };\n    var onOffline = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), {\n          online: false,\n          since: new Date()\n        });\n      });\n    };\n    var onConnectionChange = function () {\n      setState(function (prevState) {\n        return __assign(__assign({}, prevState), getConnectionProperty());\n      });\n    };\n    window.addEventListener(NetworkEventType.ONLINE, onOnline);\n    window.addEventListener(NetworkEventType.OFFLINE, onOffline);\n    var connection = getConnection();\n    connection === null || connection === void 0 ? void 0 : connection.addEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    return function () {\n      window.removeEventListener(NetworkEventType.ONLINE, onOnline);\n      window.removeEventListener(NetworkEventType.OFFLINE, onOffline);\n      connection === null || connection === void 0 ? void 0 : connection.removeEventListener(NetworkEventType.CHANGE, onConnectionChange);\n    };\n  }, []);\n  return state;\n}\nexport default useNetwork;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,SAASC,QAAQ,QAAQ,UAAU;AACnC,IAAIC,gBAAgB;AACpB,CAAC,UAAUA,gBAAgB,EAAE;EAC3BA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACrCA,gBAAgB,CAAC,SAAS,CAAC,GAAG,SAAS;EACvCA,gBAAgB,CAAC,QAAQ,CAAC,GAAG,QAAQ;AACvC,CAAC,EAAEA,gBAAgB,KAAKA,gBAAgB,GAAG,CAAC,CAAC,CAAC,CAAC;AAC/C,SAASC,aAAaA,CAAA,EAAG;EACvB,IAAIC,GAAG,GAAGC,SAAS;EACnB,IAAI,CAACJ,QAAQ,CAACG,GAAG,CAAC,EAAE,OAAO,IAAI;EAC/B,OAAOA,GAAG,CAACE,UAAU,IAAIF,GAAG,CAACG,aAAa,IAAIH,GAAG,CAACI,gBAAgB;AACpE;AACA,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,IAAIC,CAAC,GAAGP,aAAa,CAAC,CAAC;EACvB,IAAI,CAACO,CAAC,EAAE,OAAO,CAAC,CAAC;EACjB,OAAO;IACLC,GAAG,EAAED,CAAC,CAACC,GAAG;IACVC,IAAI,EAAEF,CAAC,CAACE,IAAI;IACZC,QAAQ,EAAEH,CAAC,CAACG,QAAQ;IACpBC,QAAQ,EAAEJ,CAAC,CAACI,QAAQ;IACpBC,WAAW,EAAEL,CAAC,CAACK,WAAW;IAC1BC,aAAa,EAAEN,CAAC,CAACM;EACnB,CAAC;AACH;AACA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAIC,EAAE,GAAGpB,MAAM,CAACE,QAAQ,CAAC,YAAY;MACjC,OAAOH,QAAQ,CAAC;QACdsB,KAAK,EAAEC,SAAS;QAChBC,MAAM,EAAEhB,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACiB;MAC1E,CAAC,EAAEb,qBAAqB,CAAC,CAAC,CAAC;IAC7B,CAAC,CAAC,EAAE,CAAC,CAAC;IACNc,KAAK,GAAGL,EAAE,CAAC,CAAC,CAAC;IACbM,QAAQ,GAAGN,EAAE,CAAC,CAAC,CAAC;EAClBnB,SAAS,CAAC,YAAY;IACpB,IAAI0B,QAAQ,GAAG,SAAAA,CAAA,EAAY;MACzBD,QAAQ,CAAC,UAAUE,SAAS,EAAE;QAC5B,OAAO7B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6B,SAAS,CAAC,EAAE;UACvCL,MAAM,EAAE,IAAI;UACZF,KAAK,EAAE,IAAIQ,IAAI,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAIC,SAAS,GAAG,SAAAA,CAAA,EAAY;MAC1BJ,QAAQ,CAAC,UAAUE,SAAS,EAAE;QAC5B,OAAO7B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6B,SAAS,CAAC,EAAE;UACvCL,MAAM,EAAE,KAAK;UACbF,KAAK,EAAE,IAAIQ,IAAI,CAAC;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;IACD,IAAIE,kBAAkB,GAAG,SAAAA,CAAA,EAAY;MACnCL,QAAQ,CAAC,UAAUE,SAAS,EAAE;QAC5B,OAAO7B,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE6B,SAAS,CAAC,EAAEjB,qBAAqB,CAAC,CAAC,CAAC;MACnE,CAAC,CAAC;IACJ,CAAC;IACDqB,MAAM,CAACC,gBAAgB,CAAC7B,gBAAgB,CAAC8B,MAAM,EAAEP,QAAQ,CAAC;IAC1DK,MAAM,CAACC,gBAAgB,CAAC7B,gBAAgB,CAAC+B,OAAO,EAAEL,SAAS,CAAC;IAC5D,IAAItB,UAAU,GAAGH,aAAa,CAAC,CAAC;IAChCG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACyB,gBAAgB,CAAC7B,gBAAgB,CAACgC,MAAM,EAAEL,kBAAkB,CAAC;IAChI,OAAO,YAAY;MACjBC,MAAM,CAACK,mBAAmB,CAACjC,gBAAgB,CAAC8B,MAAM,EAAEP,QAAQ,CAAC;MAC7DK,MAAM,CAACK,mBAAmB,CAACjC,gBAAgB,CAAC+B,OAAO,EAAEL,SAAS,CAAC;MAC/DtB,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAAC6B,mBAAmB,CAACjC,gBAAgB,CAACgC,MAAM,EAAEL,kBAAkB,CAAC;IACrI,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAON,KAAK;AACd;AACA,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}