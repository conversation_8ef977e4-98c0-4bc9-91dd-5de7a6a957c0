{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useInterval = function (fn, delay, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (options.immediate) {\n      timerCallback();\n    }\n    timerRef.current = setInterval(timerCallback, delay);\n    return clear;\n  }, [delay, options.immediate]);\n  return clear;\n};\nexport default useInterval;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useMemoizedFn", "isNumber", "useInterval", "fn", "delay", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timerRef", "clear", "current", "clearInterval", "immediate", "setInterval"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useInterval/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar useInterval = function (fn, delay, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var timerCallback = useMemoizedFn(fn);\n  var timerRef = useRef(null);\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (options.immediate) {\n      timerCallback();\n    }\n    timerRef.current = setInterval(timerCallback, delay);\n    return clear;\n  }, [delay, options.immediate]);\n  return clear;\n};\nexport default useInterval;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACtD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,UAAU;AACnC,IAAIC,WAAW,GAAG,SAAAA,CAAUC,EAAE,EAAEC,KAAK,EAAEC,OAAO,EAAE;EAC9C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,aAAa,GAAGN,aAAa,CAACG,EAAE,CAAC;EACrC,IAAII,QAAQ,GAAGR,MAAM,CAAC,IAAI,CAAC;EAC3B,IAAIS,KAAK,GAAGX,WAAW,CAAC,YAAY;IAClC,IAAIU,QAAQ,CAACE,OAAO,EAAE;MACpBC,aAAa,CAACH,QAAQ,CAACE,OAAO,CAAC;IACjC;EACF,CAAC,EAAE,EAAE,CAAC;EACNX,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACjC;IACF;IACA,IAAIC,OAAO,CAACM,SAAS,EAAE;MACrBL,aAAa,CAAC,CAAC;IACjB;IACAC,QAAQ,CAACE,OAAO,GAAGG,WAAW,CAACN,aAAa,EAAEF,KAAK,CAAC;IACpD,OAAOI,KAAK;EACd,CAAC,EAAE,CAACJ,KAAK,EAAEC,OAAO,CAACM,SAAS,CAAC,CAAC;EAC9B,OAAOH,KAAK;AACd,CAAC;AACD,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}