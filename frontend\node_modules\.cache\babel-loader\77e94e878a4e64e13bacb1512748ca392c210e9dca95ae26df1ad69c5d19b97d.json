{"ast": null, "code": "import { __assign, __read, __rest, __values } from \"tslib\";\nimport 'intersection-observer';\nimport { useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useInViewport(target, options) {\n  var _a = options || {},\n    callback = _a.callback,\n    option = __rest(_a, [\"callback\"]);\n  var _b = __read(useState(), 2),\n    state = _b[0],\n    setState = _b[1];\n  var _c = __read(useState(), 2),\n    ratio = _c[0],\n    setRatio = _c[1];\n  useEffectWithTarget(function () {\n    var targets = Array.isArray(target) ? target : [target];\n    var els = targets.map(function (element) {\n      return getTargetElement(element);\n    }).filter(Boolean);\n    if (!els.length) {\n      return;\n    }\n    var observer = new IntersectionObserver(function (entries) {\n      var e_1, _a;\n      try {\n        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {\n          var entry = entries_1_1.value;\n          setRatio(entry.intersectionRatio);\n          setState(entry.isIntersecting);\n          callback === null || callback === void 0 ? void 0 : callback(entry);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (entries_1_1 && !entries_1_1.done && (_a = entries_1.return)) _a.call(entries_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, __assign(__assign({}, option), {\n      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)\n    }));\n    els.forEach(function (el) {\n      return observer.observe(el);\n    });\n    return function () {\n      observer.disconnect();\n    };\n  }, [options === null || options === void 0 ? void 0 : options.rootMargin, options === null || options === void 0 ? void 0 : options.threshold, callback], target);\n  return [state, ratio];\n}\nexport default useInViewport;", "map": {"version": 3, "names": ["__assign", "__read", "__rest", "__values", "useState", "getTargetElement", "useEffectWithTarget", "useInViewport", "target", "options", "_a", "callback", "option", "_b", "state", "setState", "_c", "ratio", "setRatio", "targets", "Array", "isArray", "els", "map", "element", "filter", "Boolean", "length", "observer", "IntersectionObserver", "entries", "e_1", "entries_1", "entries_1_1", "next", "done", "entry", "value", "intersectionRatio", "isIntersecting", "e_1_1", "error", "return", "call", "root", "for<PERSON>ach", "el", "observe", "disconnect", "rootMargin", "threshold"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useInViewport/index.js"], "sourcesContent": ["import { __assign, __read, __rest, __values } from \"tslib\";\nimport 'intersection-observer';\nimport { useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useInViewport(target, options) {\n  var _a = options || {},\n    callback = _a.callback,\n    option = __rest(_a, [\"callback\"]);\n  var _b = __read(useState(), 2),\n    state = _b[0],\n    setState = _b[1];\n  var _c = __read(useState(), 2),\n    ratio = _c[0],\n    setRatio = _c[1];\n  useEffectWithTarget(function () {\n    var targets = Array.isArray(target) ? target : [target];\n    var els = targets.map(function (element) {\n      return getTargetElement(element);\n    }).filter(Boolean);\n    if (!els.length) {\n      return;\n    }\n    var observer = new IntersectionObserver(function (entries) {\n      var e_1, _a;\n      try {\n        for (var entries_1 = __values(entries), entries_1_1 = entries_1.next(); !entries_1_1.done; entries_1_1 = entries_1.next()) {\n          var entry = entries_1_1.value;\n          setRatio(entry.intersectionRatio);\n          setState(entry.isIntersecting);\n          callback === null || callback === void 0 ? void 0 : callback(entry);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (entries_1_1 && !entries_1_1.done && (_a = entries_1.return)) _a.call(entries_1);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    }, __assign(__assign({}, option), {\n      root: getTargetElement(options === null || options === void 0 ? void 0 : options.root)\n    }));\n    els.forEach(function (el) {\n      return observer.observe(el);\n    });\n    return function () {\n      observer.disconnect();\n    };\n  }, [options === null || options === void 0 ? void 0 : options.rootMargin, options === null || options === void 0 ? void 0 : options.threshold, callback], target);\n  return [state, ratio];\n}\nexport default useInViewport;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC1D,OAAO,uBAAuB;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,SAASC,aAAaA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACtC,IAAIC,EAAE,GAAGD,OAAO,IAAI,CAAC,CAAC;IACpBE,QAAQ,GAAGD,EAAE,CAACC,QAAQ;IACtBC,MAAM,GAAGV,MAAM,CAACQ,EAAE,EAAE,CAAC,UAAU,CAAC,CAAC;EACnC,IAAIG,EAAE,GAAGZ,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5BU,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,EAAE,GAAGf,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5Ba,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClBV,mBAAmB,CAAC,YAAY;IAC9B,IAAIa,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACb,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;IACvD,IAAIc,GAAG,GAAGH,OAAO,CAACI,GAAG,CAAC,UAAUC,OAAO,EAAE;MACvC,OAAOnB,gBAAgB,CAACmB,OAAO,CAAC;IAClC,CAAC,CAAC,CAACC,MAAM,CAACC,OAAO,CAAC;IAClB,IAAI,CAACJ,GAAG,CAACK,MAAM,EAAE;MACf;IACF;IACA,IAAIC,QAAQ,GAAG,IAAIC,oBAAoB,CAAC,UAAUC,OAAO,EAAE;MACzD,IAAIC,GAAG,EAAErB,EAAE;MACX,IAAI;QACF,KAAK,IAAIsB,SAAS,GAAG7B,QAAQ,CAAC2B,OAAO,CAAC,EAAEG,WAAW,GAAGD,SAAS,CAACE,IAAI,CAAC,CAAC,EAAE,CAACD,WAAW,CAACE,IAAI,EAAEF,WAAW,GAAGD,SAAS,CAACE,IAAI,CAAC,CAAC,EAAE;UACzH,IAAIE,KAAK,GAAGH,WAAW,CAACI,KAAK;UAC7BnB,QAAQ,CAACkB,KAAK,CAACE,iBAAiB,CAAC;UACjCvB,QAAQ,CAACqB,KAAK,CAACG,cAAc,CAAC;UAC9B5B,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACyB,KAAK,CAAC;QACrE;MACF,CAAC,CAAC,OAAOI,KAAK,EAAE;QACdT,GAAG,GAAG;UACJU,KAAK,EAAED;QACT,CAAC;MACH,CAAC,SAAS;QACR,IAAI;UACF,IAAIP,WAAW,IAAI,CAACA,WAAW,CAACE,IAAI,KAAKzB,EAAE,GAAGsB,SAAS,CAACU,MAAM,CAAC,EAAEhC,EAAE,CAACiC,IAAI,CAACX,SAAS,CAAC;QACrF,CAAC,SAAS;UACR,IAAID,GAAG,EAAE,MAAMA,GAAG,CAACU,KAAK;QAC1B;MACF;IACF,CAAC,EAAEzC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEY,MAAM,CAAC,EAAE;MAChCgC,IAAI,EAAEvC,gBAAgB,CAACI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACmC,IAAI;IACvF,CAAC,CAAC,CAAC;IACHtB,GAAG,CAACuB,OAAO,CAAC,UAAUC,EAAE,EAAE;MACxB,OAAOlB,QAAQ,CAACmB,OAAO,CAACD,EAAE,CAAC;IAC7B,CAAC,CAAC;IACF,OAAO,YAAY;MACjBlB,QAAQ,CAACoB,UAAU,CAAC,CAAC;IACvB,CAAC;EACH,CAAC,EAAE,CAACvC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACwC,UAAU,EAAExC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACyC,SAAS,EAAEvC,QAAQ,CAAC,EAAEH,MAAM,CAAC;EACjK,OAAO,CAACM,KAAK,EAAEG,KAAK,CAAC;AACvB;AACA,eAAeV,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}