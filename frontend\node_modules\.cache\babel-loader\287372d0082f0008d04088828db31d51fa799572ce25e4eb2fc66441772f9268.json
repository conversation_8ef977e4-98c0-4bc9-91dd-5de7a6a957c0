{"ast": null, "code": "import { useEffect, useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport isBrowser from '../utils/isBrowser';\nvar DEFAULT_OPTIONS = {\n  restoreOnUnmount: false\n};\nfunction useTitle(title, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  var titleRef = useRef(isBrowser ? document.title : '');\n  useEffect(function () {\n    document.title = title;\n  }, [title]);\n  useUnmount(function () {\n    if (options.restoreOnUnmount) {\n      document.title = titleRef.current;\n    }\n  });\n}\nexport default useTitle;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUnmount", "<PERSON><PERSON><PERSON><PERSON>", "DEFAULT_OPTIONS", "restoreOnUnmount", "useTitle", "title", "options", "titleRef", "document", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useTitle/index.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport isBrowser from '../utils/isBrowser';\nvar DEFAULT_OPTIONS = {\n  restoreOnUnmount: false\n};\nfunction useTitle(title, options) {\n  if (options === void 0) {\n    options = DEFAULT_OPTIONS;\n  }\n  var titleRef = useRef(isBrowser ? document.title : '');\n  useEffect(function () {\n    document.title = title;\n  }, [title]);\n  useUnmount(function () {\n    if (options.restoreOnUnmount) {\n      document.title = titleRef.current;\n    }\n  });\n}\nexport default useTitle;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,eAAe,GAAG;EACpBC,gBAAgB,EAAE;AACpB,CAAC;AACD,SAASC,QAAQA,CAACC,KAAK,EAAEC,OAAO,EAAE;EAChC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAGJ,eAAe;EAC3B;EACA,IAAIK,QAAQ,GAAGR,MAAM,CAACE,SAAS,GAAGO,QAAQ,CAACH,KAAK,GAAG,EAAE,CAAC;EACtDP,SAAS,CAAC,YAAY;IACpBU,QAAQ,CAACH,KAAK,GAAGA,KAAK;EACxB,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EACXL,UAAU,CAAC,YAAY;IACrB,IAAIM,OAAO,CAACH,gBAAgB,EAAE;MAC5BK,QAAQ,CAACH,KAAK,GAAGE,QAAQ,CAACE,OAAO;IACnC;EACF,CAAC,CAAC;AACJ;AACA,eAAeL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}