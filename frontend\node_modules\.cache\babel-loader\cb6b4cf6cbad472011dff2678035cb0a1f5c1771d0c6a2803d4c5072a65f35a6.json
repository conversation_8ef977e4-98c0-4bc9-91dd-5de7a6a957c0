{"ast": null, "code": "import React from 'react';\nexport const emptyImage = React.createElement(\"svg\", {\n  viewBox: '0 0 400 400',\n  xmlns: 'http://www.w3.org/2000/svg',\n  xmlnsXlink: 'http://www.w3.org/1999/xlink'\n}, React.createElement(\"defs\", null, React.createElement(\"linearGradient\", {\n  x1: '50%',\n  y1: '-116.862%',\n  x2: '50%',\n  y2: '90.764%',\n  id: 'error-block-image-empty-a'\n}, React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.207,\n  offset: '0%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.115,\n  offset: '80.072%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0,\n  offset: '100%'\n})), React.createElement(\"path\", {\n  d: 'M146.183 18.461c31.705 23.336 33.349 71.85 4.93 96.614-.252.22 6.172 5.602 13.577 11.414l.686.537.69.54.695.54.348.27.698.54a341.27 341.27 0 0 0 8.806 6.596c1.114.802 4.643-.853 10.587-4.965l-.532 12.218a1.2 1.2 0 0 1-.481.91l-10.868 8.111c-1.405 1.048-3.32 1.185-4.854.072l-35.578-25.834c-33.414 17.333-79.913 15-109.804-7-33.444-24.616-33.444-75.95 0-100.563 33.443-24.615 87.657-24.615 121.1 0Zm-60.469 7.653C51.63 26.114 24 44.534 24 67.257S51.63 108.4 85.714 108.4s61.715-18.42 61.715-41.143c0-22.722-27.63-41.143-61.715-41.143Z',\n  id: 'error-block-image-empty-b'\n})), React.createElement(\"g\", {\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M157.964 244.661H0L3.806 100.13a4.572 4.572 0 0 1 4.353-4.446l.217-.006h45.588V68.2a4.572 4.572 0 0 1 4.356-4.567l.216-.005h65.498l2.554-58.689a4.571 4.571 0 0 1 4.779-4.367l.214.015 87.79 8.222a4.572 4.572 0 0 1 4.126 4.133l.015.212 3.146 69.652L301.634 64.9a4.571 4.571 0 0 1 5.628 4.231l.005.215v43.955l56.162.001v130.264h-56.163v.001h-82.383v-.004h-66.919v1.098ZM89.503 160.03h-9.968v8.436h9.968v-8.436Zm0-14.507h-9.968v8.435h9.968v-8.435Zm197.985-5.15h-9.967v8.432h9.967v-8.431Zm-197.985-8.806h-9.968v8.436h9.968v-8.436Zm197.985-5.153h-9.967v8.432h9.967v-8.432Zm0-14.503h-9.967v8.432h9.967v-8.432Zm-84.643-.777h-30.8v8.436h30.8v-8.436Zm84.643-13.186h-9.967v8.436h9.967v-8.436Zm-84.643-3.29h-30.8v8.436h30.8v-8.436Zm0-15.912h-30.8v8.436h30.8v-8.436Z',\n  transform: 'translate(18.286 50.286)',\n  fill: 'url(#error-block-image-empty-a)'\n}), React.createElement(\"g\", {\n  transform: 'translate(108.571 189.886)'\n}, React.createElement(\"mask\", {\n  id: 'error-block-image-empty-c',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-empty-b'\n})), React.createElement(\"use\", {\n  fill: '#377EFF',\n  xlinkHref: '#error-block-image-empty-b'\n}), React.createElement(\"path\", {\n  d: 'M131.429 134.686a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm5.714 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285ZM128 133.543a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm21.143-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-9.143-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm12-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM120 128.971a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm16-1.142.125.006a1.143 1.143 0 1 1-.125-.006Zm11.429 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-22.857 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm17.143-1.143a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285ZM136 125.543a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-13.143 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm4.572-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm18.857-2.286a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-16-1.143.124.007a1.143 1.143 0 1 1-.124-.007Zm11.428 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-22.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm36.572 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.715 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-37.143 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm13.715-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm9.714-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm18.286-3.428a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-11.429 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-28 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.714-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm17.715-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.715 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm17.143-4.571a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-11.428 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-5.143 1.142a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-8-1.142a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm-9.143 0a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm30.286-3.429a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM124 109.543a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.714 0a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.715-4.572a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-22.858-1.142a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm-11.428-3.429a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM124 99.257a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM49.143 55.829a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm5.714 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-9.143-1.143a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-5.714 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm21.143-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM52 52.4a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm12-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-19.429-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm5.715 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm16-1.143.124.007a1.143 1.143 0 1 1-.124-.007Zm11.428 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-22.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm17.143-1.142a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-11.429-1.143a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-13.143 1.143a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm4.572-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM64 44.4a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-16-1.143.125.007a1.143 1.143 0 1 1-.125-.007Zm11.429 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-22.858 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm36.572 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM30.286 44.4a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM44 43.257a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm9.714-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM72 38.686a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-11.429 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-28 1.143a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm5.715-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM56 37.543a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM60.57 32.97a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-11.428 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM44 34.114a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-8-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-9.143 0a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm30.286-3.428a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-15.429 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.715 0a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.714-4.572a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-22.857-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-11.429-3.428a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM41.714 20.4a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Z',\n  fill: '#003CFF',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-empty-c)'\n})), React.createElement(\"path\", {\n  d: 'M295.213 319.24c.744.546.745 1.433.002 1.98l-11.806 8.81c-1.069.799-3.326.474-4.853-.609l-35.622-25.241c-33.375 17.037-79.545 14.615-109.28-7.271-33.443-24.615-33.443-64.521 0-89.133 33.443-24.616 87.657-24.616 121.1 0 31.706 23.336 33.35 60.42 4.931 85.185-.543.473 35.528 26.278 35.528 26.278ZM148.06 220.015c-25.44 17.853-25.44 46.8 0 64.652 25.44 17.85 66.689 17.85 92.129 0 25.436-17.853 25.436-46.799 0-64.652-25.44-17.853-66.688-17.853-92.129 0Z',\n  fill: '#5D96FE'\n}), React.createElement(\"path\", {\n  d: 'M123.514 233.021c2.185-5.241 5.67-9.735 10.453-13.482M264.967 271.54c-2.185 5.24-5.67 9.734-10.453 13.481',\n  stroke: '#FFF',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n}), React.createElement(\"path\", {\n  d: 'M81.143 252.571c7.574 0 13.714 23.88 13.714 31.649 0 6.97-4.942 12.755-11.429 13.871v11.672c0 1.235-.767 2.237-1.713 2.237-.904 0-1.644-.912-1.71-2.07l-.005-.167v-11.526c-7.04-.595-12.571-6.644-12.571-14.017 0-7.024 5.02-27.222 11.581-31.027l.096-.053c.027-.016.055-.03.082-.045l.067-.035.066-.033.1-.05.094-.041a3.34 3.34 0 0 1 .224-.093l.11-.042.097-.032c.038-.013.077-.025.115-.036l.053-.016.053-.014a3.351 3.351 0 0 1 .23-.055l.085-.016a3.95 3.95 0 0 1 .441-.054l.11-.005.11-.002Z',\n  fill: '#FFCD6B',\n  fillRule: 'nonzero'\n}), React.createElement(\"g\", {\n  transform: 'translate(283.429 177.143)',\n  fillRule: 'nonzero'\n}, React.createElement(\"path\", {\n  d: 'M22.475.847c12.34 0 22.345 37.935 22.345 50.276 0 11.395-8.53 20.798-19.552 22.172v19.019c0 1.932-1.25 3.5-2.792 3.5-1.49 0-2.707-1.46-2.79-3.301l-.004-.2-.001-19.018C8.659 71.92.13 62.518.13 51.123.13 40.071 8.154 8.49 18.694 2.015l.054-.031a5.94 5.94 0 0 1 .214-.128l.088-.048c.213-.12.427-.228.642-.326l.135-.06.18-.075.135-.053a5.796 5.796 0 0 1 .464-.16 4.44 4.44 0 0 1 .33-.092l.124-.03a7.122 7.122 0 0 1 .31-.065l.018-.003a6.305 6.305 0 0 1 .756-.088l.165-.007.166-.002Z',\n  fill: '#FFCD6B'\n}), React.createElement(\"path\", {\n  d: 'M22.475.847c12.34 0 22.345 37.935 22.345 50.276 0 11.395-8.53 20.798-19.552 22.172v19.019c0 1.932-1.25 3.5-2.792 3.5-1.543 0-2.794-1.566-2.794-3.5V73.295C8.659 71.921.13 62.518.13 51.123.13 38.783 10.134.847 22.475.847Z',\n  fill: '#FFCD6B'\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 26.4,\n  cy: 56.869,\n  r: 1.45\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 39.453,\n  cy: 58.319,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 17.698,\n  cy: 63.637,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 38.002,\n  cy: 51.068,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 18.665,\n  cy: 17.228,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 32.201,\n  cy: 13.36,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 26.83,\n  cy: 20.666,\n  r: 1.45\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 38.969,\n  cy: 31.731,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 25.433,\n  cy: 29.797,\n  r: 1.45\n}), React.createElement(\"path\", {\n  d: 'M34.197 53.033c0 9.825-6.934 18.017-16.172 19.987a22.44 22.44 0 0 0 4.45.448c12.34 0 22.344-10.004 22.344-22.345C44.82 38.783 34.815.847 22.475.847c8.947 14.03 11.722 40.891 11.722 52.186Z',\n  fill: '#FBBE47'\n}))));", "map": {"version": 3, "names": ["React", "emptyImage", "createElement", "viewBox", "xmlns", "xmlnsXlink", "x1", "y1", "x2", "y2", "id", "stopColor", "stopOpacity", "offset", "d", "fill", "fillRule", "transform", "xlinkHref", "mask", "stroke", "strokeWidth", "strokeLinecap", "strokeLinejoin", "cx", "cy", "r"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/error-block/images/empty.js"], "sourcesContent": ["import React from 'react';\nexport const emptyImage = React.createElement(\"svg\", {\n  viewBox: '0 0 400 400',\n  xmlns: 'http://www.w3.org/2000/svg',\n  xmlnsXlink: 'http://www.w3.org/1999/xlink'\n}, React.createElement(\"defs\", null, React.createElement(\"linearGradient\", {\n  x1: '50%',\n  y1: '-116.862%',\n  x2: '50%',\n  y2: '90.764%',\n  id: 'error-block-image-empty-a'\n}, React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.207,\n  offset: '0%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0.115,\n  offset: '80.072%'\n}), React.createElement(\"stop\", {\n  stopColor: '#72A7FD',\n  stopOpacity: 0,\n  offset: '100%'\n})), React.createElement(\"path\", {\n  d: 'M146.183 18.461c31.705 23.336 33.349 71.85 4.93 96.614-.252.22 6.172 5.602 13.577 11.414l.686.537.69.54.695.54.348.27.698.54a341.27 341.27 0 0 0 8.806 6.596c1.114.802 4.643-.853 10.587-4.965l-.532 12.218a1.2 1.2 0 0 1-.481.91l-10.868 8.111c-1.405 1.048-3.32 1.185-4.854.072l-35.578-25.834c-33.414 17.333-79.913 15-109.804-7-33.444-24.616-33.444-75.95 0-100.563 33.443-24.615 87.657-24.615 121.1 0Zm-60.469 7.653C51.63 26.114 24 44.534 24 67.257S51.63 108.4 85.714 108.4s61.715-18.42 61.715-41.143c0-22.722-27.63-41.143-61.715-41.143Z',\n  id: 'error-block-image-empty-b'\n})), React.createElement(\"g\", {\n  fill: 'none',\n  fillRule: 'evenodd'\n}, React.createElement(\"path\", {\n  d: 'M157.964 244.661H0L3.806 100.13a4.572 4.572 0 0 1 4.353-4.446l.217-.006h45.588V68.2a4.572 4.572 0 0 1 4.356-4.567l.216-.005h65.498l2.554-58.689a4.571 4.571 0 0 1 4.779-4.367l.214.015 87.79 8.222a4.572 4.572 0 0 1 4.126 4.133l.015.212 3.146 69.652L301.634 64.9a4.571 4.571 0 0 1 5.628 4.231l.005.215v43.955l56.162.001v130.264h-56.163v.001h-82.383v-.004h-66.919v1.098ZM89.503 160.03h-9.968v8.436h9.968v-8.436Zm0-14.507h-9.968v8.435h9.968v-8.435Zm197.985-5.15h-9.967v8.432h9.967v-8.431Zm-197.985-8.806h-9.968v8.436h9.968v-8.436Zm197.985-5.153h-9.967v8.432h9.967v-8.432Zm0-14.503h-9.967v8.432h9.967v-8.432Zm-84.643-.777h-30.8v8.436h30.8v-8.436Zm84.643-13.186h-9.967v8.436h9.967v-8.436Zm-84.643-3.29h-30.8v8.436h30.8v-8.436Zm0-15.912h-30.8v8.436h30.8v-8.436Z',\n  transform: 'translate(18.286 50.286)',\n  fill: 'url(#error-block-image-empty-a)'\n}), React.createElement(\"g\", {\n  transform: 'translate(108.571 189.886)'\n}, React.createElement(\"mask\", {\n  id: 'error-block-image-empty-c',\n  fill: '#fff'\n}, React.createElement(\"use\", {\n  xlinkHref: '#error-block-image-empty-b'\n})), React.createElement(\"use\", {\n  fill: '#377EFF',\n  xlinkHref: '#error-block-image-empty-b'\n}), React.createElement(\"path\", {\n  d: 'M131.429 134.686a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm5.714 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285ZM128 133.543a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm21.143-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-9.143-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm12-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM120 128.971a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm16-1.142.125.006a1.143 1.143 0 1 1-.125-.006Zm11.429 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-22.857 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm17.143-1.143a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285ZM136 125.543a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-13.143 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm4.572-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm18.857-2.286a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-16-1.143.124.007a1.143 1.143 0 1 1-.124-.007Zm11.428 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-22.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm36.572 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.715 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-37.143 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm13.715-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm9.714-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm18.286-3.428a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-11.429 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-28 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.714-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm17.715-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.715 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm17.143-4.571a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-11.428 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-5.143 1.142a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-8-1.142a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm-9.143 0a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm30.286-3.429a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM124 109.543a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.714 0a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.715-4.572a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-22.858-1.142a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm-11.428-3.429a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM124 99.257a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM49.143 55.829a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm5.714 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-9.143-1.143a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-5.714 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm21.143-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM52 52.4a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm12-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-19.429-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm5.715 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm16-1.143.124.007a1.143 1.143 0 1 1-.124-.007Zm11.428 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-22.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm17.143-1.142a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-11.429-1.143a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-13.143 1.143a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm4.572-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM64 44.4a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-16-1.143.125.007a1.143 1.143 0 1 1-.125-.007Zm11.429 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-22.858 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm36.572 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM30.286 44.4a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM44 43.257a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm9.714-1.143a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM72 38.686a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-11.429 0a1.143 1.143 0 1 1 0 2.285 1.143 1.143 0 0 1 0-2.285Zm-28 1.143a.571.571 0 1 1 0 1.142.571.571 0 0 1 0-1.142Zm5.715-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM56 37.543a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-5.714 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-6.857 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM60.57 32.97a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-11.428 0a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286ZM44 34.114a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-8-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-9.143 0a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm30.286-3.428a1.143 1.143 0 1 1 0 2.286 1.143 1.143 0 0 1 0-2.286Zm-15.429 1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.715 0a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm5.714-4.572a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-22.857-1.143a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Zm-11.429-3.428a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143ZM41.714 20.4a.571.571 0 1 1 0 1.143.571.571 0 0 1 0-1.143Z',\n  fill: '#003CFF',\n  fillRule: 'nonzero',\n  mask: 'url(#error-block-image-empty-c)'\n})), React.createElement(\"path\", {\n  d: 'M295.213 319.24c.744.546.745 1.433.002 1.98l-11.806 8.81c-1.069.799-3.326.474-4.853-.609l-35.622-25.241c-33.375 17.037-79.545 14.615-109.28-7.271-33.443-24.615-33.443-64.521 0-89.133 33.443-24.616 87.657-24.616 121.1 0 31.706 23.336 33.35 60.42 4.931 85.185-.543.473 35.528 26.278 35.528 26.278ZM148.06 220.015c-25.44 17.853-25.44 46.8 0 64.652 25.44 17.85 66.689 17.85 92.129 0 25.436-17.853 25.436-46.799 0-64.652-25.44-17.853-66.688-17.853-92.129 0Z',\n  fill: '#5D96FE'\n}), React.createElement(\"path\", {\n  d: 'M123.514 233.021c2.185-5.241 5.67-9.735 10.453-13.482M264.967 271.54c-2.185 5.24-5.67 9.734-10.453 13.481',\n  stroke: '#FFF',\n  strokeWidth: 0.75,\n  strokeLinecap: 'round',\n  strokeLinejoin: 'round'\n}), React.createElement(\"path\", {\n  d: 'M81.143 252.571c7.574 0 13.714 23.88 13.714 31.649 0 6.97-4.942 12.755-11.429 13.871v11.672c0 1.235-.767 2.237-1.713 2.237-.904 0-1.644-.912-1.71-2.07l-.005-.167v-11.526c-7.04-.595-12.571-6.644-12.571-14.017 0-7.024 5.02-27.222 11.581-31.027l.096-.053c.027-.016.055-.03.082-.045l.067-.035.066-.033.1-.05.094-.041a3.34 3.34 0 0 1 .224-.093l.11-.042.097-.032c.038-.013.077-.025.115-.036l.053-.016.053-.014a3.351 3.351 0 0 1 .23-.055l.085-.016a3.95 3.95 0 0 1 .441-.054l.11-.005.11-.002Z',\n  fill: '#FFCD6B',\n  fillRule: 'nonzero'\n}), React.createElement(\"g\", {\n  transform: 'translate(283.429 177.143)',\n  fillRule: 'nonzero'\n}, React.createElement(\"path\", {\n  d: 'M22.475.847c12.34 0 22.345 37.935 22.345 50.276 0 11.395-8.53 20.798-19.552 22.172v19.019c0 1.932-1.25 3.5-2.792 3.5-1.49 0-2.707-1.46-2.79-3.301l-.004-.2-.001-19.018C8.659 71.92.13 62.518.13 51.123.13 40.071 8.154 8.49 18.694 2.015l.054-.031a5.94 5.94 0 0 1 .214-.128l.088-.048c.213-.12.427-.228.642-.326l.135-.06.18-.075.135-.053a5.796 5.796 0 0 1 .464-.16 4.44 4.44 0 0 1 .33-.092l.124-.03a7.122 7.122 0 0 1 .31-.065l.018-.003a6.305 6.305 0 0 1 .756-.088l.165-.007.166-.002Z',\n  fill: '#FFCD6B'\n}), React.createElement(\"path\", {\n  d: 'M22.475.847c12.34 0 22.345 37.935 22.345 50.276 0 11.395-8.53 20.798-19.552 22.172v19.019c0 1.932-1.25 3.5-2.792 3.5-1.543 0-2.794-1.566-2.794-3.5V73.295C8.659 71.921.13 62.518.13 51.123.13 38.783 10.134.847 22.475.847Z',\n  fill: '#FFCD6B'\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 26.4,\n  cy: 56.869,\n  r: 1.45\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 39.453,\n  cy: 58.319,\n  r: 1\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 17.698,\n  cy: 63.637,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 38.002,\n  cy: 51.068,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 18.665,\n  cy: 17.228,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 32.201,\n  cy: 13.36,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 26.83,\n  cy: 20.666,\n  r: 1.45\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 38.969,\n  cy: 31.731,\n  r: 2.417\n}), React.createElement(\"circle\", {\n  fill: '#FFB400',\n  cx: 25.433,\n  cy: 29.797,\n  r: 1.45\n}), React.createElement(\"path\", {\n  d: 'M34.197 53.033c0 9.825-6.934 18.017-16.172 19.987a22.44 22.44 0 0 0 4.45.448c12.34 0 22.344-10.004 22.344-22.345C44.82 38.783 34.815.847 22.475.847c8.947 14.03 11.722 40.891 11.722 52.186Z',\n  fill: '#FBBE47'\n}))));"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAO,MAAMC,UAAU,GAAGD,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EACnDC,OAAO,EAAE,aAAa;EACtBC,KAAK,EAAE,4BAA4B;EACnCC,UAAU,EAAE;AACd,CAAC,EAAEL,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE,IAAI,EAAEF,KAAK,CAACE,aAAa,CAAC,gBAAgB,EAAE;EACzEI,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,WAAW;EACfC,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,SAAS;EACbC,EAAE,EAAE;AACN,CAAC,EAAEV,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BS,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE;AACV,CAAC,CAAC,EAAEb,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BS,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,KAAK;EAClBC,MAAM,EAAE;AACV,CAAC,CAAC,EAAEb,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BS,SAAS,EAAE,SAAS;EACpBC,WAAW,EAAE,CAAC;EACdC,MAAM,EAAE;AACV,CAAC,CAAC,CAAC,EAAEb,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC/BY,CAAC,EAAE,uhBAAuhB;EAC1hBJ,EAAE,EAAE;AACN,CAAC,CAAC,CAAC,EAAEV,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;EAC5Ba,IAAI,EAAE,MAAM;EACZC,QAAQ,EAAE;AACZ,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BY,CAAC,EAAE,mvBAAmvB;EACtvBG,SAAS,EAAE,0BAA0B;EACrCF,IAAI,EAAE;AACR,CAAC,CAAC,EAAEf,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;EAC3Be,SAAS,EAAE;AACb,CAAC,EAAEjB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BQ,EAAE,EAAE,2BAA2B;EAC/BK,IAAI,EAAE;AACR,CAAC,EAAEf,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC5BgB,SAAS,EAAE;AACb,CAAC,CAAC,CAAC,EAAElB,KAAK,CAACE,aAAa,CAAC,KAAK,EAAE;EAC9Ba,IAAI,EAAE,SAAS;EACfG,SAAS,EAAE;AACb,CAAC,CAAC,EAAElB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BY,CAAC,EAAE,60KAA60K;EACh1KC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE,SAAS;EACnBG,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,EAAEnB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC/BY,CAAC,EAAE,scAAsc;EACzcC,IAAI,EAAE;AACR,CAAC,CAAC,EAAEf,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BY,CAAC,EAAE,2GAA2G;EAC9GM,MAAM,EAAE,MAAM;EACdC,WAAW,EAAE,IAAI;EACjBC,aAAa,EAAE,OAAO;EACtBC,cAAc,EAAE;AAClB,CAAC,CAAC,EAAEvB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BY,CAAC,EAAE,seAAse;EACzeC,IAAI,EAAE,SAAS;EACfC,QAAQ,EAAE;AACZ,CAAC,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,GAAG,EAAE;EAC3Be,SAAS,EAAE,4BAA4B;EACvCD,QAAQ,EAAE;AACZ,CAAC,EAAEhB,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC7BY,CAAC,EAAE,+dAA+d;EACleC,IAAI,EAAE;AACR,CAAC,CAAC,EAAEf,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BY,CAAC,EAAE,6NAA6N;EAChOC,IAAI,EAAE;AACR,CAAC,CAAC,EAAEf,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,IAAI;EACRC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,KAAK;EACTC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,KAAK;EACTC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,QAAQ,EAAE;EAChCa,IAAI,EAAE,SAAS;EACfS,EAAE,EAAE,MAAM;EACVC,EAAE,EAAE,MAAM;EACVC,CAAC,EAAE;AACL,CAAC,CAAC,EAAE1B,KAAK,CAACE,aAAa,CAAC,MAAM,EAAE;EAC9BY,CAAC,EAAE,8LAA8L;EACjMC,IAAI,EAAE;AACR,CAAC,CAAC,CAAC,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}