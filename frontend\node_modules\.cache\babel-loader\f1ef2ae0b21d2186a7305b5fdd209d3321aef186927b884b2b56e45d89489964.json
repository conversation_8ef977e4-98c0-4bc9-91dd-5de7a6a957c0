{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useMemo } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nvar usePagination = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var _b = options.defaultPageSize,\n    defaultPageSize = _b === void 0 ? 10 : _b,\n    _c = options.defaultCurrent,\n    defaultCurrent = _c === void 0 ? 1 : _c,\n    rest = __rest(options, [\"defaultPageSize\", \"defaultCurrent\"]);\n  var result = useRequest(service, __assign({\n    defaultParams: [{\n      current: defaultCurrent,\n      pageSize: defaultPageSize\n    }],\n    refreshDepsAction: function () {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      changeCurrent(1);\n    }\n  }, rest));\n  var _d = result.params[0] || {},\n    _e = _d.current,\n    current = _e === void 0 ? 1 : _e,\n    _f = _d.pageSize,\n    pageSize = _f === void 0 ? defaultPageSize : _f;\n  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;\n  var totalPage = useMemo(function () {\n    return Math.ceil(total / pageSize);\n  }, [pageSize, total]);\n  var onChange = function (c, p) {\n    var toCurrent = c <= 0 ? 1 : c;\n    var toPageSize = p <= 0 ? 1 : p;\n    var tempTotalPage = Math.ceil(total / toPageSize);\n    if (toCurrent > tempTotalPage) {\n      toCurrent = Math.max(1, tempTotalPage);\n    }\n    var _a = __read(result.params || []),\n      _b = _a[0],\n      oldPaginationParams = _b === void 0 ? {} : _b,\n      restParams = _a.slice(1);\n    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: toCurrent,\n      pageSize: toPageSize\n    })], __read(restParams), false));\n  };\n  var changeCurrent = function (c) {\n    onChange(c, pageSize);\n  };\n  var changePageSize = function (p) {\n    onChange(current, p);\n  };\n  return __assign(__assign({}, result), {\n    pagination: {\n      current: current,\n      pageSize: pageSize,\n      total: total,\n      totalPage: totalPage,\n      onChange: useMemoizedFn(onChange),\n      changeCurrent: useMemoizedFn(changeCurrent),\n      changePageSize: useMemoizedFn(changePageSize)\n    }\n  });\n};\nexport default usePagination;", "map": {"version": 3, "names": ["__assign", "__read", "__rest", "__spread<PERSON><PERSON>y", "useMemo", "useMemoizedFn", "useRequest", "usePagination", "service", "options", "_a", "_b", "defaultPageSize", "_c", "defaultCurrent", "rest", "result", "defaultParams", "current", "pageSize", "refreshDepsAction", "changeCurrent", "_d", "params", "_e", "_f", "total", "data", "totalPage", "Math", "ceil", "onChange", "c", "p", "to<PERSON><PERSON><PERSON>", "toPageSize", "tempTotalPage", "max", "oldPaginationParams", "restParams", "slice", "run", "apply", "changePageSize", "pagination"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/usePagination/index.js"], "sourcesContent": ["import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useMemo } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nvar usePagination = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var _b = options.defaultPageSize,\n    defaultPageSize = _b === void 0 ? 10 : _b,\n    _c = options.defaultCurrent,\n    defaultCurrent = _c === void 0 ? 1 : _c,\n    rest = __rest(options, [\"defaultPageSize\", \"defaultCurrent\"]);\n  var result = useRequest(service, __assign({\n    defaultParams: [{\n      current: defaultCurrent,\n      pageSize: defaultPageSize\n    }],\n    refreshDepsAction: function () {\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      changeCurrent(1);\n    }\n  }, rest));\n  var _d = result.params[0] || {},\n    _e = _d.current,\n    current = _e === void 0 ? 1 : _e,\n    _f = _d.pageSize,\n    pageSize = _f === void 0 ? defaultPageSize : _f;\n  var total = ((_a = result.data) === null || _a === void 0 ? void 0 : _a.total) || 0;\n  var totalPage = useMemo(function () {\n    return Math.ceil(total / pageSize);\n  }, [pageSize, total]);\n  var onChange = function (c, p) {\n    var toCurrent = c <= 0 ? 1 : c;\n    var toPageSize = p <= 0 ? 1 : p;\n    var tempTotalPage = Math.ceil(total / toPageSize);\n    if (toCurrent > tempTotalPage) {\n      toCurrent = Math.max(1, tempTotalPage);\n    }\n    var _a = __read(result.params || []),\n      _b = _a[0],\n      oldPaginationParams = _b === void 0 ? {} : _b,\n      restParams = _a.slice(1);\n    result.run.apply(result, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: toCurrent,\n      pageSize: toPageSize\n    })], __read(restParams), false));\n  };\n  var changeCurrent = function (c) {\n    onChange(c, pageSize);\n  };\n  var changePageSize = function (p) {\n    onChange(current, p);\n  };\n  return __assign(__assign({}, result), {\n    pagination: {\n      current: current,\n      pageSize: pageSize,\n      total: total,\n      totalPage: totalPage,\n      onChange: useMemoizedFn(onChange),\n      changeCurrent: useMemoizedFn(changeCurrent),\n      changePageSize: useMemoizedFn(changePageSize)\n    }\n  });\n};\nexport default usePagination;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC/D,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,UAAU,MAAM,eAAe;AACtC,IAAIC,aAAa,GAAG,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;EAC9C,IAAIC,EAAE;EACN,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIE,EAAE,GAAGF,OAAO,CAACG,eAAe;IAC9BA,eAAe,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACzCE,EAAE,GAAGJ,OAAO,CAACK,cAAc;IAC3BA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;IACvCE,IAAI,GAAGb,MAAM,CAACO,OAAO,EAAE,CAAC,iBAAiB,EAAE,gBAAgB,CAAC,CAAC;EAC/D,IAAIO,MAAM,GAAGV,UAAU,CAACE,OAAO,EAAER,QAAQ,CAAC;IACxCiB,aAAa,EAAE,CAAC;MACdC,OAAO,EAAEJ,cAAc;MACvBK,QAAQ,EAAEP;IACZ,CAAC,CAAC;IACFQ,iBAAiB,EAAE,SAAAA,CAAA,EAAY;MAC7B;MACAC,aAAa,CAAC,CAAC,CAAC;IAClB;EACF,CAAC,EAAEN,IAAI,CAAC,CAAC;EACT,IAAIO,EAAE,GAAGN,MAAM,CAACO,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAC7BC,EAAE,GAAGF,EAAE,CAACJ,OAAO;IACfA,OAAO,GAAGM,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;IAChCC,EAAE,GAAGH,EAAE,CAACH,QAAQ;IAChBA,QAAQ,GAAGM,EAAE,KAAK,KAAK,CAAC,GAAGb,eAAe,GAAGa,EAAE;EACjD,IAAIC,KAAK,GAAG,CAAC,CAAChB,EAAE,GAAGM,MAAM,CAACW,IAAI,MAAM,IAAI,IAAIjB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgB,KAAK,KAAK,CAAC;EACnF,IAAIE,SAAS,GAAGxB,OAAO,CAAC,YAAY;IAClC,OAAOyB,IAAI,CAACC,IAAI,CAACJ,KAAK,GAAGP,QAAQ,CAAC;EACpC,CAAC,EAAE,CAACA,QAAQ,EAAEO,KAAK,CAAC,CAAC;EACrB,IAAIK,QAAQ,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;IAC7B,IAAIC,SAAS,GAAGF,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC;IAC9B,IAAIG,UAAU,GAAGF,CAAC,IAAI,CAAC,GAAG,CAAC,GAAGA,CAAC;IAC/B,IAAIG,aAAa,GAAGP,IAAI,CAACC,IAAI,CAACJ,KAAK,GAAGS,UAAU,CAAC;IACjD,IAAID,SAAS,GAAGE,aAAa,EAAE;MAC7BF,SAAS,GAAGL,IAAI,CAACQ,GAAG,CAAC,CAAC,EAAED,aAAa,CAAC;IACxC;IACA,IAAI1B,EAAE,GAAGT,MAAM,CAACe,MAAM,CAACO,MAAM,IAAI,EAAE,CAAC;MAClCZ,EAAE,GAAGD,EAAE,CAAC,CAAC,CAAC;MACV4B,mBAAmB,GAAG3B,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;MAC7C4B,UAAU,GAAG7B,EAAE,CAAC8B,KAAK,CAAC,CAAC,CAAC;IAC1BxB,MAAM,CAACyB,GAAG,CAACC,KAAK,CAAC1B,MAAM,EAAEb,aAAa,CAAC,CAACH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsC,mBAAmB,CAAC,EAAE;MAClFpB,OAAO,EAAEgB,SAAS;MAClBf,QAAQ,EAAEgB;IACZ,CAAC,CAAC,CAAC,EAAElC,MAAM,CAACsC,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;EAClC,CAAC;EACD,IAAIlB,aAAa,GAAG,SAAAA,CAAUW,CAAC,EAAE;IAC/BD,QAAQ,CAACC,CAAC,EAAEb,QAAQ,CAAC;EACvB,CAAC;EACD,IAAIwB,cAAc,GAAG,SAAAA,CAAUV,CAAC,EAAE;IAChCF,QAAQ,CAACb,OAAO,EAAEe,CAAC,CAAC;EACtB,CAAC;EACD,OAAOjC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEgB,MAAM,CAAC,EAAE;IACpC4B,UAAU,EAAE;MACV1B,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA,QAAQ;MAClBO,KAAK,EAAEA,KAAK;MACZE,SAAS,EAAEA,SAAS;MACpBG,QAAQ,EAAE1B,aAAa,CAAC0B,QAAQ,CAAC;MACjCV,aAAa,EAAEhB,aAAa,CAACgB,aAAa,CAAC;MAC3CsB,cAAc,EAAEtC,aAAa,CAACsC,cAAc;IAC9C;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAepC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}