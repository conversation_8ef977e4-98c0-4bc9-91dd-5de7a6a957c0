/**
 * 頁面標題欄組件
 */

import React from 'react';
import { NavBar } from 'antd-mobile';
import { useNavigate } from 'react-router-dom';

const PageHeader = ({ 
  title, 
  onBack, 
  backText = '',
  right = null,
  showBack = true,
  style = {},
  className = '' 
}) => {
  const navigate = useNavigate();

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      navigate(-1);
    }
  };

  return (
    <NavBar
      className={`page-header ${className}`}
      style={style}
      onBack={showBack ? handleBack : undefined}
      backText={backText}
      right={right}
    >
      {title}
    </NavBar>
  );
};

export default PageHeader;
