{"ast": null, "code": "export const create = () => {\n  return [1, 0, 0, 1, 0, 0];\n};\nexport const getTranslateX = m => {\n  return m[4];\n};\nexport const getTranslateY = m => {\n  return m[5];\n};\nexport const getScaleX = m => {\n  return m[0];\n};\nexport const getScaleY = m => {\n  return m[3];\n};\nexport const translate = (m, x, y) => {\n  return multiply([1, 0, 0, 1, x, y], m);\n};\nexport const scale = (m, scaleX, scaleY = scaleX) => {\n  return multiply([scaleX, 0, 0, scaleY, 0, 0], m);\n};\nexport const apply = (m, [ox, oy]) => {\n  return [m[0] * ox + m[2] * oy + m[4], m[1] * ox + m[3] * oy + m[5]];\n};\nexport const multiply = (m1, m2) => {\n  return [m1[0] * m2[0] + m1[2] * m2[1], m1[1] * m2[0] + m1[3] * m2[1], m1[0] * m2[2] + m1[2] * m2[3], m1[1] * m2[2] + m1[3] * m2[3], m1[0] * m2[4] + m1[2] * m2[5] + m1[4], m1[1] * m2[4] + m1[3] * m2[5] + m1[5]];\n};", "map": {"version": 3, "names": ["create", "getTranslateX", "m", "getTranslateY", "getScaleX", "getScaleY", "translate", "x", "y", "multiply", "scale", "scaleX", "scaleY", "apply", "ox", "oy", "m1", "m2"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/matrix.js"], "sourcesContent": ["export const create = () => {\n  return [1, 0, 0, 1, 0, 0];\n};\nexport const getTranslateX = m => {\n  return m[4];\n};\nexport const getTranslateY = m => {\n  return m[5];\n};\nexport const getScaleX = m => {\n  return m[0];\n};\nexport const getScaleY = m => {\n  return m[3];\n};\nexport const translate = (m, x, y) => {\n  return multiply([1, 0, 0, 1, x, y], m);\n};\nexport const scale = (m, scaleX, scaleY = scaleX) => {\n  return multiply([scaleX, 0, 0, scaleY, 0, 0], m);\n};\nexport const apply = (m, [ox, oy]) => {\n  return [m[0] * ox + m[2] * oy + m[4], m[1] * ox + m[3] * oy + m[5]];\n};\nexport const multiply = (m1, m2) => {\n  return [m1[0] * m2[0] + m1[2] * m2[1], m1[1] * m2[0] + m1[3] * m2[1], m1[0] * m2[2] + m1[2] * m2[3], m1[1] * m2[2] + m1[3] * m2[3], m1[0] * m2[4] + m1[2] * m2[5] + m1[4], m1[1] * m2[4] + m1[3] * m2[5] + m1[5]];\n};"], "mappings": "AAAA,OAAO,MAAMA,MAAM,GAAGA,CAAA,KAAM;EAC1B,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AAC3B,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGC,CAAC,IAAI;EAChC,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD,OAAO,MAAMC,aAAa,GAAGD,CAAC,IAAI;EAChC,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD,OAAO,MAAME,SAAS,GAAGF,CAAC,IAAI;EAC5B,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD,OAAO,MAAMG,SAAS,GAAGH,CAAC,IAAI;EAC5B,OAAOA,CAAC,CAAC,CAAC,CAAC;AACb,CAAC;AACD,OAAO,MAAMI,SAAS,GAAGA,CAACJ,CAAC,EAAEK,CAAC,EAAEC,CAAC,KAAK;EACpC,OAAOC,QAAQ,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAEF,CAAC,EAAEC,CAAC,CAAC,EAAEN,CAAC,CAAC;AACxC,CAAC;AACD,OAAO,MAAMQ,KAAK,GAAGA,CAACR,CAAC,EAAES,MAAM,EAAEC,MAAM,GAAGD,MAAM,KAAK;EACnD,OAAOF,QAAQ,CAAC,CAACE,MAAM,EAAE,CAAC,EAAE,CAAC,EAAEC,MAAM,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEV,CAAC,CAAC;AAClD,CAAC;AACD,OAAO,MAAMW,KAAK,GAAGA,CAACX,CAAC,EAAE,CAACY,EAAE,EAAEC,EAAE,CAAC,KAAK;EACpC,OAAO,CAACb,CAAC,CAAC,CAAC,CAAC,GAAGY,EAAE,GAAGZ,CAAC,CAAC,CAAC,CAAC,GAAGa,EAAE,GAAGb,CAAC,CAAC,CAAC,CAAC,EAAEA,CAAC,CAAC,CAAC,CAAC,GAAGY,EAAE,GAAGZ,CAAC,CAAC,CAAC,CAAC,GAAGa,EAAE,GAAGb,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE,CAAC;AACD,OAAO,MAAMO,QAAQ,GAAGA,CAACO,EAAE,EAAEC,EAAE,KAAK;EAClC,OAAO,CAACD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,EAAED,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,EAAEA,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,GAAGC,EAAE,CAAC,CAAC,CAAC,GAAGD,EAAE,CAAC,CAAC,CAAC,CAAC;AACnN,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}