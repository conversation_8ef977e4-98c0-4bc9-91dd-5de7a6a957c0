{"ast": null, "code": "import dayjs from 'dayjs';\nimport isLeapYear from 'dayjs/plugin/isLeapYear';\nimport isoWeek from 'dayjs/plugin/isoWeek';\nimport isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';\ndayjs.extend(isoWeek);\ndayjs.extend(isoWeeksInYear);\ndayjs.extend(isLeapYear);\nconst precisionRankRecord = {\n  year: 0,\n  week: 1,\n  'week-day': 2\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const maxYear = max.getFullYear();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const minDay = dayjs(min);\n  const maxDay = dayjs(max);\n  const minWeek = minDay.isoWeek();\n  const maxWeek = maxDay.isoWeek();\n  const minWeekday = minDay.isoWeekday();\n  const maxWeekday = maxDay.isoWeekday();\n  const selectedWeek = parseInt(selected[1]);\n  const isInMinWeek = isInMinYear && selectedWeek === minWeek;\n  const isInMaxWeek = isInMaxYear && selectedWeek === maxWeek;\n  const selectedYearWeeks = dayjs(`${selectedYear}-01-01`).isoWeeksInYear();\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.week) {\n    const lower = isInMinYear ? minWeek : 1;\n    const upper = isInMaxYear ? maxWeek : selectedYearWeeks;\n    const weeks = generateColumn(lower, upper, 'week');\n    ret.push(weeks.map(v => ({\n      label: renderLabel('week', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord['week-day']) {\n    const lower = isInMinWeek ? minWeekday : 1;\n    const upper = isInMaxWeek ? maxWeekday : 7;\n    const weeks = generateColumn(lower, upper, 'week-day');\n    ret.push(weeks.map(v => ({\n      label: renderLabel('week-day', v),\n      value: v.toString()\n    })));\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  const day = dayjs(date);\n  return [day.isoWeekYear().toString(), day.isoWeek().toString(), day.isoWeekday().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b, _c;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const weekString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const weekdayString = (_c = value[2]) !== null && _c !== void 0 ? _c : '1';\n  const day = dayjs(`${parseInt(yearString)}-01-01`).isoWeek(parseInt(weekString)).isoWeekday(parseInt(weekdayString)).hour(0).minute(0).second(0);\n  return day.toDate();\n}", "map": {"version": 3, "names": ["dayjs", "isLeapYear", "isoWeek", "isoWeeksInYear", "extend", "precisionRankRecord", "year", "week", "generateDatePickerColumns", "selected", "min", "max", "precision", "renderLabel", "filter", "ret", "minYear", "getFullYear", "maxYear", "rank", "selected<PERSON>ear", "parseInt", "isInMinYear", "isInMaxYear", "minDay", "maxDay", "minWeek", "maxWeek", "minWeekday", "isoWeekday", "maxWeekday", "selectedWeek", "isInMinWeek", "isInMaxWeek", "selectedYearWeeks", "generateColumn", "from", "to", "column", "i", "push", "prefix", "slice", "currentFilter", "date", "stringArray", "toString", "convertStringArrayToDate", "lower", "upper", "years", "map", "v", "label", "value", "weeks", "convertDateToStringArray", "day", "isoWeekYear", "_a", "_b", "_c", "yearString", "weekString", "weekdayString", "hour", "minute", "second", "toDate"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/date-picker/date-picker-week-utils.js"], "sourcesContent": ["import dayjs from 'dayjs';\nimport isLeapYear from 'dayjs/plugin/isLeapYear';\nimport isoWeek from 'dayjs/plugin/isoWeek';\nimport isoWeeksInYear from 'dayjs/plugin/isoWeeksInYear';\ndayjs.extend(isoWeek);\ndayjs.extend(isoWeeksInYear);\ndayjs.extend(isLeapYear);\nconst precisionRankRecord = {\n  year: 0,\n  week: 1,\n  'week-day': 2\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const maxYear = max.getFullYear();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const minDay = dayjs(min);\n  const maxDay = dayjs(max);\n  const minWeek = minDay.isoWeek();\n  const maxWeek = maxDay.isoWeek();\n  const minWeekday = minDay.isoWeekday();\n  const maxWeekday = maxDay.isoWeekday();\n  const selectedWeek = parseInt(selected[1]);\n  const isInMinWeek = isInMinYear && selectedWeek === minWeek;\n  const isInMaxWeek = isInMaxYear && selectedWeek === maxWeek;\n  const selectedYearWeeks = dayjs(`${selectedYear}-01-01`).isoWeeksInYear();\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.week) {\n    const lower = isInMinYear ? minWeek : 1;\n    const upper = isInMaxYear ? maxWeek : selectedYearWeeks;\n    const weeks = generateColumn(lower, upper, 'week');\n    ret.push(weeks.map(v => ({\n      label: renderLabel('week', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord['week-day']) {\n    const lower = isInMinWeek ? minWeekday : 1;\n    const upper = isInMaxWeek ? maxWeekday : 7;\n    const weeks = generateColumn(lower, upper, 'week-day');\n    ret.push(weeks.map(v => ({\n      label: renderLabel('week-day', v),\n      value: v.toString()\n    })));\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  const day = dayjs(date);\n  return [day.isoWeekYear().toString(), day.isoWeek().toString(), day.isoWeekday().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b, _c;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const weekString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const weekdayString = (_c = value[2]) !== null && _c !== void 0 ? _c : '1';\n  const day = dayjs(`${parseInt(yearString)}-01-01`).isoWeek(parseInt(weekString)).isoWeekday(parseInt(weekdayString)).hour(0).minute(0).second(0);\n  return day.toDate();\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,yBAAyB;AAChD,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,OAAOC,cAAc,MAAM,6BAA6B;AACxDH,KAAK,CAACI,MAAM,CAACF,OAAO,CAAC;AACrBF,KAAK,CAACI,MAAM,CAACD,cAAc,CAAC;AAC5BH,KAAK,CAACI,MAAM,CAACH,UAAU,CAAC;AACxB,MAAMI,mBAAmB,GAAG;EAC1BC,IAAI,EAAE,CAAC;EACPC,IAAI,EAAE,CAAC;EACP,UAAU,EAAE;AACd,CAAC;AACD,OAAO,SAASC,yBAAyBA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAE;EAC5F,MAAMC,GAAG,GAAG,EAAE;EACd,MAAMC,OAAO,GAAGN,GAAG,CAACO,WAAW,CAAC,CAAC;EACjC,MAAMC,OAAO,GAAGP,GAAG,CAACM,WAAW,CAAC,CAAC;EACjC,MAAME,IAAI,GAAGd,mBAAmB,CAACO,SAAS,CAAC;EAC3C,MAAMQ,YAAY,GAAGC,QAAQ,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAMa,WAAW,GAAGF,YAAY,KAAKJ,OAAO;EAC5C,MAAMO,WAAW,GAAGH,YAAY,KAAKF,OAAO;EAC5C,MAAMM,MAAM,GAAGxB,KAAK,CAACU,GAAG,CAAC;EACzB,MAAMe,MAAM,GAAGzB,KAAK,CAACW,GAAG,CAAC;EACzB,MAAMe,OAAO,GAAGF,MAAM,CAACtB,OAAO,CAAC,CAAC;EAChC,MAAMyB,OAAO,GAAGF,MAAM,CAACvB,OAAO,CAAC,CAAC;EAChC,MAAM0B,UAAU,GAAGJ,MAAM,CAACK,UAAU,CAAC,CAAC;EACtC,MAAMC,UAAU,GAAGL,MAAM,CAACI,UAAU,CAAC,CAAC;EACtC,MAAME,YAAY,GAAGV,QAAQ,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAMuB,WAAW,GAAGV,WAAW,IAAIS,YAAY,KAAKL,OAAO;EAC3D,MAAMO,WAAW,GAAGV,WAAW,IAAIQ,YAAY,KAAKJ,OAAO;EAC3D,MAAMO,iBAAiB,GAAGlC,KAAK,CAAC,GAAGoB,YAAY,QAAQ,CAAC,CAACjB,cAAc,CAAC,CAAC;EACzE,MAAMgC,cAAc,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAEzB,SAAS,KAAK;IAC9C,IAAI0B,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAGH,IAAI,EAAEG,CAAC,IAAIF,EAAE,EAAEE,CAAC,EAAE,EAAE;MAC/BD,MAAM,CAACE,IAAI,CAACD,CAAC,CAAC;IAChB;IACA,MAAME,MAAM,GAAGhC,QAAQ,CAACiC,KAAK,CAAC,CAAC,EAAErC,mBAAmB,CAACO,SAAS,CAAC,CAAC;IAChE,MAAM+B,aAAa,GAAG7B,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,SAAS,CAAC;IACvF,IAAI+B,aAAa,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE;MACxDL,MAAM,GAAGA,MAAM,CAACxB,MAAM,CAACyB,CAAC,IAAII,aAAa,CAACJ,CAAC,EAAE;QAC3C,IAAIK,IAAIA,CAAA,EAAG;UACT,MAAMC,WAAW,GAAG,CAAC,GAAGJ,MAAM,EAAEF,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC;UAC7C,OAAOC,wBAAwB,CAACF,WAAW,CAAC;QAC9C;MACF,CAAC,CAAC,CAAC;IACL;IACA,OAAOP,MAAM;EACf,CAAC;EACD,IAAInB,IAAI,IAAId,mBAAmB,CAACC,IAAI,EAAE;IACpC,MAAM0C,KAAK,GAAGhC,OAAO;IACrB,MAAMiC,KAAK,GAAG/B,OAAO;IACrB,MAAMgC,KAAK,GAAGf,cAAc,CAACa,KAAK,EAAEC,KAAK,EAAE,MAAM,CAAC;IAClDlC,GAAG,CAACyB,IAAI,CAACU,KAAK,CAACC,GAAG,CAACC,CAAC,KAAK;MACvBC,KAAK,EAAExC,WAAW,CAAC,MAAM,EAAEuC,CAAC,CAAC;MAC7BE,KAAK,EAAEF,CAAC,CAACN,QAAQ,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC;EACN;EACA,IAAI3B,IAAI,IAAId,mBAAmB,CAACE,IAAI,EAAE;IACpC,MAAMyC,KAAK,GAAG1B,WAAW,GAAGI,OAAO,GAAG,CAAC;IACvC,MAAMuB,KAAK,GAAG1B,WAAW,GAAGI,OAAO,GAAGO,iBAAiB;IACvD,MAAMqB,KAAK,GAAGpB,cAAc,CAACa,KAAK,EAAEC,KAAK,EAAE,MAAM,CAAC;IAClDlC,GAAG,CAACyB,IAAI,CAACe,KAAK,CAACJ,GAAG,CAACC,CAAC,KAAK;MACvBC,KAAK,EAAExC,WAAW,CAAC,MAAM,EAAEuC,CAAC,CAAC;MAC7BE,KAAK,EAAEF,CAAC,CAACN,QAAQ,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC;EACN;EACA,IAAI3B,IAAI,IAAId,mBAAmB,CAAC,UAAU,CAAC,EAAE;IAC3C,MAAM2C,KAAK,GAAGhB,WAAW,GAAGJ,UAAU,GAAG,CAAC;IAC1C,MAAMqB,KAAK,GAAGhB,WAAW,GAAGH,UAAU,GAAG,CAAC;IAC1C,MAAMyB,KAAK,GAAGpB,cAAc,CAACa,KAAK,EAAEC,KAAK,EAAE,UAAU,CAAC;IACtDlC,GAAG,CAACyB,IAAI,CAACe,KAAK,CAACJ,GAAG,CAACC,CAAC,KAAK;MACvBC,KAAK,EAAExC,WAAW,CAAC,UAAU,EAAEuC,CAAC,CAAC;MACjCE,KAAK,EAAEF,CAAC,CAACN,QAAQ,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC;EACN;EACA,OAAO/B,GAAG;AACZ;AACA,OAAO,SAASyC,wBAAwBA,CAACZ,IAAI,EAAE;EAC7C,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMa,GAAG,GAAGzD,KAAK,CAAC4C,IAAI,CAAC;EACvB,OAAO,CAACa,GAAG,CAACC,WAAW,CAAC,CAAC,CAACZ,QAAQ,CAAC,CAAC,EAAEW,GAAG,CAACvD,OAAO,CAAC,CAAC,CAAC4C,QAAQ,CAAC,CAAC,EAAEW,GAAG,CAAC5B,UAAU,CAAC,CAAC,CAACiB,QAAQ,CAAC,CAAC,CAAC;AAC9F;AACA,OAAO,SAASC,wBAAwBA,CAACO,KAAK,EAAE;EAC9C,IAAIK,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAMC,UAAU,GAAG,CAACH,EAAE,GAAGL,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;EAC1E,MAAMI,UAAU,GAAG,CAACH,EAAE,GAAGN,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIM,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;EACvE,MAAMI,aAAa,GAAG,CAACH,EAAE,GAAGP,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIO,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;EAC1E,MAAMJ,GAAG,GAAGzD,KAAK,CAAC,GAAGqB,QAAQ,CAACyC,UAAU,CAAC,QAAQ,CAAC,CAAC5D,OAAO,CAACmB,QAAQ,CAAC0C,UAAU,CAAC,CAAC,CAAClC,UAAU,CAACR,QAAQ,CAAC2C,aAAa,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;EAChJ,OAAOV,GAAG,CAACW,MAAM,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}