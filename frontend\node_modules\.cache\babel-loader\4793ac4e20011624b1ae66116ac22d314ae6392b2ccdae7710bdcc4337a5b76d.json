{"ast": null, "code": "import { __assign, __read, __rest } from \"tslib\";\nimport Cookies from 'js-cookie';\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nfunction useCookieState(cookieKey, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = __read(useState(function () {\n      var cookieValue = Cookies.get(cookieKey);\n      if (isString(cookieValue)) return cookieValue;\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  var updateState = useMemoizedFn(function (newValue, newOptions) {\n    if (newOptions === void 0) {\n      newOptions = {};\n    }\n    var _a = __assign(__assign({}, options), newOptions),\n      defaultValue = _a.defaultValue,\n      restOptions = __rest(_a, [\"defaultValue\"]);\n    var value = isFunction(newValue) ? newValue(state) : newValue;\n    setState(value);\n    if (value === undefined) {\n      Cookies.remove(cookieKey);\n    } else {\n      Cookies.set(cookieKey, value, restOptions);\n    }\n  });\n  return [state, updateState];\n}\nexport default useCookieState;", "map": {"version": 3, "names": ["__assign", "__read", "__rest", "Cookies", "useState", "useMemoizedFn", "isFunction", "isString", "useCookieState", "<PERSON><PERSON><PERSON>", "options", "_a", "cookieValue", "get", "defaultValue", "state", "setState", "updateState", "newValue", "newOptions", "restOptions", "value", "undefined", "remove", "set"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useCookieState/index.js"], "sourcesContent": ["import { __assign, __read, __rest } from \"tslib\";\nimport Cookies from 'js-cookie';\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nfunction useCookieState(cookieKey, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = __read(useState(function () {\n      var cookieValue = Cookies.get(cookieKey);\n      if (isString(cookieValue)) return cookieValue;\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  var updateState = useMemoizedFn(function (newValue, newOptions) {\n    if (newOptions === void 0) {\n      newOptions = {};\n    }\n    var _a = __assign(__assign({}, options), newOptions),\n      defaultValue = _a.defaultValue,\n      restOptions = __rest(_a, [\"defaultValue\"]);\n    var value = isFunction(newValue) ? newValue(state) : newValue;\n    setState(value);\n    if (value === undefined) {\n      Cookies.remove(cookieKey);\n    } else {\n      Cookies.set(cookieKey, value, restOptions);\n    }\n  });\n  return [state, updateState];\n}\nexport default useCookieState;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,MAAM,QAAQ,OAAO;AAChD,OAAOC,OAAO,MAAM,WAAW;AAC/B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,UAAU,EAAEC,QAAQ,QAAQ,UAAU;AAC/C,SAASC,cAAcA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC1C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,EAAE,GAAGV,MAAM,CAACG,QAAQ,CAAC,YAAY;MACjC,IAAIQ,WAAW,GAAGT,OAAO,CAACU,GAAG,CAACJ,SAAS,CAAC;MACxC,IAAIF,QAAQ,CAACK,WAAW,CAAC,EAAE,OAAOA,WAAW;MAC7C,IAAIN,UAAU,CAACI,OAAO,CAACI,YAAY,CAAC,EAAE;QACpC,OAAOJ,OAAO,CAACI,YAAY,CAAC,CAAC;MAC/B;MACA,OAAOJ,OAAO,CAACI,YAAY;IAC7B,CAAC,CAAC,EAAE,CAAC,CAAC;IACNC,KAAK,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACbK,QAAQ,GAAGL,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIM,WAAW,GAAGZ,aAAa,CAAC,UAAUa,QAAQ,EAAEC,UAAU,EAAE;IAC9D,IAAIA,UAAU,KAAK,KAAK,CAAC,EAAE;MACzBA,UAAU,GAAG,CAAC,CAAC;IACjB;IACA,IAAIR,EAAE,GAAGX,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEU,OAAO,CAAC,EAAES,UAAU,CAAC;MAClDL,YAAY,GAAGH,EAAE,CAACG,YAAY;MAC9BM,WAAW,GAAGlB,MAAM,CAACS,EAAE,EAAE,CAAC,cAAc,CAAC,CAAC;IAC5C,IAAIU,KAAK,GAAGf,UAAU,CAACY,QAAQ,CAAC,GAAGA,QAAQ,CAACH,KAAK,CAAC,GAAGG,QAAQ;IAC7DF,QAAQ,CAACK,KAAK,CAAC;IACf,IAAIA,KAAK,KAAKC,SAAS,EAAE;MACvBnB,OAAO,CAACoB,MAAM,CAACd,SAAS,CAAC;IAC3B,CAAC,MAAM;MACLN,OAAO,CAACqB,GAAG,CAACf,SAAS,EAAEY,KAAK,EAAED,WAAW,CAAC;IAC5C;EACF,CAAC,CAAC;EACF,OAAO,CAACL,KAAK,EAAEE,WAAW,CAAC;AAC7B;AACA,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}