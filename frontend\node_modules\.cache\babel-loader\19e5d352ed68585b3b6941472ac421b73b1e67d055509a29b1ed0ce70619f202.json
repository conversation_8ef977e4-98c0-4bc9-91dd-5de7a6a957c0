{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafInterval = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setInterval(callback, delay)\n    };\n  }\n  var start = Date.now();\n  var handle = {\n    id: 0\n  };\n  var loop = function () {\n    var current = Date.now();\n    if (current - start >= delay) {\n      callback();\n      start = Date.now();\n    }\n    handle.id = requestAnimationFrame(loop);\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafInterval = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearInterval(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafInterval(fn, delay, options) {\n  var immediate = options === null || options === void 0 ? void 0 : options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (immediate) {\n      fnRef.current();\n    }\n    timerRef.current = setRafInterval(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafInterval;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useLatest", "isNumber", "setRafInterval", "callback", "delay", "requestAnimationFrame", "undefined", "id", "setInterval", "start", "Date", "now", "handle", "loop", "current", "cancelAnimationFrameIsNotDefined", "t", "cancelAnimationFrame", "clearRafInterval", "clearInterval", "useRafInterval", "fn", "options", "immediate", "fnRef", "timerRef", "clear"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRafInterval/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafInterval = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setInterval(callback, delay)\n    };\n  }\n  var start = Date.now();\n  var handle = {\n    id: 0\n  };\n  var loop = function () {\n    var current = Date.now();\n    if (current - start >= delay) {\n      callback();\n      start = Date.now();\n    }\n    handle.id = requestAnimationFrame(loop);\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafInterval = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearInterval(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafInterval(fn, delay, options) {\n  var immediate = options === null || options === void 0 ? void 0 : options.immediate;\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafInterval(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) {\n      return;\n    }\n    if (immediate) {\n      fnRef.current();\n    }\n    timerRef.current = setRafInterval(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafInterval;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACtD,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,QAAQ,QAAQ,UAAU;AACnC,IAAIC,cAAc,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,KAAK,EAAE;EAC9C,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC;EACX;EACA,IAAI,OAAOC,qBAAqB,KAAK,OAAOC,SAAS,EAAE;IACrD,OAAO;MACLC,EAAE,EAAEC,WAAW,CAACL,QAAQ,EAAEC,KAAK;IACjC,CAAC;EACH;EACA,IAAIK,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EACtB,IAAIC,MAAM,GAAG;IACXL,EAAE,EAAE;EACN,CAAC;EACD,IAAIM,IAAI,GAAG,SAAAA,CAAA,EAAY;IACrB,IAAIC,OAAO,GAAGJ,IAAI,CAACC,GAAG,CAAC,CAAC;IACxB,IAAIG,OAAO,GAAGL,KAAK,IAAIL,KAAK,EAAE;MAC5BD,QAAQ,CAAC,CAAC;MACVM,KAAK,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;IACpB;IACAC,MAAM,CAACL,EAAE,GAAGF,qBAAqB,CAACQ,IAAI,CAAC;EACzC,CAAC;EACDD,MAAM,CAACL,EAAE,GAAGF,qBAAqB,CAACQ,IAAI,CAAC;EACvC,OAAOD,MAAM;AACf,CAAC;AACD,SAASG,gCAAgCA,CAACC,CAAC,EAAE;EAC3C,OAAO,OAAOC,oBAAoB,KAAK,OAAOX,SAAS;AACzD;AACA,IAAIY,gBAAgB,GAAG,SAAAA,CAAUN,MAAM,EAAE;EACvC,IAAIG,gCAAgC,CAACH,MAAM,CAACL,EAAE,CAAC,EAAE;IAC/C,OAAOY,aAAa,CAACP,MAAM,CAACL,EAAE,CAAC;EACjC;EACAU,oBAAoB,CAACL,MAAM,CAACL,EAAE,CAAC;AACjC,CAAC;AACD,SAASa,cAAcA,CAACC,EAAE,EAAEjB,KAAK,EAAEkB,OAAO,EAAE;EAC1C,IAAIC,SAAS,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACC,SAAS;EACnF,IAAIC,KAAK,GAAGxB,SAAS,CAACqB,EAAE,CAAC;EACzB,IAAII,QAAQ,GAAG1B,MAAM,CAAC,CAAC;EACvB,IAAI2B,KAAK,GAAG7B,WAAW,CAAC,YAAY;IAClC,IAAI4B,QAAQ,CAACX,OAAO,EAAE;MACpBI,gBAAgB,CAACO,QAAQ,CAACX,OAAO,CAAC;IACpC;EACF,CAAC,EAAE,EAAE,CAAC;EACNhB,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;MACjC;IACF;IACA,IAAImB,SAAS,EAAE;MACbC,KAAK,CAACV,OAAO,CAAC,CAAC;IACjB;IACAW,QAAQ,CAACX,OAAO,GAAGZ,cAAc,CAAC,YAAY;MAC5CsB,KAAK,CAACV,OAAO,CAAC,CAAC;IACjB,CAAC,EAAEV,KAAK,CAAC;IACT,OAAOsB,KAAK;EACd,CAAC,EAAE,CAACtB,KAAK,CAAC,CAAC;EACX,OAAOsB,KAAK;AACd;AACA,eAAeN,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}