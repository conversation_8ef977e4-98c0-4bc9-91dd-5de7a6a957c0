/**
 * 成功消息組件
 */

import React from 'react';
import { Card } from 'antd-mobile';
import { CheckCircleOutline } from 'antd-mobile-icons';

const SuccessMessage = ({ 
  message, 
  title = '操作成功',
  style = {},
  className = '',
  showIcon = true 
}) => {
  return (
    <Card 
      className={`success-message ${className}`}
      style={{
        margin: '16px',
        textAlign: 'center',
        border: '1px solid #52c41a',
        backgroundColor: '#f6ffed',
        ...style
      }}
    >
      <div style={{ padding: '20px' }}>
        {showIcon && (
          <CheckCircleOutline 
            style={{ 
              fontSize: '48px', 
              color: '#52c41a',
              marginBottom: '16px'
            }} 
          />
        )}
        
        <div 
          style={{
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#262626',
            marginBottom: message ? '8px' : '0'
          }}
        >
          {title}
        </div>
        
        {message && (
          <div 
            style={{
              fontSize: '14px',
              color: '#52c41a',
              lineHeight: '1.5'
            }}
          >
            {message}
          </div>
        )}
      </div>
    </Card>
  );
};

export default SuccessMessage;
