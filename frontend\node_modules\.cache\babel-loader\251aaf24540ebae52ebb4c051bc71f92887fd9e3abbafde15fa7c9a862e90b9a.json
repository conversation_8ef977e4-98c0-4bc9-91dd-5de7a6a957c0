{"ast": null, "code": "import { useRef } from 'react';\nimport depsAreSame from '../utils/depsAreSame';\nexport default function useCreation(factory, deps) {\n  var current = useRef({\n    deps: deps,\n    obj: undefined,\n    initialized: false\n  }).current;\n  if (current.initialized === false || !depsAreSame(current.deps, deps)) {\n    current.deps = deps;\n    current.obj = factory();\n    current.initialized = true;\n  }\n  return current.obj;\n}", "map": {"version": 3, "names": ["useRef", "depsAreSame", "useCreation", "factory", "deps", "current", "obj", "undefined", "initialized"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useCreation/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport depsAreSame from '../utils/depsAreSame';\nexport default function useCreation(factory, deps) {\n  var current = useRef({\n    deps: deps,\n    obj: undefined,\n    initialized: false\n  }).current;\n  if (current.initialized === false || !depsAreSame(current.deps, deps)) {\n    current.deps = deps;\n    current.obj = factory();\n    current.initialized = true;\n  }\n  return current.obj;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,WAAW,MAAM,sBAAsB;AAC9C,eAAe,SAASC,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACjD,IAAIC,OAAO,GAAGL,MAAM,CAAC;IACnBI,IAAI,EAAEA,IAAI;IACVE,GAAG,EAAEC,SAAS;IACdC,WAAW,EAAE;EACf,CAAC,CAAC,CAACH,OAAO;EACV,IAAIA,OAAO,CAACG,WAAW,KAAK,KAAK,IAAI,CAACP,WAAW,CAACI,OAAO,CAACD,IAAI,EAAEA,IAAI,CAAC,EAAE;IACrEC,OAAO,CAACD,IAAI,GAAGA,IAAI;IACnBC,OAAO,CAACC,GAAG,GAAGH,OAAO,CAAC,CAAC;IACvBE,OAAO,CAACG,WAAW,GAAG,IAAI;EAC5B;EACA,OAAOH,OAAO,CAACC,GAAG;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}