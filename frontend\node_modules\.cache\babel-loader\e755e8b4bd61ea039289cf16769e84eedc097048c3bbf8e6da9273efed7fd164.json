{"ast": null, "code": "import dayjs from 'dayjs';\nimport quarterOfYear from 'dayjs/plugin/quarterOfYear';\ndayjs.extend(quarterOfYear);\nconst precisionRankRecord = {\n  year: 0,\n  quarter: 1\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const maxYear = max.getFullYear();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const minDay = dayjs(min);\n  const maxDay = dayjs(max);\n  const minQuarter = minDay.quarter();\n  const maxQuarter = maxDay.quarter();\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.quarter) {\n    const lower = isInMinYear ? minQuarter : 1;\n    const upper = isInMaxYear ? maxQuarter : 4;\n    const quarters = generateColumn(lower, upper, 'quarter');\n    ret.push(quarters.map(v => ({\n      label: renderLabel('quarter', v),\n      value: v.toString()\n    })));\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  const day = dayjs(date);\n  return [day.year().toString(), day.quarter().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const quarterString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const day = dayjs().year(parseInt(yearString)).quarter(parseInt(quarterString)).hour(0).minute(0).second(0);\n  return day.toDate();\n}", "map": {"version": 3, "names": ["dayjs", "quarterOfYear", "extend", "precisionRankRecord", "year", "quarter", "generateDatePickerColumns", "selected", "min", "max", "precision", "renderLabel", "filter", "ret", "minYear", "getFullYear", "maxYear", "rank", "selected<PERSON>ear", "parseInt", "isInMinYear", "isInMaxYear", "minDay", "maxDay", "minQuarter", "maxQuarter", "generateColumn", "from", "to", "column", "i", "push", "prefix", "slice", "currentFilter", "date", "stringArray", "toString", "convertStringArrayToDate", "lower", "upper", "years", "map", "v", "label", "value", "quarters", "convertDateToStringArray", "day", "_a", "_b", "yearString", "quarterString", "hour", "minute", "second", "toDate"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/date-picker/date-picker-quarter-utils.js"], "sourcesContent": ["import dayjs from 'dayjs';\nimport quarterOfYear from 'dayjs/plugin/quarterOfYear';\ndayjs.extend(quarterOfYear);\nconst precisionRankRecord = {\n  year: 0,\n  quarter: 1\n};\nexport function generateDatePickerColumns(selected, min, max, precision, renderLabel, filter) {\n  const ret = [];\n  const minYear = min.getFullYear();\n  const maxYear = max.getFullYear();\n  const rank = precisionRankRecord[precision];\n  const selectedYear = parseInt(selected[0]);\n  const isInMinYear = selectedYear === minYear;\n  const isInMaxYear = selectedYear === maxYear;\n  const minDay = dayjs(min);\n  const maxDay = dayjs(max);\n  const minQuarter = minDay.quarter();\n  const maxQuarter = maxDay.quarter();\n  const generateColumn = (from, to, precision) => {\n    let column = [];\n    for (let i = from; i <= to; i++) {\n      column.push(i);\n    }\n    const prefix = selected.slice(0, precisionRankRecord[precision]);\n    const currentFilter = filter === null || filter === void 0 ? void 0 : filter[precision];\n    if (currentFilter && typeof currentFilter === 'function') {\n      column = column.filter(i => currentFilter(i, {\n        get date() {\n          const stringArray = [...prefix, i.toString()];\n          return convertStringArrayToDate(stringArray);\n        }\n      }));\n    }\n    return column;\n  };\n  if (rank >= precisionRankRecord.year) {\n    const lower = minYear;\n    const upper = maxYear;\n    const years = generateColumn(lower, upper, 'year');\n    ret.push(years.map(v => ({\n      label: renderLabel('year', v),\n      value: v.toString()\n    })));\n  }\n  if (rank >= precisionRankRecord.quarter) {\n    const lower = isInMinYear ? minQuarter : 1;\n    const upper = isInMaxYear ? maxQuarter : 4;\n    const quarters = generateColumn(lower, upper, 'quarter');\n    ret.push(quarters.map(v => ({\n      label: renderLabel('quarter', v),\n      value: v.toString()\n    })));\n  }\n  return ret;\n}\nexport function convertDateToStringArray(date) {\n  if (!date) return [];\n  const day = dayjs(date);\n  return [day.year().toString(), day.quarter().toString()];\n}\nexport function convertStringArrayToDate(value) {\n  var _a, _b;\n  const yearString = (_a = value[0]) !== null && _a !== void 0 ? _a : '1900';\n  const quarterString = (_b = value[1]) !== null && _b !== void 0 ? _b : '1';\n  const day = dayjs().year(parseInt(yearString)).quarter(parseInt(quarterString)).hour(0).minute(0).second(0);\n  return day.toDate();\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,aAAa,MAAM,4BAA4B;AACtDD,KAAK,CAACE,MAAM,CAACD,aAAa,CAAC;AAC3B,MAAME,mBAAmB,GAAG;EAC1BC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE;AACX,CAAC;AACD,OAAO,SAASC,yBAAyBA,CAACC,QAAQ,EAAEC,GAAG,EAAEC,GAAG,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAE;EAC5F,MAAMC,GAAG,GAAG,EAAE;EACd,MAAMC,OAAO,GAAGN,GAAG,CAACO,WAAW,CAAC,CAAC;EACjC,MAAMC,OAAO,GAAGP,GAAG,CAACM,WAAW,CAAC,CAAC;EACjC,MAAME,IAAI,GAAGd,mBAAmB,CAACO,SAAS,CAAC;EAC3C,MAAMQ,YAAY,GAAGC,QAAQ,CAACZ,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAMa,WAAW,GAAGF,YAAY,KAAKJ,OAAO;EAC5C,MAAMO,WAAW,GAAGH,YAAY,KAAKF,OAAO;EAC5C,MAAMM,MAAM,GAAGtB,KAAK,CAACQ,GAAG,CAAC;EACzB,MAAMe,MAAM,GAAGvB,KAAK,CAACS,GAAG,CAAC;EACzB,MAAMe,UAAU,GAAGF,MAAM,CAACjB,OAAO,CAAC,CAAC;EACnC,MAAMoB,UAAU,GAAGF,MAAM,CAAClB,OAAO,CAAC,CAAC;EACnC,MAAMqB,cAAc,GAAGA,CAACC,IAAI,EAAEC,EAAE,EAAElB,SAAS,KAAK;IAC9C,IAAImB,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,CAAC,GAAGH,IAAI,EAAEG,CAAC,IAAIF,EAAE,EAAEE,CAAC,EAAE,EAAE;MAC/BD,MAAM,CAACE,IAAI,CAACD,CAAC,CAAC;IAChB;IACA,MAAME,MAAM,GAAGzB,QAAQ,CAAC0B,KAAK,CAAC,CAAC,EAAE9B,mBAAmB,CAACO,SAAS,CAAC,CAAC;IAChE,MAAMwB,aAAa,GAAGtB,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACF,SAAS,CAAC;IACvF,IAAIwB,aAAa,IAAI,OAAOA,aAAa,KAAK,UAAU,EAAE;MACxDL,MAAM,GAAGA,MAAM,CAACjB,MAAM,CAACkB,CAAC,IAAII,aAAa,CAACJ,CAAC,EAAE;QAC3C,IAAIK,IAAIA,CAAA,EAAG;UACT,MAAMC,WAAW,GAAG,CAAC,GAAGJ,MAAM,EAAEF,CAAC,CAACO,QAAQ,CAAC,CAAC,CAAC;UAC7C,OAAOC,wBAAwB,CAACF,WAAW,CAAC;QAC9C;MACF,CAAC,CAAC,CAAC;IACL;IACA,OAAOP,MAAM;EACf,CAAC;EACD,IAAIZ,IAAI,IAAId,mBAAmB,CAACC,IAAI,EAAE;IACpC,MAAMmC,KAAK,GAAGzB,OAAO;IACrB,MAAM0B,KAAK,GAAGxB,OAAO;IACrB,MAAMyB,KAAK,GAAGf,cAAc,CAACa,KAAK,EAAEC,KAAK,EAAE,MAAM,CAAC;IAClD3B,GAAG,CAACkB,IAAI,CAACU,KAAK,CAACC,GAAG,CAACC,CAAC,KAAK;MACvBC,KAAK,EAAEjC,WAAW,CAAC,MAAM,EAAEgC,CAAC,CAAC;MAC7BE,KAAK,EAAEF,CAAC,CAACN,QAAQ,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC;EACN;EACA,IAAIpB,IAAI,IAAId,mBAAmB,CAACE,OAAO,EAAE;IACvC,MAAMkC,KAAK,GAAGnB,WAAW,GAAGI,UAAU,GAAG,CAAC;IAC1C,MAAMgB,KAAK,GAAGnB,WAAW,GAAGI,UAAU,GAAG,CAAC;IAC1C,MAAMqB,QAAQ,GAAGpB,cAAc,CAACa,KAAK,EAAEC,KAAK,EAAE,SAAS,CAAC;IACxD3B,GAAG,CAACkB,IAAI,CAACe,QAAQ,CAACJ,GAAG,CAACC,CAAC,KAAK;MAC1BC,KAAK,EAAEjC,WAAW,CAAC,SAAS,EAAEgC,CAAC,CAAC;MAChCE,KAAK,EAAEF,CAAC,CAACN,QAAQ,CAAC;IACpB,CAAC,CAAC,CAAC,CAAC;EACN;EACA,OAAOxB,GAAG;AACZ;AACA,OAAO,SAASkC,wBAAwBA,CAACZ,IAAI,EAAE;EAC7C,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMa,GAAG,GAAGhD,KAAK,CAACmC,IAAI,CAAC;EACvB,OAAO,CAACa,GAAG,CAAC5C,IAAI,CAAC,CAAC,CAACiC,QAAQ,CAAC,CAAC,EAAEW,GAAG,CAAC3C,OAAO,CAAC,CAAC,CAACgC,QAAQ,CAAC,CAAC,CAAC;AAC1D;AACA,OAAO,SAASC,wBAAwBA,CAACO,KAAK,EAAE;EAC9C,IAAII,EAAE,EAAEC,EAAE;EACV,MAAMC,UAAU,GAAG,CAACF,EAAE,GAAGJ,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAII,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM;EAC1E,MAAMG,aAAa,GAAG,CAACF,EAAE,GAAGL,KAAK,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIK,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,GAAG;EAC1E,MAAMF,GAAG,GAAGhD,KAAK,CAAC,CAAC,CAACI,IAAI,CAACe,QAAQ,CAACgC,UAAU,CAAC,CAAC,CAAC9C,OAAO,CAACc,QAAQ,CAACiC,aAAa,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAAC;EAC3G,OAAOP,GAAG,CAACQ,MAAM,CAAC,CAAC;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}