{"ast": null, "code": "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport isBrowser from '../utils/isBrowser';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar touchSupported = isBrowser && (\n// @ts-ignore\n'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch);\nfunction useLongPress(onLongPress, target, _a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.delay,\n    delay = _c === void 0 ? 300 : _c,\n    moveThreshold = _b.moveThreshold,\n    onClick = _b.onClick,\n    onLongPressEnd = _b.onLongPressEnd;\n  var onLongPressRef = useLatest(onLongPress);\n  var onClickRef = useLatest(onClick);\n  var onLongPressEndRef = useLatest(onLongPressEnd);\n  var timerRef = useRef();\n  var isTriggeredRef = useRef(false);\n  var pervPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var overThreshold = function (event) {\n      var _a = getClientPosition(event),\n        clientX = _a.clientX,\n        clientY = _a.clientY;\n      var offsetX = Math.abs(clientX - pervPositionRef.current.x);\n      var offsetY = Math.abs(clientY - pervPositionRef.current.y);\n      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);\n    };\n    function getClientPosition(event) {\n      if ('TouchEvent' in window && event instanceof TouchEvent) {\n        return {\n          clientX: event.touches[0].clientX,\n          clientY: event.touches[0].clientY\n        };\n      }\n      if (event instanceof MouseEvent) {\n        return {\n          clientX: event.clientX,\n          clientY: event.clientY\n        };\n      }\n      console.warn('Unsupported event type');\n      return {\n        clientX: 0,\n        clientY: 0\n      };\n    }\n    var onStart = function (event) {\n      if (hasMoveThreshold) {\n        var _a = getClientPosition(event),\n          clientX = _a.clientX,\n          clientY = _a.clientY;\n        pervPositionRef.current.x = clientX;\n        pervPositionRef.current.y = clientY;\n      }\n      timerRef.current = setTimeout(function () {\n        onLongPressRef.current(event);\n        isTriggeredRef.current = true;\n      }, delay);\n    };\n    var onMove = function (event) {\n      if (timerRef.current && overThreshold(event)) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n    };\n    var onEnd = function (event, shouldTriggerClick) {\n      var _a;\n      if (shouldTriggerClick === void 0) {\n        shouldTriggerClick = false;\n      }\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n      }\n      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {\n        onClickRef.current(event);\n      }\n      isTriggeredRef.current = false;\n    };\n    var onEndWithClick = function (event) {\n      return onEnd(event, true);\n    };\n    if (!touchSupported) {\n      targetElement.addEventListener('mousedown', onStart);\n      targetElement.addEventListener('mouseup', onEndWithClick);\n      targetElement.addEventListener('mouseleave', onEnd);\n      if (hasMoveThreshold) targetElement.addEventListener('mousemove', onMove);\n    } else {\n      targetElement.addEventListener('touchstart', onStart);\n      targetElement.addEventListener('touchend', onEndWithClick);\n      if (hasMoveThreshold) targetElement.addEventListener('touchmove', onMove);\n    }\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        isTriggeredRef.current = false;\n      }\n      if (!touchSupported) {\n        targetElement.removeEventListener('mousedown', onStart);\n        targetElement.removeEventListener('mouseup', onEndWithClick);\n        targetElement.removeEventListener('mouseleave', onEnd);\n        if (hasMoveThreshold) targetElement.removeEventListener('mousemove', onMove);\n      } else {\n        targetElement.removeEventListener('touchstart', onStart);\n        targetElement.removeEventListener('touchend', onEndWithClick);\n        if (hasMoveThreshold) targetElement.removeEventListener('touchmove', onMove);\n      }\n    };\n  }, [], target);\n}\nexport default useLongPress;", "map": {"version": 3, "names": ["useRef", "useLatest", "getTargetElement", "<PERSON><PERSON><PERSON><PERSON>", "useEffectWithTarget", "touchSupported", "window", "DocumentTouch", "document", "useLongPress", "onLongPress", "target", "_a", "_b", "_c", "delay", "moveT<PERSON><PERSON>old", "onClick", "onLongPressEnd", "onLongPressRef", "onClickRef", "onLongPressEndRef", "timerRef", "isTriggeredRef", "pervPositionRef", "x", "y", "hasMoveThreshold", "targetElement", "addEventListener", "overThreshold", "event", "getClientPosition", "clientX", "clientY", "offsetX", "Math", "abs", "current", "offsetY", "TouchEvent", "touches", "MouseEvent", "console", "warn", "onStart", "setTimeout", "onMove", "clearTimeout", "undefined", "onEnd", "shouldTriggerClick", "call", "onEndWithClick", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useLongPress/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport isBrowser from '../utils/isBrowser';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar touchSupported = isBrowser && (\n// @ts-ignore\n'ontouchstart' in window || window.DocumentTouch && document instanceof DocumentTouch);\nfunction useLongPress(onLongPress, target, _a) {\n  var _b = _a === void 0 ? {} : _a,\n    _c = _b.delay,\n    delay = _c === void 0 ? 300 : _c,\n    moveThreshold = _b.moveThreshold,\n    onClick = _b.onClick,\n    onLongPressEnd = _b.onLongPressEnd;\n  var onLongPressRef = useLatest(onLongPress);\n  var onClickRef = useLatest(onClick);\n  var onLongPressEndRef = useLatest(onLongPressEnd);\n  var timerRef = useRef();\n  var isTriggeredRef = useRef(false);\n  var pervPositionRef = useRef({\n    x: 0,\n    y: 0\n  });\n  var hasMoveThreshold = !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && moveThreshold.x > 0 || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && moveThreshold.y > 0);\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var overThreshold = function (event) {\n      var _a = getClientPosition(event),\n        clientX = _a.clientX,\n        clientY = _a.clientY;\n      var offsetX = Math.abs(clientX - pervPositionRef.current.x);\n      var offsetY = Math.abs(clientY - pervPositionRef.current.y);\n      return !!((moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.x) && offsetX > moveThreshold.x || (moveThreshold === null || moveThreshold === void 0 ? void 0 : moveThreshold.y) && offsetY > moveThreshold.y);\n    };\n    function getClientPosition(event) {\n      if ('TouchEvent' in window && event instanceof TouchEvent) {\n        return {\n          clientX: event.touches[0].clientX,\n          clientY: event.touches[0].clientY\n        };\n      }\n      if (event instanceof MouseEvent) {\n        return {\n          clientX: event.clientX,\n          clientY: event.clientY\n        };\n      }\n      console.warn('Unsupported event type');\n      return {\n        clientX: 0,\n        clientY: 0\n      };\n    }\n    var onStart = function (event) {\n      if (hasMoveThreshold) {\n        var _a = getClientPosition(event),\n          clientX = _a.clientX,\n          clientY = _a.clientY;\n        pervPositionRef.current.x = clientX;\n        pervPositionRef.current.y = clientY;\n      }\n      timerRef.current = setTimeout(function () {\n        onLongPressRef.current(event);\n        isTriggeredRef.current = true;\n      }, delay);\n    };\n    var onMove = function (event) {\n      if (timerRef.current && overThreshold(event)) {\n        clearTimeout(timerRef.current);\n        timerRef.current = undefined;\n      }\n    };\n    var onEnd = function (event, shouldTriggerClick) {\n      var _a;\n      if (shouldTriggerClick === void 0) {\n        shouldTriggerClick = false;\n      }\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n      }\n      if (isTriggeredRef.current) {\n        (_a = onLongPressEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onLongPressEndRef, event);\n      }\n      if (shouldTriggerClick && !isTriggeredRef.current && onClickRef.current) {\n        onClickRef.current(event);\n      }\n      isTriggeredRef.current = false;\n    };\n    var onEndWithClick = function (event) {\n      return onEnd(event, true);\n    };\n    if (!touchSupported) {\n      targetElement.addEventListener('mousedown', onStart);\n      targetElement.addEventListener('mouseup', onEndWithClick);\n      targetElement.addEventListener('mouseleave', onEnd);\n      if (hasMoveThreshold) targetElement.addEventListener('mousemove', onMove);\n    } else {\n      targetElement.addEventListener('touchstart', onStart);\n      targetElement.addEventListener('touchend', onEndWithClick);\n      if (hasMoveThreshold) targetElement.addEventListener('touchmove', onMove);\n    }\n    return function () {\n      if (timerRef.current) {\n        clearTimeout(timerRef.current);\n        isTriggeredRef.current = false;\n      }\n      if (!touchSupported) {\n        targetElement.removeEventListener('mousedown', onStart);\n        targetElement.removeEventListener('mouseup', onEndWithClick);\n        targetElement.removeEventListener('mouseleave', onEnd);\n        if (hasMoveThreshold) targetElement.removeEventListener('mousemove', onMove);\n      } else {\n        targetElement.removeEventListener('touchstart', onStart);\n        targetElement.removeEventListener('touchend', onEndWithClick);\n        if (hasMoveThreshold) targetElement.removeEventListener('touchmove', onMove);\n      }\n    };\n  }, [], target);\n}\nexport default useLongPress;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,IAAIC,cAAc,GAAGF,SAAS;AAC9B;AACA,cAAc,IAAIG,MAAM,IAAIA,MAAM,CAACC,aAAa,IAAIC,QAAQ,YAAYD,aAAa,CAAC;AACtF,SAASE,YAAYA,CAACC,WAAW,EAAEC,MAAM,EAAEC,EAAE,EAAE;EAC7C,IAAIC,EAAE,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;IAC9BE,EAAE,GAAGD,EAAE,CAACE,KAAK;IACbA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,EAAE;IAChCE,aAAa,GAAGH,EAAE,CAACG,aAAa;IAChCC,OAAO,GAAGJ,EAAE,CAACI,OAAO;IACpBC,cAAc,GAAGL,EAAE,CAACK,cAAc;EACpC,IAAIC,cAAc,GAAGlB,SAAS,CAACS,WAAW,CAAC;EAC3C,IAAIU,UAAU,GAAGnB,SAAS,CAACgB,OAAO,CAAC;EACnC,IAAII,iBAAiB,GAAGpB,SAAS,CAACiB,cAAc,CAAC;EACjD,IAAII,QAAQ,GAAGtB,MAAM,CAAC,CAAC;EACvB,IAAIuB,cAAc,GAAGvB,MAAM,CAAC,KAAK,CAAC;EAClC,IAAIwB,eAAe,GAAGxB,MAAM,CAAC;IAC3ByB,CAAC,EAAE,CAAC;IACJC,CAAC,EAAE;EACL,CAAC,CAAC;EACF,IAAIC,gBAAgB,GAAG,CAAC,EAAE,CAACX,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACS,CAAC,KAAKT,aAAa,CAACS,CAAC,GAAG,CAAC,IAAI,CAACT,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACU,CAAC,KAAKV,aAAa,CAACU,CAAC,GAAG,CAAC,CAAC;EAC3OtB,mBAAmB,CAAC,YAAY;IAC9B,IAAIwB,aAAa,GAAG1B,gBAAgB,CAACS,MAAM,CAAC;IAC5C,IAAI,EAAEiB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,gBAAgB,CAAC,EAAE;MACnG;IACF;IACA,IAAIC,aAAa,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACnC,IAAInB,EAAE,GAAGoB,iBAAiB,CAACD,KAAK,CAAC;QAC/BE,OAAO,GAAGrB,EAAE,CAACqB,OAAO;QACpBC,OAAO,GAAGtB,EAAE,CAACsB,OAAO;MACtB,IAAIC,OAAO,GAAGC,IAAI,CAACC,GAAG,CAACJ,OAAO,GAAGT,eAAe,CAACc,OAAO,CAACb,CAAC,CAAC;MAC3D,IAAIc,OAAO,GAAGH,IAAI,CAACC,GAAG,CAACH,OAAO,GAAGV,eAAe,CAACc,OAAO,CAACZ,CAAC,CAAC;MAC3D,OAAO,CAAC,EAAE,CAACV,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACS,CAAC,KAAKU,OAAO,GAAGnB,aAAa,CAACS,CAAC,IAAI,CAACT,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACU,CAAC,KAAKa,OAAO,GAAGvB,aAAa,CAACU,CAAC,CAAC;IACzO,CAAC;IACD,SAASM,iBAAiBA,CAACD,KAAK,EAAE;MAChC,IAAI,YAAY,IAAIzB,MAAM,IAAIyB,KAAK,YAAYS,UAAU,EAAE;QACzD,OAAO;UACLP,OAAO,EAAEF,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC,CAACR,OAAO;UACjCC,OAAO,EAAEH,KAAK,CAACU,OAAO,CAAC,CAAC,CAAC,CAACP;QAC5B,CAAC;MACH;MACA,IAAIH,KAAK,YAAYW,UAAU,EAAE;QAC/B,OAAO;UACLT,OAAO,EAAEF,KAAK,CAACE,OAAO;UACtBC,OAAO,EAAEH,KAAK,CAACG;QACjB,CAAC;MACH;MACAS,OAAO,CAACC,IAAI,CAAC,wBAAwB,CAAC;MACtC,OAAO;QACLX,OAAO,EAAE,CAAC;QACVC,OAAO,EAAE;MACX,CAAC;IACH;IACA,IAAIW,OAAO,GAAG,SAAAA,CAAUd,KAAK,EAAE;MAC7B,IAAIJ,gBAAgB,EAAE;QACpB,IAAIf,EAAE,GAAGoB,iBAAiB,CAACD,KAAK,CAAC;UAC/BE,OAAO,GAAGrB,EAAE,CAACqB,OAAO;UACpBC,OAAO,GAAGtB,EAAE,CAACsB,OAAO;QACtBV,eAAe,CAACc,OAAO,CAACb,CAAC,GAAGQ,OAAO;QACnCT,eAAe,CAACc,OAAO,CAACZ,CAAC,GAAGQ,OAAO;MACrC;MACAZ,QAAQ,CAACgB,OAAO,GAAGQ,UAAU,CAAC,YAAY;QACxC3B,cAAc,CAACmB,OAAO,CAACP,KAAK,CAAC;QAC7BR,cAAc,CAACe,OAAO,GAAG,IAAI;MAC/B,CAAC,EAAEvB,KAAK,CAAC;IACX,CAAC;IACD,IAAIgC,MAAM,GAAG,SAAAA,CAAUhB,KAAK,EAAE;MAC5B,IAAIT,QAAQ,CAACgB,OAAO,IAAIR,aAAa,CAACC,KAAK,CAAC,EAAE;QAC5CiB,YAAY,CAAC1B,QAAQ,CAACgB,OAAO,CAAC;QAC9BhB,QAAQ,CAACgB,OAAO,GAAGW,SAAS;MAC9B;IACF,CAAC;IACD,IAAIC,KAAK,GAAG,SAAAA,CAAUnB,KAAK,EAAEoB,kBAAkB,EAAE;MAC/C,IAAIvC,EAAE;MACN,IAAIuC,kBAAkB,KAAK,KAAK,CAAC,EAAE;QACjCA,kBAAkB,GAAG,KAAK;MAC5B;MACA,IAAI7B,QAAQ,CAACgB,OAAO,EAAE;QACpBU,YAAY,CAAC1B,QAAQ,CAACgB,OAAO,CAAC;MAChC;MACA,IAAIf,cAAc,CAACe,OAAO,EAAE;QAC1B,CAAC1B,EAAE,GAAGS,iBAAiB,CAACiB,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwC,IAAI,CAAC/B,iBAAiB,EAAEU,KAAK,CAAC;MACzG;MACA,IAAIoB,kBAAkB,IAAI,CAAC5B,cAAc,CAACe,OAAO,IAAIlB,UAAU,CAACkB,OAAO,EAAE;QACvElB,UAAU,CAACkB,OAAO,CAACP,KAAK,CAAC;MAC3B;MACAR,cAAc,CAACe,OAAO,GAAG,KAAK;IAChC,CAAC;IACD,IAAIe,cAAc,GAAG,SAAAA,CAAUtB,KAAK,EAAE;MACpC,OAAOmB,KAAK,CAACnB,KAAK,EAAE,IAAI,CAAC;IAC3B,CAAC;IACD,IAAI,CAAC1B,cAAc,EAAE;MACnBuB,aAAa,CAACC,gBAAgB,CAAC,WAAW,EAAEgB,OAAO,CAAC;MACpDjB,aAAa,CAACC,gBAAgB,CAAC,SAAS,EAAEwB,cAAc,CAAC;MACzDzB,aAAa,CAACC,gBAAgB,CAAC,YAAY,EAAEqB,KAAK,CAAC;MACnD,IAAIvB,gBAAgB,EAAEC,aAAa,CAACC,gBAAgB,CAAC,WAAW,EAAEkB,MAAM,CAAC;IAC3E,CAAC,MAAM;MACLnB,aAAa,CAACC,gBAAgB,CAAC,YAAY,EAAEgB,OAAO,CAAC;MACrDjB,aAAa,CAACC,gBAAgB,CAAC,UAAU,EAAEwB,cAAc,CAAC;MAC1D,IAAI1B,gBAAgB,EAAEC,aAAa,CAACC,gBAAgB,CAAC,WAAW,EAAEkB,MAAM,CAAC;IAC3E;IACA,OAAO,YAAY;MACjB,IAAIzB,QAAQ,CAACgB,OAAO,EAAE;QACpBU,YAAY,CAAC1B,QAAQ,CAACgB,OAAO,CAAC;QAC9Bf,cAAc,CAACe,OAAO,GAAG,KAAK;MAChC;MACA,IAAI,CAACjC,cAAc,EAAE;QACnBuB,aAAa,CAAC0B,mBAAmB,CAAC,WAAW,EAAET,OAAO,CAAC;QACvDjB,aAAa,CAAC0B,mBAAmB,CAAC,SAAS,EAAED,cAAc,CAAC;QAC5DzB,aAAa,CAAC0B,mBAAmB,CAAC,YAAY,EAAEJ,KAAK,CAAC;QACtD,IAAIvB,gBAAgB,EAAEC,aAAa,CAAC0B,mBAAmB,CAAC,WAAW,EAAEP,MAAM,CAAC;MAC9E,CAAC,MAAM;QACLnB,aAAa,CAAC0B,mBAAmB,CAAC,YAAY,EAAET,OAAO,CAAC;QACxDjB,aAAa,CAAC0B,mBAAmB,CAAC,UAAU,EAAED,cAAc,CAAC;QAC7D,IAAI1B,gBAAgB,EAAEC,aAAa,CAAC0B,mBAAmB,CAAC,WAAW,EAAEP,MAAM,CAAC;MAC9E;IACF,CAAC;EACH,CAAC,EAAE,EAAE,EAAEpC,MAAM,CAAC;AAChB;AACA,eAAeF,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}