/**
 * 表單驗證工具函數
 */

import { FIELD_VALIDATION, REQUIRED_FIELDS, ERROR_MESSAGES } from './constants';

/**
 * 驗證單個欄位
 * @param {string} fieldName - 欄位名稱
 * @param {string} value - 欄位值
 * @returns {Object} 驗證結果 { isValid: boolean, error: string }
 */
export const validateField = (fieldName, value) => {
  const validation = FIELD_VALIDATION[fieldName];
  
  if (!validation) {
    return { isValid: true, error: null };
  }

  // 檢查必填欄位
  if (validation.required && (!value || value.trim() === '')) {
    return {
      isValid: false,
      error: `${fieldName === 'name' ? '姓名' : fieldName}為必填欄位`
    };
  }

  // 如果值為空且非必填，則通過驗證
  if (!value || value.trim() === '') {
    return { isValid: true, error: null };
  }

  // 檢查最大長度
  if (validation.maxLength && value.length > validation.maxLength) {
    return {
      isValid: false,
      error: `長度不能超過${validation.maxLength}個字符`
    };
  }

  // 檢查格式
  if (validation.pattern && !validation.pattern.test(value)) {
    let errorMessage = '格式不正確';
    
    switch (fieldName) {
      case 'mobile_phone':
      case 'office_phone':
        errorMessage = '請輸入有效的電話號碼';
        break;
      case 'email':
        errorMessage = '請輸入有效的電子郵件地址';
        break;
      default:
        errorMessage = '格式不正確';
    }
    
    return {
      isValid: false,
      error: errorMessage
    };
  }

  return { isValid: true, error: null };
};

/**
 * 驗證整個表單
 * @param {Object} formData - 表單數據
 * @returns {Object} 驗證結果 { isValid: boolean, errors: Object }
 */
export const validateForm = (formData) => {
  const errors = {};
  let isValid = true;

  // 驗證所有欄位
  Object.keys(FIELD_VALIDATION).forEach(fieldName => {
    const value = formData[fieldName];
    const result = validateField(fieldName, value);
    
    if (!result.isValid) {
      errors[fieldName] = result.error;
      isValid = false;
    }
  });

  return { isValid, errors };
};

/**
 * 驗證必填欄位
 * @param {Object} formData - 表單數據
 * @returns {Object} 驗證結果
 */
export const validateRequiredFields = (formData) => {
  const errors = {};
  let isValid = true;

  REQUIRED_FIELDS.forEach(fieldName => {
    const value = formData[fieldName];
    if (!value || value.trim() === '') {
      errors[fieldName] = `${fieldName === 'name' ? '姓名' : fieldName}為必填欄位`;
      isValid = false;
    }
  });

  return { isValid, errors };
};

/**
 * 驗證電話號碼格式
 * @param {string} phone - 電話號碼
 * @returns {boolean} 是否有效
 */
export const isValidPhone = (phone) => {
  if (!phone) return true; // 非必填欄位
  const phonePattern = /^[\d\s\-\+\(\)]+$/;
  return phonePattern.test(phone);
};

/**
 * 驗證電子郵件格式
 * @param {string} email - 電子郵件
 * @returns {boolean} 是否有效
 */
export const isValidEmail = (email) => {
  if (!email) return true; // 非必填欄位
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};

/**
 * 清理表單數據（移除空值和多餘空格）
 * @param {Object} formData - 原始表單數據
 * @returns {Object} 清理後的表單數據
 */
export const cleanFormData = (formData) => {
  const cleanedData = {};
  
  Object.keys(formData).forEach(key => {
    const value = formData[key];
    if (value !== null && value !== undefined) {
      const trimmedValue = typeof value === 'string' ? value.trim() : value;
      if (trimmedValue !== '') {
        cleanedData[key] = trimmedValue;
      }
    }
  });
  
  return cleanedData;
};

/**
 * 檢查表單是否有變更
 * @param {Object} originalData - 原始數據
 * @param {Object} currentData - 當前數據
 * @returns {boolean} 是否有變更
 */
export const hasFormChanged = (originalData, currentData) => {
  const cleanedOriginal = cleanFormData(originalData);
  const cleanedCurrent = cleanFormData(currentData);
  
  const originalKeys = Object.keys(cleanedOriginal);
  const currentKeys = Object.keys(cleanedCurrent);
  
  // 檢查鍵的數量是否相同
  if (originalKeys.length !== currentKeys.length) {
    return true;
  }
  
  // 檢查每個鍵的值是否相同
  for (const key of originalKeys) {
    if (cleanedOriginal[key] !== cleanedCurrent[key]) {
      return true;
    }
  }
  
  return false;
};

/**
 * 獲取欄位的顯示名稱
 * @param {string} fieldName - 欄位名稱
 * @returns {string} 顯示名稱
 */
export const getFieldDisplayName = (fieldName) => {
  const displayNames = {
    name: '姓名',
    company_name: '公司名稱',
    position: '職位',
    mobile_phone: '手機',
    office_phone: '公司電話',
    email: 'Email',
    line_id: 'Line ID',
    notes: '備註',
    company_address_1: '公司地址一',
    company_address_2: '公司地址二'
  };
  
  return displayNames[fieldName] || fieldName;
};

export default {
  validateField,
  validateForm,
  validateRequiredFields,
  isValidPhone,
  isValidEmail,
  cleanFormData,
  hasFormChanged,
  getFieldDisplayName
};
