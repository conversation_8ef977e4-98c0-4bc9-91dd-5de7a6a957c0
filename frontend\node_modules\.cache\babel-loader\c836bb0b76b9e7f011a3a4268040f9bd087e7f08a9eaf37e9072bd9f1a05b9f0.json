{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\Camera\\\\ImagePreview.js\";\n/**\n * 圖片預覽組件\n */\n\nimport React from 'react';\nimport { Card, Button, Space, Image } from 'antd-mobile';\nimport { DeleteOutline, ReloadOutline, EyeOutline } from 'antd-mobile-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ImagePreview = ({\n  image,\n  side = 'front',\n  onRemove,\n  onRetake,\n  onPreview,\n  showActions = true,\n  style = {},\n  className = ''\n}) => {\n  if (!image || !image.preview) {\n    return null;\n  }\n  const sideText = side === 'front' ? '正面' : '反面';\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: `image-preview ${className}`,\n    style: {\n      marginBottom: '16px',\n      ...style\n    },\n    title: `${sideText}名片`,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          position: 'relative',\n          display: 'inline-block',\n          marginBottom: showActions ? '16px' : '0'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Image, {\n          src: image.preview,\n          alt: `${sideText}名片`,\n          style: {\n            maxWidth: '100%',\n            maxHeight: '200px',\n            borderRadius: '8px',\n            border: '1px solid #d9d9d9'\n          },\n          fit: \"contain\",\n          onClick: onPreview\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), image.parseStatus && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            position: 'absolute',\n            top: '8px',\n            right: '8px',\n            padding: '4px 8px',\n            borderRadius: '12px',\n            fontSize: '12px',\n            color: 'white',\n            backgroundColor: image.parseStatus === 'success' ? '#52c41a' : image.parseStatus === 'processing' ? '#1677ff' : image.parseStatus === 'error' ? '#ff4d4f' : '#8c8c8c'\n          },\n          children: image.parseStatus === 'success' ? '已識別' : image.parseStatus === 'processing' ? '識別中' : image.parseStatus === 'error' ? '識別失敗' : '待識別'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: showActions ? '16px' : '0'\n        },\n        children: [image.file && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#8c8c8c'\n          },\n          children: [image.file.name, \" (\", Math.round(image.file.size / 1024), \"KB)\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 83,\n          columnNumber: 13\n        }, this), image.ocrText && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '12px',\n            color: '#52c41a',\n            marginTop: '4px'\n          },\n          children: \"OCR\\u8B58\\u5225\\u5B8C\\u6210\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 89,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), showActions && /*#__PURE__*/_jsxDEV(Space, {\n        children: [onPreview && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          fill: \"outline\",\n          onClick: onPreview,\n          children: [/*#__PURE__*/_jsxDEV(EyeOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 110,\n            columnNumber: 17\n          }, this), \" \\u67E5\\u770B\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 105,\n          columnNumber: 15\n        }, this), onRetake && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          color: \"primary\",\n          fill: \"outline\",\n          onClick: () => onRetake(side),\n          children: [/*#__PURE__*/_jsxDEV(ReloadOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 121,\n            columnNumber: 17\n          }, this), \" \\u91CD\\u62CD\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 115,\n          columnNumber: 15\n        }, this), onRemove && /*#__PURE__*/_jsxDEV(Button, {\n          size: \"small\",\n          color: \"danger\",\n          fill: \"outline\",\n          onClick: () => onRemove(side),\n          children: [/*#__PURE__*/_jsxDEV(DeleteOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 132,\n            columnNumber: 17\n          }, this), \" \\u79FB\\u9664\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_c = ImagePreview;\nexport default ImagePreview;\nvar _c;\n$RefreshReg$(_c, \"ImagePreview\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON>", "Space", "Image", "DeleteOutline", "ReloadOutline", "EyeOutline", "jsxDEV", "_jsxDEV", "ImagePreview", "image", "side", "onRemove", "onRetake", "onPreview", "showActions", "style", "className", "preview", "sideText", "marginBottom", "title", "children", "textAlign", "position", "display", "src", "alt", "max<PERSON><PERSON><PERSON>", "maxHeight", "borderRadius", "border", "fit", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "parseStatus", "top", "right", "padding", "fontSize", "color", "backgroundColor", "file", "name", "Math", "round", "size", "ocrText", "marginTop", "fill", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Camera/ImagePreview.js"], "sourcesContent": ["/**\n * 圖片預覽組件\n */\n\nimport React from 'react';\nimport { Card, Button, Space, Image } from 'antd-mobile';\nimport { DeleteOutline, ReloadOutline, EyeOutline } from 'antd-mobile-icons';\n\nconst ImagePreview = ({\n  image,\n  side = 'front',\n  onRemove,\n  onRetake,\n  onPreview,\n  showActions = true,\n  style = {},\n  className = ''\n}) => {\n  if (!image || !image.preview) {\n    return null;\n  }\n\n  const sideText = side === 'front' ? '正面' : '反面';\n\n  return (\n    <Card \n      className={`image-preview ${className}`}\n      style={{\n        marginBottom: '16px',\n        ...style\n      }}\n      title={`${sideText}名片`}\n    >\n      <div style={{ textAlign: 'center' }}>\n        {/* 圖片預覽 */}\n        <div \n          style={{\n            position: 'relative',\n            display: 'inline-block',\n            marginBottom: showActions ? '16px' : '0'\n          }}\n        >\n          <Image\n            src={image.preview}\n            alt={`${sideText}名片`}\n            style={{\n              maxWidth: '100%',\n              maxHeight: '200px',\n              borderRadius: '8px',\n              border: '1px solid #d9d9d9'\n            }}\n            fit=\"contain\"\n            onClick={onPreview}\n          />\n          \n          {/* OCR狀態指示器 */}\n          {image.parseStatus && (\n            <div\n              style={{\n                position: 'absolute',\n                top: '8px',\n                right: '8px',\n                padding: '4px 8px',\n                borderRadius: '12px',\n                fontSize: '12px',\n                color: 'white',\n                backgroundColor: \n                  image.parseStatus === 'success' ? '#52c41a' :\n                  image.parseStatus === 'processing' ? '#1677ff' :\n                  image.parseStatus === 'error' ? '#ff4d4f' : '#8c8c8c'\n              }}\n            >\n              {image.parseStatus === 'success' ? '已識別' :\n               image.parseStatus === 'processing' ? '識別中' :\n               image.parseStatus === 'error' ? '識別失敗' : '待識別'}\n            </div>\n          )}\n        </div>\n\n        {/* 圖片信息 */}\n        <div style={{ marginBottom: showActions ? '16px' : '0' }}>\n          {image.file && (\n            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>\n              {image.file.name} ({Math.round(image.file.size / 1024)}KB)\n            </div>\n          )}\n          \n          {image.ocrText && (\n            <div \n              style={{ \n                fontSize: '12px', \n                color: '#52c41a',\n                marginTop: '4px'\n              }}\n            >\n              OCR識別完成\n            </div>\n          )}\n        </div>\n\n        {/* 操作按鈕 */}\n        {showActions && (\n          <Space>\n            {onPreview && (\n              <Button\n                size=\"small\"\n                fill=\"outline\"\n                onClick={onPreview}\n              >\n                <EyeOutline /> 查看\n              </Button>\n            )}\n            \n            {onRetake && (\n              <Button\n                size=\"small\"\n                color=\"primary\"\n                fill=\"outline\"\n                onClick={() => onRetake(side)}\n              >\n                <ReloadOutline /> 重拍\n              </Button>\n            )}\n            \n            {onRemove && (\n              <Button\n                size=\"small\"\n                color=\"danger\"\n                fill=\"outline\"\n                onClick={() => onRemove(side)}\n              >\n                <DeleteOutline /> 移除\n              </Button>\n            )}\n          </Space>\n        )}\n      </div>\n    </Card>\n  );\n};\n\nexport default ImagePreview;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,aAAa;AACxD,SAASC,aAAa,EAAEC,aAAa,EAAEC,UAAU,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7E,MAAMC,YAAY,GAAGA,CAAC;EACpBC,KAAK;EACLC,IAAI,GAAG,OAAO;EACdC,QAAQ;EACRC,QAAQ;EACRC,SAAS;EACTC,WAAW,GAAG,IAAI;EAClBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,IAAI,CAACP,KAAK,IAAI,CAACA,KAAK,CAACQ,OAAO,EAAE;IAC5B,OAAO,IAAI;EACb;EAEA,MAAMC,QAAQ,GAAGR,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;EAE/C,oBACEH,OAAA,CAACR,IAAI;IACHiB,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IACxCD,KAAK,EAAE;MACLI,YAAY,EAAE,MAAM;MACpB,GAAGJ;IACL,CAAE;IACFK,KAAK,EAAE,GAAGF,QAAQ,IAAK;IAAAG,QAAA,eAEvBd,OAAA;MAAKQ,KAAK,EAAE;QAAEO,SAAS,EAAE;MAAS,CAAE;MAAAD,QAAA,gBAElCd,OAAA;QACEQ,KAAK,EAAE;UACLQ,QAAQ,EAAE,UAAU;UACpBC,OAAO,EAAE,cAAc;UACvBL,YAAY,EAAEL,WAAW,GAAG,MAAM,GAAG;QACvC,CAAE;QAAAO,QAAA,gBAEFd,OAAA,CAACL,KAAK;UACJuB,GAAG,EAAEhB,KAAK,CAACQ,OAAQ;UACnBS,GAAG,EAAE,GAAGR,QAAQ,IAAK;UACrBH,KAAK,EAAE;YACLY,QAAQ,EAAE,MAAM;YAChBC,SAAS,EAAE,OAAO;YAClBC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UACFC,GAAG,EAAC,SAAS;UACbC,OAAO,EAAEnB;QAAU;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpB,CAAC,EAGD3B,KAAK,CAAC4B,WAAW,iBAChB9B,OAAA;UACEQ,KAAK,EAAE;YACLQ,QAAQ,EAAE,UAAU;YACpBe,GAAG,EAAE,KAAK;YACVC,KAAK,EAAE,KAAK;YACZC,OAAO,EAAE,SAAS;YAClBX,YAAY,EAAE,MAAM;YACpBY,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,OAAO;YACdC,eAAe,EACblC,KAAK,CAAC4B,WAAW,KAAK,SAAS,GAAG,SAAS,GAC3C5B,KAAK,CAAC4B,WAAW,KAAK,YAAY,GAAG,SAAS,GAC9C5B,KAAK,CAAC4B,WAAW,KAAK,OAAO,GAAG,SAAS,GAAG;UAChD,CAAE;UAAAhB,QAAA,EAEDZ,KAAK,CAAC4B,WAAW,KAAK,SAAS,GAAG,KAAK,GACvC5B,KAAK,CAAC4B,WAAW,KAAK,YAAY,GAAG,KAAK,GAC1C5B,KAAK,CAAC4B,WAAW,KAAK,OAAO,GAAG,MAAM,GAAG;QAAK;UAAAJ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN7B,OAAA;QAAKQ,KAAK,EAAE;UAAEI,YAAY,EAAEL,WAAW,GAAG,MAAM,GAAG;QAAI,CAAE;QAAAO,QAAA,GACtDZ,KAAK,CAACmC,IAAI,iBACTrC,OAAA;UAAKQ,KAAK,EAAE;YAAE0B,QAAQ,EAAE,MAAM;YAAEC,KAAK,EAAE;UAAU,CAAE;UAAArB,QAAA,GAChDZ,KAAK,CAACmC,IAAI,CAACC,IAAI,EAAC,IAAE,EAACC,IAAI,CAACC,KAAK,CAACtC,KAAK,CAACmC,IAAI,CAACI,IAAI,GAAG,IAAI,CAAC,EAAC,KACzD;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN,EAEA3B,KAAK,CAACwC,OAAO,iBACZ1C,OAAA;UACEQ,KAAK,EAAE;YACL0B,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,SAAS;YAChBQ,SAAS,EAAE;UACb,CAAE;UAAA7B,QAAA,EACH;QAED;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLtB,WAAW,iBACVP,OAAA,CAACN,KAAK;QAAAoB,QAAA,GACHR,SAAS,iBACRN,OAAA,CAACP,MAAM;UACLgD,IAAI,EAAC,OAAO;UACZG,IAAI,EAAC,SAAS;UACdnB,OAAO,EAAEnB,SAAU;UAAAQ,QAAA,gBAEnBd,OAAA,CAACF,UAAU;YAAA4B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBAChB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAxB,QAAQ,iBACPL,OAAA,CAACP,MAAM;UACLgD,IAAI,EAAC,OAAO;UACZN,KAAK,EAAC,SAAS;UACfS,IAAI,EAAC,SAAS;UACdnB,OAAO,EAAEA,CAAA,KAAMpB,QAAQ,CAACF,IAAI,CAAE;UAAAW,QAAA,gBAE9Bd,OAAA,CAACH,aAAa;YAAA6B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT,EAEAzB,QAAQ,iBACPJ,OAAA,CAACP,MAAM;UACLgD,IAAI,EAAC,OAAO;UACZN,KAAK,EAAC,QAAQ;UACdS,IAAI,EAAC,SAAS;UACdnB,OAAO,EAAEA,CAAA,KAAMrB,QAAQ,CAACD,IAAI,CAAE;UAAAW,QAAA,gBAE9Bd,OAAA,CAACJ,aAAa;YAAA8B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,iBACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CACT;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CACR;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACgB,EAAA,GAnII5C,YAAY;AAqIlB,eAAeA,YAAY;AAAC,IAAA4C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}