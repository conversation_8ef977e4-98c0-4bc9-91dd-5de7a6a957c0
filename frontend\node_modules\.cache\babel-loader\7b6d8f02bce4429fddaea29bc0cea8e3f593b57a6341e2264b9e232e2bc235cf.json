{"ast": null, "code": "/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\nmodule.exports = freeGlobal;", "map": {"version": 3, "names": ["freeGlobal", "global", "Object", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/lodash/_freeGlobal.js"], "sourcesContent": ["/** Detect free variable `global` from Node.js. */\nvar freeGlobal = typeof global == 'object' && global && global.Object === Object && global;\n\nmodule.exports = freeGlobal;\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAG,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM,IAAIA,MAAM,CAACC,MAAM,KAAKA,MAAM,IAAID,MAAM;AAE1FE,MAAM,CAACC,OAAO,GAAGJ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}