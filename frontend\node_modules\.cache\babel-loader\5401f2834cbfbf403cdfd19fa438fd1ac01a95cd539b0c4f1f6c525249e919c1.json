{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useCreation from '../useCreation';\nvar useResetState = function (initialState) {\n  var initialStateRef = useRef(initialState);\n  var initialStateMemo = useCreation(function () {\n    return isFunction(initialStateRef.current) ? initialStateRef.current() : initialStateRef.current;\n  }, []);\n  var _a = __read(useState(initialStateMemo), 2),\n    state = _a[0],\n    setState = _a[1];\n  var resetState = useMemoizedFn(function () {\n    setState(initialStateMemo);\n  });\n  return [state, setState, resetState];\n};\nexport default useResetState;", "map": {"version": 3, "names": ["__read", "useRef", "useState", "isFunction", "useMemoizedFn", "useCreation", "useResetState", "initialState", "initialStateRef", "initialStateMemo", "current", "_a", "state", "setState", "resetState"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useResetState/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useCreation from '../useCreation';\nvar useResetState = function (initialState) {\n  var initialStateRef = useRef(initialState);\n  var initialStateMemo = useCreation(function () {\n    return isFunction(initialStateRef.current) ? initialStateRef.current() : initialStateRef.current;\n  }, []);\n  var _a = __read(useState(initialStateMemo), 2),\n    state = _a[0],\n    setState = _a[1];\n  var resetState = useMemoizedFn(function () {\n    setState(initialStateMemo);\n  });\n  return [state, setState, resetState];\n};\nexport default useResetState;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,WAAW,MAAM,gBAAgB;AACxC,IAAIC,aAAa,GAAG,SAAAA,CAAUC,YAAY,EAAE;EAC1C,IAAIC,eAAe,GAAGP,MAAM,CAACM,YAAY,CAAC;EAC1C,IAAIE,gBAAgB,GAAGJ,WAAW,CAAC,YAAY;IAC7C,OAAOF,UAAU,CAACK,eAAe,CAACE,OAAO,CAAC,GAAGF,eAAe,CAACE,OAAO,CAAC,CAAC,GAAGF,eAAe,CAACE,OAAO;EAClG,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,EAAE,GAAGX,MAAM,CAACE,QAAQ,CAACO,gBAAgB,CAAC,EAAE,CAAC,CAAC;IAC5CG,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,UAAU,GAAGV,aAAa,CAAC,YAAY;IACzCS,QAAQ,CAACJ,gBAAgB,CAAC;EAC5B,CAAC,CAAC;EACF,OAAO,CAACG,KAAK,EAAEC,QAAQ,EAAEC,UAAU,CAAC;AACtC,CAAC;AACD,eAAeR,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}