{"ast": null, "code": "import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport getValue from \"rc-util/es/utils/get\";\nimport setValue from \"rc-util/es/utils/set\";\nimport { toArray } from \"./typeUtil\";\nexport { getValue, setValue };\n\n/**\n * Convert name to internal supported format.\n * This function should keep since we still thinking if need support like `a.b.c` format.\n * 'a' => ['a']\n * 123 => [123]\n * ['a', 123] => ['a', 123]\n */\nexport function getNamePath(path) {\n  return toArray(path);\n}\nexport function cloneByNamePathList(store, namePathList) {\n  var newStore = {};\n  namePathList.forEach(function (namePath) {\n    var value = getValue(store, namePath);\n    newStore = setValue(newStore, namePath, value);\n  });\n  return newStore;\n}\n\n/**\n * Check if `namePathList` includes `namePath`.\n * @param namePathList A list of `InternalNamePath[]`\n * @param namePath Compare `InternalNamePath`\n * @param partialMatch True will make `[a, b]` match `[a, b, c]`\n */\nexport function containsNamePath(namePathList, namePath) {\n  var partialMatch = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  return namePathList && namePathList.some(function (path) {\n    return matchNamePath(namePath, path, partialMatch);\n  });\n}\n\n/**\n * Check if `namePath` is super set or equal of `subNamePath`.\n * @param namePath A list of `InternalNamePath[]`\n * @param subNamePath Compare `InternalNamePath`\n * @param partialMatch True will make `[a, b]` match `[a, b, c]`\n */\nexport function matchNamePath(namePath, subNamePath) {\n  var partialMatch = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (!namePath || !subNamePath) {\n    return false;\n  }\n  if (!partialMatch && namePath.length !== subNamePath.length) {\n    return false;\n  }\n  return subNamePath.every(function (nameUnit, i) {\n    return namePath[i] === nameUnit;\n  });\n}\n\n// Like `shallowEqual`, but we not check the data which may cause re-render\n\nexport function isSimilar(source, target) {\n  if (source === target) {\n    return true;\n  }\n  if (!source && target || source && !target) {\n    return false;\n  }\n  if (!source || !target || _typeof(source) !== 'object' || _typeof(target) !== 'object') {\n    return false;\n  }\n  var sourceKeys = Object.keys(source);\n  var targetKeys = Object.keys(target);\n  var keys = new Set([].concat(sourceKeys, targetKeys));\n  return _toConsumableArray(keys).every(function (key) {\n    var sourceValue = source[key];\n    var targetValue = target[key];\n    if (typeof sourceValue === 'function' && typeof targetValue === 'function') {\n      return true;\n    }\n    return sourceValue === targetValue;\n  });\n}\nexport function defaultGetValueFromEvent(valuePropName) {\n  var event = arguments.length <= 1 ? undefined : arguments[1];\n  if (event && event.target && _typeof(event.target) === 'object' && valuePropName in event.target) {\n    return event.target[valuePropName];\n  }\n  return event;\n}\n\n/**\n * Moves an array item from one position in an array to another.\n *\n * Note: This is a pure function so a new array will be returned, instead\n * of altering the array argument.\n *\n * @param array         Array in which to move an item.         (required)\n * @param moveIndex     The index of the item to move.          (required)\n * @param toIndex       The index to move item at moveIndex to. (required)\n */\nexport function move(array, moveIndex, toIndex) {\n  var length = array.length;\n  if (moveIndex < 0 || moveIndex >= length || toIndex < 0 || toIndex >= length) {\n    return array;\n  }\n  var item = array[moveIndex];\n  var diff = moveIndex - toIndex;\n  if (diff > 0) {\n    // move left\n    return [].concat(_toConsumableArray(array.slice(0, toIndex)), [item], _toConsumableArray(array.slice(toIndex, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, length)));\n  }\n  if (diff < 0) {\n    // move right\n    return [].concat(_toConsumableArray(array.slice(0, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, toIndex + 1)), [item], _toConsumableArray(array.slice(toIndex + 1, length)));\n  }\n  return array;\n}", "map": {"version": 3, "names": ["_toConsumableArray", "_typeof", "getValue", "setValue", "toArray", "getNamePath", "path", "cloneByNamePathList", "store", "namePathList", "newStore", "for<PERSON>ach", "namePath", "value", "containsNamePath", "partialMatch", "arguments", "length", "undefined", "some", "matchNamePath", "subNamePath", "every", "nameUnit", "i", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source", "target", "sourceKeys", "Object", "keys", "targetKeys", "Set", "concat", "key", "sourceValue", "targetValue", "defaultGetValueFromEvent", "valuePropName", "event", "move", "array", "moveIndex", "toIndex", "item", "diff", "slice"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-field-form/es/utils/valueUtil.js"], "sourcesContent": ["import _toConsumableArray from \"@babel/runtime/helpers/esm/toConsumableArray\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport getValue from \"rc-util/es/utils/get\";\nimport setValue from \"rc-util/es/utils/set\";\nimport { toArray } from \"./typeUtil\";\nexport { getValue, setValue };\n\n/**\n * Convert name to internal supported format.\n * This function should keep since we still thinking if need support like `a.b.c` format.\n * 'a' => ['a']\n * 123 => [123]\n * ['a', 123] => ['a', 123]\n */\nexport function getNamePath(path) {\n  return toArray(path);\n}\nexport function cloneByNamePathList(store, namePathList) {\n  var newStore = {};\n  namePathList.forEach(function (namePath) {\n    var value = getValue(store, namePath);\n    newStore = setValue(newStore, namePath, value);\n  });\n  return newStore;\n}\n\n/**\n * Check if `namePathList` includes `namePath`.\n * @param namePathList A list of `InternalNamePath[]`\n * @param namePath Compare `InternalNamePath`\n * @param partialMatch True will make `[a, b]` match `[a, b, c]`\n */\nexport function containsNamePath(namePathList, namePath) {\n  var partialMatch = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  return namePathList && namePathList.some(function (path) {\n    return matchNamePath(namePath, path, partialMatch);\n  });\n}\n\n/**\n * Check if `namePath` is super set or equal of `subNamePath`.\n * @param namePath A list of `InternalNamePath[]`\n * @param subNamePath Compare `InternalNamePath`\n * @param partialMatch True will make `[a, b]` match `[a, b, c]`\n */\nexport function matchNamePath(namePath, subNamePath) {\n  var partialMatch = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n  if (!namePath || !subNamePath) {\n    return false;\n  }\n  if (!partialMatch && namePath.length !== subNamePath.length) {\n    return false;\n  }\n  return subNamePath.every(function (nameUnit, i) {\n    return namePath[i] === nameUnit;\n  });\n}\n\n// Like `shallowEqual`, but we not check the data which may cause re-render\n\nexport function isSimilar(source, target) {\n  if (source === target) {\n    return true;\n  }\n  if (!source && target || source && !target) {\n    return false;\n  }\n  if (!source || !target || _typeof(source) !== 'object' || _typeof(target) !== 'object') {\n    return false;\n  }\n  var sourceKeys = Object.keys(source);\n  var targetKeys = Object.keys(target);\n  var keys = new Set([].concat(sourceKeys, targetKeys));\n  return _toConsumableArray(keys).every(function (key) {\n    var sourceValue = source[key];\n    var targetValue = target[key];\n    if (typeof sourceValue === 'function' && typeof targetValue === 'function') {\n      return true;\n    }\n    return sourceValue === targetValue;\n  });\n}\nexport function defaultGetValueFromEvent(valuePropName) {\n  var event = arguments.length <= 1 ? undefined : arguments[1];\n  if (event && event.target && _typeof(event.target) === 'object' && valuePropName in event.target) {\n    return event.target[valuePropName];\n  }\n  return event;\n}\n\n/**\n * Moves an array item from one position in an array to another.\n *\n * Note: This is a pure function so a new array will be returned, instead\n * of altering the array argument.\n *\n * @param array         Array in which to move an item.         (required)\n * @param moveIndex     The index of the item to move.          (required)\n * @param toIndex       The index to move item at moveIndex to. (required)\n */\nexport function move(array, moveIndex, toIndex) {\n  var length = array.length;\n  if (moveIndex < 0 || moveIndex >= length || toIndex < 0 || toIndex >= length) {\n    return array;\n  }\n  var item = array[moveIndex];\n  var diff = moveIndex - toIndex;\n  if (diff > 0) {\n    // move left\n    return [].concat(_toConsumableArray(array.slice(0, toIndex)), [item], _toConsumableArray(array.slice(toIndex, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, length)));\n  }\n  if (diff < 0) {\n    // move right\n    return [].concat(_toConsumableArray(array.slice(0, moveIndex)), _toConsumableArray(array.slice(moveIndex + 1, toIndex + 1)), [item], _toConsumableArray(array.slice(toIndex + 1, length)));\n  }\n  return array;\n}"], "mappings": "AAAA,OAAOA,kBAAkB,MAAM,8CAA8C;AAC7E,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,OAAO,QAAQ,YAAY;AACpC,SAASF,QAAQ,EAAEC,QAAQ;;AAE3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAOF,OAAO,CAACE,IAAI,CAAC;AACtB;AACA,OAAO,SAASC,mBAAmBA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACvD,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjBD,YAAY,CAACE,OAAO,CAAC,UAAUC,QAAQ,EAAE;IACvC,IAAIC,KAAK,GAAGX,QAAQ,CAACM,KAAK,EAAEI,QAAQ,CAAC;IACrCF,QAAQ,GAAGP,QAAQ,CAACO,QAAQ,EAAEE,QAAQ,EAAEC,KAAK,CAAC;EAChD,CAAC,CAAC;EACF,OAAOH,QAAQ;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASI,gBAAgBA,CAACL,YAAY,EAAEG,QAAQ,EAAE;EACvD,IAAIG,YAAY,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC5F,OAAOP,YAAY,IAAIA,YAAY,CAACU,IAAI,CAAC,UAAUb,IAAI,EAAE;IACvD,OAAOc,aAAa,CAACR,QAAQ,EAAEN,IAAI,EAAES,YAAY,CAAC;EACpD,CAAC,CAAC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,aAAaA,CAACR,QAAQ,EAAES,WAAW,EAAE;EACnD,IAAIN,YAAY,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK;EAC5F,IAAI,CAACJ,QAAQ,IAAI,CAACS,WAAW,EAAE;IAC7B,OAAO,KAAK;EACd;EACA,IAAI,CAACN,YAAY,IAAIH,QAAQ,CAACK,MAAM,KAAKI,WAAW,CAACJ,MAAM,EAAE;IAC3D,OAAO,KAAK;EACd;EACA,OAAOI,WAAW,CAACC,KAAK,CAAC,UAAUC,QAAQ,EAAEC,CAAC,EAAE;IAC9C,OAAOZ,QAAQ,CAACY,CAAC,CAAC,KAAKD,QAAQ;EACjC,CAAC,CAAC;AACJ;;AAEA;;AAEA,OAAO,SAASE,SAASA,CAACC,MAAM,EAAEC,MAAM,EAAE;EACxC,IAAID,MAAM,KAAKC,MAAM,EAAE;IACrB,OAAO,IAAI;EACb;EACA,IAAI,CAACD,MAAM,IAAIC,MAAM,IAAID,MAAM,IAAI,CAACC,MAAM,EAAE;IAC1C,OAAO,KAAK;EACd;EACA,IAAI,CAACD,MAAM,IAAI,CAACC,MAAM,IAAI1B,OAAO,CAACyB,MAAM,CAAC,KAAK,QAAQ,IAAIzB,OAAO,CAAC0B,MAAM,CAAC,KAAK,QAAQ,EAAE;IACtF,OAAO,KAAK;EACd;EACA,IAAIC,UAAU,GAAGC,MAAM,CAACC,IAAI,CAACJ,MAAM,CAAC;EACpC,IAAIK,UAAU,GAAGF,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC;EACpC,IAAIG,IAAI,GAAG,IAAIE,GAAG,CAAC,EAAE,CAACC,MAAM,CAACL,UAAU,EAAEG,UAAU,CAAC,CAAC;EACrD,OAAO/B,kBAAkB,CAAC8B,IAAI,CAAC,CAACR,KAAK,CAAC,UAAUY,GAAG,EAAE;IACnD,IAAIC,WAAW,GAAGT,MAAM,CAACQ,GAAG,CAAC;IAC7B,IAAIE,WAAW,GAAGT,MAAM,CAACO,GAAG,CAAC;IAC7B,IAAI,OAAOC,WAAW,KAAK,UAAU,IAAI,OAAOC,WAAW,KAAK,UAAU,EAAE;MAC1E,OAAO,IAAI;IACb;IACA,OAAOD,WAAW,KAAKC,WAAW;EACpC,CAAC,CAAC;AACJ;AACA,OAAO,SAASC,wBAAwBA,CAACC,aAAa,EAAE;EACtD,IAAIC,KAAK,GAAGvB,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGC,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC;EAC5D,IAAIuB,KAAK,IAAIA,KAAK,CAACZ,MAAM,IAAI1B,OAAO,CAACsC,KAAK,CAACZ,MAAM,CAAC,KAAK,QAAQ,IAAIW,aAAa,IAAIC,KAAK,CAACZ,MAAM,EAAE;IAChG,OAAOY,KAAK,CAACZ,MAAM,CAACW,aAAa,CAAC;EACpC;EACA,OAAOC,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,IAAIA,CAACC,KAAK,EAAEC,SAAS,EAAEC,OAAO,EAAE;EAC9C,IAAI1B,MAAM,GAAGwB,KAAK,CAACxB,MAAM;EACzB,IAAIyB,SAAS,GAAG,CAAC,IAAIA,SAAS,IAAIzB,MAAM,IAAI0B,OAAO,GAAG,CAAC,IAAIA,OAAO,IAAI1B,MAAM,EAAE;IAC5E,OAAOwB,KAAK;EACd;EACA,IAAIG,IAAI,GAAGH,KAAK,CAACC,SAAS,CAAC;EAC3B,IAAIG,IAAI,GAAGH,SAAS,GAAGC,OAAO;EAC9B,IAAIE,IAAI,GAAG,CAAC,EAAE;IACZ;IACA,OAAO,EAAE,CAACZ,MAAM,CAACjC,kBAAkB,CAACyC,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEH,OAAO,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE5C,kBAAkB,CAACyC,KAAK,CAACK,KAAK,CAACH,OAAO,EAAED,SAAS,CAAC,CAAC,EAAE1C,kBAAkB,CAACyC,KAAK,CAACK,KAAK,CAACJ,SAAS,GAAG,CAAC,EAAEzB,MAAM,CAAC,CAAC,CAAC;EACpL;EACA,IAAI4B,IAAI,GAAG,CAAC,EAAE;IACZ;IACA,OAAO,EAAE,CAACZ,MAAM,CAACjC,kBAAkB,CAACyC,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEJ,SAAS,CAAC,CAAC,EAAE1C,kBAAkB,CAACyC,KAAK,CAACK,KAAK,CAACJ,SAAS,GAAG,CAAC,EAAEC,OAAO,GAAG,CAAC,CAAC,CAAC,EAAE,CAACC,IAAI,CAAC,EAAE5C,kBAAkB,CAACyC,KAAK,CAACK,KAAK,CAACH,OAAO,GAAG,CAAC,EAAE1B,MAAM,CAAC,CAAC,CAAC;EAC5L;EACA,OAAOwB,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}