{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useMemo, useState, useRef } from 'react';\nimport useEventListener from '../useEventListener';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useSize from '../useSize';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isNumber } from '../utils';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useVirtualList = function (list, options) {\n  var containerTarget = options.containerTarget,\n    wrapperTarget = options.wrapperTarget,\n    itemHeight = options.itemHeight,\n    _a = options.overscan,\n    overscan = _a === void 0 ? 5 : _a;\n  var itemHeightRef = useLatest(itemHeight);\n  var size = useSize(containerTarget);\n  var scrollTriggerByScrollToFunc = useRef(false);\n  var _b = __read(useState([]), 2),\n    targetList = _b[0],\n    setTargetList = _b[1];\n  var _c = __read(useState({}), 2),\n    wrapperStyle = _c[0],\n    setWrapperStyle = _c[1];\n  var getVisibleCount = function (containerHeight, fromIndex) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.ceil(containerHeight / itemHeightRef.current);\n    }\n    var sum = 0;\n    var endIndex = 0;\n    for (var i = fromIndex; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      endIndex = i;\n      if (sum >= containerHeight) {\n        break;\n      }\n    }\n    return endIndex - fromIndex;\n  };\n  var getOffset = function (scrollTop) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.floor(scrollTop / itemHeightRef.current);\n    }\n    var sum = 0;\n    var offset = 0;\n    for (var i = 0; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      if (sum >= scrollTop) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n  // 获取上部高度\n  var getDistanceTop = function (index) {\n    if (isNumber(itemHeightRef.current)) {\n      var height_1 = index * itemHeightRef.current;\n      return height_1;\n    }\n    var height = list.slice(0, index).reduce(function (sum, _, i) {\n      return sum + itemHeightRef.current(i, list[i]);\n    }, 0);\n    return height;\n  };\n  var totalHeight = useMemo(function () {\n    if (isNumber(itemHeightRef.current)) {\n      return list.length * itemHeightRef.current;\n    }\n    return list.reduce(function (sum, _, index) {\n      return sum + itemHeightRef.current(index, list[index]);\n    }, 0);\n  }, [list]);\n  var calculateRange = function () {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      var scrollTop = container.scrollTop,\n        clientHeight = container.clientHeight;\n      var offset = getOffset(scrollTop);\n      var visibleCount = getVisibleCount(clientHeight, offset);\n      var start_1 = Math.max(0, offset - overscan);\n      var end = Math.min(list.length, offset + visibleCount + overscan);\n      var offsetTop = getDistanceTop(start_1);\n      setWrapperStyle({\n        height: totalHeight - offsetTop + 'px',\n        marginTop: offsetTop + 'px'\n      });\n      setTargetList(list.slice(start_1, end).map(function (ele, index) {\n        return {\n          data: ele,\n          index: index + start_1\n        };\n      }));\n    }\n  };\n  useUpdateEffect(function () {\n    var wrapper = getTargetElement(wrapperTarget);\n    if (wrapper) {\n      Object.keys(wrapperStyle).forEach(function (key) {\n        return wrapper.style[key] = wrapperStyle[key];\n      });\n    }\n  }, [wrapperStyle]);\n  useEffect(function () {\n    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {\n      return;\n    }\n    calculateRange();\n  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);\n  useEventListener('scroll', function (e) {\n    if (scrollTriggerByScrollToFunc.current) {\n      scrollTriggerByScrollToFunc.current = false;\n      return;\n    }\n    e.preventDefault();\n    calculateRange();\n  }, {\n    target: containerTarget\n  });\n  var scrollTo = function (index) {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      scrollTriggerByScrollToFunc.current = true;\n      container.scrollTop = getDistanceTop(index);\n      calculateRange();\n    }\n  };\n  return [targetList, useMemoizedFn(scrollTo)];\n};\nexport default useVirtualList;", "map": {"version": 3, "names": ["__read", "useEffect", "useMemo", "useState", "useRef", "useEventListener", "useLatest", "useMemoizedFn", "useSize", "getTargetElement", "isNumber", "useUpdateEffect", "useVirtualList", "list", "options", "containerTarget", "wrapperTarget", "itemHeight", "_a", "overscan", "itemHeightRef", "size", "scrollTriggerByScrollToFunc", "_b", "targetList", "setTargetList", "_c", "wrapperStyle", "setWrapperStyle", "getVisibleCount", "containerHeight", "fromIndex", "current", "Math", "ceil", "sum", "endIndex", "i", "length", "height", "getOffset", "scrollTop", "floor", "offset", "getDistanceTop", "index", "height_1", "slice", "reduce", "_", "totalHeight", "calculateRange", "container", "clientHeight", "visibleCount", "start_1", "max", "end", "min", "offsetTop", "marginTop", "map", "ele", "data", "wrapper", "Object", "keys", "for<PERSON>ach", "key", "style", "width", "e", "preventDefault", "target", "scrollTo"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useVirtualList/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useMemo, useState, useRef } from 'react';\nimport useEventListener from '../useEventListener';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useSize from '../useSize';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isNumber } from '../utils';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useVirtualList = function (list, options) {\n  var containerTarget = options.containerTarget,\n    wrapperTarget = options.wrapperTarget,\n    itemHeight = options.itemHeight,\n    _a = options.overscan,\n    overscan = _a === void 0 ? 5 : _a;\n  var itemHeightRef = useLatest(itemHeight);\n  var size = useSize(containerTarget);\n  var scrollTriggerByScrollToFunc = useRef(false);\n  var _b = __read(useState([]), 2),\n    targetList = _b[0],\n    setTargetList = _b[1];\n  var _c = __read(useState({}), 2),\n    wrapperStyle = _c[0],\n    setWrapperStyle = _c[1];\n  var getVisibleCount = function (containerHeight, fromIndex) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.ceil(containerHeight / itemHeightRef.current);\n    }\n    var sum = 0;\n    var endIndex = 0;\n    for (var i = fromIndex; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      endIndex = i;\n      if (sum >= containerHeight) {\n        break;\n      }\n    }\n    return endIndex - fromIndex;\n  };\n  var getOffset = function (scrollTop) {\n    if (isNumber(itemHeightRef.current)) {\n      return Math.floor(scrollTop / itemHeightRef.current);\n    }\n    var sum = 0;\n    var offset = 0;\n    for (var i = 0; i < list.length; i++) {\n      var height = itemHeightRef.current(i, list[i]);\n      sum += height;\n      if (sum >= scrollTop) {\n        offset = i;\n        break;\n      }\n    }\n    return offset + 1;\n  };\n  // 获取上部高度\n  var getDistanceTop = function (index) {\n    if (isNumber(itemHeightRef.current)) {\n      var height_1 = index * itemHeightRef.current;\n      return height_1;\n    }\n    var height = list.slice(0, index).reduce(function (sum, _, i) {\n      return sum + itemHeightRef.current(i, list[i]);\n    }, 0);\n    return height;\n  };\n  var totalHeight = useMemo(function () {\n    if (isNumber(itemHeightRef.current)) {\n      return list.length * itemHeightRef.current;\n    }\n    return list.reduce(function (sum, _, index) {\n      return sum + itemHeightRef.current(index, list[index]);\n    }, 0);\n  }, [list]);\n  var calculateRange = function () {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      var scrollTop = container.scrollTop,\n        clientHeight = container.clientHeight;\n      var offset = getOffset(scrollTop);\n      var visibleCount = getVisibleCount(clientHeight, offset);\n      var start_1 = Math.max(0, offset - overscan);\n      var end = Math.min(list.length, offset + visibleCount + overscan);\n      var offsetTop = getDistanceTop(start_1);\n      setWrapperStyle({\n        height: totalHeight - offsetTop + 'px',\n        marginTop: offsetTop + 'px'\n      });\n      setTargetList(list.slice(start_1, end).map(function (ele, index) {\n        return {\n          data: ele,\n          index: index + start_1\n        };\n      }));\n    }\n  };\n  useUpdateEffect(function () {\n    var wrapper = getTargetElement(wrapperTarget);\n    if (wrapper) {\n      Object.keys(wrapperStyle).forEach(function (key) {\n        return wrapper.style[key] = wrapperStyle[key];\n      });\n    }\n  }, [wrapperStyle]);\n  useEffect(function () {\n    if (!(size === null || size === void 0 ? void 0 : size.width) || !(size === null || size === void 0 ? void 0 : size.height)) {\n      return;\n    }\n    calculateRange();\n  }, [size === null || size === void 0 ? void 0 : size.width, size === null || size === void 0 ? void 0 : size.height, list]);\n  useEventListener('scroll', function (e) {\n    if (scrollTriggerByScrollToFunc.current) {\n      scrollTriggerByScrollToFunc.current = false;\n      return;\n    }\n    e.preventDefault();\n    calculateRange();\n  }, {\n    target: containerTarget\n  });\n  var scrollTo = function (index) {\n    var container = getTargetElement(containerTarget);\n    if (container) {\n      scrollTriggerByScrollToFunc.current = true;\n      container.scrollTop = getDistanceTop(index);\n      calculateRange();\n    }\n  };\n  return [targetList, useMemoizedFn(scrollTo)];\n};\nexport default useVirtualList;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC5D,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,OAAO,MAAM,YAAY;AAChC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,QAAQ,QAAQ,UAAU;AACnC,OAAOC,eAAe,MAAM,oBAAoB;AAChD,IAAIC,cAAc,GAAG,SAAAA,CAAUC,IAAI,EAAEC,OAAO,EAAE;EAC5C,IAAIC,eAAe,GAAGD,OAAO,CAACC,eAAe;IAC3CC,aAAa,GAAGF,OAAO,CAACE,aAAa;IACrCC,UAAU,GAAGH,OAAO,CAACG,UAAU;IAC/BC,EAAE,GAAGJ,OAAO,CAACK,QAAQ;IACrBA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;EACnC,IAAIE,aAAa,GAAGd,SAAS,CAACW,UAAU,CAAC;EACzC,IAAII,IAAI,GAAGb,OAAO,CAACO,eAAe,CAAC;EACnC,IAAIO,2BAA2B,GAAGlB,MAAM,CAAC,KAAK,CAAC;EAC/C,IAAImB,EAAE,GAAGvB,MAAM,CAACG,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;IAC9BqB,UAAU,GAAGD,EAAE,CAAC,CAAC,CAAC;IAClBE,aAAa,GAAGF,EAAE,CAAC,CAAC,CAAC;EACvB,IAAIG,EAAE,GAAG1B,MAAM,CAACG,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9BwB,YAAY,GAAGD,EAAE,CAAC,CAAC,CAAC;IACpBE,eAAe,GAAGF,EAAE,CAAC,CAAC,CAAC;EACzB,IAAIG,eAAe,GAAG,SAAAA,CAAUC,eAAe,EAAEC,SAAS,EAAE;IAC1D,IAAIrB,QAAQ,CAACU,aAAa,CAACY,OAAO,CAAC,EAAE;MACnC,OAAOC,IAAI,CAACC,IAAI,CAACJ,eAAe,GAAGV,aAAa,CAACY,OAAO,CAAC;IAC3D;IACA,IAAIG,GAAG,GAAG,CAAC;IACX,IAAIC,QAAQ,GAAG,CAAC;IAChB,KAAK,IAAIC,CAAC,GAAGN,SAAS,EAAEM,CAAC,GAAGxB,IAAI,CAACyB,MAAM,EAAED,CAAC,EAAE,EAAE;MAC5C,IAAIE,MAAM,GAAGnB,aAAa,CAACY,OAAO,CAACK,CAAC,EAAExB,IAAI,CAACwB,CAAC,CAAC,CAAC;MAC9CF,GAAG,IAAII,MAAM;MACbH,QAAQ,GAAGC,CAAC;MACZ,IAAIF,GAAG,IAAIL,eAAe,EAAE;QAC1B;MACF;IACF;IACA,OAAOM,QAAQ,GAAGL,SAAS;EAC7B,CAAC;EACD,IAAIS,SAAS,GAAG,SAAAA,CAAUC,SAAS,EAAE;IACnC,IAAI/B,QAAQ,CAACU,aAAa,CAACY,OAAO,CAAC,EAAE;MACnC,OAAOC,IAAI,CAACS,KAAK,CAACD,SAAS,GAAGrB,aAAa,CAACY,OAAO,CAAC;IACtD;IACA,IAAIG,GAAG,GAAG,CAAC;IACX,IAAIQ,MAAM,GAAG,CAAC;IACd,KAAK,IAAIN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGxB,IAAI,CAACyB,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAIE,MAAM,GAAGnB,aAAa,CAACY,OAAO,CAACK,CAAC,EAAExB,IAAI,CAACwB,CAAC,CAAC,CAAC;MAC9CF,GAAG,IAAII,MAAM;MACb,IAAIJ,GAAG,IAAIM,SAAS,EAAE;QACpBE,MAAM,GAAGN,CAAC;QACV;MACF;IACF;IACA,OAAOM,MAAM,GAAG,CAAC;EACnB,CAAC;EACD;EACA,IAAIC,cAAc,GAAG,SAAAA,CAAUC,KAAK,EAAE;IACpC,IAAInC,QAAQ,CAACU,aAAa,CAACY,OAAO,CAAC,EAAE;MACnC,IAAIc,QAAQ,GAAGD,KAAK,GAAGzB,aAAa,CAACY,OAAO;MAC5C,OAAOc,QAAQ;IACjB;IACA,IAAIP,MAAM,GAAG1B,IAAI,CAACkC,KAAK,CAAC,CAAC,EAAEF,KAAK,CAAC,CAACG,MAAM,CAAC,UAAUb,GAAG,EAAEc,CAAC,EAAEZ,CAAC,EAAE;MAC5D,OAAOF,GAAG,GAAGf,aAAa,CAACY,OAAO,CAACK,CAAC,EAAExB,IAAI,CAACwB,CAAC,CAAC,CAAC;IAChD,CAAC,EAAE,CAAC,CAAC;IACL,OAAOE,MAAM;EACf,CAAC;EACD,IAAIW,WAAW,GAAGhD,OAAO,CAAC,YAAY;IACpC,IAAIQ,QAAQ,CAACU,aAAa,CAACY,OAAO,CAAC,EAAE;MACnC,OAAOnB,IAAI,CAACyB,MAAM,GAAGlB,aAAa,CAACY,OAAO;IAC5C;IACA,OAAOnB,IAAI,CAACmC,MAAM,CAAC,UAAUb,GAAG,EAAEc,CAAC,EAAEJ,KAAK,EAAE;MAC1C,OAAOV,GAAG,GAAGf,aAAa,CAACY,OAAO,CAACa,KAAK,EAAEhC,IAAI,CAACgC,KAAK,CAAC,CAAC;IACxD,CAAC,EAAE,CAAC,CAAC;EACP,CAAC,EAAE,CAAChC,IAAI,CAAC,CAAC;EACV,IAAIsC,cAAc,GAAG,SAAAA,CAAA,EAAY;IAC/B,IAAIC,SAAS,GAAG3C,gBAAgB,CAACM,eAAe,CAAC;IACjD,IAAIqC,SAAS,EAAE;MACb,IAAIX,SAAS,GAAGW,SAAS,CAACX,SAAS;QACjCY,YAAY,GAAGD,SAAS,CAACC,YAAY;MACvC,IAAIV,MAAM,GAAGH,SAAS,CAACC,SAAS,CAAC;MACjC,IAAIa,YAAY,GAAGzB,eAAe,CAACwB,YAAY,EAAEV,MAAM,CAAC;MACxD,IAAIY,OAAO,GAAGtB,IAAI,CAACuB,GAAG,CAAC,CAAC,EAAEb,MAAM,GAAGxB,QAAQ,CAAC;MAC5C,IAAIsC,GAAG,GAAGxB,IAAI,CAACyB,GAAG,CAAC7C,IAAI,CAACyB,MAAM,EAAEK,MAAM,GAAGW,YAAY,GAAGnC,QAAQ,CAAC;MACjE,IAAIwC,SAAS,GAAGf,cAAc,CAACW,OAAO,CAAC;MACvC3B,eAAe,CAAC;QACdW,MAAM,EAAEW,WAAW,GAAGS,SAAS,GAAG,IAAI;QACtCC,SAAS,EAAED,SAAS,GAAG;MACzB,CAAC,CAAC;MACFlC,aAAa,CAACZ,IAAI,CAACkC,KAAK,CAACQ,OAAO,EAAEE,GAAG,CAAC,CAACI,GAAG,CAAC,UAAUC,GAAG,EAAEjB,KAAK,EAAE;QAC/D,OAAO;UACLkB,IAAI,EAAED,GAAG;UACTjB,KAAK,EAAEA,KAAK,GAAGU;QACjB,CAAC;MACH,CAAC,CAAC,CAAC;IACL;EACF,CAAC;EACD5C,eAAe,CAAC,YAAY;IAC1B,IAAIqD,OAAO,GAAGvD,gBAAgB,CAACO,aAAa,CAAC;IAC7C,IAAIgD,OAAO,EAAE;MACXC,MAAM,CAACC,IAAI,CAACvC,YAAY,CAAC,CAACwC,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC/C,OAAOJ,OAAO,CAACK,KAAK,CAACD,GAAG,CAAC,GAAGzC,YAAY,CAACyC,GAAG,CAAC;MAC/C,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACzC,YAAY,CAAC,CAAC;EAClB1B,SAAS,CAAC,YAAY;IACpB,IAAI,EAAEoB,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiD,KAAK,CAAC,IAAI,EAAEjD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACkB,MAAM,CAAC,EAAE;MAC3H;IACF;IACAY,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE,CAAC9B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACiD,KAAK,EAAEjD,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACkB,MAAM,EAAE1B,IAAI,CAAC,CAAC;EAC3HR,gBAAgB,CAAC,QAAQ,EAAE,UAAUkE,CAAC,EAAE;IACtC,IAAIjD,2BAA2B,CAACU,OAAO,EAAE;MACvCV,2BAA2B,CAACU,OAAO,GAAG,KAAK;MAC3C;IACF;IACAuC,CAAC,CAACC,cAAc,CAAC,CAAC;IAClBrB,cAAc,CAAC,CAAC;EAClB,CAAC,EAAE;IACDsB,MAAM,EAAE1D;EACV,CAAC,CAAC;EACF,IAAI2D,QAAQ,GAAG,SAAAA,CAAU7B,KAAK,EAAE;IAC9B,IAAIO,SAAS,GAAG3C,gBAAgB,CAACM,eAAe,CAAC;IACjD,IAAIqC,SAAS,EAAE;MACb9B,2BAA2B,CAACU,OAAO,GAAG,IAAI;MAC1CoB,SAAS,CAACX,SAAS,GAAGG,cAAc,CAACC,KAAK,CAAC;MAC3CM,cAAc,CAAC,CAAC;IAClB;EACF,CAAC;EACD,OAAO,CAAC3B,UAAU,EAAEjB,aAAa,CAACmE,QAAQ,CAAC,CAAC;AAC9C,CAAC;AACD,eAAe9D,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}