{"ast": null, "code": "import { __values } from \"tslib\";\nimport useLatest from '../useLatest';\nimport { isFunction, isNumber, isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport isAppleDevice from '../utils/isAppleDevice';\n// 键盘事件 keyCode 别名\nvar aliasKeyCodeMap = {\n  '0': 48,\n  '1': 49,\n  '2': 50,\n  '3': 51,\n  '4': 52,\n  '5': 53,\n  '6': 54,\n  '7': 55,\n  '8': 56,\n  '9': 57,\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  ctrl: 17,\n  alt: 18,\n  pausebreak: 19,\n  capslock: 20,\n  esc: 27,\n  space: 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  leftarrow: 37,\n  uparrow: 38,\n  rightarrow: 39,\n  downarrow: 40,\n  insert: 45,\n  delete: 46,\n  a: 65,\n  b: 66,\n  c: 67,\n  d: 68,\n  e: 69,\n  f: 70,\n  g: 71,\n  h: 72,\n  i: 73,\n  j: 74,\n  k: 75,\n  l: 76,\n  m: 77,\n  n: 78,\n  o: 79,\n  p: 80,\n  q: 81,\n  r: 82,\n  s: 83,\n  t: 84,\n  u: 85,\n  v: 86,\n  w: 87,\n  x: 88,\n  y: 89,\n  z: 90,\n  leftwindowkey: 91,\n  rightwindowkey: 92,\n  meta: isAppleDevice ? [91, 93] : [91, 92],\n  selectkey: 93,\n  numpad0: 96,\n  numpad1: 97,\n  numpad2: 98,\n  numpad3: 99,\n  numpad4: 100,\n  numpad5: 101,\n  numpad6: 102,\n  numpad7: 103,\n  numpad8: 104,\n  numpad9: 105,\n  multiply: 106,\n  add: 107,\n  subtract: 109,\n  decimalpoint: 110,\n  divide: 111,\n  f1: 112,\n  f2: 113,\n  f3: 114,\n  f4: 115,\n  f5: 116,\n  f6: 117,\n  f7: 118,\n  f8: 119,\n  f9: 120,\n  f10: 121,\n  f11: 122,\n  f12: 123,\n  numlock: 144,\n  scrolllock: 145,\n  semicolon: 186,\n  equalsign: 187,\n  comma: 188,\n  dash: 189,\n  period: 190,\n  forwardslash: 191,\n  graveaccent: 192,\n  openbracket: 219,\n  backslash: 220,\n  closebracket: 221,\n  singlequote: 222\n};\n// 修饰键\nvar modifierKey = {\n  ctrl: function (event) {\n    return event.ctrlKey;\n  },\n  shift: function (event) {\n    return event.shiftKey;\n  },\n  alt: function (event) {\n    return event.altKey;\n  },\n  meta: function (event) {\n    if (event.type === 'keyup') {\n      return aliasKeyCodeMap.meta.includes(event.keyCode);\n    }\n    return event.metaKey;\n  }\n};\n// 判断合法的按键类型\nfunction isValidKeyType(value) {\n  return isString(value) || isNumber(value);\n}\n// 根据 event 计算激活键数量\nfunction countKeyByEvent(event) {\n  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {\n    if (modifierKey[key](event)) {\n      return total + 1;\n    }\n    return total;\n  }, 0);\n  // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1\n  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;\n}\n/**\n * 判断按键是否激活\n * @param [event: KeyboardEvent]键盘事件\n * @param [keyFilter: any] 当前键\n * @returns string | number | boolean\n */\nfunction genFilterKey(event, keyFilter, exactMatch) {\n  var e_1, _a;\n  // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空\n  if (!event.key) {\n    return false;\n  }\n  // 数字类型直接匹配事件的 keyCode\n  if (isNumber(keyFilter)) {\n    return event.keyCode === keyFilter ? keyFilter : false;\n  }\n  // 字符串依次判断是否有组合键\n  var genArr = keyFilter.split('.');\n  var genLen = 0;\n  try {\n    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {\n      var key = genArr_1_1.value;\n      // 组合键\n      var genModifier = modifierKey[key];\n      // keyCode 别名\n      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];\n      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {\n        genLen++;\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1.return)) _a.call(genArr_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  /**\n   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位\n   * genLen === genArr.length 能判断出来触发的键位里有监听的键位\n   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量\n   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。\n   */\n  if (exactMatch) {\n    return genLen === genArr.length && countKeyByEvent(event) === genArr.length ? keyFilter : false;\n  }\n  return genLen === genArr.length ? keyFilter : false;\n}\n/**\n * 键盘输入预处理方法\n * @param [keyFilter: any] 当前键\n * @returns () => Boolean\n */\nfunction genKeyFormatter(keyFilter, exactMatch) {\n  if (isFunction(keyFilter)) {\n    return keyFilter;\n  }\n  if (isValidKeyType(keyFilter)) {\n    return function (event) {\n      return genFilterKey(event, keyFilter, exactMatch);\n    };\n  }\n  if (Array.isArray(keyFilter)) {\n    return function (event) {\n      return keyFilter.find(function (item) {\n        return genFilterKey(event, item, exactMatch);\n      });\n    };\n  }\n  return function () {\n    return Boolean(keyFilter);\n  };\n}\nvar defaultEvents = ['keydown'];\nfunction useKeyPress(keyFilter, eventHandler, option) {\n  var _a = option || {},\n    _b = _a.events,\n    events = _b === void 0 ? defaultEvents : _b,\n    target = _a.target,\n    _c = _a.exactMatch,\n    exactMatch = _c === void 0 ? false : _c,\n    _d = _a.useCapture,\n    useCapture = _d === void 0 ? false : _d;\n  var eventHandlerRef = useLatest(eventHandler);\n  var keyFilterRef = useLatest(keyFilter);\n  useDeepCompareEffectWithTarget(function () {\n    var e_2, _a;\n    var _b;\n    var el = getTargetElement(target, window);\n    if (!el) {\n      return;\n    }\n    var callbackHandler = function (event) {\n      var _a;\n      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);\n      var keyGuard = genGuard(event);\n      var firedKey = isValidKeyType(keyGuard) ? keyGuard : event.key;\n      if (keyGuard) {\n        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event, firedKey);\n      }\n    };\n    try {\n      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {\n        var eventName = events_1_1.value;\n        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (events_1_1 && !events_1_1.done && (_a = events_1.return)) _a.call(events_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return function () {\n      var e_3, _a;\n      var _b;\n      try {\n        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {\n          var eventName = events_2_1.value;\n          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (events_2_1 && !events_2_1.done && (_a = events_2.return)) _a.call(events_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n    };\n  }, [events], target);\n}\nexport default useKeyPress;", "map": {"version": 3, "names": ["__values", "useLatest", "isFunction", "isNumber", "isString", "getTargetElement", "useDeepCompareEffectWithTarget", "isAppleDevice", "aliasKeyCodeMap", "backspace", "tab", "enter", "shift", "ctrl", "alt", "pausebreak", "capslock", "esc", "space", "pageup", "pagedown", "end", "home", "leftarrow", "uparrow", "rightarrow", "downarrow", "insert", "delete", "a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s", "t", "u", "v", "w", "x", "y", "z", "leftwindowkey", "rightwindow<PERSON>", "meta", "selectkey", "numpad0", "numpad1", "numpad2", "numpad3", "numpad4", "numpad5", "numpad6", "numpad7", "numpad8", "numpad9", "multiply", "add", "subtract", "decimalpoint", "divide", "f1", "f2", "f3", "f4", "f5", "f6", "f7", "f8", "f9", "f10", "f11", "f12", "numlock", "scrolllock", "semicolon", "equalsign", "comma", "dash", "period", "forwardslash", "<PERSON><PERSON><PERSON>", "openbracket", "backslash", "closebracket", "singlequote", "modifierKey", "event", "ctrl<PERSON>ey", "shift<PERSON>ey", "altKey", "type", "includes", "keyCode", "metaKey", "isValidKeyType", "value", "count<PERSON>ey<PERSON>yEvent", "countOfModifier", "Object", "keys", "reduce", "total", "key", "gen<PERSON><PERSON>er<PERSON><PERSON>", "keyFilter", "exactMatch", "e_1", "_a", "genArr", "split", "genLen", "genArr_1", "genArr_1_1", "next", "done", "genModifier", "aliasKeyCode", "toLowerCase", "e_1_1", "error", "return", "call", "length", "gen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "find", "item", "Boolean", "defaultEvents", "useKeyPress", "<PERSON><PERSON><PERSON><PERSON>", "option", "_b", "events", "target", "_c", "_d", "useCapture", "eventHandlerRef", "keyFilterRef", "e_2", "el", "window", "call<PERSON><PERSON><PERSON><PERSON>", "g<PERSON><PERSON><PERSON>", "current", "key<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "events_1", "events_1_1", "eventName", "addEventListener", "e_2_1", "e_3", "events_2", "events_2_1", "removeEventListener", "e_3_1"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useKeyPress/index.js"], "sourcesContent": ["import { __values } from \"tslib\";\nimport useLatest from '../useLatest';\nimport { isFunction, isNumber, isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport isAppleDevice from '../utils/isAppleDevice';\n// 键盘事件 keyCode 别名\nvar aliasKeyCodeMap = {\n  '0': 48,\n  '1': 49,\n  '2': 50,\n  '3': 51,\n  '4': 52,\n  '5': 53,\n  '6': 54,\n  '7': 55,\n  '8': 56,\n  '9': 57,\n  backspace: 8,\n  tab: 9,\n  enter: 13,\n  shift: 16,\n  ctrl: 17,\n  alt: 18,\n  pausebreak: 19,\n  capslock: 20,\n  esc: 27,\n  space: 32,\n  pageup: 33,\n  pagedown: 34,\n  end: 35,\n  home: 36,\n  leftarrow: 37,\n  uparrow: 38,\n  rightarrow: 39,\n  downarrow: 40,\n  insert: 45,\n  delete: 46,\n  a: 65,\n  b: 66,\n  c: 67,\n  d: 68,\n  e: 69,\n  f: 70,\n  g: 71,\n  h: 72,\n  i: 73,\n  j: 74,\n  k: 75,\n  l: 76,\n  m: 77,\n  n: 78,\n  o: 79,\n  p: 80,\n  q: 81,\n  r: 82,\n  s: 83,\n  t: 84,\n  u: 85,\n  v: 86,\n  w: 87,\n  x: 88,\n  y: 89,\n  z: 90,\n  leftwindowkey: 91,\n  rightwindowkey: 92,\n  meta: isAppleDevice ? [91, 93] : [91, 92],\n  selectkey: 93,\n  numpad0: 96,\n  numpad1: 97,\n  numpad2: 98,\n  numpad3: 99,\n  numpad4: 100,\n  numpad5: 101,\n  numpad6: 102,\n  numpad7: 103,\n  numpad8: 104,\n  numpad9: 105,\n  multiply: 106,\n  add: 107,\n  subtract: 109,\n  decimalpoint: 110,\n  divide: 111,\n  f1: 112,\n  f2: 113,\n  f3: 114,\n  f4: 115,\n  f5: 116,\n  f6: 117,\n  f7: 118,\n  f8: 119,\n  f9: 120,\n  f10: 121,\n  f11: 122,\n  f12: 123,\n  numlock: 144,\n  scrolllock: 145,\n  semicolon: 186,\n  equalsign: 187,\n  comma: 188,\n  dash: 189,\n  period: 190,\n  forwardslash: 191,\n  graveaccent: 192,\n  openbracket: 219,\n  backslash: 220,\n  closebracket: 221,\n  singlequote: 222\n};\n// 修饰键\nvar modifierKey = {\n  ctrl: function (event) {\n    return event.ctrlKey;\n  },\n  shift: function (event) {\n    return event.shiftKey;\n  },\n  alt: function (event) {\n    return event.altKey;\n  },\n  meta: function (event) {\n    if (event.type === 'keyup') {\n      return aliasKeyCodeMap.meta.includes(event.keyCode);\n    }\n    return event.metaKey;\n  }\n};\n// 判断合法的按键类型\nfunction isValidKeyType(value) {\n  return isString(value) || isNumber(value);\n}\n// 根据 event 计算激活键数量\nfunction countKeyByEvent(event) {\n  var countOfModifier = Object.keys(modifierKey).reduce(function (total, key) {\n    if (modifierKey[key](event)) {\n      return total + 1;\n    }\n    return total;\n  }, 0);\n  // 16 17 18 91 92 是修饰键的 keyCode，如果 keyCode 是修饰键，那么激活数量就是修饰键的数量，如果不是，那么就需要 +1\n  return [16, 17, 18, 91, 92].includes(event.keyCode) ? countOfModifier : countOfModifier + 1;\n}\n/**\n * 判断按键是否激活\n * @param [event: KeyboardEvent]键盘事件\n * @param [keyFilter: any] 当前键\n * @returns string | number | boolean\n */\nfunction genFilterKey(event, keyFilter, exactMatch) {\n  var e_1, _a;\n  // 浏览器自动补全 input 的时候，会触发 keyDown、keyUp 事件，但此时 event.key 等为空\n  if (!event.key) {\n    return false;\n  }\n  // 数字类型直接匹配事件的 keyCode\n  if (isNumber(keyFilter)) {\n    return event.keyCode === keyFilter ? keyFilter : false;\n  }\n  // 字符串依次判断是否有组合键\n  var genArr = keyFilter.split('.');\n  var genLen = 0;\n  try {\n    for (var genArr_1 = __values(genArr), genArr_1_1 = genArr_1.next(); !genArr_1_1.done; genArr_1_1 = genArr_1.next()) {\n      var key = genArr_1_1.value;\n      // 组合键\n      var genModifier = modifierKey[key];\n      // keyCode 别名\n      var aliasKeyCode = aliasKeyCodeMap[key.toLowerCase()];\n      if (genModifier && genModifier(event) || aliasKeyCode && aliasKeyCode === event.keyCode) {\n        genLen++;\n      }\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (genArr_1_1 && !genArr_1_1.done && (_a = genArr_1.return)) _a.call(genArr_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n  /**\n   * 需要判断触发的键位和监听的键位完全一致，判断方法就是触发的键位里有且等于监听的键位\n   * genLen === genArr.length 能判断出来触发的键位里有监听的键位\n   * countKeyByEvent(event) === genArr.length 判断出来触发的键位数量里有且等于监听的键位数量\n   * 主要用来防止按组合键其子集也会触发的情况，例如监听 ctrl+a 会触发监听 ctrl 和 a 两个键的事件。\n   */\n  if (exactMatch) {\n    return genLen === genArr.length && countKeyByEvent(event) === genArr.length ? keyFilter : false;\n  }\n  return genLen === genArr.length ? keyFilter : false;\n}\n/**\n * 键盘输入预处理方法\n * @param [keyFilter: any] 当前键\n * @returns () => Boolean\n */\nfunction genKeyFormatter(keyFilter, exactMatch) {\n  if (isFunction(keyFilter)) {\n    return keyFilter;\n  }\n  if (isValidKeyType(keyFilter)) {\n    return function (event) {\n      return genFilterKey(event, keyFilter, exactMatch);\n    };\n  }\n  if (Array.isArray(keyFilter)) {\n    return function (event) {\n      return keyFilter.find(function (item) {\n        return genFilterKey(event, item, exactMatch);\n      });\n    };\n  }\n  return function () {\n    return Boolean(keyFilter);\n  };\n}\nvar defaultEvents = ['keydown'];\nfunction useKeyPress(keyFilter, eventHandler, option) {\n  var _a = option || {},\n    _b = _a.events,\n    events = _b === void 0 ? defaultEvents : _b,\n    target = _a.target,\n    _c = _a.exactMatch,\n    exactMatch = _c === void 0 ? false : _c,\n    _d = _a.useCapture,\n    useCapture = _d === void 0 ? false : _d;\n  var eventHandlerRef = useLatest(eventHandler);\n  var keyFilterRef = useLatest(keyFilter);\n  useDeepCompareEffectWithTarget(function () {\n    var e_2, _a;\n    var _b;\n    var el = getTargetElement(target, window);\n    if (!el) {\n      return;\n    }\n    var callbackHandler = function (event) {\n      var _a;\n      var genGuard = genKeyFormatter(keyFilterRef.current, exactMatch);\n      var keyGuard = genGuard(event);\n      var firedKey = isValidKeyType(keyGuard) ? keyGuard : event.key;\n      if (keyGuard) {\n        return (_a = eventHandlerRef.current) === null || _a === void 0 ? void 0 : _a.call(eventHandlerRef, event, firedKey);\n      }\n    };\n    try {\n      for (var events_1 = __values(events), events_1_1 = events_1.next(); !events_1_1.done; events_1_1 = events_1.next()) {\n        var eventName = events_1_1.value;\n        (_b = el === null || el === void 0 ? void 0 : el.addEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n      }\n    } catch (e_2_1) {\n      e_2 = {\n        error: e_2_1\n      };\n    } finally {\n      try {\n        if (events_1_1 && !events_1_1.done && (_a = events_1.return)) _a.call(events_1);\n      } finally {\n        if (e_2) throw e_2.error;\n      }\n    }\n    return function () {\n      var e_3, _a;\n      var _b;\n      try {\n        for (var events_2 = __values(events), events_2_1 = events_2.next(); !events_2_1.done; events_2_1 = events_2.next()) {\n          var eventName = events_2_1.value;\n          (_b = el === null || el === void 0 ? void 0 : el.removeEventListener) === null || _b === void 0 ? void 0 : _b.call(el, eventName, callbackHandler, useCapture);\n        }\n      } catch (e_3_1) {\n        e_3 = {\n          error: e_3_1\n        };\n      } finally {\n        try {\n          if (events_2_1 && !events_2_1.done && (_a = events_2.return)) _a.call(events_2);\n        } finally {\n          if (e_3) throw e_3.error;\n        }\n      }\n    };\n  }, [events], target);\n}\nexport default useKeyPress;"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,UAAU;AACzD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,8BAA8B,MAAM,mCAAmC;AAC9E,OAAOC,aAAa,MAAM,wBAAwB;AAClD;AACA,IAAIC,eAAe,GAAG;EACpB,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACP,GAAG,EAAE,EAAE;EACPC,SAAS,EAAE,CAAC;EACZC,GAAG,EAAE,CAAC;EACNC,KAAK,EAAE,EAAE;EACTC,KAAK,EAAE,EAAE;EACTC,IAAI,EAAE,EAAE;EACRC,GAAG,EAAE,EAAE;EACPC,UAAU,EAAE,EAAE;EACdC,QAAQ,EAAE,EAAE;EACZC,GAAG,EAAE,EAAE;EACPC,KAAK,EAAE,EAAE;EACTC,MAAM,EAAE,EAAE;EACVC,QAAQ,EAAE,EAAE;EACZC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,EAAE;EACXC,UAAU,EAAE,EAAE;EACdC,SAAS,EAAE,EAAE;EACbC,MAAM,EAAE,EAAE;EACVC,MAAM,EAAE,EAAE;EACVC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,CAAC,EAAE,EAAE;EACLC,aAAa,EAAE,EAAE;EACjBC,cAAc,EAAE,EAAE;EAClBC,IAAI,EAAElD,aAAa,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,CAAC,EAAE,EAAE,EAAE,CAAC;EACzCmD,SAAS,EAAE,EAAE;EACbC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,EAAE;EACXC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,OAAO,EAAE,GAAG;EACZC,QAAQ,EAAE,GAAG;EACbC,GAAG,EAAE,GAAG;EACRC,QAAQ,EAAE,GAAG;EACbC,YAAY,EAAE,GAAG;EACjBC,MAAM,EAAE,GAAG;EACXC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,GAAG,EAAE,GAAG;EACRC,GAAG,EAAE,GAAG;EACRC,GAAG,EAAE,GAAG;EACRC,OAAO,EAAE,GAAG;EACZC,UAAU,EAAE,GAAG;EACfC,SAAS,EAAE,GAAG;EACdC,SAAS,EAAE,GAAG;EACdC,KAAK,EAAE,GAAG;EACVC,IAAI,EAAE,GAAG;EACTC,MAAM,EAAE,GAAG;EACXC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE,GAAG;EAChBC,WAAW,EAAE,GAAG;EAChBC,SAAS,EAAE,GAAG;EACdC,YAAY,EAAE,GAAG;EACjBC,WAAW,EAAE;AACf,CAAC;AACD;AACA,IAAIC,WAAW,GAAG;EAChBtF,IAAI,EAAE,SAAAA,CAAUuF,KAAK,EAAE;IACrB,OAAOA,KAAK,CAACC,OAAO;EACtB,CAAC;EACDzF,KAAK,EAAE,SAAAA,CAAUwF,KAAK,EAAE;IACtB,OAAOA,KAAK,CAACE,QAAQ;EACvB,CAAC;EACDxF,GAAG,EAAE,SAAAA,CAAUsF,KAAK,EAAE;IACpB,OAAOA,KAAK,CAACG,MAAM;EACrB,CAAC;EACD9C,IAAI,EAAE,SAAAA,CAAU2C,KAAK,EAAE;IACrB,IAAIA,KAAK,CAACI,IAAI,KAAK,OAAO,EAAE;MAC1B,OAAOhG,eAAe,CAACiD,IAAI,CAACgD,QAAQ,CAACL,KAAK,CAACM,OAAO,CAAC;IACrD;IACA,OAAON,KAAK,CAACO,OAAO;EACtB;AACF,CAAC;AACD;AACA,SAASC,cAAcA,CAACC,KAAK,EAAE;EAC7B,OAAOzG,QAAQ,CAACyG,KAAK,CAAC,IAAI1G,QAAQ,CAAC0G,KAAK,CAAC;AAC3C;AACA;AACA,SAASC,eAAeA,CAACV,KAAK,EAAE;EAC9B,IAAIW,eAAe,GAAGC,MAAM,CAACC,IAAI,CAACd,WAAW,CAAC,CAACe,MAAM,CAAC,UAAUC,KAAK,EAAEC,GAAG,EAAE;IAC1E,IAAIjB,WAAW,CAACiB,GAAG,CAAC,CAAChB,KAAK,CAAC,EAAE;MAC3B,OAAOe,KAAK,GAAG,CAAC;IAClB;IACA,OAAOA,KAAK;EACd,CAAC,EAAE,CAAC,CAAC;EACL;EACA,OAAO,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,CAACV,QAAQ,CAACL,KAAK,CAACM,OAAO,CAAC,GAAGK,eAAe,GAAGA,eAAe,GAAG,CAAC;AAC7F;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,YAAYA,CAACjB,KAAK,EAAEkB,SAAS,EAAEC,UAAU,EAAE;EAClD,IAAIC,GAAG,EAAEC,EAAE;EACX;EACA,IAAI,CAACrB,KAAK,CAACgB,GAAG,EAAE;IACd,OAAO,KAAK;EACd;EACA;EACA,IAAIjH,QAAQ,CAACmH,SAAS,CAAC,EAAE;IACvB,OAAOlB,KAAK,CAACM,OAAO,KAAKY,SAAS,GAAGA,SAAS,GAAG,KAAK;EACxD;EACA;EACA,IAAII,MAAM,GAAGJ,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC;EACjC,IAAIC,MAAM,GAAG,CAAC;EACd,IAAI;IACF,KAAK,IAAIC,QAAQ,GAAG7H,QAAQ,CAAC0H,MAAM,CAAC,EAAEI,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE,CAACD,UAAU,CAACE,IAAI,EAAEF,UAAU,GAAGD,QAAQ,CAACE,IAAI,CAAC,CAAC,EAAE;MAClH,IAAIX,GAAG,GAAGU,UAAU,CAACjB,KAAK;MAC1B;MACA,IAAIoB,WAAW,GAAG9B,WAAW,CAACiB,GAAG,CAAC;MAClC;MACA,IAAIc,YAAY,GAAG1H,eAAe,CAAC4G,GAAG,CAACe,WAAW,CAAC,CAAC,CAAC;MACrD,IAAIF,WAAW,IAAIA,WAAW,CAAC7B,KAAK,CAAC,IAAI8B,YAAY,IAAIA,YAAY,KAAK9B,KAAK,CAACM,OAAO,EAAE;QACvFkB,MAAM,EAAE;MACV;IACF;EACF,CAAC,CAAC,OAAOQ,KAAK,EAAE;IACdZ,GAAG,GAAG;MACJa,KAAK,EAAED;IACT,CAAC;EACH,CAAC,SAAS;IACR,IAAI;MACF,IAAIN,UAAU,IAAI,CAACA,UAAU,CAACE,IAAI,KAAKP,EAAE,GAAGI,QAAQ,CAACS,MAAM,CAAC,EAAEb,EAAE,CAACc,IAAI,CAACV,QAAQ,CAAC;IACjF,CAAC,SAAS;MACR,IAAIL,GAAG,EAAE,MAAMA,GAAG,CAACa,KAAK;IAC1B;EACF;EACA;AACF;AACA;AACA;AACA;AACA;EACE,IAAId,UAAU,EAAE;IACd,OAAOK,MAAM,KAAKF,MAAM,CAACc,MAAM,IAAI1B,eAAe,CAACV,KAAK,CAAC,KAAKsB,MAAM,CAACc,MAAM,GAAGlB,SAAS,GAAG,KAAK;EACjG;EACA,OAAOM,MAAM,KAAKF,MAAM,CAACc,MAAM,GAAGlB,SAAS,GAAG,KAAK;AACrD;AACA;AACA;AACA;AACA;AACA;AACA,SAASmB,eAAeA,CAACnB,SAAS,EAAEC,UAAU,EAAE;EAC9C,IAAIrH,UAAU,CAACoH,SAAS,CAAC,EAAE;IACzB,OAAOA,SAAS;EAClB;EACA,IAAIV,cAAc,CAACU,SAAS,CAAC,EAAE;IAC7B,OAAO,UAAUlB,KAAK,EAAE;MACtB,OAAOiB,YAAY,CAACjB,KAAK,EAAEkB,SAAS,EAAEC,UAAU,CAAC;IACnD,CAAC;EACH;EACA,IAAImB,KAAK,CAACC,OAAO,CAACrB,SAAS,CAAC,EAAE;IAC5B,OAAO,UAAUlB,KAAK,EAAE;MACtB,OAAOkB,SAAS,CAACsB,IAAI,CAAC,UAAUC,IAAI,EAAE;QACpC,OAAOxB,YAAY,CAACjB,KAAK,EAAEyC,IAAI,EAAEtB,UAAU,CAAC;MAC9C,CAAC,CAAC;IACJ,CAAC;EACH;EACA,OAAO,YAAY;IACjB,OAAOuB,OAAO,CAACxB,SAAS,CAAC;EAC3B,CAAC;AACH;AACA,IAAIyB,aAAa,GAAG,CAAC,SAAS,CAAC;AAC/B,SAASC,WAAWA,CAAC1B,SAAS,EAAE2B,YAAY,EAAEC,MAAM,EAAE;EACpD,IAAIzB,EAAE,GAAGyB,MAAM,IAAI,CAAC,CAAC;IACnBC,EAAE,GAAG1B,EAAE,CAAC2B,MAAM;IACdA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAGJ,aAAa,GAAGI,EAAE;IAC3CE,MAAM,GAAG5B,EAAE,CAAC4B,MAAM;IAClBC,EAAE,GAAG7B,EAAE,CAACF,UAAU;IAClBA,UAAU,GAAG+B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IACvCC,EAAE,GAAG9B,EAAE,CAAC+B,UAAU;IAClBA,UAAU,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;EACzC,IAAIE,eAAe,GAAGxJ,SAAS,CAACgJ,YAAY,CAAC;EAC7C,IAAIS,YAAY,GAAGzJ,SAAS,CAACqH,SAAS,CAAC;EACvChH,8BAA8B,CAAC,YAAY;IACzC,IAAIqJ,GAAG,EAAElC,EAAE;IACX,IAAI0B,EAAE;IACN,IAAIS,EAAE,GAAGvJ,gBAAgB,CAACgJ,MAAM,EAAEQ,MAAM,CAAC;IACzC,IAAI,CAACD,EAAE,EAAE;MACP;IACF;IACA,IAAIE,eAAe,GAAG,SAAAA,CAAU1D,KAAK,EAAE;MACrC,IAAIqB,EAAE;MACN,IAAIsC,QAAQ,GAAGtB,eAAe,CAACiB,YAAY,CAACM,OAAO,EAAEzC,UAAU,CAAC;MAChE,IAAI0C,QAAQ,GAAGF,QAAQ,CAAC3D,KAAK,CAAC;MAC9B,IAAI8D,QAAQ,GAAGtD,cAAc,CAACqD,QAAQ,CAAC,GAAGA,QAAQ,GAAG7D,KAAK,CAACgB,GAAG;MAC9D,IAAI6C,QAAQ,EAAE;QACZ,OAAO,CAACxC,EAAE,GAAGgC,eAAe,CAACO,OAAO,MAAM,IAAI,IAAIvC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACc,IAAI,CAACkB,eAAe,EAAErD,KAAK,EAAE8D,QAAQ,CAAC;MACtH;IACF,CAAC;IACD,IAAI;MACF,KAAK,IAAIC,QAAQ,GAAGnK,QAAQ,CAACoJ,MAAM,CAAC,EAAEgB,UAAU,GAAGD,QAAQ,CAACpC,IAAI,CAAC,CAAC,EAAE,CAACqC,UAAU,CAACpC,IAAI,EAAEoC,UAAU,GAAGD,QAAQ,CAACpC,IAAI,CAAC,CAAC,EAAE;QAClH,IAAIsC,SAAS,GAAGD,UAAU,CAACvD,KAAK;QAChC,CAACsC,EAAE,GAAGS,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACU,gBAAgB,MAAM,IAAI,IAAInB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACZ,IAAI,CAACqB,EAAE,EAAES,SAAS,EAAEP,eAAe,EAAEN,UAAU,CAAC;MAC7J;IACF,CAAC,CAAC,OAAOe,KAAK,EAAE;MACdZ,GAAG,GAAG;QACJtB,KAAK,EAAEkC;MACT,CAAC;IACH,CAAC,SAAS;MACR,IAAI;QACF,IAAIH,UAAU,IAAI,CAACA,UAAU,CAACpC,IAAI,KAAKP,EAAE,GAAG0C,QAAQ,CAAC7B,MAAM,CAAC,EAAEb,EAAE,CAACc,IAAI,CAAC4B,QAAQ,CAAC;MACjF,CAAC,SAAS;QACR,IAAIR,GAAG,EAAE,MAAMA,GAAG,CAACtB,KAAK;MAC1B;IACF;IACA,OAAO,YAAY;MACjB,IAAImC,GAAG,EAAE/C,EAAE;MACX,IAAI0B,EAAE;MACN,IAAI;QACF,KAAK,IAAIsB,QAAQ,GAAGzK,QAAQ,CAACoJ,MAAM,CAAC,EAAEsB,UAAU,GAAGD,QAAQ,CAAC1C,IAAI,CAAC,CAAC,EAAE,CAAC2C,UAAU,CAAC1C,IAAI,EAAE0C,UAAU,GAAGD,QAAQ,CAAC1C,IAAI,CAAC,CAAC,EAAE;UAClH,IAAIsC,SAAS,GAAGK,UAAU,CAAC7D,KAAK;UAChC,CAACsC,EAAE,GAAGS,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACe,mBAAmB,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACZ,IAAI,CAACqB,EAAE,EAAES,SAAS,EAAEP,eAAe,EAAEN,UAAU,CAAC;QAChK;MACF,CAAC,CAAC,OAAOoB,KAAK,EAAE;QACdJ,GAAG,GAAG;UACJnC,KAAK,EAAEuC;QACT,CAAC;MACH,CAAC,SAAS;QACR,IAAI;UACF,IAAIF,UAAU,IAAI,CAACA,UAAU,CAAC1C,IAAI,KAAKP,EAAE,GAAGgD,QAAQ,CAACnC,MAAM,CAAC,EAAEb,EAAE,CAACc,IAAI,CAACkC,QAAQ,CAAC;QACjF,CAAC,SAAS;UACR,IAAID,GAAG,EAAE,MAAMA,GAAG,CAACnC,KAAK;QAC1B;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACe,MAAM,CAAC,EAAEC,MAAM,CAAC;AACtB;AACA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}