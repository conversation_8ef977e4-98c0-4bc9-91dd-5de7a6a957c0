{"ast": null, "code": "import { eachProp, is, toArray, getFluidValue, isAnimatedString, Globals, useIsomorphicLayoutEffect, each, easings, raf, flush, FluidValue, deprecateInterpolate, callFluidObservers, frameLoop, hasFluidValue, flushCalls, isEqual, getFluidObservers, addFluidObserver, removeFluidObserver, noop, useMemoOne, deprecateDirectCall, useForceUpdate, usePrev, useOnce, useConstant, onScroll, onResize, createInterpolator, createStringInterpolator } from '@react-spring/shared';\nexport { Globals, createInterpolator, easings, useIsomorphicLayoutEffect, useReducedMotion } from '@react-spring/shared';\nimport * as React from 'react';\nimport { useContext, useMemo, useRef, useState } from 'react';\nimport { getAnimated, AnimatedValue, getPayload, AnimatedString, getAnimatedType, setAnimated } from '@react-spring/animated';\nexport * from '@react-spring/types/animated';\nexport * from '@react-spring/types/interpolation';\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nfunction callProp(value, ...args) {\n  return is.fun(value) ? value(...args) : value;\n}\nconst matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));\nconst resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;\nconst getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : undefined;\nconst noopTransform = value => value;\nconst getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n  const defaults = {};\n  for (const key of keys) {\n    const value = transform(props[key], key);\n    if (!is.und(value)) {\n      defaults[key] = value;\n    }\n  }\n  return defaults;\n};\nconst DEFAULT_PROPS = ['config', 'onProps', 'onStart', 'onChange', 'onPause', 'onResume', 'onRest'];\nconst RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n  if (count) {\n    return forward;\n  }\n}\nfunction inferTo(props) {\n  const to = getForwardProps(props);\n  if (to) {\n    const out = {\n      to\n    };\n    eachProp(props, (val, key) => key in to || (out[key] = val));\n    return out;\n  }\n  return _extends({}, props);\n}\nfunction computeGoal(value) {\n  value = getFluidValue(value);\n  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? Globals.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n  return false;\n}\nfunction isAsyncTo(to) {\n  return is.fun(to) || is.arr(to) && is.obj(to[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  var _ctrl$ref;\n  (_ctrl$ref = ctrl.ref) == null ? void 0 : _ctrl$ref.delete(ctrl);\n  ref == null ? void 0 : ref.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    var _ctrl$ref2;\n    (_ctrl$ref2 = ctrl.ref) == null ? void 0 : _ctrl$ref2.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\nfunction useChain(refs, timeSteps, timeFrame = 1000) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      each(refs, (ref, i) => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;else prevDelay = delay;\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              const memoizedDelayProp = props.delay;\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      each(refs, ref => {\n        const controllers = ref.current;\n        if (controllers.length) {\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            each(controllers, (ctrl, i) => each(queues[i] || [], update => ctrl.queue.push(update)));\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\nconst config = {\n  default: {\n    tension: 170,\n    friction: 26\n  },\n  gentle: {\n    tension: 120,\n    friction: 14\n  },\n  wobbly: {\n    tension: 180,\n    friction: 12\n  },\n  stiff: {\n    tension: 210,\n    friction: 20\n  },\n  slow: {\n    tension: 280,\n    friction: 60\n  },\n  molasses: {\n    tension: 280,\n    friction: 120\n  }\n};\nconst defaults = _extends({}, config.default, {\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false\n});\nclass AnimationConfig {\n  constructor() {\n    this.tension = void 0;\n    this.friction = void 0;\n    this.frequency = void 0;\n    this.damping = void 0;\n    this.mass = void 0;\n    this.velocity = 0;\n    this.restVelocity = void 0;\n    this.precision = void 0;\n    this.progress = void 0;\n    this.duration = void 0;\n    this.easing = void 0;\n    this.clamp = void 0;\n    this.bounce = void 0;\n    this.decay = void 0;\n    this.round = void 0;\n    Object.assign(this, defaults);\n  }\n}\nfunction mergeConfig(config, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = _extends({}, defaultConfig);\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = _extends({}, defaultConfig, newConfig);\n  }\n  sanitizeConfig(config, newConfig);\n  Object.assign(config, newConfig);\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key];\n    }\n  }\n  let {\n    mass,\n    frequency,\n    damping\n  } = config;\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n  return config;\n}\nfunction sanitizeConfig(config, props) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined;\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction);\n    if (isTensionConfig || !is.und(props.frequency) || !is.und(props.damping) || !is.und(props.mass)) {\n      config.duration = undefined;\n      config.decay = undefined;\n    }\n    if (isTensionConfig) {\n      config.frequency = undefined;\n    }\n  }\n}\nconst emptyArray = [];\nclass Animation {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.to = void 0;\n    this.from = void 0;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n}\nfunction scheduleProps(callId, {\n  key,\n  props,\n  defaultProps,\n  state,\n  actions\n}) {\n  return new Promise((resolve, reject) => {\n    var _props$cancel;\n    let delay;\n    let timeout;\n    let cancel = matchProp((_props$cancel = props.cancel) != null ? _props$cancel : defaultProps == null ? void 0 : defaultProps.cancel, key);\n    if (cancel) {\n      onStart();\n    } else {\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n      let pause = defaultProps == null ? void 0 : defaultProps.pause;\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n      delay = callProp(props.delay || 0, key);\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - raf.now();\n    }\n    function onResume() {\n      if (delay > 0 && !Globals.skipAnimation) {\n        state.delayed = true;\n        timeout = raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n      try {\n        actions.start(_extends({}, props, {\n          callId,\n          cancel\n        }), resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\nconst getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some(result => result.cancelled) ? getCancelledResult(target.get()) : results.every(result => result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every(result => result.finished));\nconst getNoopResult = value => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nconst getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nconst getCancelledResult = value => ({\n  value,\n  cancelled: true,\n  finished: false\n});\nfunction runAsync(to, props, state, target) {\n  const {\n    callId,\n    parentId,\n    onRest\n  } = props;\n  const {\n    asyncTo: prevTo,\n    promise: prevPromise\n  } = state;\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise;\n  }\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to;\n    const defaultProps = getDefaultProps(props, (value, key) => key === 'onRest' ? undefined : value);\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise((resolve, reject) => (preventBail = resolve, bail = reject));\n    const bailIfEnded = bailSignal => {\n      const bailResult = callId <= (state.cancelId || 0) && getCancelledResult(target) || callId !== state.asyncId && getFinishedResult(target, false);\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (Globals.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n        bailIfEnded(bailSignal);\n        const props = is.obj(arg1) ? _extends({}, arg1) : _extends({}, arg2, {\n          to: arg1\n        });\n        props.parentId = callId;\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value;\n          }\n        });\n        const result = await target.start(props);\n        bailIfEnded(bailSignal);\n        if (state.paused) {\n          await new Promise(resume => {\n            state.resumeQueue.add(resume);\n          });\n        }\n        return result;\n      })();\n    };\n    let result;\n    if (Globals.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n    try {\n      let animating;\n      if (is.arr(to)) {\n        animating = (async queue => {\n          for (const props of queue) {\n            await animate(props);\n          }\n        })(to);\n      } else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)));\n      }\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : undefined;\n        state.promise = parentId ? prevPromise : undefined;\n      }\n    }\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  flush(state.timeouts, t => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = undefined;\n  if (cancelId) state.cancelId = cancelId;\n}\nclass BailSignal extends Error {\n  constructor() {\n    super('An async animation has been interrupted. You see this error because you ' + 'forgot to use `await` or `.catch(...)` on its returned promise.');\n    this.result = void 0;\n  }\n}\nclass SkipAnimationSignal extends Error {\n  constructor() {\n    super('SkipAnimationSignal');\n    this.result = void 0;\n  }\n}\nconst isFrameValue = value => value instanceof FrameValue;\nlet nextId$1 = 1;\nclass FrameValue extends FluidValue {\n  constructor(...args) {\n    super(...args);\n    this.id = nextId$1++;\n    this.key = void 0;\n    this._priority = 0;\n  }\n  get priority() {\n    return this._priority;\n  }\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n      this._onPriorityChange(priority);\n    }\n  }\n  get() {\n    const node = getAnimated(this);\n    return node && node.getValue();\n  }\n  to(...args) {\n    return Globals.to(this, args);\n  }\n  interpolate(...args) {\n    deprecateInterpolate();\n    return Globals.to(this, args);\n  }\n  toJSON() {\n    return this.get();\n  }\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n  _attach() {}\n  _detach() {}\n  _onChange(value, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle\n    });\n  }\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      frameLoop.sort(this);\n    }\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority\n    });\n  }\n}\nconst $P = Symbol.for('SpringPhase');\nconst HAS_ANIMATED = 1;\nconst IS_ANIMATING = 2;\nconst IS_PAUSED = 4;\nconst hasAnimated = target => (target[$P] & HAS_ANIMATED) > 0;\nconst isAnimating = target => (target[$P] & IS_ANIMATING) > 0;\nconst isPaused = target => (target[$P] & IS_PAUSED) > 0;\nconst setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nconst setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\nclass SpringValue extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    this.key = void 0;\n    this.animation = new Animation();\n    this.queue = void 0;\n    this.defaultProps = {};\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: new Set(),\n      resumeQueue: new Set(),\n      timeouts: new Set()\n    };\n    this._pendingCalls = new Set();\n    this._lastCallId = 0;\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? _extends({}, arg1) : _extends({}, arg2, {\n        from: arg1\n      });\n      if (is.und(props.default)) {\n        props.default = true;\n      }\n      this.start(props);\n    }\n  }\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n  get goal() {\n    return getFluidValue(this.animation.to);\n  }\n  get velocity() {\n    const node = getAnimated(this);\n    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map(node => node.lastVelocity || 0);\n  }\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n  get isAnimating() {\n    return isAnimating(this);\n  }\n  get isPaused() {\n    return isPaused(this);\n  }\n  get isDelayed() {\n    return this._state.delayed;\n  }\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let {\n      config,\n      toValues\n    } = anim;\n    const payload = getPayload(anim.to);\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to));\n    }\n    anim.values.forEach((node, i) => {\n      if (node.done) return;\n      const to = node.constructor == AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i];\n      let finished = anim.immediate;\n      let position = to;\n      if (!finished) {\n        position = node.lastPosition;\n        if (config.tension <= 0) {\n          node.done = true;\n          return;\n        }\n        let elapsed = node.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node.v0 != null ? node.v0 : node.v0 = is.arr(config.velocity) ? config.velocity[i] : config.velocity;\n        let velocity;\n        const precision = config.precision || (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001));\n        if (!is.und(config.duration)) {\n          let p = 1;\n          if (config.duration > 0) {\n            if (this._memoizedDuration !== config.duration) {\n              this._memoizedDuration = config.duration;\n              if (node.durationProgress > 0) {\n                node.elapsedTime = config.duration * node.durationProgress;\n                elapsed = node.elapsedTime += dt;\n              }\n            }\n            p = (config.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node.durationProgress = p;\n          }\n          position = from + config.easing(p) * (to - from);\n          velocity = (position - node.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity;\n          const restVelocity = config.restVelocity || precision / 10;\n          const bounceFactor = config.clamp ? 0 : config.bounce;\n          const canBounce = !is.und(bounceFactor);\n          const isGrowing = from == to ? node.v0 > 0 : from < to;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision;\n              if (finished) {\n                break;\n              }\n            }\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing;\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to;\n              }\n            }\n            const springForce = -config.tension * 0.000001 * (position - to);\n            const dampingForce = -config.friction * 0.001 * velocity;\n            const acceleration = (springForce + dampingForce) / config.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n        node.lastVelocity = velocity;\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n      if (finished) {\n        node.done = true;\n      } else {\n        idle = false;\n      }\n      if (node.setValue(position, config.round)) {\n        changed = true;\n      }\n    });\n    const node = getAnimated(this);\n    const currVal = node.getValue();\n    if (idle) {\n      const finalVal = getFluidValue(anim.to);\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        node.setValue(finalVal);\n        this._onChange(finalVal);\n      } else if (changed && config.decay) {\n        this._onChange(currVal);\n      }\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n  set(value) {\n    raf.batchedUpdates(() => {\n      this._stop();\n      this._focus(value);\n      this._set(value);\n    });\n    return this;\n  }\n  pause() {\n    this._update({\n      pause: true\n    });\n  }\n  resume() {\n    this._update({\n      pause: false\n    });\n  }\n  finish() {\n    if (isAnimating(this)) {\n      const {\n        to,\n        config\n      } = this.animation;\n      raf.batchedUpdates(() => {\n        this._onStart();\n        if (!config.decay) {\n          this._set(to, false);\n        }\n        this._stop();\n      });\n    }\n    return this;\n  }\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n  start(to, arg2) {\n    let queue;\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : _extends({}, arg2, {\n        to\n      })];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n    return Promise.all(queue.map(props => {\n      const up = this._update(props);\n      return up;\n    })).then(results => getCombinedResult(this, results));\n  }\n  stop(cancel) {\n    const {\n      to\n    } = this.animation;\n    this._focus(this.get());\n    stopAsync(this._state, cancel && this._lastCallId);\n    raf.batchedUpdates(() => this._stop(to, cancel));\n    return this;\n  }\n  reset() {\n    this._update({\n      reset: true\n    });\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      this._start();\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1;\n    }\n  }\n  _prepareNode(props) {\n    const key = this.key || '';\n    let {\n      to,\n      from\n    } = props;\n    to = is.obj(to) ? to[key] : to;\n    if (to == null || isAsyncTo(to)) {\n      to = undefined;\n    }\n    from = is.obj(from) ? from[key] : from;\n    if (from == null) {\n      from = undefined;\n    }\n    const range = {\n      to,\n      from\n    };\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to];\n      from = getFluidValue(from);\n      if (!is.und(from)) {\n        this._set(from);\n      } else if (!getAnimated(this)) {\n        this._set(to);\n      }\n    }\n    return range;\n  }\n  _update(_ref, isLoop) {\n    let props = _extends({}, _ref);\n    const {\n      key,\n      defaultProps\n    } = this;\n    if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value));\n    mergeActiveFn(this, props, 'onProps');\n    sendEvent(this, 'onProps', props, this);\n    const range = this._prepareNode(props);\n    if (Object.isFrozen(this)) {\n      throw Error('Cannot animate a `SpringValue` object that is frozen. ' + 'Did you forget to pass your component to `animated(...)` before animating its props?');\n    }\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            flushCalls(state.pauseQueue);\n            sendEvent(this, 'onPause', getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n            if (isAnimating(this)) {\n              this._resume();\n            }\n            flushCalls(state.resumeQueue);\n            sendEvent(this, 'onResume', getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n      return result;\n    });\n  }\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n    const hasToProp = !is.und(range.to);\n    const hasFromProp = !is.und(range.from);\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n    const {\n      key,\n      defaultProps,\n      animation: anim\n    } = this;\n    const {\n      to: prevTo,\n      from: prevFrom\n    } = anim;\n    let {\n      to = prevTo,\n      from = prevFrom\n    } = range;\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from;\n    }\n    if (props.reverse) [to, from] = [from, to];\n    const hasFromChanged = !isEqual(from, prevFrom);\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n    from = getFluidValue(from);\n    const hasToChanged = !isEqual(to, prevTo);\n    if (hasToChanged) {\n      this._focus(to);\n    }\n    const hasAsyncTo = isAsyncTo(props.to);\n    const {\n      config\n    } = anim;\n    const {\n      decay,\n      velocity\n    } = config;\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0;\n    }\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(config, callProp(props.config, key), props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);\n    }\n    let node = getAnimated(this);\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true));\n    }\n    const reset = is.und(props.reset) ? hasFromProp && !props.default : !is.und(from) && matchProp(props.reset, key);\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to);\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to);\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else throw Error(`Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`);\n      }\n    }\n    const goalType = node.constructor;\n    let started = hasFluidValue(to);\n    let finished = false;\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal);\n        started = !finished;\n      }\n      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config.decay, decay) || !isEqual(config.velocity, velocity)) {\n        started = true;\n      }\n    }\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n    if (!hasAsyncTo) {\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = hasFluidValue(to) ? null : goalType == AnimatedString ? [1] : toArray(goal);\n      }\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n      if (started) {\n        const {\n          onRest\n        } = anim;\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        flushCalls(this._pendingCalls, result);\n        this._pendingCalls.add(resolve);\n        if (anim.changed) raf.batchedUpdates(() => {\n          anim.changed = !reset;\n          onRest == null ? void 0 : onRest(result, this);\n          if (reset) {\n            callProp(defaultProps.onRest, result);\n          } else {\n            anim.onStart == null ? void 0 : anim.onStart(result, this);\n          }\n        });\n      }\n    }\n    if (reset) {\n      this._set(value);\n    }\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n  _focus(value) {\n    const anim = this.animation;\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach();\n      }\n      anim.to = value;\n      if (getFluidObservers(this)) {\n        this._attach();\n      }\n    }\n  }\n  _attach() {\n    let priority = 0;\n    const {\n      to\n    } = this.animation;\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this);\n      if (isFrameValue(to)) {\n        priority = to.priority + 1;\n      }\n    }\n    this.priority = priority;\n  }\n  _detach() {\n    const {\n      to\n    } = this.animation;\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this);\n    }\n  }\n  _set(arg, idle = true) {\n    const value = getFluidValue(arg);\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this);\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        const nodeType = getAnimatedType(value);\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n    return getAnimated(this);\n  }\n  _onStart() {\n    const anim = this.animation;\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(this, 'onStart', getFinishedResult(this, checkFinished(this, anim.to)), this);\n    }\n  }\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n      callProp(this.animation.onChange, value, this);\n    }\n    callProp(this.defaultProps.onChange, value, this);\n    super._onChange(value, idle);\n  }\n  _start() {\n    const anim = this.animation;\n    getAnimated(this).reset(getFluidValue(anim.to));\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition);\n    }\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n  _resume() {\n    if (Globals.skipAnimation) {\n      this.finish();\n    } else {\n      frameLoop.start(this);\n    }\n  }\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      each(anim.values, node => {\n        node.done = true;\n      });\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined;\n      }\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal != null ? goal : anim.to));\n      flushCalls(this._pendingCalls, result);\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, 'onRest', result, this);\n      }\n    }\n  }\n}\nfunction checkFinished(target, to) {\n  const goal = computeGoal(to);\n  const value = computeGoal(target.get());\n  return isEqual(value, goal);\n}\nfunction createLoopUpdate(props, loop = props.loop, to = props.to) {\n  let loopRet = callProp(loop);\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate(_extends({}, props, {\n      loop,\n      default: false,\n      pause: undefined,\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n      from: reset ? props.from : undefined,\n      reset\n    }, overrides));\n  }\n}\nfunction createUpdate(props) {\n  const {\n    to,\n    from\n  } = props = inferTo(props);\n  const keys = new Set();\n  if (is.obj(to)) findDefined(to, keys);\n  if (is.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update = createUpdate(props);\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update);\n  }\n  return update;\n}\nfunction findDefined(values, keys) {\n  eachProp(values, (value, key) => value != null && keys.add(key));\n}\nconst ACTIVE_EVENTS = ['onStart', 'onRest', 'onChange', 'onPause', 'onResume'];\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : undefined;\n}\nfunction sendEvent(target, type, ...args) {\n  var _target$animation$typ, _target$animation, _target$defaultProps$, _target$defaultProps;\n  (_target$animation$typ = (_target$animation = target.animation)[type]) == null ? void 0 : _target$animation$typ.call(_target$animation, ...args);\n  (_target$defaultProps$ = (_target$defaultProps = target.defaultProps)[type]) == null ? void 0 : _target$defaultProps$.call(_target$defaultProps, ...args);\n}\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'];\nlet nextId = 1;\nclass Controller {\n  constructor(props, flush) {\n    this.id = nextId++;\n    this.springs = {};\n    this.queue = [];\n    this.ref = void 0;\n    this._flush = void 0;\n    this._initialProps = void 0;\n    this._lastAsyncId = 0;\n    this._active = new Set();\n    this._changed = new Set();\n    this._started = false;\n    this._item = void 0;\n    this._state = {\n      paused: false,\n      pauseQueue: new Set(),\n      resumeQueue: new Set(),\n      timeouts: new Set()\n    };\n    this._events = {\n      onStart: new Map(),\n      onChange: new Map(),\n      onRest: new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n    if (flush) {\n      this._flush = flush;\n    }\n    if (props) {\n      this.start(_extends({\n        default: true\n      }, props));\n    }\n  }\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every(spring => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n  get item() {\n    return this._item;\n  }\n  set item(item) {\n    this._item = item;\n  }\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n      if (!is.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n    return this;\n  }\n  start(props) {\n    let {\n      queue\n    } = this;\n    if (props) {\n      queue = toArray(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n    if (keys) {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each(spring => spring.stop(!!arg));\n    }\n    return this;\n  }\n  pause(keys) {\n    if (is.und(keys)) {\n      this.start({\n        pause: true\n      });\n    } else {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].pause());\n    }\n    return this;\n  }\n  resume(keys) {\n    if (is.und(keys)) {\n      this.start({\n        pause: false\n      });\n    } else {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].resume());\n    }\n    return this;\n  }\n  each(iterator) {\n    eachProp(this.springs, iterator);\n  }\n  _onFrame() {\n    const {\n      onStart,\n      onChange,\n      onRest\n    } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get();\n        onStart(result, this, this._item);\n      });\n    }\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values;\n        onChange(result, this, this._item);\n      });\n    }\n    if (idle) {\n      this._started = false;\n      flush(onRest, ([onRest, result]) => {\n        result.value = values;\n        onRest(result, this, this._item);\n      });\n    }\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent);\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent);\n    } else return;\n    raf.onFrame(this._onFrame);\n  }\n}\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(results => getCombinedResult(ctrl, results));\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const {\n    keys,\n    to,\n    from,\n    loop,\n    onRest,\n    onResolve\n  } = props;\n  const defaults = is.obj(props.default) && props.default;\n  if (loop) {\n    props.loop = false;\n  }\n  if (to === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined;\n  if (asyncTo) {\n    props.to = undefined;\n    props.onRest = undefined;\n    if (defaults) {\n      defaults.onRest = undefined;\n    }\n  } else {\n    each(BATCHED_EVENTS, key => {\n      const handler = props[key];\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key];\n        props[key] = ({\n          finished,\n          cancelled\n        }) => {\n          const result = queue.get(handler);\n          if (result) {\n            if (!finished) result.finished = false;\n            if (cancelled) result.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n        if (defaults) {\n          defaults[key] = props[key];\n        }\n      }\n    });\n  }\n  const state = ctrl['_state'];\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n  const promises = (keys || Object.keys(ctrl.springs)).map(key => ctrl.springs[key].start(props));\n  const cancel = props.cancel === true || getDefaultProp(props, 'cancel') === true;\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(scheduleProps(++ctrl['_lastAsyncId'], {\n      props,\n      state,\n      actions: {\n        pause: noop,\n        resume: noop,\n        start(props, resolve) {\n          if (cancel) {\n            stopAsync(state, ctrl['_lastAsyncId']);\n            resolve(getCancelledResult(ctrl));\n          } else {\n            props.onRest = onRest;\n            resolve(runAsync(asyncTo, props, state, ctrl));\n          }\n        }\n      }\n    }));\n  }\n  if (state.paused) {\n    await new Promise(resume => {\n      state.resumeQueue.add(resume);\n    });\n  }\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to);\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = _extends({}, ctrl.springs);\n  if (props) {\n    each(toArray(props), props => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props);\n      }\n      if (!is.obj(props.to)) {\n        props = _extends({}, props, {\n          to: undefined\n        });\n      }\n      prepareSprings(springs, props, key => {\n        return createSpring(key);\n      });\n    });\n  }\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      addFluidObserver(spring, ctrl);\n    }\n  });\n}\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n  if (observer) {\n    addFluidObserver(spring, observer);\n  }\n  return spring;\n}\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring['_prepareNode'](props);\n    });\n  }\n}\nfunction prepareKeys(ctrl, queue) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nconst _excluded$6 = [\"children\"];\nconst SpringContext = _ref => {\n  let {\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded$6);\n  const inherited = useContext(ctx);\n  const pause = props.pause || !!inherited.pause,\n    immediate = props.immediate || !!inherited.immediate;\n  props = useMemoOne(() => ({\n    pause,\n    immediate\n  }), [pause, immediate]);\n  const {\n    Provider\n  } = ctx;\n  return React.createElement(Provider, {\n    value: props\n  }, children);\n};\nconst ctx = makeContext(SpringContext, {});\nSpringContext.Provider = ctx.Provider;\nSpringContext.Consumer = ctx.Consumer;\nfunction makeContext(target, init) {\n  Object.assign(target, React.createContext(init));\n  target.Provider._context = target;\n  target.Consumer._context = target;\n  return target;\n}\nconst SpringRef = () => {\n  const current = [];\n  const SpringRef = function SpringRef(props) {\n    deprecateDirectCall();\n    const results = [];\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update = _getProps(props, ctrl, i);\n        if (update) {\n          results.push(ctrl.start(update));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef.current = current;\n  SpringRef.add = function (ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n  SpringRef.delete = function (ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments));\n    return this;\n  };\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments));\n    return this;\n  };\n  SpringRef.set = function (values) {\n    each(current, ctrl => ctrl.set(values));\n  };\n  SpringRef.start = function (props) {\n    const results = [];\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update = this._getProps(props, ctrl, i);\n        if (update) {\n          results.push(ctrl.start(update));\n        }\n      }\n    });\n    return results;\n  };\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments));\n    return this;\n  };\n  SpringRef.update = function (props) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n  const _getProps = function _getProps(arg, ctrl, index) {\n    return is.fun(arg) ? arg(index, ctrl) : arg;\n  };\n  SpringRef._getProps = _getProps;\n  return SpringRef;\n};\nfunction useSprings(length, props, deps) {\n  const propsFn = is.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const layoutId = useRef(0);\n  const forceUpdate = useForceUpdate();\n  const state = useMemo(() => ({\n    ctrls: [],\n    queue: [],\n    flush(ctrl, updates) {\n      const springs = getSprings(ctrl, updates);\n      const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs).some(key => !ctrl.springs[key]);\n      return canFlushSync ? flushUpdateQueue(ctrl, updates) : new Promise(resolve => {\n        setSprings(ctrl, springs);\n        state.queue.push(() => {\n          resolve(flushUpdateQueue(ctrl, updates));\n        });\n        forceUpdate();\n      });\n    }\n  }), []);\n  const ctrls = useRef([...state.ctrls]);\n  const updates = [];\n  const prevLength = usePrev(length) || 0;\n  useMemo(() => {\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update = propsFn ? propsFn(i, ctrl) : props[i];\n      if (update) {\n        updates[i] = declareUpdate(update);\n      }\n    }\n  }\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]));\n  const context = useContext(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const {\n      queue\n    } = state;\n    if (queue.length) {\n      state.queue = [];\n      each(queue, cb => cb());\n    }\n    each(ctrls.current, (ctrl, i) => {\n      ref == null ? void 0 : ref.add(ctrl);\n      if (hasContext) {\n        ctrl.start({\n          default: context\n        });\n      }\n      const update = updates[i];\n      if (update) {\n        replaceRef(ctrl, update.ref);\n        if (ctrl.ref) {\n          ctrl.queue.push(update);\n        } else {\n          ctrl.start(update);\n        }\n      }\n    });\n  });\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true));\n  });\n  const values = springs.map(x => _extends({}, x));\n  return ref ? [values, ref] : values;\n}\nfunction useSpring(props, deps) {\n  const isFn = is.fun(props);\n  const [[values], ref] = useSprings(1, isFn ? props : [props], isFn ? deps || [] : deps);\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\nconst initSpringRef = () => SpringRef();\nconst useSpringRef = () => useState(initSpringRef)[0];\nconst useSpringValue = (initial, props) => {\n  const springValue = useConstant(() => new SpringValue(initial, props));\n  useOnce(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = is.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = undefined;\n  const result = useSprings(length, (i, ctrl) => {\n    const props = propsFn ? propsFn(i, ctrl) : propsArg;\n    passedRef = props.ref;\n    reverse = reverse && props.reverse;\n    return props;\n  }, deps || [{}]);\n  useIsomorphicLayoutEffect(() => {\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({\n            to: parent.springs\n          });\n        }\n        return;\n      }\n      if (parent) {\n        ctrl.start({\n          to: parent.springs\n        });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n  if (propsFn || arguments.length == 3) {\n    var _passedRef;\n    const ref = (_passedRef = passedRef) != null ? _passedRef : result[1];\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg;\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n    return result;\n  }\n  return result[0];\n}\nlet TransitionPhase;\n(function (TransitionPhase) {\n  TransitionPhase[\"MOUNT\"] = \"mount\";\n  TransitionPhase[\"ENTER\"] = \"enter\";\n  TransitionPhase[\"UPDATE\"] = \"update\";\n  TransitionPhase[\"LEAVE\"] = \"leave\";\n})(TransitionPhase || (TransitionPhase = {}));\nfunction useTransition(data, props, deps) {\n  const propsFn = is.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const items = toArray(data);\n  const transitions = [];\n  const usedTransitions = useRef(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions;\n  });\n  useOnce(() => {\n    each(transitions, t => {\n      ref == null ? void 0 : ref.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      each(usedTransitions.current, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  useIsomorphicLayoutEffect(() => each(expired, ({\n    ctrl,\n    item,\n    key\n  }) => {\n    detachRefs(ctrl, ref);\n    callProp(onDestroyed, item, key);\n  }));\n  const reused = [];\n  if (prevTransitions) each(prevTransitions, (t, i) => {\n    if (t.expired) {\n      clearTimeout(t.expirationId);\n      expired.push(t);\n    } else {\n      i = reused[i] = keys.indexOf(t.key);\n      if (~i) transitions[i] = t;\n    }\n  });\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n  if (reused.length) {\n    let i = -1;\n    const {\n      leave\n    } = propsFn ? propsFn() : props;\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = _extends({}, t, {\n          item: items[keyIndex]\n        });\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n  let delay = -trail;\n  const forceUpdate = useForceUpdate();\n  const defaultProps = getDefaultProps(props);\n  const changes = new Map();\n  const exitingTransitions = useRef(new Map());\n  const forceChange = useRef(false);\n  each(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to;\n    let phase;\n    let propsDelay = callProp(p.delay || 0, key);\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter;\n      phase = TransitionPhase.ENTER;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave;\n          phase = TransitionPhase.LEAVE;\n        } else if (to = p.update) {\n          phase = TransitionPhase.UPDATE;\n        } else return;\n      } else if (!isLeave) {\n        to = p.enter;\n        phase = TransitionPhase.ENTER;\n      } else return;\n    }\n    to = callProp(to, t.item, i);\n    to = is.obj(to) ? inferTo(to) : {\n      to\n    };\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config;\n      to.config = callProp(config, t.item, i, phase);\n    }\n    delay += trail;\n    const payload = _extends({}, defaultProps, {\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      reset: false\n    }, to);\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const _p = propsFn ? propsFn() : props;\n      const from = is.und(_p.initial) || prevTransitions ? _p.from : _p.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n    const {\n      onResolve\n    } = payload;\n    payload.onResolve = result => {\n      callProp(onResolve, result);\n      const transitions = usedTransitions.current;\n      const t = transitions.find(t => t.key === key);\n      if (!t) return;\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        return;\n      }\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle);\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item);\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t.expired = true;\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 0x7fffffff) t.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n        if (idle && transitions.some(t => t.expired)) {\n          exitingTransitions.current.delete(t);\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n          forceUpdate();\n        }\n      }\n    };\n    const springs = getSprings(t.ctrl, payload);\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    } else {\n      changes.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    }\n  });\n  const context = useContext(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({\n          default: context\n        });\n      });\n    }\n  }, [context]);\n  each(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  useIsomorphicLayoutEffect(() => {\n    each(exitingTransitions.current.size ? exitingTransitions.current : changes, ({\n      phase,\n      payload\n    }, t) => {\n      const {\n        ctrl\n      } = t;\n      t.phase = phase;\n      ref == null ? void 0 : ref.add(ctrl);\n      if (hasContext && phase == TransitionPhase.ENTER) {\n        ctrl.start({\n          default: context\n        });\n      }\n      if (payload) {\n        replaceRef(ctrl, payload.ref);\n        if ((ctrl.ref || ref) && !forceChange.current) {\n          ctrl.update(payload);\n        } else {\n          ctrl.start(payload);\n          if (forceChange.current) {\n            forceChange.current = false;\n          }\n        }\n      }\n    });\n  }, reset ? void 0 : deps);\n  const renderTransitions = render => React.createElement(React.Fragment, null, transitions.map((t, i) => {\n    const {\n      springs\n    } = changes.get(t) || t.ctrl;\n    const elem = render(_extends({}, springs), t.item, t, i);\n    return elem && elem.type ? React.createElement(elem.type, _extends({}, elem.props, {\n      key: is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id,\n      ref: elem.ref\n    })) : elem;\n  }));\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nlet nextKey = 1;\nfunction getKeys(items, {\n  key,\n  keys = key\n}, prevTransitions) {\n  if (keys === null) {\n    const reused = new Set();\n    return items.map(item => {\n      const t = prevTransitions && prevTransitions.find(t => t.item === item && t.phase !== TransitionPhase.LEAVE && !reused.has(t));\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n      return nextKey++;\n    });\n  }\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys);\n}\nconst _excluded$5 = [\"container\"];\nconst useScroll = (_ref = {}) => {\n  let {\n      container\n    } = _ref,\n    springOptions = _objectWithoutPropertiesLoose(_ref, _excluded$5);\n  const [scrollValues, api] = useSpring(() => _extends({\n    scrollX: 0,\n    scrollY: 0,\n    scrollXProgress: 0,\n    scrollYProgress: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(({\n      x,\n      y\n    }) => {\n      api.start({\n        scrollX: x.current,\n        scrollXProgress: x.progress,\n        scrollY: y.current,\n        scrollYProgress: y.progress\n      });\n    }, {\n      container: (container == null ? void 0 : container.current) || undefined\n    });\n    return () => {\n      each(Object.values(scrollValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\nconst _excluded$4 = [\"container\"];\nconst useResize = _ref => {\n  let {\n      container\n    } = _ref,\n    springOptions = _objectWithoutPropertiesLoose(_ref, _excluded$4);\n  const [sizeValues, api] = useSpring(() => _extends({\n    width: 0,\n    height: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(({\n      width,\n      height\n    }) => {\n      api.start({\n        width,\n        height,\n        immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n      });\n    }, {\n      container: (container == null ? void 0 : container.current) || undefined\n    });\n    return () => {\n      each(Object.values(sizeValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\nconst _excluded$3 = [\"to\", \"from\"],\n  _excluded2 = [\"root\", \"once\", \"amount\"];\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = useState(false);\n  const ref = useRef();\n  const propsFn = is.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n  const {\n      to = {},\n      from = {}\n    } = springsProps,\n    restSpringProps = _objectWithoutPropertiesLoose(springsProps, _excluded$3);\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => _extends({\n    from\n  }, restSpringProps), []);\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current;\n    const _ref = intersectionArguments != null ? intersectionArguments : {},\n      {\n        root,\n        once,\n        amount = 'any'\n      } = _ref,\n      restArgs = _objectWithoutPropertiesLoose(_ref, _excluded2);\n    if (!element || once && isInView || typeof IntersectionObserver === 'undefined') return;\n    const activeIntersections = new WeakMap();\n    const onEnter = () => {\n      if (to) {\n        api.start(to);\n      }\n      setIsInView(true);\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n        setIsInView(false);\n      };\n      return once ? undefined : cleanup;\n    };\n    const handleIntersection = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target);\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n    const observer = new IntersectionObserver(handleIntersection, _extends({\n      root: root && root.current || undefined,\n      threshold: typeof amount === 'number' || Array.isArray(amount) ? amount : defaultThresholdOptions[amount]\n    }, restArgs));\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n  if (propsFn) {\n    return [ref, springs];\n  }\n  return [ref, isInView];\n}\nconst _excluded$2 = [\"children\"];\nfunction Spring(_ref) {\n  let {\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n  return children(useSpring(props));\n}\nconst _excluded$1 = [\"items\", \"children\"];\nfunction Trail(_ref) {\n  let {\n      items,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return is.fun(result) ? result(trails[index]) : result;\n  });\n}\nconst _excluded = [\"items\", \"children\"];\nfunction Transition(_ref) {\n  let {\n      items,\n      children\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  return useTransition(items, props)(children);\n}\nclass Interpolation extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.key = void 0;\n    this.idle = true;\n    this.calc = void 0;\n    this._active = new Set();\n    this.source = source;\n    this.calc = createInterpolator(...args);\n    const value = this._get();\n    const nodeType = getAnimatedType(value);\n    setAnimated(this, nodeType.create(value));\n  }\n  advance(_dt) {\n    const value = this._get();\n    const oldValue = this.get();\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this).setValue(value);\n      this._onChange(value, this.idle);\n    }\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n  _get() {\n    const inputs = is.arr(this.source) ? this.source.map(getFluidValue) : toArray(getFluidValue(this.source));\n    return this.calc(...inputs);\n  }\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      each(getPayload(this), node => {\n        node.done = false;\n      });\n      if (Globals.skipAnimation) {\n        raf.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        frameLoop.start(this);\n      }\n    }\n  }\n  _attach() {\n    let priority = 1;\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this);\n      }\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n    this._start();\n  }\n  _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this);\n      }\n    });\n    this._active.clear();\n    becomeIdle(this);\n  }\n  eventObserved(event) {\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n        this._start();\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent);\n    } else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce((highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);\n    }\n  }\n}\nfunction isIdle(source) {\n  return source.idle !== false;\n}\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    each(getPayload(self), node => {\n      node.done = true;\n    });\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self\n    });\n  }\n}\nconst to = (source, ...args) => new Interpolation(source, args);\nconst interpolate = (source, ...args) => (deprecateInterpolate(), new Interpolation(source, args));\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nconst update = frameLoop.advance;\nexport { BailSignal, Controller, FrameValue, Interpolation, Spring, SpringContext, SpringRef, SpringValue, Trail, Transition, config, inferTo, interpolate, to, update, useChain, useInView, useResize, useScroll, useSpring, useSpringRef, useSpringValue, useSprings, useTrail, useTransition };", "map": {"version": 3, "names": ["eachProp", "is", "toArray", "getFluidValue", "isAnimatedString", "Globals", "useIsomorphicLayoutEffect", "each", "easings", "raf", "flush", "FluidValue", "deprecateInterpolate", "callFluidObservers", "frameLoop", "hasFluidValue", "flushCalls", "isEqual", "getFluidObservers", "addFluidObserver", "removeFluidObserver", "noop", "useMemoOne", "deprecateDirectCall", "useForceUpdate", "usePrev", "useOnce", "useConstant", "onScroll", "onResize", "createInterpolator", "createStringInterpolator", "useReducedMotion", "React", "useContext", "useMemo", "useRef", "useState", "getAnimated", "AnimatedValue", "getPayload", "AnimatedString", "getAnimatedType", "setAnimated", "_extends", "Object", "assign", "bind", "target", "i", "arguments", "length", "source", "key", "prototype", "hasOwnProperty", "call", "apply", "callProp", "value", "args", "fun", "matchProp", "includes", "resolveProp", "prop", "obj", "getDefaultProp", "props", "default", "undefined", "noopTransform", "getDefaultProps", "transform", "keys", "DEFAULT_PROPS", "defaults", "und", "RESERVED_PROPS", "config", "from", "to", "ref", "loop", "reset", "pause", "cancel", "reverse", "immediate", "delay", "onProps", "onStart", "onChange", "onPause", "onResume", "onRest", "onResolve", "items", "trail", "sort", "expires", "initial", "enter", "update", "leave", "children", "onDestroyed", "callId", "parentId", "getForwardProps", "forward", "count", "inferTo", "out", "val", "computeGoal", "arr", "map", "range", "output", "hasProps", "_", "isAsyncTo", "detachRefs", "ctrl", "_ctrl$ref", "delete", "replaceRef", "_ctrl$ref2", "add", "<PERSON><PERSON><PERSON>n", "refs", "timeSteps", "timeFrame", "prev<PERSON><PERSON><PERSON>", "controllers", "current", "isNaN", "queue", "memoizedDelayProp", "start", "p", "Promise", "resolve", "queues", "q", "then", "push", "all", "tension", "friction", "gentle", "wobbly", "stiff", "slow", "molasses", "mass", "damping", "easing", "linear", "clamp", "AnimationConfig", "constructor", "frequency", "velocity", "restVelocity", "precision", "progress", "duration", "bounce", "decay", "round", "mergeConfig", "newConfig", "defaultConfig", "sanitizeConfig", "Math", "pow", "PI", "isTensionConfig", "emptyArray", "Animation", "changed", "values", "to<PERSON><PERSON><PERSON>", "fromValues", "scheduleProps", "defaultProps", "state", "actions", "reject", "_props$cancel", "timeout", "paused", "resumeQueue", "resume", "timeouts", "time", "now", "skipAnimation", "delayed", "setTimeout", "pauseQueue", "cancelId", "err", "getCombinedResult", "results", "some", "result", "cancelled", "getCancelledResult", "get", "every", "getNoopResult", "getFinishedResult", "finished", "runAsync", "asyncTo", "prevTo", "promise", "prevPromise", "asyncId", "preventBail", "bail", "bailPromise", "bailIfEnded", "bailSignal", "bailResult", "animate", "arg1", "arg2", "BailSignal", "skipAnimationSignal", "SkipAnimationSignal", "stopAsync", "animating", "stop", "batchedUpdates", "item", "t", "clear", "Error", "isFrameValue", "FrameValue", "nextId$1", "id", "_priority", "priority", "_onPriorityChange", "node", "getValue", "interpolate", "toJSON", "observerAdded", "_attach", "observerRemoved", "_detach", "_onChange", "idle", "type", "parent", "$P", "Symbol", "for", "HAS_ANIMATED", "IS_ANIMATING", "IS_PAUSED", "hasAnimated", "isAnimating", "isPaused", "setActiveBit", "active", "setPausedBit", "SpringValue", "animation", "_state", "Set", "_pendingCalls", "_lastCallId", "_lastToId", "_memoizedDuration", "goal", "lastVelocity", "<PERSON><PERSON><PERSON><PERSON>", "advance", "dt", "anim", "payload", "for<PERSON>ach", "done", "lastPosition", "position", "elapsed", "elapsedTime", "v0", "min", "abs", "durationProgress", "e", "exp", "bounceFactor", "canBounce", "isGrowing", "isMoving", "isBouncing", "step", "numSteps", "ceil", "n", "springForce", "dampingForce", "acceleration", "Number", "console", "warn", "setValue", "currVal", "finalVal", "_stop", "set", "_focus", "_set", "_update", "finish", "_onStart", "up", "eventObserved", "event", "_start", "_prepareNode", "_ref", "isLoop", "test", "mergeActiveFn", "sendEvent", "isFrozen", "checkFinished", "_resume", "_merge", "nextProps", "createLoopUpdate", "hasToProp", "hasFromProp", "prevFrom", "has<PERSON>romC<PERSON>ed", "hasToChanged", "hasAsyncTo", "isAnimatable", "num", "nodeType", "name", "goalType", "started", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ACTIVE_EVENTS", "arg", "oldNode", "create", "loopRet", "overrides", "createUpdate", "findDefined", "size", "Array", "declareUpdate", "_target$animation$typ", "_target$animation", "_target$defaultProps$", "_target$defaultProps", "BATCHED_EVENTS", "nextId", "Controller", "springs", "_flush", "_initialProps", "_lastAsyncId", "_active", "_changed", "_started", "_item", "_events", "Map", "_onFrame", "spring", "prepare<PERSON>eys", "flushUpdateQueue", "iterator", "onFrame", "flushUpdate", "handler", "promises", "getSprings", "prepareSprings", "createSpring", "setSprings", "observer", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "indexOf", "_excluded$6", "SpringContext", "inherited", "ctx", "Provider", "createElement", "makeContext", "Consumer", "init", "createContext", "_context", "SpringRef", "_getProps", "splice", "index", "useSprings", "deps", "propsFn", "layoutId", "forceUpdate", "ctrls", "updates", "canFlushSync", "prevLength", "slice", "declareUpdates", "startIndex", "endIndex", "context", "prevContext", "hasContext", "cb", "x", "useSpring", "isFn", "initSpringRef", "useSpringRef", "useSpringValue", "springValue", "useTrail", "propsArg", "passedRef", "_passedRef", "TransitionPhase", "useTransition", "data", "exitBeforeEnter", "propsRef", "propsConfig", "transitions", "usedTransitions", "prevTransitions", "expired", "clearTimeout", "expirationId", "get<PERSON><PERSON><PERSON>", "reused", "phase", "MOUNT", "keyIndex", "prevIndex", "a", "b", "changes", "exitingTransitions", "forceChange", "prevPhase", "props<PERSON><PERSON><PERSON>", "ENTER", "isLeave", "LEAVE", "UPDATE", "_p", "find", "expiry", "expiryMs", "ind", "findIndex", "renderTransitions", "render", "Fragment", "elem", "str", "<PERSON><PERSON><PERSON>", "has", "_excluded$5", "useScroll", "container", "springOptions", "scrollValues", "api", "scrollX", "scrollY", "scrollXProgress", "scrollYProgress", "cleanupScroll", "y", "_excluded$4", "useResize", "sizeValues", "width", "height", "_excluded$3", "_excluded2", "defaultThresholdOptions", "any", "useInView", "isInView", "setIsInView", "springsProps", "restSpringProps", "intersectionArguments", "element", "root", "once", "amount", "restArgs", "IntersectionObserver", "activeIntersections", "WeakMap", "onEnter", "cleanup", "handleIntersection", "entries", "entry", "onLeave", "isIntersecting", "Boolean", "newOnLeave", "unobserve", "threshold", "isArray", "observe", "_excluded$2", "Spring", "_excluded$1", "Trail", "trails", "_excluded", "Transition", "Interpolation", "calc", "_get", "_dt", "oldValue", "checkIdle", "becomeIdle", "inputs", "max", "reduce", "highest", "isIdle", "self"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/@react-spring/core/dist/react-spring-core.esm.js"], "sourcesContent": ["import { eachProp, is, toArray, getFluidValue, isAnimatedString, Globals, useIsomorphicLayoutEffect, each, easings, raf, flush, FluidValue, deprecateInterpolate, callFluidObservers, frameLoop, hasFluidValue, flushCalls, isEqual, getFluidObservers, addFluidObserver, removeFluidObserver, noop, useMemoOne, deprecateDirectCall, useForceUpdate, usePrev, useOnce, useConstant, onScroll, onResize, createInterpolator, createStringInterpolator } from '@react-spring/shared';\nexport { Globals, createInterpolator, easings, useIsomorphicLayoutEffect, useReducedMotion } from '@react-spring/shared';\nimport * as React from 'react';\nimport { useContext, useMemo, useRef, useState } from 'react';\nimport { getAnimated, AnimatedValue, getPayload, AnimatedString, getAnimatedType, setAnimated } from '@react-spring/animated';\nexport * from '@react-spring/types/animated';\nexport * from '@react-spring/types/interpolation';\n\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\n\nfunction callProp(value, ...args) {\n  return is.fun(value) ? value(...args) : value;\n}\nconst matchProp = (value, key) => value === true || !!(key && value && (is.fun(value) ? value(key) : toArray(value).includes(key)));\nconst resolveProp = (prop, key) => is.obj(prop) ? key && prop[key] : prop;\nconst getDefaultProp = (props, key) => props.default === true ? props[key] : props.default ? props.default[key] : undefined;\n\nconst noopTransform = value => value;\n\nconst getDefaultProps = (props, transform = noopTransform) => {\n  let keys = DEFAULT_PROPS;\n\n  if (props.default && props.default !== true) {\n    props = props.default;\n    keys = Object.keys(props);\n  }\n\n  const defaults = {};\n\n  for (const key of keys) {\n    const value = transform(props[key], key);\n\n    if (!is.und(value)) {\n      defaults[key] = value;\n    }\n  }\n\n  return defaults;\n};\nconst DEFAULT_PROPS = ['config', 'onProps', 'onStart', 'onChange', 'onPause', 'onResume', 'onRest'];\nconst RESERVED_PROPS = {\n  config: 1,\n  from: 1,\n  to: 1,\n  ref: 1,\n  loop: 1,\n  reset: 1,\n  pause: 1,\n  cancel: 1,\n  reverse: 1,\n  immediate: 1,\n  default: 1,\n  delay: 1,\n  onProps: 1,\n  onStart: 1,\n  onChange: 1,\n  onPause: 1,\n  onResume: 1,\n  onRest: 1,\n  onResolve: 1,\n  items: 1,\n  trail: 1,\n  sort: 1,\n  expires: 1,\n  initial: 1,\n  enter: 1,\n  update: 1,\n  leave: 1,\n  children: 1,\n  onDestroyed: 1,\n  keys: 1,\n  callId: 1,\n  parentId: 1\n};\n\nfunction getForwardProps(props) {\n  const forward = {};\n  let count = 0;\n  eachProp(props, (value, prop) => {\n    if (!RESERVED_PROPS[prop]) {\n      forward[prop] = value;\n      count++;\n    }\n  });\n\n  if (count) {\n    return forward;\n  }\n}\n\nfunction inferTo(props) {\n  const to = getForwardProps(props);\n\n  if (to) {\n    const out = {\n      to\n    };\n    eachProp(props, (val, key) => key in to || (out[key] = val));\n    return out;\n  }\n\n  return _extends({}, props);\n}\nfunction computeGoal(value) {\n  value = getFluidValue(value);\n  return is.arr(value) ? value.map(computeGoal) : isAnimatedString(value) ? Globals.createStringInterpolator({\n    range: [0, 1],\n    output: [value, value]\n  })(1) : value;\n}\nfunction hasProps(props) {\n  for (const _ in props) return true;\n\n  return false;\n}\nfunction isAsyncTo(to) {\n  return is.fun(to) || is.arr(to) && is.obj(to[0]);\n}\nfunction detachRefs(ctrl, ref) {\n  var _ctrl$ref;\n\n  (_ctrl$ref = ctrl.ref) == null ? void 0 : _ctrl$ref.delete(ctrl);\n  ref == null ? void 0 : ref.delete(ctrl);\n}\nfunction replaceRef(ctrl, ref) {\n  if (ref && ctrl.ref !== ref) {\n    var _ctrl$ref2;\n\n    (_ctrl$ref2 = ctrl.ref) == null ? void 0 : _ctrl$ref2.delete(ctrl);\n    ref.add(ctrl);\n    ctrl.ref = ref;\n  }\n}\n\nfunction useChain(refs, timeSteps, timeFrame = 1000) {\n  useIsomorphicLayoutEffect(() => {\n    if (timeSteps) {\n      let prevDelay = 0;\n      each(refs, (ref, i) => {\n        const controllers = ref.current;\n\n        if (controllers.length) {\n          let delay = timeFrame * timeSteps[i];\n          if (isNaN(delay)) delay = prevDelay;else prevDelay = delay;\n          each(controllers, ctrl => {\n            each(ctrl.queue, props => {\n              const memoizedDelayProp = props.delay;\n\n              props.delay = key => delay + callProp(memoizedDelayProp || 0, key);\n            });\n          });\n          ref.start();\n        }\n      });\n    } else {\n      let p = Promise.resolve();\n      each(refs, ref => {\n        const controllers = ref.current;\n\n        if (controllers.length) {\n          const queues = controllers.map(ctrl => {\n            const q = ctrl.queue;\n            ctrl.queue = [];\n            return q;\n          });\n          p = p.then(() => {\n            each(controllers, (ctrl, i) => each(queues[i] || [], update => ctrl.queue.push(update)));\n            return Promise.all(ref.start());\n          });\n        }\n      });\n    }\n  });\n}\n\nconst config = {\n  default: {\n    tension: 170,\n    friction: 26\n  },\n  gentle: {\n    tension: 120,\n    friction: 14\n  },\n  wobbly: {\n    tension: 180,\n    friction: 12\n  },\n  stiff: {\n    tension: 210,\n    friction: 20\n  },\n  slow: {\n    tension: 280,\n    friction: 60\n  },\n  molasses: {\n    tension: 280,\n    friction: 120\n  }\n};\n\nconst defaults = _extends({}, config.default, {\n  mass: 1,\n  damping: 1,\n  easing: easings.linear,\n  clamp: false\n});\n\nclass AnimationConfig {\n  constructor() {\n    this.tension = void 0;\n    this.friction = void 0;\n    this.frequency = void 0;\n    this.damping = void 0;\n    this.mass = void 0;\n    this.velocity = 0;\n    this.restVelocity = void 0;\n    this.precision = void 0;\n    this.progress = void 0;\n    this.duration = void 0;\n    this.easing = void 0;\n    this.clamp = void 0;\n    this.bounce = void 0;\n    this.decay = void 0;\n    this.round = void 0;\n    Object.assign(this, defaults);\n  }\n\n}\nfunction mergeConfig(config, newConfig, defaultConfig) {\n  if (defaultConfig) {\n    defaultConfig = _extends({}, defaultConfig);\n    sanitizeConfig(defaultConfig, newConfig);\n    newConfig = _extends({}, defaultConfig, newConfig);\n  }\n\n  sanitizeConfig(config, newConfig);\n  Object.assign(config, newConfig);\n\n  for (const key in defaults) {\n    if (config[key] == null) {\n      config[key] = defaults[key];\n    }\n  }\n\n  let {\n    mass,\n    frequency,\n    damping\n  } = config;\n\n  if (!is.und(frequency)) {\n    if (frequency < 0.01) frequency = 0.01;\n    if (damping < 0) damping = 0;\n    config.tension = Math.pow(2 * Math.PI / frequency, 2) * mass;\n    config.friction = 4 * Math.PI * damping * mass / frequency;\n  }\n\n  return config;\n}\n\nfunction sanitizeConfig(config, props) {\n  if (!is.und(props.decay)) {\n    config.duration = undefined;\n  } else {\n    const isTensionConfig = !is.und(props.tension) || !is.und(props.friction);\n\n    if (isTensionConfig || !is.und(props.frequency) || !is.und(props.damping) || !is.und(props.mass)) {\n      config.duration = undefined;\n      config.decay = undefined;\n    }\n\n    if (isTensionConfig) {\n      config.frequency = undefined;\n    }\n  }\n}\n\nconst emptyArray = [];\nclass Animation {\n  constructor() {\n    this.changed = false;\n    this.values = emptyArray;\n    this.toValues = null;\n    this.fromValues = emptyArray;\n    this.to = void 0;\n    this.from = void 0;\n    this.config = new AnimationConfig();\n    this.immediate = false;\n  }\n\n}\n\nfunction scheduleProps(callId, {\n  key,\n  props,\n  defaultProps,\n  state,\n  actions\n}) {\n  return new Promise((resolve, reject) => {\n    var _props$cancel;\n\n    let delay;\n    let timeout;\n    let cancel = matchProp((_props$cancel = props.cancel) != null ? _props$cancel : defaultProps == null ? void 0 : defaultProps.cancel, key);\n\n    if (cancel) {\n      onStart();\n    } else {\n      if (!is.und(props.pause)) {\n        state.paused = matchProp(props.pause, key);\n      }\n\n      let pause = defaultProps == null ? void 0 : defaultProps.pause;\n\n      if (pause !== true) {\n        pause = state.paused || matchProp(pause, key);\n      }\n\n      delay = callProp(props.delay || 0, key);\n\n      if (pause) {\n        state.resumeQueue.add(onResume);\n        actions.pause();\n      } else {\n        actions.resume();\n        onResume();\n      }\n    }\n\n    function onPause() {\n      state.resumeQueue.add(onResume);\n      state.timeouts.delete(timeout);\n      timeout.cancel();\n      delay = timeout.time - raf.now();\n    }\n\n    function onResume() {\n      if (delay > 0 && !Globals.skipAnimation) {\n        state.delayed = true;\n        timeout = raf.setTimeout(onStart, delay);\n        state.pauseQueue.add(onPause);\n        state.timeouts.add(timeout);\n      } else {\n        onStart();\n      }\n    }\n\n    function onStart() {\n      if (state.delayed) {\n        state.delayed = false;\n      }\n\n      state.pauseQueue.delete(onPause);\n      state.timeouts.delete(timeout);\n\n      if (callId <= (state.cancelId || 0)) {\n        cancel = true;\n      }\n\n      try {\n        actions.start(_extends({}, props, {\n          callId,\n          cancel\n        }), resolve);\n      } catch (err) {\n        reject(err);\n      }\n    }\n  });\n}\n\nconst getCombinedResult = (target, results) => results.length == 1 ? results[0] : results.some(result => result.cancelled) ? getCancelledResult(target.get()) : results.every(result => result.noop) ? getNoopResult(target.get()) : getFinishedResult(target.get(), results.every(result => result.finished));\nconst getNoopResult = value => ({\n  value,\n  noop: true,\n  finished: true,\n  cancelled: false\n});\nconst getFinishedResult = (value, finished, cancelled = false) => ({\n  value,\n  finished,\n  cancelled\n});\nconst getCancelledResult = value => ({\n  value,\n  cancelled: true,\n  finished: false\n});\n\nfunction runAsync(to, props, state, target) {\n  const {\n    callId,\n    parentId,\n    onRest\n  } = props;\n  const {\n    asyncTo: prevTo,\n    promise: prevPromise\n  } = state;\n\n  if (!parentId && to === prevTo && !props.reset) {\n    return prevPromise;\n  }\n\n  return state.promise = (async () => {\n    state.asyncId = callId;\n    state.asyncTo = to;\n    const defaultProps = getDefaultProps(props, (value, key) => key === 'onRest' ? undefined : value);\n    let preventBail;\n    let bail;\n    const bailPromise = new Promise((resolve, reject) => (preventBail = resolve, bail = reject));\n\n    const bailIfEnded = bailSignal => {\n      const bailResult = callId <= (state.cancelId || 0) && getCancelledResult(target) || callId !== state.asyncId && getFinishedResult(target, false);\n\n      if (bailResult) {\n        bailSignal.result = bailResult;\n        bail(bailSignal);\n        throw bailSignal;\n      }\n    };\n\n    const animate = (arg1, arg2) => {\n      const bailSignal = new BailSignal();\n      const skipAnimationSignal = new SkipAnimationSignal();\n      return (async () => {\n        if (Globals.skipAnimation) {\n          stopAsync(state);\n          skipAnimationSignal.result = getFinishedResult(target, false);\n          bail(skipAnimationSignal);\n          throw skipAnimationSignal;\n        }\n\n        bailIfEnded(bailSignal);\n        const props = is.obj(arg1) ? _extends({}, arg1) : _extends({}, arg2, {\n          to: arg1\n        });\n        props.parentId = callId;\n        eachProp(defaultProps, (value, key) => {\n          if (is.und(props[key])) {\n            props[key] = value;\n          }\n        });\n        const result = await target.start(props);\n        bailIfEnded(bailSignal);\n\n        if (state.paused) {\n          await new Promise(resume => {\n            state.resumeQueue.add(resume);\n          });\n        }\n\n        return result;\n      })();\n    };\n\n    let result;\n\n    if (Globals.skipAnimation) {\n      stopAsync(state);\n      return getFinishedResult(target, false);\n    }\n\n    try {\n      let animating;\n\n      if (is.arr(to)) {\n        animating = (async queue => {\n          for (const props of queue) {\n            await animate(props);\n          }\n        })(to);\n      } else {\n        animating = Promise.resolve(to(animate, target.stop.bind(target)));\n      }\n\n      await Promise.all([animating.then(preventBail), bailPromise]);\n      result = getFinishedResult(target.get(), true, false);\n    } catch (err) {\n      if (err instanceof BailSignal) {\n        result = err.result;\n      } else if (err instanceof SkipAnimationSignal) {\n        result = err.result;\n      } else {\n        throw err;\n      }\n    } finally {\n      if (callId == state.asyncId) {\n        state.asyncId = parentId;\n        state.asyncTo = parentId ? prevTo : undefined;\n        state.promise = parentId ? prevPromise : undefined;\n      }\n    }\n\n    if (is.fun(onRest)) {\n      raf.batchedUpdates(() => {\n        onRest(result, target, target.item);\n      });\n    }\n\n    return result;\n  })();\n}\nfunction stopAsync(state, cancelId) {\n  flush(state.timeouts, t => t.cancel());\n  state.pauseQueue.clear();\n  state.resumeQueue.clear();\n  state.asyncId = state.asyncTo = state.promise = undefined;\n  if (cancelId) state.cancelId = cancelId;\n}\nclass BailSignal extends Error {\n  constructor() {\n    super('An async animation has been interrupted. You see this error because you ' + 'forgot to use `await` or `.catch(...)` on its returned promise.');\n    this.result = void 0;\n  }\n\n}\nclass SkipAnimationSignal extends Error {\n  constructor() {\n    super('SkipAnimationSignal');\n    this.result = void 0;\n  }\n\n}\n\nconst isFrameValue = value => value instanceof FrameValue;\nlet nextId$1 = 1;\nclass FrameValue extends FluidValue {\n  constructor(...args) {\n    super(...args);\n    this.id = nextId$1++;\n    this.key = void 0;\n    this._priority = 0;\n  }\n\n  get priority() {\n    return this._priority;\n  }\n\n  set priority(priority) {\n    if (this._priority != priority) {\n      this._priority = priority;\n\n      this._onPriorityChange(priority);\n    }\n  }\n\n  get() {\n    const node = getAnimated(this);\n    return node && node.getValue();\n  }\n\n  to(...args) {\n    return Globals.to(this, args);\n  }\n\n  interpolate(...args) {\n    deprecateInterpolate();\n    return Globals.to(this, args);\n  }\n\n  toJSON() {\n    return this.get();\n  }\n\n  observerAdded(count) {\n    if (count == 1) this._attach();\n  }\n\n  observerRemoved(count) {\n    if (count == 0) this._detach();\n  }\n\n  _attach() {}\n\n  _detach() {}\n\n  _onChange(value, idle = false) {\n    callFluidObservers(this, {\n      type: 'change',\n      parent: this,\n      value,\n      idle\n    });\n  }\n\n  _onPriorityChange(priority) {\n    if (!this.idle) {\n      frameLoop.sort(this);\n    }\n\n    callFluidObservers(this, {\n      type: 'priority',\n      parent: this,\n      priority\n    });\n  }\n\n}\n\nconst $P = Symbol.for('SpringPhase');\nconst HAS_ANIMATED = 1;\nconst IS_ANIMATING = 2;\nconst IS_PAUSED = 4;\nconst hasAnimated = target => (target[$P] & HAS_ANIMATED) > 0;\nconst isAnimating = target => (target[$P] & IS_ANIMATING) > 0;\nconst isPaused = target => (target[$P] & IS_PAUSED) > 0;\nconst setActiveBit = (target, active) => active ? target[$P] |= IS_ANIMATING | HAS_ANIMATED : target[$P] &= ~IS_ANIMATING;\nconst setPausedBit = (target, paused) => paused ? target[$P] |= IS_PAUSED : target[$P] &= ~IS_PAUSED;\n\nclass SpringValue extends FrameValue {\n  constructor(arg1, arg2) {\n    super();\n    this.key = void 0;\n    this.animation = new Animation();\n    this.queue = void 0;\n    this.defaultProps = {};\n    this._state = {\n      paused: false,\n      delayed: false,\n      pauseQueue: new Set(),\n      resumeQueue: new Set(),\n      timeouts: new Set()\n    };\n    this._pendingCalls = new Set();\n    this._lastCallId = 0;\n    this._lastToId = 0;\n    this._memoizedDuration = 0;\n\n    if (!is.und(arg1) || !is.und(arg2)) {\n      const props = is.obj(arg1) ? _extends({}, arg1) : _extends({}, arg2, {\n        from: arg1\n      });\n\n      if (is.und(props.default)) {\n        props.default = true;\n      }\n\n      this.start(props);\n    }\n  }\n\n  get idle() {\n    return !(isAnimating(this) || this._state.asyncTo) || isPaused(this);\n  }\n\n  get goal() {\n    return getFluidValue(this.animation.to);\n  }\n\n  get velocity() {\n    const node = getAnimated(this);\n    return node instanceof AnimatedValue ? node.lastVelocity || 0 : node.getPayload().map(node => node.lastVelocity || 0);\n  }\n\n  get hasAnimated() {\n    return hasAnimated(this);\n  }\n\n  get isAnimating() {\n    return isAnimating(this);\n  }\n\n  get isPaused() {\n    return isPaused(this);\n  }\n\n  get isDelayed() {\n    return this._state.delayed;\n  }\n\n  advance(dt) {\n    let idle = true;\n    let changed = false;\n    const anim = this.animation;\n    let {\n      config,\n      toValues\n    } = anim;\n    const payload = getPayload(anim.to);\n\n    if (!payload && hasFluidValue(anim.to)) {\n      toValues = toArray(getFluidValue(anim.to));\n    }\n\n    anim.values.forEach((node, i) => {\n      if (node.done) return;\n      const to = node.constructor == AnimatedString ? 1 : payload ? payload[i].lastPosition : toValues[i];\n      let finished = anim.immediate;\n      let position = to;\n\n      if (!finished) {\n        position = node.lastPosition;\n\n        if (config.tension <= 0) {\n          node.done = true;\n          return;\n        }\n\n        let elapsed = node.elapsedTime += dt;\n        const from = anim.fromValues[i];\n        const v0 = node.v0 != null ? node.v0 : node.v0 = is.arr(config.velocity) ? config.velocity[i] : config.velocity;\n        let velocity;\n        const precision = config.precision || (from == to ? 0.005 : Math.min(1, Math.abs(to - from) * 0.001));\n\n        if (!is.und(config.duration)) {\n          let p = 1;\n\n          if (config.duration > 0) {\n            if (this._memoizedDuration !== config.duration) {\n              this._memoizedDuration = config.duration;\n\n              if (node.durationProgress > 0) {\n                node.elapsedTime = config.duration * node.durationProgress;\n                elapsed = node.elapsedTime += dt;\n              }\n            }\n\n            p = (config.progress || 0) + elapsed / this._memoizedDuration;\n            p = p > 1 ? 1 : p < 0 ? 0 : p;\n            node.durationProgress = p;\n          }\n\n          position = from + config.easing(p) * (to - from);\n          velocity = (position - node.lastPosition) / dt;\n          finished = p == 1;\n        } else if (config.decay) {\n          const decay = config.decay === true ? 0.998 : config.decay;\n          const e = Math.exp(-(1 - decay) * elapsed);\n          position = from + v0 / (1 - decay) * (1 - e);\n          finished = Math.abs(node.lastPosition - position) <= precision;\n          velocity = v0 * e;\n        } else {\n          velocity = node.lastVelocity == null ? v0 : node.lastVelocity;\n          const restVelocity = config.restVelocity || precision / 10;\n          const bounceFactor = config.clamp ? 0 : config.bounce;\n          const canBounce = !is.und(bounceFactor);\n          const isGrowing = from == to ? node.v0 > 0 : from < to;\n          let isMoving;\n          let isBouncing = false;\n          const step = 1;\n          const numSteps = Math.ceil(dt / step);\n\n          for (let n = 0; n < numSteps; ++n) {\n            isMoving = Math.abs(velocity) > restVelocity;\n\n            if (!isMoving) {\n              finished = Math.abs(to - position) <= precision;\n\n              if (finished) {\n                break;\n              }\n            }\n\n            if (canBounce) {\n              isBouncing = position == to || position > to == isGrowing;\n\n              if (isBouncing) {\n                velocity = -velocity * bounceFactor;\n                position = to;\n              }\n            }\n\n            const springForce = -config.tension * 0.000001 * (position - to);\n            const dampingForce = -config.friction * 0.001 * velocity;\n            const acceleration = (springForce + dampingForce) / config.mass;\n            velocity = velocity + acceleration * step;\n            position = position + velocity * step;\n          }\n        }\n\n        node.lastVelocity = velocity;\n\n        if (Number.isNaN(position)) {\n          console.warn(`Got NaN while animating:`, this);\n          finished = true;\n        }\n      }\n\n      if (payload && !payload[i].done) {\n        finished = false;\n      }\n\n      if (finished) {\n        node.done = true;\n      } else {\n        idle = false;\n      }\n\n      if (node.setValue(position, config.round)) {\n        changed = true;\n      }\n    });\n    const node = getAnimated(this);\n    const currVal = node.getValue();\n\n    if (idle) {\n      const finalVal = getFluidValue(anim.to);\n\n      if ((currVal !== finalVal || changed) && !config.decay) {\n        node.setValue(finalVal);\n\n        this._onChange(finalVal);\n      } else if (changed && config.decay) {\n        this._onChange(currVal);\n      }\n\n      this._stop();\n    } else if (changed) {\n      this._onChange(currVal);\n    }\n  }\n\n  set(value) {\n    raf.batchedUpdates(() => {\n      this._stop();\n\n      this._focus(value);\n\n      this._set(value);\n    });\n    return this;\n  }\n\n  pause() {\n    this._update({\n      pause: true\n    });\n  }\n\n  resume() {\n    this._update({\n      pause: false\n    });\n  }\n\n  finish() {\n    if (isAnimating(this)) {\n      const {\n        to,\n        config\n      } = this.animation;\n      raf.batchedUpdates(() => {\n        this._onStart();\n\n        if (!config.decay) {\n          this._set(to, false);\n        }\n\n        this._stop();\n      });\n    }\n\n    return this;\n  }\n\n  update(props) {\n    const queue = this.queue || (this.queue = []);\n    queue.push(props);\n    return this;\n  }\n\n  start(to, arg2) {\n    let queue;\n\n    if (!is.und(to)) {\n      queue = [is.obj(to) ? to : _extends({}, arg2, {\n        to\n      })];\n    } else {\n      queue = this.queue || [];\n      this.queue = [];\n    }\n\n    return Promise.all(queue.map(props => {\n      const up = this._update(props);\n\n      return up;\n    })).then(results => getCombinedResult(this, results));\n  }\n\n  stop(cancel) {\n    const {\n      to\n    } = this.animation;\n\n    this._focus(this.get());\n\n    stopAsync(this._state, cancel && this._lastCallId);\n    raf.batchedUpdates(() => this._stop(to, cancel));\n    return this;\n  }\n\n  reset() {\n    this._update({\n      reset: true\n    });\n  }\n\n  eventObserved(event) {\n    if (event.type == 'change') {\n      this._start();\n    } else if (event.type == 'priority') {\n      this.priority = event.priority + 1;\n    }\n  }\n\n  _prepareNode(props) {\n    const key = this.key || '';\n    let {\n      to,\n      from\n    } = props;\n    to = is.obj(to) ? to[key] : to;\n\n    if (to == null || isAsyncTo(to)) {\n      to = undefined;\n    }\n\n    from = is.obj(from) ? from[key] : from;\n\n    if (from == null) {\n      from = undefined;\n    }\n\n    const range = {\n      to,\n      from\n    };\n\n    if (!hasAnimated(this)) {\n      if (props.reverse) [to, from] = [from, to];\n      from = getFluidValue(from);\n\n      if (!is.und(from)) {\n        this._set(from);\n      } else if (!getAnimated(this)) {\n        this._set(to);\n      }\n    }\n\n    return range;\n  }\n\n  _update(_ref, isLoop) {\n    let props = _extends({}, _ref);\n\n    const {\n      key,\n      defaultProps\n    } = this;\n    if (props.default) Object.assign(defaultProps, getDefaultProps(props, (value, prop) => /^on/.test(prop) ? resolveProp(value, key) : value));\n    mergeActiveFn(this, props, 'onProps');\n    sendEvent(this, 'onProps', props, this);\n\n    const range = this._prepareNode(props);\n\n    if (Object.isFrozen(this)) {\n      throw Error('Cannot animate a `SpringValue` object that is frozen. ' + 'Did you forget to pass your component to `animated(...)` before animating its props?');\n    }\n\n    const state = this._state;\n    return scheduleProps(++this._lastCallId, {\n      key,\n      props,\n      defaultProps,\n      state,\n      actions: {\n        pause: () => {\n          if (!isPaused(this)) {\n            setPausedBit(this, true);\n            flushCalls(state.pauseQueue);\n            sendEvent(this, 'onPause', getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        resume: () => {\n          if (isPaused(this)) {\n            setPausedBit(this, false);\n\n            if (isAnimating(this)) {\n              this._resume();\n            }\n\n            flushCalls(state.resumeQueue);\n            sendEvent(this, 'onResume', getFinishedResult(this, checkFinished(this, this.animation.to)), this);\n          }\n        },\n        start: this._merge.bind(this, range)\n      }\n    }).then(result => {\n      if (props.loop && result.finished && !(isLoop && result.noop)) {\n        const nextProps = createLoopUpdate(props);\n\n        if (nextProps) {\n          return this._update(nextProps, true);\n        }\n      }\n\n      return result;\n    });\n  }\n\n  _merge(range, props, resolve) {\n    if (props.cancel) {\n      this.stop(true);\n      return resolve(getCancelledResult(this));\n    }\n\n    const hasToProp = !is.und(range.to);\n    const hasFromProp = !is.und(range.from);\n\n    if (hasToProp || hasFromProp) {\n      if (props.callId > this._lastToId) {\n        this._lastToId = props.callId;\n      } else {\n        return resolve(getCancelledResult(this));\n      }\n    }\n\n    const {\n      key,\n      defaultProps,\n      animation: anim\n    } = this;\n    const {\n      to: prevTo,\n      from: prevFrom\n    } = anim;\n    let {\n      to = prevTo,\n      from = prevFrom\n    } = range;\n\n    if (hasFromProp && !hasToProp && (!props.default || is.und(to))) {\n      to = from;\n    }\n\n    if (props.reverse) [to, from] = [from, to];\n    const hasFromChanged = !isEqual(from, prevFrom);\n\n    if (hasFromChanged) {\n      anim.from = from;\n    }\n\n    from = getFluidValue(from);\n    const hasToChanged = !isEqual(to, prevTo);\n\n    if (hasToChanged) {\n      this._focus(to);\n    }\n\n    const hasAsyncTo = isAsyncTo(props.to);\n    const {\n      config\n    } = anim;\n    const {\n      decay,\n      velocity\n    } = config;\n\n    if (hasToProp || hasFromProp) {\n      config.velocity = 0;\n    }\n\n    if (props.config && !hasAsyncTo) {\n      mergeConfig(config, callProp(props.config, key), props.config !== defaultProps.config ? callProp(defaultProps.config, key) : void 0);\n    }\n\n    let node = getAnimated(this);\n\n    if (!node || is.und(to)) {\n      return resolve(getFinishedResult(this, true));\n    }\n\n    const reset = is.und(props.reset) ? hasFromProp && !props.default : !is.und(from) && matchProp(props.reset, key);\n    const value = reset ? from : this.get();\n    const goal = computeGoal(to);\n    const isAnimatable = is.num(goal) || is.arr(goal) || isAnimatedString(goal);\n    const immediate = !hasAsyncTo && (!isAnimatable || matchProp(defaultProps.immediate || props.immediate, key));\n\n    if (hasToChanged) {\n      const nodeType = getAnimatedType(to);\n\n      if (nodeType !== node.constructor) {\n        if (immediate) {\n          node = this._set(goal);\n        } else throw Error(`Cannot animate between ${node.constructor.name} and ${nodeType.name}, as the \"to\" prop suggests`);\n      }\n    }\n\n    const goalType = node.constructor;\n    let started = hasFluidValue(to);\n    let finished = false;\n\n    if (!started) {\n      const hasValueChanged = reset || !hasAnimated(this) && hasFromChanged;\n\n      if (hasToChanged || hasValueChanged) {\n        finished = isEqual(computeGoal(value), goal);\n        started = !finished;\n      }\n\n      if (!isEqual(anim.immediate, immediate) && !immediate || !isEqual(config.decay, decay) || !isEqual(config.velocity, velocity)) {\n        started = true;\n      }\n    }\n\n    if (finished && isAnimating(this)) {\n      if (anim.changed && !reset) {\n        started = true;\n      } else if (!started) {\n        this._stop(prevTo);\n      }\n    }\n\n    if (!hasAsyncTo) {\n      if (started || hasFluidValue(prevTo)) {\n        anim.values = node.getPayload();\n        anim.toValues = hasFluidValue(to) ? null : goalType == AnimatedString ? [1] : toArray(goal);\n      }\n\n      if (anim.immediate != immediate) {\n        anim.immediate = immediate;\n\n        if (!immediate && !reset) {\n          this._set(prevTo);\n        }\n      }\n\n      if (started) {\n        const {\n          onRest\n        } = anim;\n        each(ACTIVE_EVENTS, type => mergeActiveFn(this, props, type));\n        const result = getFinishedResult(this, checkFinished(this, prevTo));\n        flushCalls(this._pendingCalls, result);\n\n        this._pendingCalls.add(resolve);\n\n        if (anim.changed) raf.batchedUpdates(() => {\n          anim.changed = !reset;\n          onRest == null ? void 0 : onRest(result, this);\n\n          if (reset) {\n            callProp(defaultProps.onRest, result);\n          } else {\n            anim.onStart == null ? void 0 : anim.onStart(result, this);\n          }\n        });\n      }\n    }\n\n    if (reset) {\n      this._set(value);\n    }\n\n    if (hasAsyncTo) {\n      resolve(runAsync(props.to, props, this._state, this));\n    } else if (started) {\n      this._start();\n    } else if (isAnimating(this) && !hasToChanged) {\n      this._pendingCalls.add(resolve);\n    } else {\n      resolve(getNoopResult(value));\n    }\n  }\n\n  _focus(value) {\n    const anim = this.animation;\n\n    if (value !== anim.to) {\n      if (getFluidObservers(this)) {\n        this._detach();\n      }\n\n      anim.to = value;\n\n      if (getFluidObservers(this)) {\n        this._attach();\n      }\n    }\n  }\n\n  _attach() {\n    let priority = 0;\n    const {\n      to\n    } = this.animation;\n\n    if (hasFluidValue(to)) {\n      addFluidObserver(to, this);\n\n      if (isFrameValue(to)) {\n        priority = to.priority + 1;\n      }\n    }\n\n    this.priority = priority;\n  }\n\n  _detach() {\n    const {\n      to\n    } = this.animation;\n\n    if (hasFluidValue(to)) {\n      removeFluidObserver(to, this);\n    }\n  }\n\n  _set(arg, idle = true) {\n    const value = getFluidValue(arg);\n\n    if (!is.und(value)) {\n      const oldNode = getAnimated(this);\n\n      if (!oldNode || !isEqual(value, oldNode.getValue())) {\n        const nodeType = getAnimatedType(value);\n\n        if (!oldNode || oldNode.constructor != nodeType) {\n          setAnimated(this, nodeType.create(value));\n        } else {\n          oldNode.setValue(value);\n        }\n\n        if (oldNode) {\n          raf.batchedUpdates(() => {\n            this._onChange(value, idle);\n          });\n        }\n      }\n    }\n\n    return getAnimated(this);\n  }\n\n  _onStart() {\n    const anim = this.animation;\n\n    if (!anim.changed) {\n      anim.changed = true;\n      sendEvent(this, 'onStart', getFinishedResult(this, checkFinished(this, anim.to)), this);\n    }\n  }\n\n  _onChange(value, idle) {\n    if (!idle) {\n      this._onStart();\n\n      callProp(this.animation.onChange, value, this);\n    }\n\n    callProp(this.defaultProps.onChange, value, this);\n\n    super._onChange(value, idle);\n  }\n\n  _start() {\n    const anim = this.animation;\n    getAnimated(this).reset(getFluidValue(anim.to));\n\n    if (!anim.immediate) {\n      anim.fromValues = anim.values.map(node => node.lastPosition);\n    }\n\n    if (!isAnimating(this)) {\n      setActiveBit(this, true);\n\n      if (!isPaused(this)) {\n        this._resume();\n      }\n    }\n  }\n\n  _resume() {\n    if (Globals.skipAnimation) {\n      this.finish();\n    } else {\n      frameLoop.start(this);\n    }\n  }\n\n  _stop(goal, cancel) {\n    if (isAnimating(this)) {\n      setActiveBit(this, false);\n      const anim = this.animation;\n      each(anim.values, node => {\n        node.done = true;\n      });\n\n      if (anim.toValues) {\n        anim.onChange = anim.onPause = anim.onResume = undefined;\n      }\n\n      callFluidObservers(this, {\n        type: 'idle',\n        parent: this\n      });\n      const result = cancel ? getCancelledResult(this.get()) : getFinishedResult(this.get(), checkFinished(this, goal != null ? goal : anim.to));\n      flushCalls(this._pendingCalls, result);\n\n      if (anim.changed) {\n        anim.changed = false;\n        sendEvent(this, 'onRest', result, this);\n      }\n    }\n  }\n\n}\n\nfunction checkFinished(target, to) {\n  const goal = computeGoal(to);\n  const value = computeGoal(target.get());\n  return isEqual(value, goal);\n}\n\nfunction createLoopUpdate(props, loop = props.loop, to = props.to) {\n  let loopRet = callProp(loop);\n\n  if (loopRet) {\n    const overrides = loopRet !== true && inferTo(loopRet);\n    const reverse = (overrides || props).reverse;\n    const reset = !overrides || overrides.reset;\n    return createUpdate(_extends({}, props, {\n      loop,\n      default: false,\n      pause: undefined,\n      to: !reverse || isAsyncTo(to) ? to : undefined,\n      from: reset ? props.from : undefined,\n      reset\n    }, overrides));\n  }\n}\nfunction createUpdate(props) {\n  const {\n    to,\n    from\n  } = props = inferTo(props);\n  const keys = new Set();\n  if (is.obj(to)) findDefined(to, keys);\n  if (is.obj(from)) findDefined(from, keys);\n  props.keys = keys.size ? Array.from(keys) : null;\n  return props;\n}\nfunction declareUpdate(props) {\n  const update = createUpdate(props);\n\n  if (is.und(update.default)) {\n    update.default = getDefaultProps(update);\n  }\n\n  return update;\n}\n\nfunction findDefined(values, keys) {\n  eachProp(values, (value, key) => value != null && keys.add(key));\n}\n\nconst ACTIVE_EVENTS = ['onStart', 'onRest', 'onChange', 'onPause', 'onResume'];\n\nfunction mergeActiveFn(target, props, type) {\n  target.animation[type] = props[type] !== getDefaultProp(props, type) ? resolveProp(props[type], target.key) : undefined;\n}\n\nfunction sendEvent(target, type, ...args) {\n  var _target$animation$typ, _target$animation, _target$defaultProps$, _target$defaultProps;\n\n  (_target$animation$typ = (_target$animation = target.animation)[type]) == null ? void 0 : _target$animation$typ.call(_target$animation, ...args);\n  (_target$defaultProps$ = (_target$defaultProps = target.defaultProps)[type]) == null ? void 0 : _target$defaultProps$.call(_target$defaultProps, ...args);\n}\n\nconst BATCHED_EVENTS = ['onStart', 'onChange', 'onRest'];\nlet nextId = 1;\nclass Controller {\n  constructor(props, flush) {\n    this.id = nextId++;\n    this.springs = {};\n    this.queue = [];\n    this.ref = void 0;\n    this._flush = void 0;\n    this._initialProps = void 0;\n    this._lastAsyncId = 0;\n    this._active = new Set();\n    this._changed = new Set();\n    this._started = false;\n    this._item = void 0;\n    this._state = {\n      paused: false,\n      pauseQueue: new Set(),\n      resumeQueue: new Set(),\n      timeouts: new Set()\n    };\n    this._events = {\n      onStart: new Map(),\n      onChange: new Map(),\n      onRest: new Map()\n    };\n    this._onFrame = this._onFrame.bind(this);\n\n    if (flush) {\n      this._flush = flush;\n    }\n\n    if (props) {\n      this.start(_extends({\n        default: true\n      }, props));\n    }\n  }\n\n  get idle() {\n    return !this._state.asyncTo && Object.values(this.springs).every(spring => {\n      return spring.idle && !spring.isDelayed && !spring.isPaused;\n    });\n  }\n\n  get item() {\n    return this._item;\n  }\n\n  set item(item) {\n    this._item = item;\n  }\n\n  get() {\n    const values = {};\n    this.each((spring, key) => values[key] = spring.get());\n    return values;\n  }\n\n  set(values) {\n    for (const key in values) {\n      const value = values[key];\n\n      if (!is.und(value)) {\n        this.springs[key].set(value);\n      }\n    }\n  }\n\n  update(props) {\n    if (props) {\n      this.queue.push(createUpdate(props));\n    }\n\n    return this;\n  }\n\n  start(props) {\n    let {\n      queue\n    } = this;\n\n    if (props) {\n      queue = toArray(props).map(createUpdate);\n    } else {\n      this.queue = [];\n    }\n\n    if (this._flush) {\n      return this._flush(this, queue);\n    }\n\n    prepareKeys(this, queue);\n    return flushUpdateQueue(this, queue);\n  }\n\n  stop(arg, keys) {\n    if (arg !== !!arg) {\n      keys = arg;\n    }\n\n    if (keys) {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].stop(!!arg));\n    } else {\n      stopAsync(this._state, this._lastAsyncId);\n      this.each(spring => spring.stop(!!arg));\n    }\n\n    return this;\n  }\n\n  pause(keys) {\n    if (is.und(keys)) {\n      this.start({\n        pause: true\n      });\n    } else {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].pause());\n    }\n\n    return this;\n  }\n\n  resume(keys) {\n    if (is.und(keys)) {\n      this.start({\n        pause: false\n      });\n    } else {\n      const springs = this.springs;\n      each(toArray(keys), key => springs[key].resume());\n    }\n\n    return this;\n  }\n\n  each(iterator) {\n    eachProp(this.springs, iterator);\n  }\n\n  _onFrame() {\n    const {\n      onStart,\n      onChange,\n      onRest\n    } = this._events;\n    const active = this._active.size > 0;\n    const changed = this._changed.size > 0;\n\n    if (active && !this._started || changed && !this._started) {\n      this._started = true;\n      flush(onStart, ([onStart, result]) => {\n        result.value = this.get();\n        onStart(result, this, this._item);\n      });\n    }\n\n    const idle = !active && this._started;\n    const values = changed || idle && onRest.size ? this.get() : null;\n\n    if (changed && onChange.size) {\n      flush(onChange, ([onChange, result]) => {\n        result.value = values;\n        onChange(result, this, this._item);\n      });\n    }\n\n    if (idle) {\n      this._started = false;\n      flush(onRest, ([onRest, result]) => {\n        result.value = values;\n        onRest(result, this, this._item);\n      });\n    }\n  }\n\n  eventObserved(event) {\n    if (event.type == 'change') {\n      this._changed.add(event.parent);\n\n      if (!event.idle) {\n        this._active.add(event.parent);\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent);\n    } else return;\n\n    raf.onFrame(this._onFrame);\n  }\n\n}\nfunction flushUpdateQueue(ctrl, queue) {\n  return Promise.all(queue.map(props => flushUpdate(ctrl, props))).then(results => getCombinedResult(ctrl, results));\n}\nasync function flushUpdate(ctrl, props, isLoop) {\n  const {\n    keys,\n    to,\n    from,\n    loop,\n    onRest,\n    onResolve\n  } = props;\n  const defaults = is.obj(props.default) && props.default;\n\n  if (loop) {\n    props.loop = false;\n  }\n\n  if (to === false) props.to = null;\n  if (from === false) props.from = null;\n  const asyncTo = is.arr(to) || is.fun(to) ? to : undefined;\n\n  if (asyncTo) {\n    props.to = undefined;\n    props.onRest = undefined;\n\n    if (defaults) {\n      defaults.onRest = undefined;\n    }\n  } else {\n    each(BATCHED_EVENTS, key => {\n      const handler = props[key];\n\n      if (is.fun(handler)) {\n        const queue = ctrl['_events'][key];\n\n        props[key] = ({\n          finished,\n          cancelled\n        }) => {\n          const result = queue.get(handler);\n\n          if (result) {\n            if (!finished) result.finished = false;\n            if (cancelled) result.cancelled = true;\n          } else {\n            queue.set(handler, {\n              value: null,\n              finished: finished || false,\n              cancelled: cancelled || false\n            });\n          }\n        };\n\n        if (defaults) {\n          defaults[key] = props[key];\n        }\n      }\n    });\n  }\n\n  const state = ctrl['_state'];\n\n  if (props.pause === !state.paused) {\n    state.paused = props.pause;\n    flushCalls(props.pause ? state.pauseQueue : state.resumeQueue);\n  } else if (state.paused) {\n    props.pause = true;\n  }\n\n  const promises = (keys || Object.keys(ctrl.springs)).map(key => ctrl.springs[key].start(props));\n  const cancel = props.cancel === true || getDefaultProp(props, 'cancel') === true;\n\n  if (asyncTo || cancel && state.asyncId) {\n    promises.push(scheduleProps(++ctrl['_lastAsyncId'], {\n      props,\n      state,\n      actions: {\n        pause: noop,\n        resume: noop,\n\n        start(props, resolve) {\n          if (cancel) {\n            stopAsync(state, ctrl['_lastAsyncId']);\n            resolve(getCancelledResult(ctrl));\n          } else {\n            props.onRest = onRest;\n            resolve(runAsync(asyncTo, props, state, ctrl));\n          }\n        }\n\n      }\n    }));\n  }\n\n  if (state.paused) {\n    await new Promise(resume => {\n      state.resumeQueue.add(resume);\n    });\n  }\n\n  const result = getCombinedResult(ctrl, await Promise.all(promises));\n\n  if (loop && result.finished && !(isLoop && result.noop)) {\n    const nextProps = createLoopUpdate(props, loop, to);\n\n    if (nextProps) {\n      prepareKeys(ctrl, [nextProps]);\n      return flushUpdate(ctrl, nextProps, true);\n    }\n  }\n\n  if (onResolve) {\n    raf.batchedUpdates(() => onResolve(result, ctrl, ctrl.item));\n  }\n\n  return result;\n}\nfunction getSprings(ctrl, props) {\n  const springs = _extends({}, ctrl.springs);\n\n  if (props) {\n    each(toArray(props), props => {\n      if (is.und(props.keys)) {\n        props = createUpdate(props);\n      }\n\n      if (!is.obj(props.to)) {\n        props = _extends({}, props, {\n          to: undefined\n        });\n      }\n\n      prepareSprings(springs, props, key => {\n        return createSpring(key);\n      });\n    });\n  }\n\n  setSprings(ctrl, springs);\n  return springs;\n}\nfunction setSprings(ctrl, springs) {\n  eachProp(springs, (spring, key) => {\n    if (!ctrl.springs[key]) {\n      ctrl.springs[key] = spring;\n      addFluidObserver(spring, ctrl);\n    }\n  });\n}\n\nfunction createSpring(key, observer) {\n  const spring = new SpringValue();\n  spring.key = key;\n\n  if (observer) {\n    addFluidObserver(spring, observer);\n  }\n\n  return spring;\n}\n\nfunction prepareSprings(springs, props, create) {\n  if (props.keys) {\n    each(props.keys, key => {\n      const spring = springs[key] || (springs[key] = create(key));\n      spring['_prepareNode'](props);\n    });\n  }\n}\n\nfunction prepareKeys(ctrl, queue) {\n  each(queue, props => {\n    prepareSprings(ctrl.springs, props, key => {\n      return createSpring(key, ctrl);\n    });\n  });\n}\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n\n  return target;\n}\n\nconst _excluded$6 = [\"children\"];\nconst SpringContext = _ref => {\n  let {\n    children\n  } = _ref,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded$6);\n\n  const inherited = useContext(ctx);\n  const pause = props.pause || !!inherited.pause,\n        immediate = props.immediate || !!inherited.immediate;\n  props = useMemoOne(() => ({\n    pause,\n    immediate\n  }), [pause, immediate]);\n  const {\n    Provider\n  } = ctx;\n  return React.createElement(Provider, {\n    value: props\n  }, children);\n};\nconst ctx = makeContext(SpringContext, {});\nSpringContext.Provider = ctx.Provider;\nSpringContext.Consumer = ctx.Consumer;\n\nfunction makeContext(target, init) {\n  Object.assign(target, React.createContext(init));\n  target.Provider._context = target;\n  target.Consumer._context = target;\n  return target;\n}\n\nconst SpringRef = () => {\n  const current = [];\n\n  const SpringRef = function SpringRef(props) {\n    deprecateDirectCall();\n    const results = [];\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update = _getProps(props, ctrl, i);\n\n        if (update) {\n          results.push(ctrl.start(update));\n        }\n      }\n    });\n    return results;\n  };\n\n  SpringRef.current = current;\n\n  SpringRef.add = function (ctrl) {\n    if (!current.includes(ctrl)) {\n      current.push(ctrl);\n    }\n  };\n\n  SpringRef.delete = function (ctrl) {\n    const i = current.indexOf(ctrl);\n    if (~i) current.splice(i, 1);\n  };\n\n  SpringRef.pause = function () {\n    each(current, ctrl => ctrl.pause(...arguments));\n    return this;\n  };\n\n  SpringRef.resume = function () {\n    each(current, ctrl => ctrl.resume(...arguments));\n    return this;\n  };\n\n  SpringRef.set = function (values) {\n    each(current, ctrl => ctrl.set(values));\n  };\n\n  SpringRef.start = function (props) {\n    const results = [];\n    each(current, (ctrl, i) => {\n      if (is.und(props)) {\n        results.push(ctrl.start());\n      } else {\n        const update = this._getProps(props, ctrl, i);\n\n        if (update) {\n          results.push(ctrl.start(update));\n        }\n      }\n    });\n    return results;\n  };\n\n  SpringRef.stop = function () {\n    each(current, ctrl => ctrl.stop(...arguments));\n    return this;\n  };\n\n  SpringRef.update = function (props) {\n    each(current, (ctrl, i) => ctrl.update(this._getProps(props, ctrl, i)));\n    return this;\n  };\n\n  const _getProps = function _getProps(arg, ctrl, index) {\n    return is.fun(arg) ? arg(index, ctrl) : arg;\n  };\n\n  SpringRef._getProps = _getProps;\n  return SpringRef;\n};\n\nfunction useSprings(length, props, deps) {\n  const propsFn = is.fun(props) && props;\n  if (propsFn && !deps) deps = [];\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const layoutId = useRef(0);\n  const forceUpdate = useForceUpdate();\n  const state = useMemo(() => ({\n    ctrls: [],\n    queue: [],\n\n    flush(ctrl, updates) {\n      const springs = getSprings(ctrl, updates);\n      const canFlushSync = layoutId.current > 0 && !state.queue.length && !Object.keys(springs).some(key => !ctrl.springs[key]);\n      return canFlushSync ? flushUpdateQueue(ctrl, updates) : new Promise(resolve => {\n        setSprings(ctrl, springs);\n        state.queue.push(() => {\n          resolve(flushUpdateQueue(ctrl, updates));\n        });\n        forceUpdate();\n      });\n    }\n\n  }), []);\n  const ctrls = useRef([...state.ctrls]);\n  const updates = [];\n  const prevLength = usePrev(length) || 0;\n  useMemo(() => {\n    each(ctrls.current.slice(length, prevLength), ctrl => {\n      detachRefs(ctrl, ref);\n      ctrl.stop(true);\n    });\n    ctrls.current.length = length;\n    declareUpdates(prevLength, length);\n  }, [length]);\n  useMemo(() => {\n    declareUpdates(0, Math.min(prevLength, length));\n  }, deps);\n\n  function declareUpdates(startIndex, endIndex) {\n    for (let i = startIndex; i < endIndex; i++) {\n      const ctrl = ctrls.current[i] || (ctrls.current[i] = new Controller(null, state.flush));\n      const update = propsFn ? propsFn(i, ctrl) : props[i];\n\n      if (update) {\n        updates[i] = declareUpdate(update);\n      }\n    }\n  }\n\n  const springs = ctrls.current.map((ctrl, i) => getSprings(ctrl, updates[i]));\n  const context = useContext(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect(() => {\n    layoutId.current++;\n    state.ctrls = ctrls.current;\n    const {\n      queue\n    } = state;\n\n    if (queue.length) {\n      state.queue = [];\n      each(queue, cb => cb());\n    }\n\n    each(ctrls.current, (ctrl, i) => {\n      ref == null ? void 0 : ref.add(ctrl);\n\n      if (hasContext) {\n        ctrl.start({\n          default: context\n        });\n      }\n\n      const update = updates[i];\n\n      if (update) {\n        replaceRef(ctrl, update.ref);\n\n        if (ctrl.ref) {\n          ctrl.queue.push(update);\n        } else {\n          ctrl.start(update);\n        }\n      }\n    });\n  });\n  useOnce(() => () => {\n    each(state.ctrls, ctrl => ctrl.stop(true));\n  });\n  const values = springs.map(x => _extends({}, x));\n  return ref ? [values, ref] : values;\n}\n\nfunction useSpring(props, deps) {\n  const isFn = is.fun(props);\n  const [[values], ref] = useSprings(1, isFn ? props : [props], isFn ? deps || [] : deps);\n  return isFn || arguments.length == 2 ? [values, ref] : values;\n}\n\nconst initSpringRef = () => SpringRef();\n\nconst useSpringRef = () => useState(initSpringRef)[0];\n\nconst useSpringValue = (initial, props) => {\n  const springValue = useConstant(() => new SpringValue(initial, props));\n  useOnce(() => () => {\n    springValue.stop();\n  });\n  return springValue;\n};\n\nfunction useTrail(length, propsArg, deps) {\n  const propsFn = is.fun(propsArg) && propsArg;\n  if (propsFn && !deps) deps = [];\n  let reverse = true;\n  let passedRef = undefined;\n  const result = useSprings(length, (i, ctrl) => {\n    const props = propsFn ? propsFn(i, ctrl) : propsArg;\n    passedRef = props.ref;\n    reverse = reverse && props.reverse;\n    return props;\n  }, deps || [{}]);\n  useIsomorphicLayoutEffect(() => {\n    each(result[1].current, (ctrl, i) => {\n      const parent = result[1].current[i + (reverse ? 1 : -1)];\n      replaceRef(ctrl, passedRef);\n\n      if (ctrl.ref) {\n        if (parent) {\n          ctrl.update({\n            to: parent.springs\n          });\n        }\n\n        return;\n      }\n\n      if (parent) {\n        ctrl.start({\n          to: parent.springs\n        });\n      } else {\n        ctrl.start();\n      }\n    });\n  }, deps);\n\n  if (propsFn || arguments.length == 3) {\n    var _passedRef;\n\n    const ref = (_passedRef = passedRef) != null ? _passedRef : result[1];\n\n    ref['_getProps'] = (propsArg, ctrl, i) => {\n      const props = is.fun(propsArg) ? propsArg(i, ctrl) : propsArg;\n\n      if (props) {\n        const parent = ref.current[i + (props.reverse ? 1 : -1)];\n        if (parent) props.to = parent.springs;\n        return props;\n      }\n    };\n\n    return result;\n  }\n\n  return result[0];\n}\n\nlet TransitionPhase;\n\n(function (TransitionPhase) {\n  TransitionPhase[\"MOUNT\"] = \"mount\";\n  TransitionPhase[\"ENTER\"] = \"enter\";\n  TransitionPhase[\"UPDATE\"] = \"update\";\n  TransitionPhase[\"LEAVE\"] = \"leave\";\n})(TransitionPhase || (TransitionPhase = {}));\n\nfunction useTransition(data, props, deps) {\n  const propsFn = is.fun(props) && props;\n  const {\n    reset,\n    sort,\n    trail = 0,\n    expires = true,\n    exitBeforeEnter = false,\n    onDestroyed,\n    ref: propsRef,\n    config: propsConfig\n  } = propsFn ? propsFn() : props;\n  const ref = useMemo(() => propsFn || arguments.length == 3 ? SpringRef() : void 0, []);\n  const items = toArray(data);\n  const transitions = [];\n  const usedTransitions = useRef(null);\n  const prevTransitions = reset ? null : usedTransitions.current;\n  useIsomorphicLayoutEffect(() => {\n    usedTransitions.current = transitions;\n  });\n  useOnce(() => {\n    each(transitions, t => {\n      ref == null ? void 0 : ref.add(t.ctrl);\n      t.ctrl.ref = ref;\n    });\n    return () => {\n      each(usedTransitions.current, t => {\n        if (t.expired) {\n          clearTimeout(t.expirationId);\n        }\n\n        detachRefs(t.ctrl, ref);\n        t.ctrl.stop(true);\n      });\n    };\n  });\n  const keys = getKeys(items, propsFn ? propsFn() : props, prevTransitions);\n  const expired = reset && usedTransitions.current || [];\n  useIsomorphicLayoutEffect(() => each(expired, ({\n    ctrl,\n    item,\n    key\n  }) => {\n    detachRefs(ctrl, ref);\n    callProp(onDestroyed, item, key);\n  }));\n  const reused = [];\n  if (prevTransitions) each(prevTransitions, (t, i) => {\n    if (t.expired) {\n      clearTimeout(t.expirationId);\n      expired.push(t);\n    } else {\n      i = reused[i] = keys.indexOf(t.key);\n      if (~i) transitions[i] = t;\n    }\n  });\n  each(items, (item, i) => {\n    if (!transitions[i]) {\n      transitions[i] = {\n        key: keys[i],\n        item,\n        phase: TransitionPhase.MOUNT,\n        ctrl: new Controller()\n      };\n      transitions[i].ctrl.item = item;\n    }\n  });\n\n  if (reused.length) {\n    let i = -1;\n    const {\n      leave\n    } = propsFn ? propsFn() : props;\n    each(reused, (keyIndex, prevIndex) => {\n      const t = prevTransitions[prevIndex];\n\n      if (~keyIndex) {\n        i = transitions.indexOf(t);\n        transitions[i] = _extends({}, t, {\n          item: items[keyIndex]\n        });\n      } else if (leave) {\n        transitions.splice(++i, 0, t);\n      }\n    });\n  }\n\n  if (is.fun(sort)) {\n    transitions.sort((a, b) => sort(a.item, b.item));\n  }\n\n  let delay = -trail;\n  const forceUpdate = useForceUpdate();\n  const defaultProps = getDefaultProps(props);\n  const changes = new Map();\n  const exitingTransitions = useRef(new Map());\n  const forceChange = useRef(false);\n  each(transitions, (t, i) => {\n    const key = t.key;\n    const prevPhase = t.phase;\n    const p = propsFn ? propsFn() : props;\n    let to;\n    let phase;\n    let propsDelay = callProp(p.delay || 0, key);\n\n    if (prevPhase == TransitionPhase.MOUNT) {\n      to = p.enter;\n      phase = TransitionPhase.ENTER;\n    } else {\n      const isLeave = keys.indexOf(key) < 0;\n\n      if (prevPhase != TransitionPhase.LEAVE) {\n        if (isLeave) {\n          to = p.leave;\n          phase = TransitionPhase.LEAVE;\n        } else if (to = p.update) {\n          phase = TransitionPhase.UPDATE;\n        } else return;\n      } else if (!isLeave) {\n        to = p.enter;\n        phase = TransitionPhase.ENTER;\n      } else return;\n    }\n\n    to = callProp(to, t.item, i);\n    to = is.obj(to) ? inferTo(to) : {\n      to\n    };\n\n    if (!to.config) {\n      const config = propsConfig || defaultProps.config;\n      to.config = callProp(config, t.item, i, phase);\n    }\n\n    delay += trail;\n\n    const payload = _extends({}, defaultProps, {\n      delay: propsDelay + delay,\n      ref: propsRef,\n      immediate: p.immediate,\n      reset: false\n    }, to);\n\n    if (phase == TransitionPhase.ENTER && is.und(payload.from)) {\n      const _p = propsFn ? propsFn() : props;\n\n      const from = is.und(_p.initial) || prevTransitions ? _p.from : _p.initial;\n      payload.from = callProp(from, t.item, i);\n    }\n\n    const {\n      onResolve\n    } = payload;\n\n    payload.onResolve = result => {\n      callProp(onResolve, result);\n      const transitions = usedTransitions.current;\n      const t = transitions.find(t => t.key === key);\n      if (!t) return;\n\n      if (result.cancelled && t.phase != TransitionPhase.UPDATE) {\n        return;\n      }\n\n      if (t.ctrl.idle) {\n        const idle = transitions.every(t => t.ctrl.idle);\n\n        if (t.phase == TransitionPhase.LEAVE) {\n          const expiry = callProp(expires, t.item);\n\n          if (expiry !== false) {\n            const expiryMs = expiry === true ? 0 : expiry;\n            t.expired = true;\n\n            if (!idle && expiryMs > 0) {\n              if (expiryMs <= 0x7fffffff) t.expirationId = setTimeout(forceUpdate, expiryMs);\n              return;\n            }\n          }\n        }\n\n        if (idle && transitions.some(t => t.expired)) {\n          exitingTransitions.current.delete(t);\n\n          if (exitBeforeEnter) {\n            forceChange.current = true;\n          }\n\n          forceUpdate();\n        }\n      }\n    };\n\n    const springs = getSprings(t.ctrl, payload);\n\n    if (phase === TransitionPhase.LEAVE && exitBeforeEnter) {\n      exitingTransitions.current.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    } else {\n      changes.set(t, {\n        phase,\n        springs,\n        payload\n      });\n    }\n  });\n  const context = useContext(SpringContext);\n  const prevContext = usePrev(context);\n  const hasContext = context !== prevContext && hasProps(context);\n  useIsomorphicLayoutEffect(() => {\n    if (hasContext) {\n      each(transitions, t => {\n        t.ctrl.start({\n          default: context\n        });\n      });\n    }\n  }, [context]);\n  each(changes, (_, t) => {\n    if (exitingTransitions.current.size) {\n      const ind = transitions.findIndex(state => state.key === t.key);\n      transitions.splice(ind, 1);\n    }\n  });\n  useIsomorphicLayoutEffect(() => {\n    each(exitingTransitions.current.size ? exitingTransitions.current : changes, ({\n      phase,\n      payload\n    }, t) => {\n      const {\n        ctrl\n      } = t;\n      t.phase = phase;\n      ref == null ? void 0 : ref.add(ctrl);\n\n      if (hasContext && phase == TransitionPhase.ENTER) {\n        ctrl.start({\n          default: context\n        });\n      }\n\n      if (payload) {\n        replaceRef(ctrl, payload.ref);\n\n        if ((ctrl.ref || ref) && !forceChange.current) {\n          ctrl.update(payload);\n        } else {\n          ctrl.start(payload);\n\n          if (forceChange.current) {\n            forceChange.current = false;\n          }\n        }\n      }\n    });\n  }, reset ? void 0 : deps);\n\n  const renderTransitions = render => React.createElement(React.Fragment, null, transitions.map((t, i) => {\n    const {\n      springs\n    } = changes.get(t) || t.ctrl;\n    const elem = render(_extends({}, springs), t.item, t, i);\n    return elem && elem.type ? React.createElement(elem.type, _extends({}, elem.props, {\n      key: is.str(t.key) || is.num(t.key) ? t.key : t.ctrl.id,\n      ref: elem.ref\n    })) : elem;\n  }));\n\n  return ref ? [renderTransitions, ref] : renderTransitions;\n}\nlet nextKey = 1;\n\nfunction getKeys(items, {\n  key,\n  keys = key\n}, prevTransitions) {\n  if (keys === null) {\n    const reused = new Set();\n    return items.map(item => {\n      const t = prevTransitions && prevTransitions.find(t => t.item === item && t.phase !== TransitionPhase.LEAVE && !reused.has(t));\n\n      if (t) {\n        reused.add(t);\n        return t.key;\n      }\n\n      return nextKey++;\n    });\n  }\n\n  return is.und(keys) ? items : is.fun(keys) ? items.map(keys) : toArray(keys);\n}\n\nconst _excluded$5 = [\"container\"];\nconst useScroll = (_ref = {}) => {\n  let {\n    container\n  } = _ref,\n      springOptions = _objectWithoutPropertiesLoose(_ref, _excluded$5);\n\n  const [scrollValues, api] = useSpring(() => _extends({\n    scrollX: 0,\n    scrollY: 0,\n    scrollXProgress: 0,\n    scrollYProgress: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onScroll(({\n      x,\n      y\n    }) => {\n      api.start({\n        scrollX: x.current,\n        scrollXProgress: x.progress,\n        scrollY: y.current,\n        scrollYProgress: y.progress\n      });\n    }, {\n      container: (container == null ? void 0 : container.current) || undefined\n    });\n    return () => {\n      each(Object.values(scrollValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return scrollValues;\n};\n\nconst _excluded$4 = [\"container\"];\nconst useResize = _ref => {\n  let {\n    container\n  } = _ref,\n      springOptions = _objectWithoutPropertiesLoose(_ref, _excluded$4);\n\n  const [sizeValues, api] = useSpring(() => _extends({\n    width: 0,\n    height: 0\n  }, springOptions), []);\n  useIsomorphicLayoutEffect(() => {\n    const cleanupScroll = onResize(({\n      width,\n      height\n    }) => {\n      api.start({\n        width,\n        height,\n        immediate: sizeValues.width.get() === 0 || sizeValues.height.get() === 0\n      });\n    }, {\n      container: (container == null ? void 0 : container.current) || undefined\n    });\n    return () => {\n      each(Object.values(sizeValues), value => value.stop());\n      cleanupScroll();\n    };\n  }, []);\n  return sizeValues;\n};\n\nconst _excluded$3 = [\"to\", \"from\"],\n      _excluded2 = [\"root\", \"once\", \"amount\"];\nconst defaultThresholdOptions = {\n  any: 0,\n  all: 1\n};\nfunction useInView(props, args) {\n  const [isInView, setIsInView] = useState(false);\n  const ref = useRef();\n  const propsFn = is.fun(props) && props;\n  const springsProps = propsFn ? propsFn() : {};\n\n  const {\n    to = {},\n    from = {}\n  } = springsProps,\n        restSpringProps = _objectWithoutPropertiesLoose(springsProps, _excluded$3);\n\n  const intersectionArguments = propsFn ? args : props;\n  const [springs, api] = useSpring(() => _extends({\n    from\n  }, restSpringProps), []);\n  useIsomorphicLayoutEffect(() => {\n    const element = ref.current;\n\n    const _ref = intersectionArguments != null ? intersectionArguments : {},\n          {\n      root,\n      once,\n      amount = 'any'\n    } = _ref,\n          restArgs = _objectWithoutPropertiesLoose(_ref, _excluded2);\n\n    if (!element || once && isInView || typeof IntersectionObserver === 'undefined') return;\n    const activeIntersections = new WeakMap();\n\n    const onEnter = () => {\n      if (to) {\n        api.start(to);\n      }\n\n      setIsInView(true);\n\n      const cleanup = () => {\n        if (from) {\n          api.start(from);\n        }\n\n        setIsInView(false);\n      };\n\n      return once ? undefined : cleanup;\n    };\n\n    const handleIntersection = entries => {\n      entries.forEach(entry => {\n        const onLeave = activeIntersections.get(entry.target);\n\n        if (entry.isIntersecting === Boolean(onLeave)) {\n          return;\n        }\n\n        if (entry.isIntersecting) {\n          const newOnLeave = onEnter();\n\n          if (is.fun(newOnLeave)) {\n            activeIntersections.set(entry.target, newOnLeave);\n          } else {\n            observer.unobserve(entry.target);\n          }\n        } else if (onLeave) {\n          onLeave();\n          activeIntersections.delete(entry.target);\n        }\n      });\n    };\n\n    const observer = new IntersectionObserver(handleIntersection, _extends({\n      root: root && root.current || undefined,\n      threshold: typeof amount === 'number' || Array.isArray(amount) ? amount : defaultThresholdOptions[amount]\n    }, restArgs));\n    observer.observe(element);\n    return () => observer.unobserve(element);\n  }, [intersectionArguments]);\n\n  if (propsFn) {\n    return [ref, springs];\n  }\n\n  return [ref, isInView];\n}\n\nconst _excluded$2 = [\"children\"];\nfunction Spring(_ref) {\n  let {\n    children\n  } = _ref,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded$2);\n\n  return children(useSpring(props));\n}\n\nconst _excluded$1 = [\"items\", \"children\"];\nfunction Trail(_ref) {\n  let {\n    items,\n    children\n  } = _ref,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded$1);\n\n  const trails = useTrail(items.length, props);\n  return items.map((item, index) => {\n    const result = children(item, index);\n    return is.fun(result) ? result(trails[index]) : result;\n  });\n}\n\nconst _excluded = [\"items\", \"children\"];\nfunction Transition(_ref) {\n  let {\n    items,\n    children\n  } = _ref,\n      props = _objectWithoutPropertiesLoose(_ref, _excluded);\n\n  return useTransition(items, props)(children);\n}\n\nclass Interpolation extends FrameValue {\n  constructor(source, args) {\n    super();\n    this.key = void 0;\n    this.idle = true;\n    this.calc = void 0;\n    this._active = new Set();\n    this.source = source;\n    this.calc = createInterpolator(...args);\n\n    const value = this._get();\n\n    const nodeType = getAnimatedType(value);\n    setAnimated(this, nodeType.create(value));\n  }\n\n  advance(_dt) {\n    const value = this._get();\n\n    const oldValue = this.get();\n\n    if (!isEqual(value, oldValue)) {\n      getAnimated(this).setValue(value);\n\n      this._onChange(value, this.idle);\n    }\n\n    if (!this.idle && checkIdle(this._active)) {\n      becomeIdle(this);\n    }\n  }\n\n  _get() {\n    const inputs = is.arr(this.source) ? this.source.map(getFluidValue) : toArray(getFluidValue(this.source));\n    return this.calc(...inputs);\n  }\n\n  _start() {\n    if (this.idle && !checkIdle(this._active)) {\n      this.idle = false;\n      each(getPayload(this), node => {\n        node.done = false;\n      });\n\n      if (Globals.skipAnimation) {\n        raf.batchedUpdates(() => this.advance());\n        becomeIdle(this);\n      } else {\n        frameLoop.start(this);\n      }\n    }\n  }\n\n  _attach() {\n    let priority = 1;\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        addFluidObserver(source, this);\n      }\n\n      if (isFrameValue(source)) {\n        if (!source.idle) {\n          this._active.add(source);\n        }\n\n        priority = Math.max(priority, source.priority + 1);\n      }\n    });\n    this.priority = priority;\n\n    this._start();\n  }\n\n  _detach() {\n    each(toArray(this.source), source => {\n      if (hasFluidValue(source)) {\n        removeFluidObserver(source, this);\n      }\n    });\n\n    this._active.clear();\n\n    becomeIdle(this);\n  }\n\n  eventObserved(event) {\n    if (event.type == 'change') {\n      if (event.idle) {\n        this.advance();\n      } else {\n        this._active.add(event.parent);\n\n        this._start();\n      }\n    } else if (event.type == 'idle') {\n      this._active.delete(event.parent);\n    } else if (event.type == 'priority') {\n      this.priority = toArray(this.source).reduce((highest, parent) => Math.max(highest, (isFrameValue(parent) ? parent.priority : 0) + 1), 0);\n    }\n  }\n\n}\n\nfunction isIdle(source) {\n  return source.idle !== false;\n}\n\nfunction checkIdle(active) {\n  return !active.size || Array.from(active).every(isIdle);\n}\n\nfunction becomeIdle(self) {\n  if (!self.idle) {\n    self.idle = true;\n    each(getPayload(self), node => {\n      node.done = true;\n    });\n    callFluidObservers(self, {\n      type: 'idle',\n      parent: self\n    });\n  }\n}\n\nconst to = (source, ...args) => new Interpolation(source, args);\nconst interpolate = (source, ...args) => (deprecateInterpolate(), new Interpolation(source, args));\n\nGlobals.assign({\n  createStringInterpolator,\n  to: (source, args) => new Interpolation(source, args)\n});\nconst update = frameLoop.advance;\n\nexport { BailSignal, Controller, FrameValue, Interpolation, Spring, SpringContext, SpringRef, SpringValue, Trail, Transition, config, inferTo, interpolate, to, update, useChain, useInView, useResize, useScroll, useSpring, useSpringRef, useSpringValue, useSprings, useTrail, useTransition };\n"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,EAAE,EAAEC,OAAO,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,yBAAyB,EAAEC,IAAI,EAAEC,OAAO,EAAEC,GAAG,EAAEC,KAAK,EAAEC,UAAU,EAAEC,oBAAoB,EAAEC,kBAAkB,EAAEC,SAAS,EAAEC,aAAa,EAAEC,UAAU,EAAEC,OAAO,EAAEC,iBAAiB,EAAEC,gBAAgB,EAAEC,mBAAmB,EAAEC,IAAI,EAAEC,UAAU,EAAEC,mBAAmB,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,kBAAkB,EAAEC,wBAAwB,QAAQ,sBAAsB;AACnd,SAAS1B,OAAO,EAAEyB,kBAAkB,EAAEtB,OAAO,EAAEF,yBAAyB,EAAE0B,gBAAgB,QAAQ,sBAAsB;AACxH,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAC7D,SAASC,WAAW,EAAEC,aAAa,EAAEC,UAAU,EAAEC,cAAc,EAAEC,eAAe,EAAEC,WAAW,QAAQ,wBAAwB;AAC7H,cAAc,8BAA8B;AAC5C,cAAc,mCAAmC;AAEjD,SAASC,QAAQA,CAAA,EAAG;EAClBA,QAAQ,GAAGC,MAAM,CAACC,MAAM,GAAGD,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUC,MAAM,EAAE;IAClE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;MACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;MAEzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;QACtB,IAAIP,MAAM,CAACS,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAEC,GAAG,CAAC,EAAE;UACrDL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;QAC3B;MACF;IACF;IAEA,OAAOL,MAAM;EACf,CAAC;EACD,OAAOJ,QAAQ,CAACa,KAAK,CAAC,IAAI,EAAEP,SAAS,CAAC;AACxC;AAEA,SAASQ,QAAQA,CAACC,KAAK,EAAE,GAAGC,IAAI,EAAE;EAChC,OAAO3D,EAAE,CAAC4D,GAAG,CAACF,KAAK,CAAC,GAAGA,KAAK,CAAC,GAAGC,IAAI,CAAC,GAAGD,KAAK;AAC/C;AACA,MAAMG,SAAS,GAAGA,CAACH,KAAK,EAAEN,GAAG,KAAKM,KAAK,KAAK,IAAI,IAAI,CAAC,EAAEN,GAAG,IAAIM,KAAK,KAAK1D,EAAE,CAAC4D,GAAG,CAACF,KAAK,CAAC,GAAGA,KAAK,CAACN,GAAG,CAAC,GAAGnD,OAAO,CAACyD,KAAK,CAAC,CAACI,QAAQ,CAACV,GAAG,CAAC,CAAC,CAAC;AACnI,MAAMW,WAAW,GAAGA,CAACC,IAAI,EAAEZ,GAAG,KAAKpD,EAAE,CAACiE,GAAG,CAACD,IAAI,CAAC,GAAGZ,GAAG,IAAIY,IAAI,CAACZ,GAAG,CAAC,GAAGY,IAAI;AACzE,MAAME,cAAc,GAAGA,CAACC,KAAK,EAAEf,GAAG,KAAKe,KAAK,CAACC,OAAO,KAAK,IAAI,GAAGD,KAAK,CAACf,GAAG,CAAC,GAAGe,KAAK,CAACC,OAAO,GAAGD,KAAK,CAACC,OAAO,CAAChB,GAAG,CAAC,GAAGiB,SAAS;AAE3H,MAAMC,aAAa,GAAGZ,KAAK,IAAIA,KAAK;AAEpC,MAAMa,eAAe,GAAGA,CAACJ,KAAK,EAAEK,SAAS,GAAGF,aAAa,KAAK;EAC5D,IAAIG,IAAI,GAAGC,aAAa;EAExB,IAAIP,KAAK,CAACC,OAAO,IAAID,KAAK,CAACC,OAAO,KAAK,IAAI,EAAE;IAC3CD,KAAK,GAAGA,KAAK,CAACC,OAAO;IACrBK,IAAI,GAAG7B,MAAM,CAAC6B,IAAI,CAACN,KAAK,CAAC;EAC3B;EAEA,MAAMQ,QAAQ,GAAG,CAAC,CAAC;EAEnB,KAAK,MAAMvB,GAAG,IAAIqB,IAAI,EAAE;IACtB,MAAMf,KAAK,GAAGc,SAAS,CAACL,KAAK,CAACf,GAAG,CAAC,EAAEA,GAAG,CAAC;IAExC,IAAI,CAACpD,EAAE,CAAC4E,GAAG,CAAClB,KAAK,CAAC,EAAE;MAClBiB,QAAQ,CAACvB,GAAG,CAAC,GAAGM,KAAK;IACvB;EACF;EAEA,OAAOiB,QAAQ;AACjB,CAAC;AACD,MAAMD,aAAa,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;AACnG,MAAMG,cAAc,GAAG;EACrBC,MAAM,EAAE,CAAC;EACTC,IAAI,EAAE,CAAC;EACPC,EAAE,EAAE,CAAC;EACLC,GAAG,EAAE,CAAC;EACNC,IAAI,EAAE,CAAC;EACPC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,OAAO,EAAE,CAAC;EACVC,SAAS,EAAE,CAAC;EACZnB,OAAO,EAAE,CAAC;EACVoB,KAAK,EAAE,CAAC;EACRC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,OAAO,EAAE,CAAC;EACVC,QAAQ,EAAE,CAAC;EACXC,MAAM,EAAE,CAAC;EACTC,SAAS,EAAE,CAAC;EACZC,KAAK,EAAE,CAAC;EACRC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,CAAC;EACVC,OAAO,EAAE,CAAC;EACVC,KAAK,EAAE,CAAC;EACRC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,QAAQ,EAAE,CAAC;EACXC,WAAW,EAAE,CAAC;EACdhC,IAAI,EAAE,CAAC;EACPiC,MAAM,EAAE,CAAC;EACTC,QAAQ,EAAE;AACZ,CAAC;AAED,SAASC,eAAeA,CAACzC,KAAK,EAAE;EAC9B,MAAM0C,OAAO,GAAG,CAAC,CAAC;EAClB,IAAIC,KAAK,GAAG,CAAC;EACb/G,QAAQ,CAACoE,KAAK,EAAE,CAACT,KAAK,EAAEM,IAAI,KAAK;IAC/B,IAAI,CAACa,cAAc,CAACb,IAAI,CAAC,EAAE;MACzB6C,OAAO,CAAC7C,IAAI,CAAC,GAAGN,KAAK;MACrBoD,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EAEF,IAAIA,KAAK,EAAE;IACT,OAAOD,OAAO;EAChB;AACF;AAEA,SAASE,OAAOA,CAAC5C,KAAK,EAAE;EACtB,MAAMa,EAAE,GAAG4B,eAAe,CAACzC,KAAK,CAAC;EAEjC,IAAIa,EAAE,EAAE;IACN,MAAMgC,GAAG,GAAG;MACVhC;IACF,CAAC;IACDjF,QAAQ,CAACoE,KAAK,EAAE,CAAC8C,GAAG,EAAE7D,GAAG,KAAKA,GAAG,IAAI4B,EAAE,KAAKgC,GAAG,CAAC5D,GAAG,CAAC,GAAG6D,GAAG,CAAC,CAAC;IAC5D,OAAOD,GAAG;EACZ;EAEA,OAAOrE,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,CAAC;AAC5B;AACA,SAAS+C,WAAWA,CAACxD,KAAK,EAAE;EAC1BA,KAAK,GAAGxD,aAAa,CAACwD,KAAK,CAAC;EAC5B,OAAO1D,EAAE,CAACmH,GAAG,CAACzD,KAAK,CAAC,GAAGA,KAAK,CAAC0D,GAAG,CAACF,WAAW,CAAC,GAAG/G,gBAAgB,CAACuD,KAAK,CAAC,GAAGtD,OAAO,CAAC0B,wBAAwB,CAAC;IACzGuF,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACbC,MAAM,EAAE,CAAC5D,KAAK,EAAEA,KAAK;EACvB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK;AACf;AACA,SAAS6D,QAAQA,CAACpD,KAAK,EAAE;EACvB,KAAK,MAAMqD,CAAC,IAAIrD,KAAK,EAAE,OAAO,IAAI;EAElC,OAAO,KAAK;AACd;AACA,SAASsD,SAASA,CAACzC,EAAE,EAAE;EACrB,OAAOhF,EAAE,CAAC4D,GAAG,CAACoB,EAAE,CAAC,IAAIhF,EAAE,CAACmH,GAAG,CAACnC,EAAE,CAAC,IAAIhF,EAAE,CAACiE,GAAG,CAACe,EAAE,CAAC,CAAC,CAAC,CAAC;AAClD;AACA,SAAS0C,UAAUA,CAACC,IAAI,EAAE1C,GAAG,EAAE;EAC7B,IAAI2C,SAAS;EAEb,CAACA,SAAS,GAAGD,IAAI,CAAC1C,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG2C,SAAS,CAACC,MAAM,CAACF,IAAI,CAAC;EAChE1C,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC4C,MAAM,CAACF,IAAI,CAAC;AACzC;AACA,SAASG,UAAUA,CAACH,IAAI,EAAE1C,GAAG,EAAE;EAC7B,IAAIA,GAAG,IAAI0C,IAAI,CAAC1C,GAAG,KAAKA,GAAG,EAAE;IAC3B,IAAI8C,UAAU;IAEd,CAACA,UAAU,GAAGJ,IAAI,CAAC1C,GAAG,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8C,UAAU,CAACF,MAAM,CAACF,IAAI,CAAC;IAClE1C,GAAG,CAAC+C,GAAG,CAACL,IAAI,CAAC;IACbA,IAAI,CAAC1C,GAAG,GAAGA,GAAG;EAChB;AACF;AAEA,SAASgD,QAAQA,CAACC,IAAI,EAAEC,SAAS,EAAEC,SAAS,GAAG,IAAI,EAAE;EACnD/H,yBAAyB,CAAC,MAAM;IAC9B,IAAI8H,SAAS,EAAE;MACb,IAAIE,SAAS,GAAG,CAAC;MACjB/H,IAAI,CAAC4H,IAAI,EAAE,CAACjD,GAAG,EAAEjC,CAAC,KAAK;QACrB,MAAMsF,WAAW,GAAGrD,GAAG,CAACsD,OAAO;QAE/B,IAAID,WAAW,CAACpF,MAAM,EAAE;UACtB,IAAIsC,KAAK,GAAG4C,SAAS,GAAGD,SAAS,CAACnF,CAAC,CAAC;UACpC,IAAIwF,KAAK,CAAChD,KAAK,CAAC,EAAEA,KAAK,GAAG6C,SAAS,CAAC,KAAKA,SAAS,GAAG7C,KAAK;UAC1DlF,IAAI,CAACgI,WAAW,EAAEX,IAAI,IAAI;YACxBrH,IAAI,CAACqH,IAAI,CAACc,KAAK,EAAEtE,KAAK,IAAI;cACxB,MAAMuE,iBAAiB,GAAGvE,KAAK,CAACqB,KAAK;cAErCrB,KAAK,CAACqB,KAAK,GAAGpC,GAAG,IAAIoC,KAAK,GAAG/B,QAAQ,CAACiF,iBAAiB,IAAI,CAAC,EAAEtF,GAAG,CAAC;YACpE,CAAC,CAAC;UACJ,CAAC,CAAC;UACF6B,GAAG,CAAC0D,KAAK,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAIC,CAAC,GAAGC,OAAO,CAACC,OAAO,CAAC,CAAC;MACzBxI,IAAI,CAAC4H,IAAI,EAAEjD,GAAG,IAAI;QAChB,MAAMqD,WAAW,GAAGrD,GAAG,CAACsD,OAAO;QAE/B,IAAID,WAAW,CAACpF,MAAM,EAAE;UACtB,MAAM6F,MAAM,GAAGT,WAAW,CAAClB,GAAG,CAACO,IAAI,IAAI;YACrC,MAAMqB,CAAC,GAAGrB,IAAI,CAACc,KAAK;YACpBd,IAAI,CAACc,KAAK,GAAG,EAAE;YACf,OAAOO,CAAC;UACV,CAAC,CAAC;UACFJ,CAAC,GAAGA,CAAC,CAACK,IAAI,CAAC,MAAM;YACf3I,IAAI,CAACgI,WAAW,EAAE,CAACX,IAAI,EAAE3E,CAAC,KAAK1C,IAAI,CAACyI,MAAM,CAAC/F,CAAC,CAAC,IAAI,EAAE,EAAEsD,MAAM,IAAIqB,IAAI,CAACc,KAAK,CAACS,IAAI,CAAC5C,MAAM,CAAC,CAAC,CAAC;YACxF,OAAOuC,OAAO,CAACM,GAAG,CAAClE,GAAG,CAAC0D,KAAK,CAAC,CAAC,CAAC;UACjC,CAAC,CAAC;QACJ;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;AACJ;AAEA,MAAM7D,MAAM,GAAG;EACbV,OAAO,EAAE;IACPgF,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDC,MAAM,EAAE;IACNF,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDE,MAAM,EAAE;IACNH,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDG,KAAK,EAAE;IACLJ,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDI,IAAI,EAAE;IACJL,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ,CAAC;EACDK,QAAQ,EAAE;IACRN,OAAO,EAAE,GAAG;IACZC,QAAQ,EAAE;EACZ;AACF,CAAC;AAED,MAAM1E,QAAQ,GAAGhC,QAAQ,CAAC,CAAC,CAAC,EAAEmC,MAAM,CAACV,OAAO,EAAE;EAC5CuF,IAAI,EAAE,CAAC;EACPC,OAAO,EAAE,CAAC;EACVC,MAAM,EAAEtJ,OAAO,CAACuJ,MAAM;EACtBC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,MAAMC,eAAe,CAAC;EACpBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACb,OAAO,GAAG,KAAK,CAAC;IACrB,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;IACtB,IAAI,CAACa,SAAS,GAAG,KAAK,CAAC;IACvB,IAAI,CAACN,OAAO,GAAG,KAAK,CAAC;IACrB,IAAI,CAACD,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACQ,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,YAAY,GAAG,KAAK,CAAC;IAC1B,IAAI,CAACC,SAAS,GAAG,KAAK,CAAC;IACvB,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK,CAAC;IACtB,IAAI,CAACV,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAACE,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACS,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAC;IACnB9H,MAAM,CAACC,MAAM,CAAC,IAAI,EAAE8B,QAAQ,CAAC;EAC/B;AAEF;AACA,SAASgG,WAAWA,CAAC7F,MAAM,EAAE8F,SAAS,EAAEC,aAAa,EAAE;EACrD,IAAIA,aAAa,EAAE;IACjBA,aAAa,GAAGlI,QAAQ,CAAC,CAAC,CAAC,EAAEkI,aAAa,CAAC;IAC3CC,cAAc,CAACD,aAAa,EAAED,SAAS,CAAC;IACxCA,SAAS,GAAGjI,QAAQ,CAAC,CAAC,CAAC,EAAEkI,aAAa,EAAED,SAAS,CAAC;EACpD;EAEAE,cAAc,CAAChG,MAAM,EAAE8F,SAAS,CAAC;EACjChI,MAAM,CAACC,MAAM,CAACiC,MAAM,EAAE8F,SAAS,CAAC;EAEhC,KAAK,MAAMxH,GAAG,IAAIuB,QAAQ,EAAE;IAC1B,IAAIG,MAAM,CAAC1B,GAAG,CAAC,IAAI,IAAI,EAAE;MACvB0B,MAAM,CAAC1B,GAAG,CAAC,GAAGuB,QAAQ,CAACvB,GAAG,CAAC;IAC7B;EACF;EAEA,IAAI;IACFuG,IAAI;IACJO,SAAS;IACTN;EACF,CAAC,GAAG9E,MAAM;EAEV,IAAI,CAAC9E,EAAE,CAAC4E,GAAG,CAACsF,SAAS,CAAC,EAAE;IACtB,IAAIA,SAAS,GAAG,IAAI,EAAEA,SAAS,GAAG,IAAI;IACtC,IAAIN,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAG,CAAC;IAC5B9E,MAAM,CAACsE,OAAO,GAAG2B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGD,IAAI,CAACE,EAAE,GAAGf,SAAS,EAAE,CAAC,CAAC,GAAGP,IAAI;IAC5D7E,MAAM,CAACuE,QAAQ,GAAG,CAAC,GAAG0B,IAAI,CAACE,EAAE,GAAGrB,OAAO,GAAGD,IAAI,GAAGO,SAAS;EAC5D;EAEA,OAAOpF,MAAM;AACf;AAEA,SAASgG,cAAcA,CAAChG,MAAM,EAAEX,KAAK,EAAE;EACrC,IAAI,CAACnE,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACsG,KAAK,CAAC,EAAE;IACxB3F,MAAM,CAACyF,QAAQ,GAAGlG,SAAS;EAC7B,CAAC,MAAM;IACL,MAAM6G,eAAe,GAAG,CAAClL,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACiF,OAAO,CAAC,IAAI,CAACpJ,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACkF,QAAQ,CAAC;IAEzE,IAAI6B,eAAe,IAAI,CAAClL,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAAC+F,SAAS,CAAC,IAAI,CAAClK,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACyF,OAAO,CAAC,IAAI,CAAC5J,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACwF,IAAI,CAAC,EAAE;MAChG7E,MAAM,CAACyF,QAAQ,GAAGlG,SAAS;MAC3BS,MAAM,CAAC2F,KAAK,GAAGpG,SAAS;IAC1B;IAEA,IAAI6G,eAAe,EAAE;MACnBpG,MAAM,CAACoF,SAAS,GAAG7F,SAAS;IAC9B;EACF;AACF;AAEA,MAAM8G,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,CAAC;EACdnB,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACoB,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,MAAM,GAAGH,UAAU;IACxB,IAAI,CAACI,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,UAAU,GAAGL,UAAU;IAC5B,IAAI,CAACnG,EAAE,GAAG,KAAK,CAAC;IAChB,IAAI,CAACD,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACD,MAAM,GAAG,IAAIkF,eAAe,CAAC,CAAC;IACnC,IAAI,CAACzE,SAAS,GAAG,KAAK;EACxB;AAEF;AAEA,SAASkG,aAAaA,CAAC/E,MAAM,EAAE;EAC7BtD,GAAG;EACHe,KAAK;EACLuH,YAAY;EACZC,KAAK;EACLC;AACF,CAAC,EAAE;EACD,OAAO,IAAI/C,OAAO,CAAC,CAACC,OAAO,EAAE+C,MAAM,KAAK;IACtC,IAAIC,aAAa;IAEjB,IAAItG,KAAK;IACT,IAAIuG,OAAO;IACX,IAAI1G,MAAM,GAAGxB,SAAS,CAAC,CAACiI,aAAa,GAAG3H,KAAK,CAACkB,MAAM,KAAK,IAAI,GAAGyG,aAAa,GAAGJ,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACrG,MAAM,EAAEjC,GAAG,CAAC;IAEzI,IAAIiC,MAAM,EAAE;MACVK,OAAO,CAAC,CAAC;IACX,CAAC,MAAM;MACL,IAAI,CAAC1F,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACiB,KAAK,CAAC,EAAE;QACxBuG,KAAK,CAACK,MAAM,GAAGnI,SAAS,CAACM,KAAK,CAACiB,KAAK,EAAEhC,GAAG,CAAC;MAC5C;MAEA,IAAIgC,KAAK,GAAGsG,YAAY,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACtG,KAAK;MAE9D,IAAIA,KAAK,KAAK,IAAI,EAAE;QAClBA,KAAK,GAAGuG,KAAK,CAACK,MAAM,IAAInI,SAAS,CAACuB,KAAK,EAAEhC,GAAG,CAAC;MAC/C;MAEAoC,KAAK,GAAG/B,QAAQ,CAACU,KAAK,CAACqB,KAAK,IAAI,CAAC,EAAEpC,GAAG,CAAC;MAEvC,IAAIgC,KAAK,EAAE;QACTuG,KAAK,CAACM,WAAW,CAACjE,GAAG,CAACnC,QAAQ,CAAC;QAC/B+F,OAAO,CAACxG,KAAK,CAAC,CAAC;MACjB,CAAC,MAAM;QACLwG,OAAO,CAACM,MAAM,CAAC,CAAC;QAChBrG,QAAQ,CAAC,CAAC;MACZ;IACF;IAEA,SAASD,OAAOA,CAAA,EAAG;MACjB+F,KAAK,CAACM,WAAW,CAACjE,GAAG,CAACnC,QAAQ,CAAC;MAC/B8F,KAAK,CAACQ,QAAQ,CAACtE,MAAM,CAACkE,OAAO,CAAC;MAC9BA,OAAO,CAAC1G,MAAM,CAAC,CAAC;MAChBG,KAAK,GAAGuG,OAAO,CAACK,IAAI,GAAG5L,GAAG,CAAC6L,GAAG,CAAC,CAAC;IAClC;IAEA,SAASxG,QAAQA,CAAA,EAAG;MAClB,IAAIL,KAAK,GAAG,CAAC,IAAI,CAACpF,OAAO,CAACkM,aAAa,EAAE;QACvCX,KAAK,CAACY,OAAO,GAAG,IAAI;QACpBR,OAAO,GAAGvL,GAAG,CAACgM,UAAU,CAAC9G,OAAO,EAAEF,KAAK,CAAC;QACxCmG,KAAK,CAACc,UAAU,CAACzE,GAAG,CAACpC,OAAO,CAAC;QAC7B+F,KAAK,CAACQ,QAAQ,CAACnE,GAAG,CAAC+D,OAAO,CAAC;MAC7B,CAAC,MAAM;QACLrG,OAAO,CAAC,CAAC;MACX;IACF;IAEA,SAASA,OAAOA,CAAA,EAAG;MACjB,IAAIiG,KAAK,CAACY,OAAO,EAAE;QACjBZ,KAAK,CAACY,OAAO,GAAG,KAAK;MACvB;MAEAZ,KAAK,CAACc,UAAU,CAAC5E,MAAM,CAACjC,OAAO,CAAC;MAChC+F,KAAK,CAACQ,QAAQ,CAACtE,MAAM,CAACkE,OAAO,CAAC;MAE9B,IAAIrF,MAAM,KAAKiF,KAAK,CAACe,QAAQ,IAAI,CAAC,CAAC,EAAE;QACnCrH,MAAM,GAAG,IAAI;MACf;MAEA,IAAI;QACFuG,OAAO,CAACjD,KAAK,CAAChG,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UAChCuC,MAAM;UACNrB;QACF,CAAC,CAAC,EAAEyD,OAAO,CAAC;MACd,CAAC,CAAC,OAAO6D,GAAG,EAAE;QACZd,MAAM,CAACc,GAAG,CAAC;MACb;IACF;EACF,CAAC,CAAC;AACJ;AAEA,MAAMC,iBAAiB,GAAGA,CAAC7J,MAAM,EAAE8J,OAAO,KAAKA,OAAO,CAAC3J,MAAM,IAAI,CAAC,GAAG2J,OAAO,CAAC,CAAC,CAAC,GAAGA,OAAO,CAACC,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,SAAS,CAAC,GAAGC,kBAAkB,CAAClK,MAAM,CAACmK,GAAG,CAAC,CAAC,CAAC,GAAGL,OAAO,CAACM,KAAK,CAACJ,MAAM,IAAIA,MAAM,CAAC3L,IAAI,CAAC,GAAGgM,aAAa,CAACrK,MAAM,CAACmK,GAAG,CAAC,CAAC,CAAC,GAAGG,iBAAiB,CAACtK,MAAM,CAACmK,GAAG,CAAC,CAAC,EAAEL,OAAO,CAACM,KAAK,CAACJ,MAAM,IAAIA,MAAM,CAACO,QAAQ,CAAC,CAAC;AAC9S,MAAMF,aAAa,GAAG1J,KAAK,KAAK;EAC9BA,KAAK;EACLtC,IAAI,EAAE,IAAI;EACVkM,QAAQ,EAAE,IAAI;EACdN,SAAS,EAAE;AACb,CAAC,CAAC;AACF,MAAMK,iBAAiB,GAAGA,CAAC3J,KAAK,EAAE4J,QAAQ,EAAEN,SAAS,GAAG,KAAK,MAAM;EACjEtJ,KAAK;EACL4J,QAAQ;EACRN;AACF,CAAC,CAAC;AACF,MAAMC,kBAAkB,GAAGvJ,KAAK,KAAK;EACnCA,KAAK;EACLsJ,SAAS,EAAE,IAAI;EACfM,QAAQ,EAAE;AACZ,CAAC,CAAC;AAEF,SAASC,QAAQA,CAACvI,EAAE,EAAEb,KAAK,EAAEwH,KAAK,EAAE5I,MAAM,EAAE;EAC1C,MAAM;IACJ2D,MAAM;IACNC,QAAQ;IACRb;EACF,CAAC,GAAG3B,KAAK;EACT,MAAM;IACJqJ,OAAO,EAAEC,MAAM;IACfC,OAAO,EAAEC;EACX,CAAC,GAAGhC,KAAK;EAET,IAAI,CAAChF,QAAQ,IAAI3B,EAAE,KAAKyI,MAAM,IAAI,CAACtJ,KAAK,CAACgB,KAAK,EAAE;IAC9C,OAAOwI,WAAW;EACpB;EAEA,OAAOhC,KAAK,CAAC+B,OAAO,GAAG,CAAC,YAAY;IAClC/B,KAAK,CAACiC,OAAO,GAAGlH,MAAM;IACtBiF,KAAK,CAAC6B,OAAO,GAAGxI,EAAE;IAClB,MAAM0G,YAAY,GAAGnH,eAAe,CAACJ,KAAK,EAAE,CAACT,KAAK,EAAEN,GAAG,KAAKA,GAAG,KAAK,QAAQ,GAAGiB,SAAS,GAAGX,KAAK,CAAC;IACjG,IAAImK,WAAW;IACf,IAAIC,IAAI;IACR,MAAMC,WAAW,GAAG,IAAIlF,OAAO,CAAC,CAACC,OAAO,EAAE+C,MAAM,MAAMgC,WAAW,GAAG/E,OAAO,EAAEgF,IAAI,GAAGjC,MAAM,CAAC,CAAC;IAE5F,MAAMmC,WAAW,GAAGC,UAAU,IAAI;MAChC,MAAMC,UAAU,GAAGxH,MAAM,KAAKiF,KAAK,CAACe,QAAQ,IAAI,CAAC,CAAC,IAAIO,kBAAkB,CAAClK,MAAM,CAAC,IAAI2D,MAAM,KAAKiF,KAAK,CAACiC,OAAO,IAAIP,iBAAiB,CAACtK,MAAM,EAAE,KAAK,CAAC;MAEhJ,IAAImL,UAAU,EAAE;QACdD,UAAU,CAAClB,MAAM,GAAGmB,UAAU;QAC9BJ,IAAI,CAACG,UAAU,CAAC;QAChB,MAAMA,UAAU;MAClB;IACF,CAAC;IAED,MAAME,OAAO,GAAGA,CAACC,IAAI,EAAEC,IAAI,KAAK;MAC9B,MAAMJ,UAAU,GAAG,IAAIK,UAAU,CAAC,CAAC;MACnC,MAAMC,mBAAmB,GAAG,IAAIC,mBAAmB,CAAC,CAAC;MACrD,OAAO,CAAC,YAAY;QAClB,IAAIpO,OAAO,CAACkM,aAAa,EAAE;UACzBmC,SAAS,CAAC9C,KAAK,CAAC;UAChB4C,mBAAmB,CAACxB,MAAM,GAAGM,iBAAiB,CAACtK,MAAM,EAAE,KAAK,CAAC;UAC7D+K,IAAI,CAACS,mBAAmB,CAAC;UACzB,MAAMA,mBAAmB;QAC3B;QAEAP,WAAW,CAACC,UAAU,CAAC;QACvB,MAAM9J,KAAK,GAAGnE,EAAE,CAACiE,GAAG,CAACmK,IAAI,CAAC,GAAGzL,QAAQ,CAAC,CAAC,CAAC,EAAEyL,IAAI,CAAC,GAAGzL,QAAQ,CAAC,CAAC,CAAC,EAAE0L,IAAI,EAAE;UACnErJ,EAAE,EAAEoJ;QACN,CAAC,CAAC;QACFjK,KAAK,CAACwC,QAAQ,GAAGD,MAAM;QACvB3G,QAAQ,CAAC2L,YAAY,EAAE,CAAChI,KAAK,EAAEN,GAAG,KAAK;UACrC,IAAIpD,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACf,GAAG,CAAC,CAAC,EAAE;YACtBe,KAAK,CAACf,GAAG,CAAC,GAAGM,KAAK;UACpB;QACF,CAAC,CAAC;QACF,MAAMqJ,MAAM,GAAG,MAAMhK,MAAM,CAAC4F,KAAK,CAACxE,KAAK,CAAC;QACxC6J,WAAW,CAACC,UAAU,CAAC;QAEvB,IAAItC,KAAK,CAACK,MAAM,EAAE;UAChB,MAAM,IAAInD,OAAO,CAACqD,MAAM,IAAI;YAC1BP,KAAK,CAACM,WAAW,CAACjE,GAAG,CAACkE,MAAM,CAAC;UAC/B,CAAC,CAAC;QACJ;QAEA,OAAOa,MAAM;MACf,CAAC,EAAE,CAAC;IACN,CAAC;IAED,IAAIA,MAAM;IAEV,IAAI3M,OAAO,CAACkM,aAAa,EAAE;MACzBmC,SAAS,CAAC9C,KAAK,CAAC;MAChB,OAAO0B,iBAAiB,CAACtK,MAAM,EAAE,KAAK,CAAC;IACzC;IAEA,IAAI;MACF,IAAI2L,SAAS;MAEb,IAAI1O,EAAE,CAACmH,GAAG,CAACnC,EAAE,CAAC,EAAE;QACd0J,SAAS,GAAG,CAAC,MAAMjG,KAAK,IAAI;UAC1B,KAAK,MAAMtE,KAAK,IAAIsE,KAAK,EAAE;YACzB,MAAM0F,OAAO,CAAChK,KAAK,CAAC;UACtB;QACF,CAAC,EAAEa,EAAE,CAAC;MACR,CAAC,MAAM;QACL0J,SAAS,GAAG7F,OAAO,CAACC,OAAO,CAAC9D,EAAE,CAACmJ,OAAO,EAAEpL,MAAM,CAAC4L,IAAI,CAAC7L,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC;MACpE;MAEA,MAAM8F,OAAO,CAACM,GAAG,CAAC,CAACuF,SAAS,CAACzF,IAAI,CAAC4E,WAAW,CAAC,EAAEE,WAAW,CAAC,CAAC;MAC7DhB,MAAM,GAAGM,iBAAiB,CAACtK,MAAM,CAACmK,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,CAAC;IACvD,CAAC,CAAC,OAAOP,GAAG,EAAE;MACZ,IAAIA,GAAG,YAAY2B,UAAU,EAAE;QAC7BvB,MAAM,GAAGJ,GAAG,CAACI,MAAM;MACrB,CAAC,MAAM,IAAIJ,GAAG,YAAY6B,mBAAmB,EAAE;QAC7CzB,MAAM,GAAGJ,GAAG,CAACI,MAAM;MACrB,CAAC,MAAM;QACL,MAAMJ,GAAG;MACX;IACF,CAAC,SAAS;MACR,IAAIjG,MAAM,IAAIiF,KAAK,CAACiC,OAAO,EAAE;QAC3BjC,KAAK,CAACiC,OAAO,GAAGjH,QAAQ;QACxBgF,KAAK,CAAC6B,OAAO,GAAG7G,QAAQ,GAAG8G,MAAM,GAAGpJ,SAAS;QAC7CsH,KAAK,CAAC+B,OAAO,GAAG/G,QAAQ,GAAGgH,WAAW,GAAGtJ,SAAS;MACpD;IACF;IAEA,IAAIrE,EAAE,CAAC4D,GAAG,CAACkC,MAAM,CAAC,EAAE;MAClBtF,GAAG,CAACoO,cAAc,CAAC,MAAM;QACvB9I,MAAM,CAACiH,MAAM,EAAEhK,MAAM,EAAEA,MAAM,CAAC8L,IAAI,CAAC;MACrC,CAAC,CAAC;IACJ;IAEA,OAAO9B,MAAM;EACf,CAAC,EAAE,CAAC;AACN;AACA,SAAS0B,SAASA,CAAC9C,KAAK,EAAEe,QAAQ,EAAE;EAClCjM,KAAK,CAACkL,KAAK,CAACQ,QAAQ,EAAE2C,CAAC,IAAIA,CAAC,CAACzJ,MAAM,CAAC,CAAC,CAAC;EACtCsG,KAAK,CAACc,UAAU,CAACsC,KAAK,CAAC,CAAC;EACxBpD,KAAK,CAACM,WAAW,CAAC8C,KAAK,CAAC,CAAC;EACzBpD,KAAK,CAACiC,OAAO,GAAGjC,KAAK,CAAC6B,OAAO,GAAG7B,KAAK,CAAC+B,OAAO,GAAGrJ,SAAS;EACzD,IAAIqI,QAAQ,EAAEf,KAAK,CAACe,QAAQ,GAAGA,QAAQ;AACzC;AACA,MAAM4B,UAAU,SAASU,KAAK,CAAC;EAC7B/E,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,0EAA0E,GAAG,iEAAiE,CAAC;IACrJ,IAAI,CAAC8C,MAAM,GAAG,KAAK,CAAC;EACtB;AAEF;AACA,MAAMyB,mBAAmB,SAASQ,KAAK,CAAC;EACtC/E,WAAWA,CAAA,EAAG;IACZ,KAAK,CAAC,qBAAqB,CAAC;IAC5B,IAAI,CAAC8C,MAAM,GAAG,KAAK,CAAC;EACtB;AAEF;AAEA,MAAMkC,YAAY,GAAGvL,KAAK,IAAIA,KAAK,YAAYwL,UAAU;AACzD,IAAIC,QAAQ,GAAG,CAAC;AAChB,MAAMD,UAAU,SAASxO,UAAU,CAAC;EAClCuJ,WAAWA,CAAC,GAAGtG,IAAI,EAAE;IACnB,KAAK,CAAC,GAAGA,IAAI,CAAC;IACd,IAAI,CAACyL,EAAE,GAAGD,QAAQ,EAAE;IACpB,IAAI,CAAC/L,GAAG,GAAG,KAAK,CAAC;IACjB,IAAI,CAACiM,SAAS,GAAG,CAAC;EACpB;EAEA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACD,SAAS;EACvB;EAEA,IAAIC,QAAQA,CAACA,QAAQ,EAAE;IACrB,IAAI,IAAI,CAACD,SAAS,IAAIC,QAAQ,EAAE;MAC9B,IAAI,CAACD,SAAS,GAAGC,QAAQ;MAEzB,IAAI,CAACC,iBAAiB,CAACD,QAAQ,CAAC;IAClC;EACF;EAEApC,GAAGA,CAAA,EAAG;IACJ,MAAMsC,IAAI,GAAGnN,WAAW,CAAC,IAAI,CAAC;IAC9B,OAAOmN,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,CAAC;EAChC;EAEAzK,EAAEA,CAAC,GAAGrB,IAAI,EAAE;IACV,OAAOvD,OAAO,CAAC4E,EAAE,CAAC,IAAI,EAAErB,IAAI,CAAC;EAC/B;EAEA+L,WAAWA,CAAC,GAAG/L,IAAI,EAAE;IACnBhD,oBAAoB,CAAC,CAAC;IACtB,OAAOP,OAAO,CAAC4E,EAAE,CAAC,IAAI,EAAErB,IAAI,CAAC;EAC/B;EAEAgM,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzC,GAAG,CAAC,CAAC;EACnB;EAEA0C,aAAaA,CAAC9I,KAAK,EAAE;IACnB,IAAIA,KAAK,IAAI,CAAC,EAAE,IAAI,CAAC+I,OAAO,CAAC,CAAC;EAChC;EAEAC,eAAeA,CAAChJ,KAAK,EAAE;IACrB,IAAIA,KAAK,IAAI,CAAC,EAAE,IAAI,CAACiJ,OAAO,CAAC,CAAC;EAChC;EAEAF,OAAOA,CAAA,EAAG,CAAC;EAEXE,OAAOA,CAAA,EAAG,CAAC;EAEXC,SAASA,CAACtM,KAAK,EAAEuM,IAAI,GAAG,KAAK,EAAE;IAC7BrP,kBAAkB,CAAC,IAAI,EAAE;MACvBsP,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,IAAI;MACZzM,KAAK;MACLuM;IACF,CAAC,CAAC;EACJ;EAEAV,iBAAiBA,CAACD,QAAQ,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACW,IAAI,EAAE;MACdpP,SAAS,CAACqF,IAAI,CAAC,IAAI,CAAC;IACtB;IAEAtF,kBAAkB,CAAC,IAAI,EAAE;MACvBsP,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,IAAI;MACZb;IACF,CAAC,CAAC;EACJ;AAEF;AAEA,MAAMc,EAAE,GAAGC,MAAM,CAACC,GAAG,CAAC,aAAa,CAAC;AACpC,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,YAAY,GAAG,CAAC;AACtB,MAAMC,SAAS,GAAG,CAAC;AACnB,MAAMC,WAAW,GAAG3N,MAAM,IAAI,CAACA,MAAM,CAACqN,EAAE,CAAC,GAAGG,YAAY,IAAI,CAAC;AAC7D,MAAMI,WAAW,GAAG5N,MAAM,IAAI,CAACA,MAAM,CAACqN,EAAE,CAAC,GAAGI,YAAY,IAAI,CAAC;AAC7D,MAAMI,QAAQ,GAAG7N,MAAM,IAAI,CAACA,MAAM,CAACqN,EAAE,CAAC,GAAGK,SAAS,IAAI,CAAC;AACvD,MAAMI,YAAY,GAAGA,CAAC9N,MAAM,EAAE+N,MAAM,KAAKA,MAAM,GAAG/N,MAAM,CAACqN,EAAE,CAAC,IAAII,YAAY,GAAGD,YAAY,GAAGxN,MAAM,CAACqN,EAAE,CAAC,IAAI,CAACI,YAAY;AACzH,MAAMO,YAAY,GAAGA,CAAChO,MAAM,EAAEiJ,MAAM,KAAKA,MAAM,GAAGjJ,MAAM,CAACqN,EAAE,CAAC,IAAIK,SAAS,GAAG1N,MAAM,CAACqN,EAAE,CAAC,IAAI,CAACK,SAAS;AAEpG,MAAMO,WAAW,SAAS9B,UAAU,CAAC;EACnCjF,WAAWA,CAACmE,IAAI,EAAEC,IAAI,EAAE;IACtB,KAAK,CAAC,CAAC;IACP,IAAI,CAACjL,GAAG,GAAG,KAAK,CAAC;IACjB,IAAI,CAAC6N,SAAS,GAAG,IAAI7F,SAAS,CAAC,CAAC;IAChC,IAAI,CAAC3C,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACiD,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACwF,MAAM,GAAG;MACZlF,MAAM,EAAE,KAAK;MACbO,OAAO,EAAE,KAAK;MACdE,UAAU,EAAE,IAAI0E,GAAG,CAAC,CAAC;MACrBlF,WAAW,EAAE,IAAIkF,GAAG,CAAC,CAAC;MACtBhF,QAAQ,EAAE,IAAIgF,GAAG,CAAC;IACpB,CAAC;IACD,IAAI,CAACC,aAAa,GAAG,IAAID,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACE,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,SAAS,GAAG,CAAC;IAClB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAE1B,IAAI,CAACvR,EAAE,CAAC4E,GAAG,CAACwJ,IAAI,CAAC,IAAI,CAACpO,EAAE,CAAC4E,GAAG,CAACyJ,IAAI,CAAC,EAAE;MAClC,MAAMlK,KAAK,GAAGnE,EAAE,CAACiE,GAAG,CAACmK,IAAI,CAAC,GAAGzL,QAAQ,CAAC,CAAC,CAAC,EAAEyL,IAAI,CAAC,GAAGzL,QAAQ,CAAC,CAAC,CAAC,EAAE0L,IAAI,EAAE;QACnEtJ,IAAI,EAAEqJ;MACR,CAAC,CAAC;MAEF,IAAIpO,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACC,OAAO,CAAC,EAAE;QACzBD,KAAK,CAACC,OAAO,GAAG,IAAI;MACtB;MAEA,IAAI,CAACuE,KAAK,CAACxE,KAAK,CAAC;IACnB;EACF;EAEA,IAAI8L,IAAIA,CAAA,EAAG;IACT,OAAO,EAAEU,WAAW,CAAC,IAAI,CAAC,IAAI,IAAI,CAACO,MAAM,CAAC1D,OAAO,CAAC,IAAIoD,QAAQ,CAAC,IAAI,CAAC;EACtE;EAEA,IAAIY,IAAIA,CAAA,EAAG;IACT,OAAOtR,aAAa,CAAC,IAAI,CAAC+Q,SAAS,CAACjM,EAAE,CAAC;EACzC;EAEA,IAAImF,QAAQA,CAAA,EAAG;IACb,MAAMqF,IAAI,GAAGnN,WAAW,CAAC,IAAI,CAAC;IAC9B,OAAOmN,IAAI,YAAYlN,aAAa,GAAGkN,IAAI,CAACiC,YAAY,IAAI,CAAC,GAAGjC,IAAI,CAACjN,UAAU,CAAC,CAAC,CAAC6E,GAAG,CAACoI,IAAI,IAAIA,IAAI,CAACiC,YAAY,IAAI,CAAC,CAAC;EACvH;EAEA,IAAIf,WAAWA,CAAA,EAAG;IAChB,OAAOA,WAAW,CAAC,IAAI,CAAC;EAC1B;EAEA,IAAIC,WAAWA,CAAA,EAAG;IAChB,OAAOA,WAAW,CAAC,IAAI,CAAC;EAC1B;EAEA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAOA,QAAQ,CAAC,IAAI,CAAC;EACvB;EAEA,IAAIc,SAASA,CAAA,EAAG;IACd,OAAO,IAAI,CAACR,MAAM,CAAC3E,OAAO;EAC5B;EAEAoF,OAAOA,CAACC,EAAE,EAAE;IACV,IAAI3B,IAAI,GAAG,IAAI;IACf,IAAI5E,OAAO,GAAG,KAAK;IACnB,MAAMwG,IAAI,GAAG,IAAI,CAACZ,SAAS;IAC3B,IAAI;MACFnM,MAAM;MACNyG;IACF,CAAC,GAAGsG,IAAI;IACR,MAAMC,OAAO,GAAGvP,UAAU,CAACsP,IAAI,CAAC7M,EAAE,CAAC;IAEnC,IAAI,CAAC8M,OAAO,IAAIhR,aAAa,CAAC+Q,IAAI,CAAC7M,EAAE,CAAC,EAAE;MACtCuG,QAAQ,GAAGtL,OAAO,CAACC,aAAa,CAAC2R,IAAI,CAAC7M,EAAE,CAAC,CAAC;IAC5C;IAEA6M,IAAI,CAACvG,MAAM,CAACyG,OAAO,CAAC,CAACvC,IAAI,EAAExM,CAAC,KAAK;MAC/B,IAAIwM,IAAI,CAACwC,IAAI,EAAE;MACf,MAAMhN,EAAE,GAAGwK,IAAI,CAACvF,WAAW,IAAIzH,cAAc,GAAG,CAAC,GAAGsP,OAAO,GAAGA,OAAO,CAAC9O,CAAC,CAAC,CAACiP,YAAY,GAAG1G,QAAQ,CAACvI,CAAC,CAAC;MACnG,IAAIsK,QAAQ,GAAGuE,IAAI,CAACtM,SAAS;MAC7B,IAAI2M,QAAQ,GAAGlN,EAAE;MAEjB,IAAI,CAACsI,QAAQ,EAAE;QACb4E,QAAQ,GAAG1C,IAAI,CAACyC,YAAY;QAE5B,IAAInN,MAAM,CAACsE,OAAO,IAAI,CAAC,EAAE;UACvBoG,IAAI,CAACwC,IAAI,GAAG,IAAI;UAChB;QACF;QAEA,IAAIG,OAAO,GAAG3C,IAAI,CAAC4C,WAAW,IAAIR,EAAE;QACpC,MAAM7M,IAAI,GAAG8M,IAAI,CAACrG,UAAU,CAACxI,CAAC,CAAC;QAC/B,MAAMqP,EAAE,GAAG7C,IAAI,CAAC6C,EAAE,IAAI,IAAI,GAAG7C,IAAI,CAAC6C,EAAE,GAAG7C,IAAI,CAAC6C,EAAE,GAAGrS,EAAE,CAACmH,GAAG,CAACrC,MAAM,CAACqF,QAAQ,CAAC,GAAGrF,MAAM,CAACqF,QAAQ,CAACnH,CAAC,CAAC,GAAG8B,MAAM,CAACqF,QAAQ;QAC/G,IAAIA,QAAQ;QACZ,MAAME,SAAS,GAAGvF,MAAM,CAACuF,SAAS,KAAKtF,IAAI,IAAIC,EAAE,GAAG,KAAK,GAAG+F,IAAI,CAACuH,GAAG,CAAC,CAAC,EAAEvH,IAAI,CAACwH,GAAG,CAACvN,EAAE,GAAGD,IAAI,CAAC,GAAG,KAAK,CAAC,CAAC;QAErG,IAAI,CAAC/E,EAAE,CAAC4E,GAAG,CAACE,MAAM,CAACyF,QAAQ,CAAC,EAAE;UAC5B,IAAI3B,CAAC,GAAG,CAAC;UAET,IAAI9D,MAAM,CAACyF,QAAQ,GAAG,CAAC,EAAE;YACvB,IAAI,IAAI,CAACgH,iBAAiB,KAAKzM,MAAM,CAACyF,QAAQ,EAAE;cAC9C,IAAI,CAACgH,iBAAiB,GAAGzM,MAAM,CAACyF,QAAQ;cAExC,IAAIiF,IAAI,CAACgD,gBAAgB,GAAG,CAAC,EAAE;gBAC7BhD,IAAI,CAAC4C,WAAW,GAAGtN,MAAM,CAACyF,QAAQ,GAAGiF,IAAI,CAACgD,gBAAgB;gBAC1DL,OAAO,GAAG3C,IAAI,CAAC4C,WAAW,IAAIR,EAAE;cAClC;YACF;YAEAhJ,CAAC,GAAG,CAAC9D,MAAM,CAACwF,QAAQ,IAAI,CAAC,IAAI6H,OAAO,GAAG,IAAI,CAACZ,iBAAiB;YAC7D3I,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC,GAAG,CAAC,GAAG,CAAC,GAAGA,CAAC;YAC7B4G,IAAI,CAACgD,gBAAgB,GAAG5J,CAAC;UAC3B;UAEAsJ,QAAQ,GAAGnN,IAAI,GAAGD,MAAM,CAAC+E,MAAM,CAACjB,CAAC,CAAC,IAAI5D,EAAE,GAAGD,IAAI,CAAC;UAChDoF,QAAQ,GAAG,CAAC+H,QAAQ,GAAG1C,IAAI,CAACyC,YAAY,IAAIL,EAAE;UAC9CtE,QAAQ,GAAG1E,CAAC,IAAI,CAAC;QACnB,CAAC,MAAM,IAAI9D,MAAM,CAAC2F,KAAK,EAAE;UACvB,MAAMA,KAAK,GAAG3F,MAAM,CAAC2F,KAAK,KAAK,IAAI,GAAG,KAAK,GAAG3F,MAAM,CAAC2F,KAAK;UAC1D,MAAMgI,CAAC,GAAG1H,IAAI,CAAC2H,GAAG,CAAC,EAAE,CAAC,GAAGjI,KAAK,CAAC,GAAG0H,OAAO,CAAC;UAC1CD,QAAQ,GAAGnN,IAAI,GAAGsN,EAAE,IAAI,CAAC,GAAG5H,KAAK,CAAC,IAAI,CAAC,GAAGgI,CAAC,CAAC;UAC5CnF,QAAQ,GAAGvC,IAAI,CAACwH,GAAG,CAAC/C,IAAI,CAACyC,YAAY,GAAGC,QAAQ,CAAC,IAAI7H,SAAS;UAC9DF,QAAQ,GAAGkI,EAAE,GAAGI,CAAC;QACnB,CAAC,MAAM;UACLtI,QAAQ,GAAGqF,IAAI,CAACiC,YAAY,IAAI,IAAI,GAAGY,EAAE,GAAG7C,IAAI,CAACiC,YAAY;UAC7D,MAAMrH,YAAY,GAAGtF,MAAM,CAACsF,YAAY,IAAIC,SAAS,GAAG,EAAE;UAC1D,MAAMsI,YAAY,GAAG7N,MAAM,CAACiF,KAAK,GAAG,CAAC,GAAGjF,MAAM,CAAC0F,MAAM;UACrD,MAAMoI,SAAS,GAAG,CAAC5S,EAAE,CAAC4E,GAAG,CAAC+N,YAAY,CAAC;UACvC,MAAME,SAAS,GAAG9N,IAAI,IAAIC,EAAE,GAAGwK,IAAI,CAAC6C,EAAE,GAAG,CAAC,GAAGtN,IAAI,GAAGC,EAAE;UACtD,IAAI8N,QAAQ;UACZ,IAAIC,UAAU,GAAG,KAAK;UACtB,MAAMC,IAAI,GAAG,CAAC;UACd,MAAMC,QAAQ,GAAGlI,IAAI,CAACmI,IAAI,CAACtB,EAAE,GAAGoB,IAAI,CAAC;UAErC,KAAK,IAAIG,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,QAAQ,EAAE,EAAEE,CAAC,EAAE;YACjCL,QAAQ,GAAG/H,IAAI,CAACwH,GAAG,CAACpI,QAAQ,CAAC,GAAGC,YAAY;YAE5C,IAAI,CAAC0I,QAAQ,EAAE;cACbxF,QAAQ,GAAGvC,IAAI,CAACwH,GAAG,CAACvN,EAAE,GAAGkN,QAAQ,CAAC,IAAI7H,SAAS;cAE/C,IAAIiD,QAAQ,EAAE;gBACZ;cACF;YACF;YAEA,IAAIsF,SAAS,EAAE;cACbG,UAAU,GAAGb,QAAQ,IAAIlN,EAAE,IAAIkN,QAAQ,GAAGlN,EAAE,IAAI6N,SAAS;cAEzD,IAAIE,UAAU,EAAE;gBACd5I,QAAQ,GAAG,CAACA,QAAQ,GAAGwI,YAAY;gBACnCT,QAAQ,GAAGlN,EAAE;cACf;YACF;YAEA,MAAMoO,WAAW,GAAG,CAACtO,MAAM,CAACsE,OAAO,GAAG,QAAQ,IAAI8I,QAAQ,GAAGlN,EAAE,CAAC;YAChE,MAAMqO,YAAY,GAAG,CAACvO,MAAM,CAACuE,QAAQ,GAAG,KAAK,GAAGc,QAAQ;YACxD,MAAMmJ,YAAY,GAAG,CAACF,WAAW,GAAGC,YAAY,IAAIvO,MAAM,CAAC6E,IAAI;YAC/DQ,QAAQ,GAAGA,QAAQ,GAAGmJ,YAAY,GAAGN,IAAI;YACzCd,QAAQ,GAAGA,QAAQ,GAAG/H,QAAQ,GAAG6I,IAAI;UACvC;QACF;QAEAxD,IAAI,CAACiC,YAAY,GAAGtH,QAAQ;QAE5B,IAAIoJ,MAAM,CAAC/K,KAAK,CAAC0J,QAAQ,CAAC,EAAE;UAC1BsB,OAAO,CAACC,IAAI,CAAC,0BAA0B,EAAE,IAAI,CAAC;UAC9CnG,QAAQ,GAAG,IAAI;QACjB;MACF;MAEA,IAAIwE,OAAO,IAAI,CAACA,OAAO,CAAC9O,CAAC,CAAC,CAACgP,IAAI,EAAE;QAC/B1E,QAAQ,GAAG,KAAK;MAClB;MAEA,IAAIA,QAAQ,EAAE;QACZkC,IAAI,CAACwC,IAAI,GAAG,IAAI;MAClB,CAAC,MAAM;QACL/B,IAAI,GAAG,KAAK;MACd;MAEA,IAAIT,IAAI,CAACkE,QAAQ,CAACxB,QAAQ,EAAEpN,MAAM,CAAC4F,KAAK,CAAC,EAAE;QACzCW,OAAO,GAAG,IAAI;MAChB;IACF,CAAC,CAAC;IACF,MAAMmE,IAAI,GAAGnN,WAAW,CAAC,IAAI,CAAC;IAC9B,MAAMsR,OAAO,GAAGnE,IAAI,CAACC,QAAQ,CAAC,CAAC;IAE/B,IAAIQ,IAAI,EAAE;MACR,MAAM2D,QAAQ,GAAG1T,aAAa,CAAC2R,IAAI,CAAC7M,EAAE,CAAC;MAEvC,IAAI,CAAC2O,OAAO,KAAKC,QAAQ,IAAIvI,OAAO,KAAK,CAACvG,MAAM,CAAC2F,KAAK,EAAE;QACtD+E,IAAI,CAACkE,QAAQ,CAACE,QAAQ,CAAC;QAEvB,IAAI,CAAC5D,SAAS,CAAC4D,QAAQ,CAAC;MAC1B,CAAC,MAAM,IAAIvI,OAAO,IAAIvG,MAAM,CAAC2F,KAAK,EAAE;QAClC,IAAI,CAACuF,SAAS,CAAC2D,OAAO,CAAC;MACzB;MAEA,IAAI,CAACE,KAAK,CAAC,CAAC;IACd,CAAC,MAAM,IAAIxI,OAAO,EAAE;MAClB,IAAI,CAAC2E,SAAS,CAAC2D,OAAO,CAAC;IACzB;EACF;EAEAG,GAAGA,CAACpQ,KAAK,EAAE;IACTlD,GAAG,CAACoO,cAAc,CAAC,MAAM;MACvB,IAAI,CAACiF,KAAK,CAAC,CAAC;MAEZ,IAAI,CAACE,MAAM,CAACrQ,KAAK,CAAC;MAElB,IAAI,CAACsQ,IAAI,CAACtQ,KAAK,CAAC;IAClB,CAAC,CAAC;IACF,OAAO,IAAI;EACb;EAEA0B,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC6O,OAAO,CAAC;MACX7O,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;EAEA8G,MAAMA,CAAA,EAAG;IACP,IAAI,CAAC+H,OAAO,CAAC;MACX7O,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;EAEA8O,MAAMA,CAAA,EAAG;IACP,IAAIvD,WAAW,CAAC,IAAI,CAAC,EAAE;MACrB,MAAM;QACJ3L,EAAE;QACFF;MACF,CAAC,GAAG,IAAI,CAACmM,SAAS;MAClBzQ,GAAG,CAACoO,cAAc,CAAC,MAAM;QACvB,IAAI,CAACuF,QAAQ,CAAC,CAAC;QAEf,IAAI,CAACrP,MAAM,CAAC2F,KAAK,EAAE;UACjB,IAAI,CAACuJ,IAAI,CAAChP,EAAE,EAAE,KAAK,CAAC;QACtB;QAEA,IAAI,CAAC6O,KAAK,CAAC,CAAC;MACd,CAAC,CAAC;IACJ;IAEA,OAAO,IAAI;EACb;EAEAvN,MAAMA,CAACnC,KAAK,EAAE;IACZ,MAAMsE,KAAK,GAAG,IAAI,CAACA,KAAK,KAAK,IAAI,CAACA,KAAK,GAAG,EAAE,CAAC;IAC7CA,KAAK,CAACS,IAAI,CAAC/E,KAAK,CAAC;IACjB,OAAO,IAAI;EACb;EAEAwE,KAAKA,CAAC3D,EAAE,EAAEqJ,IAAI,EAAE;IACd,IAAI5F,KAAK;IAET,IAAI,CAACzI,EAAE,CAAC4E,GAAG,CAACI,EAAE,CAAC,EAAE;MACfyD,KAAK,GAAG,CAACzI,EAAE,CAACiE,GAAG,CAACe,EAAE,CAAC,GAAGA,EAAE,GAAGrC,QAAQ,CAAC,CAAC,CAAC,EAAE0L,IAAI,EAAE;QAC5CrJ;MACF,CAAC,CAAC,CAAC;IACL,CAAC,MAAM;MACLyD,KAAK,GAAG,IAAI,CAACA,KAAK,IAAI,EAAE;MACxB,IAAI,CAACA,KAAK,GAAG,EAAE;IACjB;IAEA,OAAOI,OAAO,CAACM,GAAG,CAACV,KAAK,CAACrB,GAAG,CAACjD,KAAK,IAAI;MACpC,MAAMiQ,EAAE,GAAG,IAAI,CAACH,OAAO,CAAC9P,KAAK,CAAC;MAE9B,OAAOiQ,EAAE;IACX,CAAC,CAAC,CAAC,CAACnL,IAAI,CAAC4D,OAAO,IAAID,iBAAiB,CAAC,IAAI,EAAEC,OAAO,CAAC,CAAC;EACvD;EAEA8B,IAAIA,CAACtJ,MAAM,EAAE;IACX,MAAM;MACJL;IACF,CAAC,GAAG,IAAI,CAACiM,SAAS;IAElB,IAAI,CAAC8C,MAAM,CAAC,IAAI,CAAC7G,GAAG,CAAC,CAAC,CAAC;IAEvBuB,SAAS,CAAC,IAAI,CAACyC,MAAM,EAAE7L,MAAM,IAAI,IAAI,CAACgM,WAAW,CAAC;IAClD7Q,GAAG,CAACoO,cAAc,CAAC,MAAM,IAAI,CAACiF,KAAK,CAAC7O,EAAE,EAAEK,MAAM,CAAC,CAAC;IAChD,OAAO,IAAI;EACb;EAEAF,KAAKA,CAAA,EAAG;IACN,IAAI,CAAC8O,OAAO,CAAC;MACX9O,KAAK,EAAE;IACT,CAAC,CAAC;EACJ;EAEAkP,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAIA,KAAK,CAACpE,IAAI,IAAI,QAAQ,EAAE;MAC1B,IAAI,CAACqE,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAID,KAAK,CAACpE,IAAI,IAAI,UAAU,EAAE;MACnC,IAAI,CAACZ,QAAQ,GAAGgF,KAAK,CAAChF,QAAQ,GAAG,CAAC;IACpC;EACF;EAEAkF,YAAYA,CAACrQ,KAAK,EAAE;IAClB,MAAMf,GAAG,GAAG,IAAI,CAACA,GAAG,IAAI,EAAE;IAC1B,IAAI;MACF4B,EAAE;MACFD;IACF,CAAC,GAAGZ,KAAK;IACTa,EAAE,GAAGhF,EAAE,CAACiE,GAAG,CAACe,EAAE,CAAC,GAAGA,EAAE,CAAC5B,GAAG,CAAC,GAAG4B,EAAE;IAE9B,IAAIA,EAAE,IAAI,IAAI,IAAIyC,SAAS,CAACzC,EAAE,CAAC,EAAE;MAC/BA,EAAE,GAAGX,SAAS;IAChB;IAEAU,IAAI,GAAG/E,EAAE,CAACiE,GAAG,CAACc,IAAI,CAAC,GAAGA,IAAI,CAAC3B,GAAG,CAAC,GAAG2B,IAAI;IAEtC,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChBA,IAAI,GAAGV,SAAS;IAClB;IAEA,MAAMgD,KAAK,GAAG;MACZrC,EAAE;MACFD;IACF,CAAC;IAED,IAAI,CAAC2L,WAAW,CAAC,IAAI,CAAC,EAAE;MACtB,IAAIvM,KAAK,CAACmB,OAAO,EAAE,CAACN,EAAE,EAAED,IAAI,CAAC,GAAG,CAACA,IAAI,EAAEC,EAAE,CAAC;MAC1CD,IAAI,GAAG7E,aAAa,CAAC6E,IAAI,CAAC;MAE1B,IAAI,CAAC/E,EAAE,CAAC4E,GAAG,CAACG,IAAI,CAAC,EAAE;QACjB,IAAI,CAACiP,IAAI,CAACjP,IAAI,CAAC;MACjB,CAAC,MAAM,IAAI,CAAC1C,WAAW,CAAC,IAAI,CAAC,EAAE;QAC7B,IAAI,CAAC2R,IAAI,CAAChP,EAAE,CAAC;MACf;IACF;IAEA,OAAOqC,KAAK;EACd;EAEA4M,OAAOA,CAACQ,IAAI,EAAEC,MAAM,EAAE;IACpB,IAAIvQ,KAAK,GAAGxB,QAAQ,CAAC,CAAC,CAAC,EAAE8R,IAAI,CAAC;IAE9B,MAAM;MACJrR,GAAG;MACHsI;IACF,CAAC,GAAG,IAAI;IACR,IAAIvH,KAAK,CAACC,OAAO,EAAExB,MAAM,CAACC,MAAM,CAAC6I,YAAY,EAAEnH,eAAe,CAACJ,KAAK,EAAE,CAACT,KAAK,EAAEM,IAAI,KAAK,KAAK,CAAC2Q,IAAI,CAAC3Q,IAAI,CAAC,GAAGD,WAAW,CAACL,KAAK,EAAEN,GAAG,CAAC,GAAGM,KAAK,CAAC,CAAC;IAC3IkR,aAAa,CAAC,IAAI,EAAEzQ,KAAK,EAAE,SAAS,CAAC;IACrC0Q,SAAS,CAAC,IAAI,EAAE,SAAS,EAAE1Q,KAAK,EAAE,IAAI,CAAC;IAEvC,MAAMkD,KAAK,GAAG,IAAI,CAACmN,YAAY,CAACrQ,KAAK,CAAC;IAEtC,IAAIvB,MAAM,CAACkS,QAAQ,CAAC,IAAI,CAAC,EAAE;MACzB,MAAM9F,KAAK,CAAC,wDAAwD,GAAG,sFAAsF,CAAC;IAChK;IAEA,MAAMrD,KAAK,GAAG,IAAI,CAACuF,MAAM;IACzB,OAAOzF,aAAa,CAAC,EAAE,IAAI,CAAC4F,WAAW,EAAE;MACvCjO,GAAG;MACHe,KAAK;MACLuH,YAAY;MACZC,KAAK;MACLC,OAAO,EAAE;QACPxG,KAAK,EAAEA,CAAA,KAAM;UACX,IAAI,CAACwL,QAAQ,CAAC,IAAI,CAAC,EAAE;YACnBG,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;YACxBhQ,UAAU,CAAC4K,KAAK,CAACc,UAAU,CAAC;YAC5BoI,SAAS,CAAC,IAAI,EAAE,SAAS,EAAExH,iBAAiB,CAAC,IAAI,EAAE0H,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC9D,SAAS,CAACjM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;UACnG;QACF,CAAC;QACDkH,MAAM,EAAEA,CAAA,KAAM;UACZ,IAAI0E,QAAQ,CAAC,IAAI,CAAC,EAAE;YAClBG,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;YAEzB,IAAIJ,WAAW,CAAC,IAAI,CAAC,EAAE;cACrB,IAAI,CAACqE,OAAO,CAAC,CAAC;YAChB;YAEAjU,UAAU,CAAC4K,KAAK,CAACM,WAAW,CAAC;YAC7B4I,SAAS,CAAC,IAAI,EAAE,UAAU,EAAExH,iBAAiB,CAAC,IAAI,EAAE0H,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC9D,SAAS,CAACjM,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;UACpG;QACF,CAAC;QACD2D,KAAK,EAAE,IAAI,CAACsM,MAAM,CAACnS,IAAI,CAAC,IAAI,EAAEuE,KAAK;MACrC;IACF,CAAC,CAAC,CAAC4B,IAAI,CAAC8D,MAAM,IAAI;MAChB,IAAI5I,KAAK,CAACe,IAAI,IAAI6H,MAAM,CAACO,QAAQ,IAAI,EAAEoH,MAAM,IAAI3H,MAAM,CAAC3L,IAAI,CAAC,EAAE;QAC7D,MAAM8T,SAAS,GAAGC,gBAAgB,CAAChR,KAAK,CAAC;QAEzC,IAAI+Q,SAAS,EAAE;UACb,OAAO,IAAI,CAACjB,OAAO,CAACiB,SAAS,EAAE,IAAI,CAAC;QACtC;MACF;MAEA,OAAOnI,MAAM;IACf,CAAC,CAAC;EACJ;EAEAkI,MAAMA,CAAC5N,KAAK,EAAElD,KAAK,EAAE2E,OAAO,EAAE;IAC5B,IAAI3E,KAAK,CAACkB,MAAM,EAAE;MAChB,IAAI,CAACsJ,IAAI,CAAC,IAAI,CAAC;MACf,OAAO7F,OAAO,CAACmE,kBAAkB,CAAC,IAAI,CAAC,CAAC;IAC1C;IAEA,MAAMmI,SAAS,GAAG,CAACpV,EAAE,CAAC4E,GAAG,CAACyC,KAAK,CAACrC,EAAE,CAAC;IACnC,MAAMqQ,WAAW,GAAG,CAACrV,EAAE,CAAC4E,GAAG,CAACyC,KAAK,CAACtC,IAAI,CAAC;IAEvC,IAAIqQ,SAAS,IAAIC,WAAW,EAAE;MAC5B,IAAIlR,KAAK,CAACuC,MAAM,GAAG,IAAI,CAAC4K,SAAS,EAAE;QACjC,IAAI,CAACA,SAAS,GAAGnN,KAAK,CAACuC,MAAM;MAC/B,CAAC,MAAM;QACL,OAAOoC,OAAO,CAACmE,kBAAkB,CAAC,IAAI,CAAC,CAAC;MAC1C;IACF;IAEA,MAAM;MACJ7J,GAAG;MACHsI,YAAY;MACZuF,SAAS,EAAEY;IACb,CAAC,GAAG,IAAI;IACR,MAAM;MACJ7M,EAAE,EAAEyI,MAAM;MACV1I,IAAI,EAAEuQ;IACR,CAAC,GAAGzD,IAAI;IACR,IAAI;MACF7M,EAAE,GAAGyI,MAAM;MACX1I,IAAI,GAAGuQ;IACT,CAAC,GAAGjO,KAAK;IAET,IAAIgO,WAAW,IAAI,CAACD,SAAS,KAAK,CAACjR,KAAK,CAACC,OAAO,IAAIpE,EAAE,CAAC4E,GAAG,CAACI,EAAE,CAAC,CAAC,EAAE;MAC/DA,EAAE,GAAGD,IAAI;IACX;IAEA,IAAIZ,KAAK,CAACmB,OAAO,EAAE,CAACN,EAAE,EAAED,IAAI,CAAC,GAAG,CAACA,IAAI,EAAEC,EAAE,CAAC;IAC1C,MAAMuQ,cAAc,GAAG,CAACvU,OAAO,CAAC+D,IAAI,EAAEuQ,QAAQ,CAAC;IAE/C,IAAIC,cAAc,EAAE;MAClB1D,IAAI,CAAC9M,IAAI,GAAGA,IAAI;IAClB;IAEAA,IAAI,GAAG7E,aAAa,CAAC6E,IAAI,CAAC;IAC1B,MAAMyQ,YAAY,GAAG,CAACxU,OAAO,CAACgE,EAAE,EAAEyI,MAAM,CAAC;IAEzC,IAAI+H,YAAY,EAAE;MAChB,IAAI,CAACzB,MAAM,CAAC/O,EAAE,CAAC;IACjB;IAEA,MAAMyQ,UAAU,GAAGhO,SAAS,CAACtD,KAAK,CAACa,EAAE,CAAC;IACtC,MAAM;MACJF;IACF,CAAC,GAAG+M,IAAI;IACR,MAAM;MACJpH,KAAK;MACLN;IACF,CAAC,GAAGrF,MAAM;IAEV,IAAIsQ,SAAS,IAAIC,WAAW,EAAE;MAC5BvQ,MAAM,CAACqF,QAAQ,GAAG,CAAC;IACrB;IAEA,IAAIhG,KAAK,CAACW,MAAM,IAAI,CAAC2Q,UAAU,EAAE;MAC/B9K,WAAW,CAAC7F,MAAM,EAAErB,QAAQ,CAACU,KAAK,CAACW,MAAM,EAAE1B,GAAG,CAAC,EAAEe,KAAK,CAACW,MAAM,KAAK4G,YAAY,CAAC5G,MAAM,GAAGrB,QAAQ,CAACiI,YAAY,CAAC5G,MAAM,EAAE1B,GAAG,CAAC,GAAG,KAAK,CAAC,CAAC;IACtI;IAEA,IAAIoM,IAAI,GAAGnN,WAAW,CAAC,IAAI,CAAC;IAE5B,IAAI,CAACmN,IAAI,IAAIxP,EAAE,CAAC4E,GAAG,CAACI,EAAE,CAAC,EAAE;MACvB,OAAO8D,OAAO,CAACuE,iBAAiB,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;IAC/C;IAEA,MAAMlI,KAAK,GAAGnF,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACgB,KAAK,CAAC,GAAGkQ,WAAW,IAAI,CAAClR,KAAK,CAACC,OAAO,GAAG,CAACpE,EAAE,CAAC4E,GAAG,CAACG,IAAI,CAAC,IAAIlB,SAAS,CAACM,KAAK,CAACgB,KAAK,EAAE/B,GAAG,CAAC;IAChH,MAAMM,KAAK,GAAGyB,KAAK,GAAGJ,IAAI,GAAG,IAAI,CAACmI,GAAG,CAAC,CAAC;IACvC,MAAMsE,IAAI,GAAGtK,WAAW,CAAClC,EAAE,CAAC;IAC5B,MAAM0Q,YAAY,GAAG1V,EAAE,CAAC2V,GAAG,CAACnE,IAAI,CAAC,IAAIxR,EAAE,CAACmH,GAAG,CAACqK,IAAI,CAAC,IAAIrR,gBAAgB,CAACqR,IAAI,CAAC;IAC3E,MAAMjM,SAAS,GAAG,CAACkQ,UAAU,KAAK,CAACC,YAAY,IAAI7R,SAAS,CAAC6H,YAAY,CAACnG,SAAS,IAAIpB,KAAK,CAACoB,SAAS,EAAEnC,GAAG,CAAC,CAAC;IAE7G,IAAIoS,YAAY,EAAE;MAChB,MAAMI,QAAQ,GAAGnT,eAAe,CAACuC,EAAE,CAAC;MAEpC,IAAI4Q,QAAQ,KAAKpG,IAAI,CAACvF,WAAW,EAAE;QACjC,IAAI1E,SAAS,EAAE;UACbiK,IAAI,GAAG,IAAI,CAACwE,IAAI,CAACxC,IAAI,CAAC;QACxB,CAAC,MAAM,MAAMxC,KAAK,CAAC,0BAA0BQ,IAAI,CAACvF,WAAW,CAAC4L,IAAI,QAAQD,QAAQ,CAACC,IAAI,6BAA6B,CAAC;MACvH;IACF;IAEA,MAAMC,QAAQ,GAAGtG,IAAI,CAACvF,WAAW;IACjC,IAAI8L,OAAO,GAAGjV,aAAa,CAACkE,EAAE,CAAC;IAC/B,IAAIsI,QAAQ,GAAG,KAAK;IAEpB,IAAI,CAACyI,OAAO,EAAE;MACZ,MAAMC,eAAe,GAAG7Q,KAAK,IAAI,CAACuL,WAAW,CAAC,IAAI,CAAC,IAAI6E,cAAc;MAErE,IAAIC,YAAY,IAAIQ,eAAe,EAAE;QACnC1I,QAAQ,GAAGtM,OAAO,CAACkG,WAAW,CAACxD,KAAK,CAAC,EAAE8N,IAAI,CAAC;QAC5CuE,OAAO,GAAG,CAACzI,QAAQ;MACrB;MAEA,IAAI,CAACtM,OAAO,CAAC6Q,IAAI,CAACtM,SAAS,EAAEA,SAAS,CAAC,IAAI,CAACA,SAAS,IAAI,CAACvE,OAAO,CAAC8D,MAAM,CAAC2F,KAAK,EAAEA,KAAK,CAAC,IAAI,CAACzJ,OAAO,CAAC8D,MAAM,CAACqF,QAAQ,EAAEA,QAAQ,CAAC,EAAE;QAC7H4L,OAAO,GAAG,IAAI;MAChB;IACF;IAEA,IAAIzI,QAAQ,IAAIqD,WAAW,CAAC,IAAI,CAAC,EAAE;MACjC,IAAIkB,IAAI,CAACxG,OAAO,IAAI,CAAClG,KAAK,EAAE;QAC1B4Q,OAAO,GAAG,IAAI;MAChB,CAAC,MAAM,IAAI,CAACA,OAAO,EAAE;QACnB,IAAI,CAAClC,KAAK,CAACpG,MAAM,CAAC;MACpB;IACF;IAEA,IAAI,CAACgI,UAAU,EAAE;MACf,IAAIM,OAAO,IAAIjV,aAAa,CAAC2M,MAAM,CAAC,EAAE;QACpCoE,IAAI,CAACvG,MAAM,GAAGkE,IAAI,CAACjN,UAAU,CAAC,CAAC;QAC/BsP,IAAI,CAACtG,QAAQ,GAAGzK,aAAa,CAACkE,EAAE,CAAC,GAAG,IAAI,GAAG8Q,QAAQ,IAAItT,cAAc,GAAG,CAAC,CAAC,CAAC,GAAGvC,OAAO,CAACuR,IAAI,CAAC;MAC7F;MAEA,IAAIK,IAAI,CAACtM,SAAS,IAAIA,SAAS,EAAE;QAC/BsM,IAAI,CAACtM,SAAS,GAAGA,SAAS;QAE1B,IAAI,CAACA,SAAS,IAAI,CAACJ,KAAK,EAAE;UACxB,IAAI,CAAC6O,IAAI,CAACvG,MAAM,CAAC;QACnB;MACF;MAEA,IAAIsI,OAAO,EAAE;QACX,MAAM;UACJjQ;QACF,CAAC,GAAG+L,IAAI;QACRvR,IAAI,CAAC2V,aAAa,EAAE/F,IAAI,IAAI0E,aAAa,CAAC,IAAI,EAAEzQ,KAAK,EAAE+L,IAAI,CAAC,CAAC;QAC7D,MAAMnD,MAAM,GAAGM,iBAAiB,CAAC,IAAI,EAAE0H,aAAa,CAAC,IAAI,EAAEtH,MAAM,CAAC,CAAC;QACnE1M,UAAU,CAAC,IAAI,CAACqQ,aAAa,EAAErE,MAAM,CAAC;QAEtC,IAAI,CAACqE,aAAa,CAACpJ,GAAG,CAACc,OAAO,CAAC;QAE/B,IAAI+I,IAAI,CAACxG,OAAO,EAAE7K,GAAG,CAACoO,cAAc,CAAC,MAAM;UACzCiD,IAAI,CAACxG,OAAO,GAAG,CAAClG,KAAK;UACrBW,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACiH,MAAM,EAAE,IAAI,CAAC;UAE9C,IAAI5H,KAAK,EAAE;YACT1B,QAAQ,CAACiI,YAAY,CAAC5F,MAAM,EAAEiH,MAAM,CAAC;UACvC,CAAC,MAAM;YACL8E,IAAI,CAACnM,OAAO,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGmM,IAAI,CAACnM,OAAO,CAACqH,MAAM,EAAE,IAAI,CAAC;UAC5D;QACF,CAAC,CAAC;MACJ;IACF;IAEA,IAAI5H,KAAK,EAAE;MACT,IAAI,CAAC6O,IAAI,CAACtQ,KAAK,CAAC;IAClB;IAEA,IAAI+R,UAAU,EAAE;MACd3M,OAAO,CAACyE,QAAQ,CAACpJ,KAAK,CAACa,EAAE,EAAEb,KAAK,EAAE,IAAI,CAAC+M,MAAM,EAAE,IAAI,CAAC,CAAC;IACvD,CAAC,MAAM,IAAI6E,OAAO,EAAE;MAClB,IAAI,CAACxB,MAAM,CAAC,CAAC;IACf,CAAC,MAAM,IAAI5D,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC6E,YAAY,EAAE;MAC7C,IAAI,CAACpE,aAAa,CAACpJ,GAAG,CAACc,OAAO,CAAC;IACjC,CAAC,MAAM;MACLA,OAAO,CAACsE,aAAa,CAAC1J,KAAK,CAAC,CAAC;IAC/B;EACF;EAEAqQ,MAAMA,CAACrQ,KAAK,EAAE;IACZ,MAAMmO,IAAI,GAAG,IAAI,CAACZ,SAAS;IAE3B,IAAIvN,KAAK,KAAKmO,IAAI,CAAC7M,EAAE,EAAE;MACrB,IAAI/D,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC8O,OAAO,CAAC,CAAC;MAChB;MAEA8B,IAAI,CAAC7M,EAAE,GAAGtB,KAAK;MAEf,IAAIzC,iBAAiB,CAAC,IAAI,CAAC,EAAE;QAC3B,IAAI,CAAC4O,OAAO,CAAC,CAAC;MAChB;IACF;EACF;EAEAA,OAAOA,CAAA,EAAG;IACR,IAAIP,QAAQ,GAAG,CAAC;IAChB,MAAM;MACJtK;IACF,CAAC,GAAG,IAAI,CAACiM,SAAS;IAElB,IAAInQ,aAAa,CAACkE,EAAE,CAAC,EAAE;MACrB9D,gBAAgB,CAAC8D,EAAE,EAAE,IAAI,CAAC;MAE1B,IAAIiK,YAAY,CAACjK,EAAE,CAAC,EAAE;QACpBsK,QAAQ,GAAGtK,EAAE,CAACsK,QAAQ,GAAG,CAAC;MAC5B;IACF;IAEA,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;EAEAS,OAAOA,CAAA,EAAG;IACR,MAAM;MACJ/K;IACF,CAAC,GAAG,IAAI,CAACiM,SAAS;IAElB,IAAInQ,aAAa,CAACkE,EAAE,CAAC,EAAE;MACrB7D,mBAAmB,CAAC6D,EAAE,EAAE,IAAI,CAAC;IAC/B;EACF;EAEAgP,IAAIA,CAACkC,GAAG,EAAEjG,IAAI,GAAG,IAAI,EAAE;IACrB,MAAMvM,KAAK,GAAGxD,aAAa,CAACgW,GAAG,CAAC;IAEhC,IAAI,CAAClW,EAAE,CAAC4E,GAAG,CAAClB,KAAK,CAAC,EAAE;MAClB,MAAMyS,OAAO,GAAG9T,WAAW,CAAC,IAAI,CAAC;MAEjC,IAAI,CAAC8T,OAAO,IAAI,CAACnV,OAAO,CAAC0C,KAAK,EAAEyS,OAAO,CAAC1G,QAAQ,CAAC,CAAC,CAAC,EAAE;QACnD,MAAMmG,QAAQ,GAAGnT,eAAe,CAACiB,KAAK,CAAC;QAEvC,IAAI,CAACyS,OAAO,IAAIA,OAAO,CAAClM,WAAW,IAAI2L,QAAQ,EAAE;UAC/ClT,WAAW,CAAC,IAAI,EAAEkT,QAAQ,CAACQ,MAAM,CAAC1S,KAAK,CAAC,CAAC;QAC3C,CAAC,MAAM;UACLyS,OAAO,CAACzC,QAAQ,CAAChQ,KAAK,CAAC;QACzB;QAEA,IAAIyS,OAAO,EAAE;UACX3V,GAAG,CAACoO,cAAc,CAAC,MAAM;YACvB,IAAI,CAACoB,SAAS,CAACtM,KAAK,EAAEuM,IAAI,CAAC;UAC7B,CAAC,CAAC;QACJ;MACF;IACF;IAEA,OAAO5N,WAAW,CAAC,IAAI,CAAC;EAC1B;EAEA8R,QAAQA,CAAA,EAAG;IACT,MAAMtC,IAAI,GAAG,IAAI,CAACZ,SAAS;IAE3B,IAAI,CAACY,IAAI,CAACxG,OAAO,EAAE;MACjBwG,IAAI,CAACxG,OAAO,GAAG,IAAI;MACnBwJ,SAAS,CAAC,IAAI,EAAE,SAAS,EAAExH,iBAAiB,CAAC,IAAI,EAAE0H,aAAa,CAAC,IAAI,EAAElD,IAAI,CAAC7M,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IACzF;EACF;EAEAgL,SAASA,CAACtM,KAAK,EAAEuM,IAAI,EAAE;IACrB,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACkE,QAAQ,CAAC,CAAC;MAEf1Q,QAAQ,CAAC,IAAI,CAACwN,SAAS,CAACtL,QAAQ,EAAEjC,KAAK,EAAE,IAAI,CAAC;IAChD;IAEAD,QAAQ,CAAC,IAAI,CAACiI,YAAY,CAAC/F,QAAQ,EAAEjC,KAAK,EAAE,IAAI,CAAC;IAEjD,KAAK,CAACsM,SAAS,CAACtM,KAAK,EAAEuM,IAAI,CAAC;EAC9B;EAEAsE,MAAMA,CAAA,EAAG;IACP,MAAM1C,IAAI,GAAG,IAAI,CAACZ,SAAS;IAC3B5O,WAAW,CAAC,IAAI,CAAC,CAAC8C,KAAK,CAACjF,aAAa,CAAC2R,IAAI,CAAC7M,EAAE,CAAC,CAAC;IAE/C,IAAI,CAAC6M,IAAI,CAACtM,SAAS,EAAE;MACnBsM,IAAI,CAACrG,UAAU,GAAGqG,IAAI,CAACvG,MAAM,CAAClE,GAAG,CAACoI,IAAI,IAAIA,IAAI,CAACyC,YAAY,CAAC;IAC9D;IAEA,IAAI,CAACtB,WAAW,CAAC,IAAI,CAAC,EAAE;MACtBE,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC;MAExB,IAAI,CAACD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACnB,IAAI,CAACoE,OAAO,CAAC,CAAC;MAChB;IACF;EACF;EAEAA,OAAOA,CAAA,EAAG;IACR,IAAI5U,OAAO,CAACkM,aAAa,EAAE;MACzB,IAAI,CAAC4H,MAAM,CAAC,CAAC;IACf,CAAC,MAAM;MACLrT,SAAS,CAAC8H,KAAK,CAAC,IAAI,CAAC;IACvB;EACF;EAEAkL,KAAKA,CAACrC,IAAI,EAAEnM,MAAM,EAAE;IAClB,IAAIsL,WAAW,CAAC,IAAI,CAAC,EAAE;MACrBE,YAAY,CAAC,IAAI,EAAE,KAAK,CAAC;MACzB,MAAMgB,IAAI,GAAG,IAAI,CAACZ,SAAS;MAC3B3Q,IAAI,CAACuR,IAAI,CAACvG,MAAM,EAAEkE,IAAI,IAAI;QACxBA,IAAI,CAACwC,IAAI,GAAG,IAAI;MAClB,CAAC,CAAC;MAEF,IAAIH,IAAI,CAACtG,QAAQ,EAAE;QACjBsG,IAAI,CAAClM,QAAQ,GAAGkM,IAAI,CAACjM,OAAO,GAAGiM,IAAI,CAAChM,QAAQ,GAAGxB,SAAS;MAC1D;MAEAzD,kBAAkB,CAAC,IAAI,EAAE;QACvBsP,IAAI,EAAE,MAAM;QACZC,MAAM,EAAE;MACV,CAAC,CAAC;MACF,MAAMpD,MAAM,GAAG1H,MAAM,GAAG4H,kBAAkB,CAAC,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC,GAAGG,iBAAiB,CAAC,IAAI,CAACH,GAAG,CAAC,CAAC,EAAE6H,aAAa,CAAC,IAAI,EAAEvD,IAAI,IAAI,IAAI,GAAGA,IAAI,GAAGK,IAAI,CAAC7M,EAAE,CAAC,CAAC;MAC1IjE,UAAU,CAAC,IAAI,CAACqQ,aAAa,EAAErE,MAAM,CAAC;MAEtC,IAAI8E,IAAI,CAACxG,OAAO,EAAE;QAChBwG,IAAI,CAACxG,OAAO,GAAG,KAAK;QACpBwJ,SAAS,CAAC,IAAI,EAAE,QAAQ,EAAE9H,MAAM,EAAE,IAAI,CAAC;MACzC;IACF;EACF;AAEF;AAEA,SAASgI,aAAaA,CAAChS,MAAM,EAAEiC,EAAE,EAAE;EACjC,MAAMwM,IAAI,GAAGtK,WAAW,CAAClC,EAAE,CAAC;EAC5B,MAAMtB,KAAK,GAAGwD,WAAW,CAACnE,MAAM,CAACmK,GAAG,CAAC,CAAC,CAAC;EACvC,OAAOlM,OAAO,CAAC0C,KAAK,EAAE8N,IAAI,CAAC;AAC7B;AAEA,SAAS2D,gBAAgBA,CAAChR,KAAK,EAAEe,IAAI,GAAGf,KAAK,CAACe,IAAI,EAAEF,EAAE,GAAGb,KAAK,CAACa,EAAE,EAAE;EACjE,IAAIqR,OAAO,GAAG5S,QAAQ,CAACyB,IAAI,CAAC;EAE5B,IAAImR,OAAO,EAAE;IACX,MAAMC,SAAS,GAAGD,OAAO,KAAK,IAAI,IAAItP,OAAO,CAACsP,OAAO,CAAC;IACtD,MAAM/Q,OAAO,GAAG,CAACgR,SAAS,IAAInS,KAAK,EAAEmB,OAAO;IAC5C,MAAMH,KAAK,GAAG,CAACmR,SAAS,IAAIA,SAAS,CAACnR,KAAK;IAC3C,OAAOoR,YAAY,CAAC5T,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;MACtCe,IAAI;MACJd,OAAO,EAAE,KAAK;MACdgB,KAAK,EAAEf,SAAS;MAChBW,EAAE,EAAE,CAACM,OAAO,IAAImC,SAAS,CAACzC,EAAE,CAAC,GAAGA,EAAE,GAAGX,SAAS;MAC9CU,IAAI,EAAEI,KAAK,GAAGhB,KAAK,CAACY,IAAI,GAAGV,SAAS;MACpCc;IACF,CAAC,EAAEmR,SAAS,CAAC,CAAC;EAChB;AACF;AACA,SAASC,YAAYA,CAACpS,KAAK,EAAE;EAC3B,MAAM;IACJa,EAAE;IACFD;EACF,CAAC,GAAGZ,KAAK,GAAG4C,OAAO,CAAC5C,KAAK,CAAC;EAC1B,MAAMM,IAAI,GAAG,IAAI0M,GAAG,CAAC,CAAC;EACtB,IAAInR,EAAE,CAACiE,GAAG,CAACe,EAAE,CAAC,EAAEwR,WAAW,CAACxR,EAAE,EAAEP,IAAI,CAAC;EACrC,IAAIzE,EAAE,CAACiE,GAAG,CAACc,IAAI,CAAC,EAAEyR,WAAW,CAACzR,IAAI,EAAEN,IAAI,CAAC;EACzCN,KAAK,CAACM,IAAI,GAAGA,IAAI,CAACgS,IAAI,GAAGC,KAAK,CAAC3R,IAAI,CAACN,IAAI,CAAC,GAAG,IAAI;EAChD,OAAON,KAAK;AACd;AACA,SAASwS,aAAaA,CAACxS,KAAK,EAAE;EAC5B,MAAMmC,MAAM,GAAGiQ,YAAY,CAACpS,KAAK,CAAC;EAElC,IAAInE,EAAE,CAAC4E,GAAG,CAAC0B,MAAM,CAAClC,OAAO,CAAC,EAAE;IAC1BkC,MAAM,CAAClC,OAAO,GAAGG,eAAe,CAAC+B,MAAM,CAAC;EAC1C;EAEA,OAAOA,MAAM;AACf;AAEA,SAASkQ,WAAWA,CAAClL,MAAM,EAAE7G,IAAI,EAAE;EACjC1E,QAAQ,CAACuL,MAAM,EAAE,CAAC5H,KAAK,EAAEN,GAAG,KAAKM,KAAK,IAAI,IAAI,IAAIe,IAAI,CAACuD,GAAG,CAAC5E,GAAG,CAAC,CAAC;AAClE;AAEA,MAAM6S,aAAa,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,CAAC;AAE9E,SAASrB,aAAaA,CAAC7R,MAAM,EAAEoB,KAAK,EAAE+L,IAAI,EAAE;EAC1CnN,MAAM,CAACkO,SAAS,CAACf,IAAI,CAAC,GAAG/L,KAAK,CAAC+L,IAAI,CAAC,KAAKhM,cAAc,CAACC,KAAK,EAAE+L,IAAI,CAAC,GAAGnM,WAAW,CAACI,KAAK,CAAC+L,IAAI,CAAC,EAAEnN,MAAM,CAACK,GAAG,CAAC,GAAGiB,SAAS;AACzH;AAEA,SAASwQ,SAASA,CAAC9R,MAAM,EAAEmN,IAAI,EAAE,GAAGvM,IAAI,EAAE;EACxC,IAAIiT,qBAAqB,EAAEC,iBAAiB,EAAEC,qBAAqB,EAAEC,oBAAoB;EAEzF,CAACH,qBAAqB,GAAG,CAACC,iBAAiB,GAAG9T,MAAM,CAACkO,SAAS,EAAEf,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0G,qBAAqB,CAACrT,IAAI,CAACsT,iBAAiB,EAAE,GAAGlT,IAAI,CAAC;EAChJ,CAACmT,qBAAqB,GAAG,CAACC,oBAAoB,GAAGhU,MAAM,CAAC2I,YAAY,EAAEwE,IAAI,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG4G,qBAAqB,CAACvT,IAAI,CAACwT,oBAAoB,EAAE,GAAGpT,IAAI,CAAC;AAC3J;AAEA,MAAMqT,cAAc,GAAG,CAAC,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC;AACxD,IAAIC,MAAM,GAAG,CAAC;AACd,MAAMC,UAAU,CAAC;EACfjN,WAAWA,CAAC9F,KAAK,EAAE1D,KAAK,EAAE;IACxB,IAAI,CAAC2O,EAAE,GAAG6H,MAAM,EAAE;IAClB,IAAI,CAACE,OAAO,GAAG,CAAC,CAAC;IACjB,IAAI,CAAC1O,KAAK,GAAG,EAAE;IACf,IAAI,CAACxD,GAAG,GAAG,KAAK,CAAC;IACjB,IAAI,CAACmS,MAAM,GAAG,KAAK,CAAC;IACpB,IAAI,CAACC,aAAa,GAAG,KAAK,CAAC;IAC3B,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB,IAAI,CAACC,OAAO,GAAG,IAAIpG,GAAG,CAAC,CAAC;IACxB,IAAI,CAACqG,QAAQ,GAAG,IAAIrG,GAAG,CAAC,CAAC;IACzB,IAAI,CAACsG,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,KAAK,GAAG,KAAK,CAAC;IACnB,IAAI,CAACxG,MAAM,GAAG;MACZlF,MAAM,EAAE,KAAK;MACbS,UAAU,EAAE,IAAI0E,GAAG,CAAC,CAAC;MACrBlF,WAAW,EAAE,IAAIkF,GAAG,CAAC,CAAC;MACtBhF,QAAQ,EAAE,IAAIgF,GAAG,CAAC;IACpB,CAAC;IACD,IAAI,CAACwG,OAAO,GAAG;MACbjS,OAAO,EAAE,IAAIkS,GAAG,CAAC,CAAC;MAClBjS,QAAQ,EAAE,IAAIiS,GAAG,CAAC,CAAC;MACnB9R,MAAM,EAAE,IAAI8R,GAAG,CAAC;IAClB,CAAC;IACD,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC/U,IAAI,CAAC,IAAI,CAAC;IAExC,IAAIrC,KAAK,EAAE;MACT,IAAI,CAAC2W,MAAM,GAAG3W,KAAK;IACrB;IAEA,IAAI0D,KAAK,EAAE;MACT,IAAI,CAACwE,KAAK,CAAChG,QAAQ,CAAC;QAClByB,OAAO,EAAE;MACX,CAAC,EAAED,KAAK,CAAC,CAAC;IACZ;EACF;EAEA,IAAI8L,IAAIA,CAAA,EAAG;IACT,OAAO,CAAC,IAAI,CAACiB,MAAM,CAAC1D,OAAO,IAAI5K,MAAM,CAAC0I,MAAM,CAAC,IAAI,CAAC6L,OAAO,CAAC,CAAChK,KAAK,CAAC2K,MAAM,IAAI;MACzE,OAAOA,MAAM,CAAC7H,IAAI,IAAI,CAAC6H,MAAM,CAACpG,SAAS,IAAI,CAACoG,MAAM,CAAClH,QAAQ;IAC7D,CAAC,CAAC;EACJ;EAEA,IAAI/B,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC6I,KAAK;EACnB;EAEA,IAAI7I,IAAIA,CAACA,IAAI,EAAE;IACb,IAAI,CAAC6I,KAAK,GAAG7I,IAAI;EACnB;EAEA3B,GAAGA,CAAA,EAAG;IACJ,MAAM5B,MAAM,GAAG,CAAC,CAAC;IACjB,IAAI,CAAChL,IAAI,CAAC,CAACwX,MAAM,EAAE1U,GAAG,KAAKkI,MAAM,CAAClI,GAAG,CAAC,GAAG0U,MAAM,CAAC5K,GAAG,CAAC,CAAC,CAAC;IACtD,OAAO5B,MAAM;EACf;EAEAwI,GAAGA,CAACxI,MAAM,EAAE;IACV,KAAK,MAAMlI,GAAG,IAAIkI,MAAM,EAAE;MACxB,MAAM5H,KAAK,GAAG4H,MAAM,CAAClI,GAAG,CAAC;MAEzB,IAAI,CAACpD,EAAE,CAAC4E,GAAG,CAAClB,KAAK,CAAC,EAAE;QAClB,IAAI,CAACyT,OAAO,CAAC/T,GAAG,CAAC,CAAC0Q,GAAG,CAACpQ,KAAK,CAAC;MAC9B;IACF;EACF;EAEA4C,MAAMA,CAACnC,KAAK,EAAE;IACZ,IAAIA,KAAK,EAAE;MACT,IAAI,CAACsE,KAAK,CAACS,IAAI,CAACqN,YAAY,CAACpS,KAAK,CAAC,CAAC;IACtC;IAEA,OAAO,IAAI;EACb;EAEAwE,KAAKA,CAACxE,KAAK,EAAE;IACX,IAAI;MACFsE;IACF,CAAC,GAAG,IAAI;IAER,IAAItE,KAAK,EAAE;MACTsE,KAAK,GAAGxI,OAAO,CAACkE,KAAK,CAAC,CAACiD,GAAG,CAACmP,YAAY,CAAC;IAC1C,CAAC,MAAM;MACL,IAAI,CAAC9N,KAAK,GAAG,EAAE;IACjB;IAEA,IAAI,IAAI,CAAC2O,MAAM,EAAE;MACf,OAAO,IAAI,CAACA,MAAM,CAAC,IAAI,EAAE3O,KAAK,CAAC;IACjC;IAEAsP,WAAW,CAAC,IAAI,EAAEtP,KAAK,CAAC;IACxB,OAAOuP,gBAAgB,CAAC,IAAI,EAAEvP,KAAK,CAAC;EACtC;EAEAkG,IAAIA,CAACuH,GAAG,EAAEzR,IAAI,EAAE;IACd,IAAIyR,GAAG,KAAK,CAAC,CAACA,GAAG,EAAE;MACjBzR,IAAI,GAAGyR,GAAG;IACZ;IAEA,IAAIzR,IAAI,EAAE;MACR,MAAM0S,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B7W,IAAI,CAACL,OAAO,CAACwE,IAAI,CAAC,EAAErB,GAAG,IAAI+T,OAAO,CAAC/T,GAAG,CAAC,CAACuL,IAAI,CAAC,CAAC,CAACuH,GAAG,CAAC,CAAC;IACtD,CAAC,MAAM;MACLzH,SAAS,CAAC,IAAI,CAACyC,MAAM,EAAE,IAAI,CAACoG,YAAY,CAAC;MACzC,IAAI,CAAChX,IAAI,CAACwX,MAAM,IAAIA,MAAM,CAACnJ,IAAI,CAAC,CAAC,CAACuH,GAAG,CAAC,CAAC;IACzC;IAEA,OAAO,IAAI;EACb;EAEA9Q,KAAKA,CAACX,IAAI,EAAE;IACV,IAAIzE,EAAE,CAAC4E,GAAG,CAACH,IAAI,CAAC,EAAE;MAChB,IAAI,CAACkE,KAAK,CAAC;QACTvD,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAM+R,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B7W,IAAI,CAACL,OAAO,CAACwE,IAAI,CAAC,EAAErB,GAAG,IAAI+T,OAAO,CAAC/T,GAAG,CAAC,CAACgC,KAAK,CAAC,CAAC,CAAC;IAClD;IAEA,OAAO,IAAI;EACb;EAEA8G,MAAMA,CAACzH,IAAI,EAAE;IACX,IAAIzE,EAAE,CAAC4E,GAAG,CAACH,IAAI,CAAC,EAAE;MAChB,IAAI,CAACkE,KAAK,CAAC;QACTvD,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAM+R,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B7W,IAAI,CAACL,OAAO,CAACwE,IAAI,CAAC,EAAErB,GAAG,IAAI+T,OAAO,CAAC/T,GAAG,CAAC,CAAC8I,MAAM,CAAC,CAAC,CAAC;IACnD;IAEA,OAAO,IAAI;EACb;EAEA5L,IAAIA,CAAC2X,QAAQ,EAAE;IACblY,QAAQ,CAAC,IAAI,CAACoX,OAAO,EAAEc,QAAQ,CAAC;EAClC;EAEAJ,QAAQA,CAAA,EAAG;IACT,MAAM;MACJnS,OAAO;MACPC,QAAQ;MACRG;IACF,CAAC,GAAG,IAAI,CAAC6R,OAAO;IAChB,MAAM7G,MAAM,GAAG,IAAI,CAACyG,OAAO,CAACd,IAAI,GAAG,CAAC;IACpC,MAAMpL,OAAO,GAAG,IAAI,CAACmM,QAAQ,CAACf,IAAI,GAAG,CAAC;IAEtC,IAAI3F,MAAM,IAAI,CAAC,IAAI,CAAC2G,QAAQ,IAAIpM,OAAO,IAAI,CAAC,IAAI,CAACoM,QAAQ,EAAE;MACzD,IAAI,CAACA,QAAQ,GAAG,IAAI;MACpBhX,KAAK,CAACiF,OAAO,EAAE,CAAC,CAACA,OAAO,EAAEqH,MAAM,CAAC,KAAK;QACpCA,MAAM,CAACrJ,KAAK,GAAG,IAAI,CAACwJ,GAAG,CAAC,CAAC;QACzBxH,OAAO,CAACqH,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC2K,KAAK,CAAC;MACnC,CAAC,CAAC;IACJ;IAEA,MAAMzH,IAAI,GAAG,CAACa,MAAM,IAAI,IAAI,CAAC2G,QAAQ;IACrC,MAAMnM,MAAM,GAAGD,OAAO,IAAI4E,IAAI,IAAInK,MAAM,CAAC2Q,IAAI,GAAG,IAAI,CAACvJ,GAAG,CAAC,CAAC,GAAG,IAAI;IAEjE,IAAI7B,OAAO,IAAI1F,QAAQ,CAAC8Q,IAAI,EAAE;MAC5BhW,KAAK,CAACkF,QAAQ,EAAE,CAAC,CAACA,QAAQ,EAAEoH,MAAM,CAAC,KAAK;QACtCA,MAAM,CAACrJ,KAAK,GAAG4H,MAAM;QACrB3F,QAAQ,CAACoH,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC2K,KAAK,CAAC;MACpC,CAAC,CAAC;IACJ;IAEA,IAAIzH,IAAI,EAAE;MACR,IAAI,CAACwH,QAAQ,GAAG,KAAK;MACrBhX,KAAK,CAACqF,MAAM,EAAE,CAAC,CAACA,MAAM,EAAEiH,MAAM,CAAC,KAAK;QAClCA,MAAM,CAACrJ,KAAK,GAAG4H,MAAM;QACrBxF,MAAM,CAACiH,MAAM,EAAE,IAAI,EAAE,IAAI,CAAC2K,KAAK,CAAC;MAClC,CAAC,CAAC;IACJ;EACF;EAEArD,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAIA,KAAK,CAACpE,IAAI,IAAI,QAAQ,EAAE;MAC1B,IAAI,CAACsH,QAAQ,CAACxP,GAAG,CAACsM,KAAK,CAACnE,MAAM,CAAC;MAE/B,IAAI,CAACmE,KAAK,CAACrE,IAAI,EAAE;QACf,IAAI,CAACsH,OAAO,CAACvP,GAAG,CAACsM,KAAK,CAACnE,MAAM,CAAC;MAChC;IACF,CAAC,MAAM,IAAImE,KAAK,CAACpE,IAAI,IAAI,MAAM,EAAE;MAC/B,IAAI,CAACqH,OAAO,CAAC1P,MAAM,CAACyM,KAAK,CAACnE,MAAM,CAAC;IACnC,CAAC,MAAM;IAEP3P,GAAG,CAAC0X,OAAO,CAAC,IAAI,CAACL,QAAQ,CAAC;EAC5B;AAEF;AACA,SAASG,gBAAgBA,CAACrQ,IAAI,EAAEc,KAAK,EAAE;EACrC,OAAOI,OAAO,CAACM,GAAG,CAACV,KAAK,CAACrB,GAAG,CAACjD,KAAK,IAAIgU,WAAW,CAACxQ,IAAI,EAAExD,KAAK,CAAC,CAAC,CAAC,CAAC8E,IAAI,CAAC4D,OAAO,IAAID,iBAAiB,CAACjF,IAAI,EAAEkF,OAAO,CAAC,CAAC;AACpH;AACA,eAAesL,WAAWA,CAACxQ,IAAI,EAAExD,KAAK,EAAEuQ,MAAM,EAAE;EAC9C,MAAM;IACJjQ,IAAI;IACJO,EAAE;IACFD,IAAI;IACJG,IAAI;IACJY,MAAM;IACNC;EACF,CAAC,GAAG5B,KAAK;EACT,MAAMQ,QAAQ,GAAG3E,EAAE,CAACiE,GAAG,CAACE,KAAK,CAACC,OAAO,CAAC,IAAID,KAAK,CAACC,OAAO;EAEvD,IAAIc,IAAI,EAAE;IACRf,KAAK,CAACe,IAAI,GAAG,KAAK;EACpB;EAEA,IAAIF,EAAE,KAAK,KAAK,EAAEb,KAAK,CAACa,EAAE,GAAG,IAAI;EACjC,IAAID,IAAI,KAAK,KAAK,EAAEZ,KAAK,CAACY,IAAI,GAAG,IAAI;EACrC,MAAMyI,OAAO,GAAGxN,EAAE,CAACmH,GAAG,CAACnC,EAAE,CAAC,IAAIhF,EAAE,CAAC4D,GAAG,CAACoB,EAAE,CAAC,GAAGA,EAAE,GAAGX,SAAS;EAEzD,IAAImJ,OAAO,EAAE;IACXrJ,KAAK,CAACa,EAAE,GAAGX,SAAS;IACpBF,KAAK,CAAC2B,MAAM,GAAGzB,SAAS;IAExB,IAAIM,QAAQ,EAAE;MACZA,QAAQ,CAACmB,MAAM,GAAGzB,SAAS;IAC7B;EACF,CAAC,MAAM;IACL/D,IAAI,CAAC0W,cAAc,EAAE5T,GAAG,IAAI;MAC1B,MAAMgV,OAAO,GAAGjU,KAAK,CAACf,GAAG,CAAC;MAE1B,IAAIpD,EAAE,CAAC4D,GAAG,CAACwU,OAAO,CAAC,EAAE;QACnB,MAAM3P,KAAK,GAAGd,IAAI,CAAC,SAAS,CAAC,CAACvE,GAAG,CAAC;QAElCe,KAAK,CAACf,GAAG,CAAC,GAAG,CAAC;UACZkK,QAAQ;UACRN;QACF,CAAC,KAAK;UACJ,MAAMD,MAAM,GAAGtE,KAAK,CAACyE,GAAG,CAACkL,OAAO,CAAC;UAEjC,IAAIrL,MAAM,EAAE;YACV,IAAI,CAACO,QAAQ,EAAEP,MAAM,CAACO,QAAQ,GAAG,KAAK;YACtC,IAAIN,SAAS,EAAED,MAAM,CAACC,SAAS,GAAG,IAAI;UACxC,CAAC,MAAM;YACLvE,KAAK,CAACqL,GAAG,CAACsE,OAAO,EAAE;cACjB1U,KAAK,EAAE,IAAI;cACX4J,QAAQ,EAAEA,QAAQ,IAAI,KAAK;cAC3BN,SAAS,EAAEA,SAAS,IAAI;YAC1B,CAAC,CAAC;UACJ;QACF,CAAC;QAED,IAAIrI,QAAQ,EAAE;UACZA,QAAQ,CAACvB,GAAG,CAAC,GAAGe,KAAK,CAACf,GAAG,CAAC;QAC5B;MACF;IACF,CAAC,CAAC;EACJ;EAEA,MAAMuI,KAAK,GAAGhE,IAAI,CAAC,QAAQ,CAAC;EAE5B,IAAIxD,KAAK,CAACiB,KAAK,KAAK,CAACuG,KAAK,CAACK,MAAM,EAAE;IACjCL,KAAK,CAACK,MAAM,GAAG7H,KAAK,CAACiB,KAAK;IAC1BrE,UAAU,CAACoD,KAAK,CAACiB,KAAK,GAAGuG,KAAK,CAACc,UAAU,GAAGd,KAAK,CAACM,WAAW,CAAC;EAChE,CAAC,MAAM,IAAIN,KAAK,CAACK,MAAM,EAAE;IACvB7H,KAAK,CAACiB,KAAK,GAAG,IAAI;EACpB;EAEA,MAAMiT,QAAQ,GAAG,CAAC5T,IAAI,IAAI7B,MAAM,CAAC6B,IAAI,CAACkD,IAAI,CAACwP,OAAO,CAAC,EAAE/P,GAAG,CAAChE,GAAG,IAAIuE,IAAI,CAACwP,OAAO,CAAC/T,GAAG,CAAC,CAACuF,KAAK,CAACxE,KAAK,CAAC,CAAC;EAC/F,MAAMkB,MAAM,GAAGlB,KAAK,CAACkB,MAAM,KAAK,IAAI,IAAInB,cAAc,CAACC,KAAK,EAAE,QAAQ,CAAC,KAAK,IAAI;EAEhF,IAAIqJ,OAAO,IAAInI,MAAM,IAAIsG,KAAK,CAACiC,OAAO,EAAE;IACtCyK,QAAQ,CAACnP,IAAI,CAACuC,aAAa,CAAC,EAAE9D,IAAI,CAAC,cAAc,CAAC,EAAE;MAClDxD,KAAK;MACLwH,KAAK;MACLC,OAAO,EAAE;QACPxG,KAAK,EAAEhE,IAAI;QACX8K,MAAM,EAAE9K,IAAI;QAEZuH,KAAKA,CAACxE,KAAK,EAAE2E,OAAO,EAAE;UACpB,IAAIzD,MAAM,EAAE;YACVoJ,SAAS,CAAC9C,KAAK,EAAEhE,IAAI,CAAC,cAAc,CAAC,CAAC;YACtCmB,OAAO,CAACmE,kBAAkB,CAACtF,IAAI,CAAC,CAAC;UACnC,CAAC,MAAM;YACLxD,KAAK,CAAC2B,MAAM,GAAGA,MAAM;YACrBgD,OAAO,CAACyE,QAAQ,CAACC,OAAO,EAAErJ,KAAK,EAAEwH,KAAK,EAAEhE,IAAI,CAAC,CAAC;UAChD;QACF;MAEF;IACF,CAAC,CAAC,CAAC;EACL;EAEA,IAAIgE,KAAK,CAACK,MAAM,EAAE;IAChB,MAAM,IAAInD,OAAO,CAACqD,MAAM,IAAI;MAC1BP,KAAK,CAACM,WAAW,CAACjE,GAAG,CAACkE,MAAM,CAAC;IAC/B,CAAC,CAAC;EACJ;EAEA,MAAMa,MAAM,GAAGH,iBAAiB,CAACjF,IAAI,EAAE,MAAMkB,OAAO,CAACM,GAAG,CAACkP,QAAQ,CAAC,CAAC;EAEnE,IAAInT,IAAI,IAAI6H,MAAM,CAACO,QAAQ,IAAI,EAAEoH,MAAM,IAAI3H,MAAM,CAAC3L,IAAI,CAAC,EAAE;IACvD,MAAM8T,SAAS,GAAGC,gBAAgB,CAAChR,KAAK,EAAEe,IAAI,EAAEF,EAAE,CAAC;IAEnD,IAAIkQ,SAAS,EAAE;MACb6C,WAAW,CAACpQ,IAAI,EAAE,CAACuN,SAAS,CAAC,CAAC;MAC9B,OAAOiD,WAAW,CAACxQ,IAAI,EAAEuN,SAAS,EAAE,IAAI,CAAC;IAC3C;EACF;EAEA,IAAInP,SAAS,EAAE;IACbvF,GAAG,CAACoO,cAAc,CAAC,MAAM7I,SAAS,CAACgH,MAAM,EAAEpF,IAAI,EAAEA,IAAI,CAACkH,IAAI,CAAC,CAAC;EAC9D;EAEA,OAAO9B,MAAM;AACf;AACA,SAASuL,UAAUA,CAAC3Q,IAAI,EAAExD,KAAK,EAAE;EAC/B,MAAMgT,OAAO,GAAGxU,QAAQ,CAAC,CAAC,CAAC,EAAEgF,IAAI,CAACwP,OAAO,CAAC;EAE1C,IAAIhT,KAAK,EAAE;IACT7D,IAAI,CAACL,OAAO,CAACkE,KAAK,CAAC,EAAEA,KAAK,IAAI;MAC5B,IAAInE,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAACM,IAAI,CAAC,EAAE;QACtBN,KAAK,GAAGoS,YAAY,CAACpS,KAAK,CAAC;MAC7B;MAEA,IAAI,CAACnE,EAAE,CAACiE,GAAG,CAACE,KAAK,CAACa,EAAE,CAAC,EAAE;QACrBb,KAAK,GAAGxB,QAAQ,CAAC,CAAC,CAAC,EAAEwB,KAAK,EAAE;UAC1Ba,EAAE,EAAEX;QACN,CAAC,CAAC;MACJ;MAEAkU,cAAc,CAACpB,OAAO,EAAEhT,KAAK,EAAEf,GAAG,IAAI;QACpC,OAAOoV,YAAY,CAACpV,GAAG,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAqV,UAAU,CAAC9Q,IAAI,EAAEwP,OAAO,CAAC;EACzB,OAAOA,OAAO;AAChB;AACA,SAASsB,UAAUA,CAAC9Q,IAAI,EAAEwP,OAAO,EAAE;EACjCpX,QAAQ,CAACoX,OAAO,EAAE,CAACW,MAAM,EAAE1U,GAAG,KAAK;IACjC,IAAI,CAACuE,IAAI,CAACwP,OAAO,CAAC/T,GAAG,CAAC,EAAE;MACtBuE,IAAI,CAACwP,OAAO,CAAC/T,GAAG,CAAC,GAAG0U,MAAM;MAC1B5W,gBAAgB,CAAC4W,MAAM,EAAEnQ,IAAI,CAAC;IAChC;EACF,CAAC,CAAC;AACJ;AAEA,SAAS6Q,YAAYA,CAACpV,GAAG,EAAEsV,QAAQ,EAAE;EACnC,MAAMZ,MAAM,GAAG,IAAI9G,WAAW,CAAC,CAAC;EAChC8G,MAAM,CAAC1U,GAAG,GAAGA,GAAG;EAEhB,IAAIsV,QAAQ,EAAE;IACZxX,gBAAgB,CAAC4W,MAAM,EAAEY,QAAQ,CAAC;EACpC;EAEA,OAAOZ,MAAM;AACf;AAEA,SAASS,cAAcA,CAACpB,OAAO,EAAEhT,KAAK,EAAEiS,MAAM,EAAE;EAC9C,IAAIjS,KAAK,CAACM,IAAI,EAAE;IACdnE,IAAI,CAAC6D,KAAK,CAACM,IAAI,EAAErB,GAAG,IAAI;MACtB,MAAM0U,MAAM,GAAGX,OAAO,CAAC/T,GAAG,CAAC,KAAK+T,OAAO,CAAC/T,GAAG,CAAC,GAAGgT,MAAM,CAAChT,GAAG,CAAC,CAAC;MAC3D0U,MAAM,CAAC,cAAc,CAAC,CAAC3T,KAAK,CAAC;IAC/B,CAAC,CAAC;EACJ;AACF;AAEA,SAAS4T,WAAWA,CAACpQ,IAAI,EAAEc,KAAK,EAAE;EAChCnI,IAAI,CAACmI,KAAK,EAAEtE,KAAK,IAAI;IACnBoU,cAAc,CAAC5Q,IAAI,CAACwP,OAAO,EAAEhT,KAAK,EAAEf,GAAG,IAAI;MACzC,OAAOoV,YAAY,CAACpV,GAAG,EAAEuE,IAAI,CAAC;IAChC,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ;AAEA,SAASgR,6BAA6BA,CAACxV,MAAM,EAAEyV,QAAQ,EAAE;EACvD,IAAIzV,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;EAC7B,IAAIJ,MAAM,GAAG,CAAC,CAAC;EACf,IAAI8V,UAAU,GAAGjW,MAAM,CAAC6B,IAAI,CAACtB,MAAM,CAAC;EACpC,IAAIC,GAAG,EAAEJ,CAAC;EAEV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG6V,UAAU,CAAC3V,MAAM,EAAEF,CAAC,EAAE,EAAE;IACtCI,GAAG,GAAGyV,UAAU,CAAC7V,CAAC,CAAC;IACnB,IAAI4V,QAAQ,CAACE,OAAO,CAAC1V,GAAG,CAAC,IAAI,CAAC,EAAE;IAChCL,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;EAC3B;EAEA,OAAOL,MAAM;AACf;AAEA,MAAMgW,WAAW,GAAG,CAAC,UAAU,CAAC;AAChC,MAAMC,aAAa,GAAGvE,IAAI,IAAI;EAC5B,IAAI;MACFjO;IACF,CAAC,GAAGiO,IAAI;IACJtQ,KAAK,GAAGwU,6BAA6B,CAAClE,IAAI,EAAEsE,WAAW,CAAC;EAE5D,MAAME,SAAS,GAAGhX,UAAU,CAACiX,GAAG,CAAC;EACjC,MAAM9T,KAAK,GAAGjB,KAAK,CAACiB,KAAK,IAAI,CAAC,CAAC6T,SAAS,CAAC7T,KAAK;IACxCG,SAAS,GAAGpB,KAAK,CAACoB,SAAS,IAAI,CAAC,CAAC0T,SAAS,CAAC1T,SAAS;EAC1DpB,KAAK,GAAG9C,UAAU,CAAC,OAAO;IACxB+D,KAAK;IACLG;EACF,CAAC,CAAC,EAAE,CAACH,KAAK,EAAEG,SAAS,CAAC,CAAC;EACvB,MAAM;IACJ4T;EACF,CAAC,GAAGD,GAAG;EACP,OAAOlX,KAAK,CAACoX,aAAa,CAACD,QAAQ,EAAE;IACnCzV,KAAK,EAAES;EACT,CAAC,EAAEqC,QAAQ,CAAC;AACd,CAAC;AACD,MAAM0S,GAAG,GAAGG,WAAW,CAACL,aAAa,EAAE,CAAC,CAAC,CAAC;AAC1CA,aAAa,CAACG,QAAQ,GAAGD,GAAG,CAACC,QAAQ;AACrCH,aAAa,CAACM,QAAQ,GAAGJ,GAAG,CAACI,QAAQ;AAErC,SAASD,WAAWA,CAACtW,MAAM,EAAEwW,IAAI,EAAE;EACjC3W,MAAM,CAACC,MAAM,CAACE,MAAM,EAAEf,KAAK,CAACwX,aAAa,CAACD,IAAI,CAAC,CAAC;EAChDxW,MAAM,CAACoW,QAAQ,CAACM,QAAQ,GAAG1W,MAAM;EACjCA,MAAM,CAACuW,QAAQ,CAACG,QAAQ,GAAG1W,MAAM;EACjC,OAAOA,MAAM;AACf;AAEA,MAAM2W,SAAS,GAAGA,CAAA,KAAM;EACtB,MAAMnR,OAAO,GAAG,EAAE;EAElB,MAAMmR,SAAS,GAAG,SAASA,SAASA,CAACvV,KAAK,EAAE;IAC1C7C,mBAAmB,CAAC,CAAC;IACrB,MAAMuL,OAAO,GAAG,EAAE;IAClBvM,IAAI,CAACiI,OAAO,EAAE,CAACZ,IAAI,EAAE3E,CAAC,KAAK;MACzB,IAAIhD,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAAC,EAAE;QACjB0I,OAAO,CAAC3D,IAAI,CAACvB,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL,MAAMrC,MAAM,GAAGqT,SAAS,CAACxV,KAAK,EAAEwD,IAAI,EAAE3E,CAAC,CAAC;QAExC,IAAIsD,MAAM,EAAE;UACVuG,OAAO,CAAC3D,IAAI,CAACvB,IAAI,CAACgB,KAAK,CAACrC,MAAM,CAAC,CAAC;QAClC;MACF;IACF,CAAC,CAAC;IACF,OAAOuG,OAAO;EAChB,CAAC;EAED6M,SAAS,CAACnR,OAAO,GAAGA,OAAO;EAE3BmR,SAAS,CAAC1R,GAAG,GAAG,UAAUL,IAAI,EAAE;IAC9B,IAAI,CAACY,OAAO,CAACzE,QAAQ,CAAC6D,IAAI,CAAC,EAAE;MAC3BY,OAAO,CAACW,IAAI,CAACvB,IAAI,CAAC;IACpB;EACF,CAAC;EAED+R,SAAS,CAAC7R,MAAM,GAAG,UAAUF,IAAI,EAAE;IACjC,MAAM3E,CAAC,GAAGuF,OAAO,CAACuQ,OAAO,CAACnR,IAAI,CAAC;IAC/B,IAAI,CAAC3E,CAAC,EAAEuF,OAAO,CAACqR,MAAM,CAAC5W,CAAC,EAAE,CAAC,CAAC;EAC9B,CAAC;EAED0W,SAAS,CAACtU,KAAK,GAAG,YAAY;IAC5B9E,IAAI,CAACiI,OAAO,EAAEZ,IAAI,IAAIA,IAAI,CAACvC,KAAK,CAAC,GAAGnC,SAAS,CAAC,CAAC;IAC/C,OAAO,IAAI;EACb,CAAC;EAEDyW,SAAS,CAACxN,MAAM,GAAG,YAAY;IAC7B5L,IAAI,CAACiI,OAAO,EAAEZ,IAAI,IAAIA,IAAI,CAACuE,MAAM,CAAC,GAAGjJ,SAAS,CAAC,CAAC;IAChD,OAAO,IAAI;EACb,CAAC;EAEDyW,SAAS,CAAC5F,GAAG,GAAG,UAAUxI,MAAM,EAAE;IAChChL,IAAI,CAACiI,OAAO,EAAEZ,IAAI,IAAIA,IAAI,CAACmM,GAAG,CAACxI,MAAM,CAAC,CAAC;EACzC,CAAC;EAEDoO,SAAS,CAAC/Q,KAAK,GAAG,UAAUxE,KAAK,EAAE;IACjC,MAAM0I,OAAO,GAAG,EAAE;IAClBvM,IAAI,CAACiI,OAAO,EAAE,CAACZ,IAAI,EAAE3E,CAAC,KAAK;MACzB,IAAIhD,EAAE,CAAC4E,GAAG,CAACT,KAAK,CAAC,EAAE;QACjB0I,OAAO,CAAC3D,IAAI,CAACvB,IAAI,CAACgB,KAAK,CAAC,CAAC,CAAC;MAC5B,CAAC,MAAM;QACL,MAAMrC,MAAM,GAAG,IAAI,CAACqT,SAAS,CAACxV,KAAK,EAAEwD,IAAI,EAAE3E,CAAC,CAAC;QAE7C,IAAIsD,MAAM,EAAE;UACVuG,OAAO,CAAC3D,IAAI,CAACvB,IAAI,CAACgB,KAAK,CAACrC,MAAM,CAAC,CAAC;QAClC;MACF;IACF,CAAC,CAAC;IACF,OAAOuG,OAAO;EAChB,CAAC;EAED6M,SAAS,CAAC/K,IAAI,GAAG,YAAY;IAC3BrO,IAAI,CAACiI,OAAO,EAAEZ,IAAI,IAAIA,IAAI,CAACgH,IAAI,CAAC,GAAG1L,SAAS,CAAC,CAAC;IAC9C,OAAO,IAAI;EACb,CAAC;EAEDyW,SAAS,CAACpT,MAAM,GAAG,UAAUnC,KAAK,EAAE;IAClC7D,IAAI,CAACiI,OAAO,EAAE,CAACZ,IAAI,EAAE3E,CAAC,KAAK2E,IAAI,CAACrB,MAAM,CAAC,IAAI,CAACqT,SAAS,CAACxV,KAAK,EAAEwD,IAAI,EAAE3E,CAAC,CAAC,CAAC,CAAC;IACvE,OAAO,IAAI;EACb,CAAC;EAED,MAAM2W,SAAS,GAAG,SAASA,SAASA,CAACzD,GAAG,EAAEvO,IAAI,EAAEkS,KAAK,EAAE;IACrD,OAAO7Z,EAAE,CAAC4D,GAAG,CAACsS,GAAG,CAAC,GAAGA,GAAG,CAAC2D,KAAK,EAAElS,IAAI,CAAC,GAAGuO,GAAG;EAC7C,CAAC;EAEDwD,SAAS,CAACC,SAAS,GAAGA,SAAS;EAC/B,OAAOD,SAAS;AAClB,CAAC;AAED,SAASI,UAAUA,CAAC5W,MAAM,EAAEiB,KAAK,EAAE4V,IAAI,EAAE;EACvC,MAAMC,OAAO,GAAGha,EAAE,CAAC4D,GAAG,CAACO,KAAK,CAAC,IAAIA,KAAK;EACtC,IAAI6V,OAAO,IAAI,CAACD,IAAI,EAAEA,IAAI,GAAG,EAAE;EAC/B,MAAM9U,GAAG,GAAG/C,OAAO,CAAC,MAAM8X,OAAO,IAAI/W,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGwW,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC;EACtF,MAAMO,QAAQ,GAAG9X,MAAM,CAAC,CAAC,CAAC;EAC1B,MAAM+X,WAAW,GAAG3Y,cAAc,CAAC,CAAC;EACpC,MAAMoK,KAAK,GAAGzJ,OAAO,CAAC,OAAO;IAC3BiY,KAAK,EAAE,EAAE;IACT1R,KAAK,EAAE,EAAE;IAEThI,KAAKA,CAACkH,IAAI,EAAEyS,OAAO,EAAE;MACnB,MAAMjD,OAAO,GAAGmB,UAAU,CAAC3Q,IAAI,EAAEyS,OAAO,CAAC;MACzC,MAAMC,YAAY,GAAGJ,QAAQ,CAAC1R,OAAO,GAAG,CAAC,IAAI,CAACoD,KAAK,CAAClD,KAAK,CAACvF,MAAM,IAAI,CAACN,MAAM,CAAC6B,IAAI,CAAC0S,OAAO,CAAC,CAACrK,IAAI,CAAC1J,GAAG,IAAI,CAACuE,IAAI,CAACwP,OAAO,CAAC/T,GAAG,CAAC,CAAC;MACzH,OAAOiX,YAAY,GAAGrC,gBAAgB,CAACrQ,IAAI,EAAEyS,OAAO,CAAC,GAAG,IAAIvR,OAAO,CAACC,OAAO,IAAI;QAC7E2P,UAAU,CAAC9Q,IAAI,EAAEwP,OAAO,CAAC;QACzBxL,KAAK,CAAClD,KAAK,CAACS,IAAI,CAAC,MAAM;UACrBJ,OAAO,CAACkP,gBAAgB,CAACrQ,IAAI,EAAEyS,OAAO,CAAC,CAAC;QAC1C,CAAC,CAAC;QACFF,WAAW,CAAC,CAAC;MACf,CAAC,CAAC;IACJ;EAEF,CAAC,CAAC,EAAE,EAAE,CAAC;EACP,MAAMC,KAAK,GAAGhY,MAAM,CAAC,CAAC,GAAGwJ,KAAK,CAACwO,KAAK,CAAC,CAAC;EACtC,MAAMC,OAAO,GAAG,EAAE;EAClB,MAAME,UAAU,GAAG9Y,OAAO,CAAC0B,MAAM,CAAC,IAAI,CAAC;EACvChB,OAAO,CAAC,MAAM;IACZ5B,IAAI,CAAC6Z,KAAK,CAAC5R,OAAO,CAACgS,KAAK,CAACrX,MAAM,EAAEoX,UAAU,CAAC,EAAE3S,IAAI,IAAI;MACpDD,UAAU,CAACC,IAAI,EAAE1C,GAAG,CAAC;MACrB0C,IAAI,CAACgH,IAAI,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC;IACFwL,KAAK,CAAC5R,OAAO,CAACrF,MAAM,GAAGA,MAAM;IAC7BsX,cAAc,CAACF,UAAU,EAAEpX,MAAM,CAAC;EACpC,CAAC,EAAE,CAACA,MAAM,CAAC,CAAC;EACZhB,OAAO,CAAC,MAAM;IACZsY,cAAc,CAAC,CAAC,EAAEzP,IAAI,CAACuH,GAAG,CAACgI,UAAU,EAAEpX,MAAM,CAAC,CAAC;EACjD,CAAC,EAAE6W,IAAI,CAAC;EAER,SAASS,cAAcA,CAACC,UAAU,EAAEC,QAAQ,EAAE;IAC5C,KAAK,IAAI1X,CAAC,GAAGyX,UAAU,EAAEzX,CAAC,GAAG0X,QAAQ,EAAE1X,CAAC,EAAE,EAAE;MAC1C,MAAM2E,IAAI,GAAGwS,KAAK,CAAC5R,OAAO,CAACvF,CAAC,CAAC,KAAKmX,KAAK,CAAC5R,OAAO,CAACvF,CAAC,CAAC,GAAG,IAAIkU,UAAU,CAAC,IAAI,EAAEvL,KAAK,CAAClL,KAAK,CAAC,CAAC;MACvF,MAAM6F,MAAM,GAAG0T,OAAO,GAAGA,OAAO,CAAChX,CAAC,EAAE2E,IAAI,CAAC,GAAGxD,KAAK,CAACnB,CAAC,CAAC;MAEpD,IAAIsD,MAAM,EAAE;QACV8T,OAAO,CAACpX,CAAC,CAAC,GAAG2T,aAAa,CAACrQ,MAAM,CAAC;MACpC;IACF;EACF;EAEA,MAAM6Q,OAAO,GAAGgD,KAAK,CAAC5R,OAAO,CAACnB,GAAG,CAAC,CAACO,IAAI,EAAE3E,CAAC,KAAKsV,UAAU,CAAC3Q,IAAI,EAAEyS,OAAO,CAACpX,CAAC,CAAC,CAAC,CAAC;EAC5E,MAAM2X,OAAO,GAAG1Y,UAAU,CAAC+W,aAAa,CAAC;EACzC,MAAM4B,WAAW,GAAGpZ,OAAO,CAACmZ,OAAO,CAAC;EACpC,MAAME,UAAU,GAAGF,OAAO,KAAKC,WAAW,IAAIrT,QAAQ,CAACoT,OAAO,CAAC;EAC/Dta,yBAAyB,CAAC,MAAM;IAC9B4Z,QAAQ,CAAC1R,OAAO,EAAE;IAClBoD,KAAK,CAACwO,KAAK,GAAGA,KAAK,CAAC5R,OAAO;IAC3B,MAAM;MACJE;IACF,CAAC,GAAGkD,KAAK;IAET,IAAIlD,KAAK,CAACvF,MAAM,EAAE;MAChByI,KAAK,CAAClD,KAAK,GAAG,EAAE;MAChBnI,IAAI,CAACmI,KAAK,EAAEqS,EAAE,IAAIA,EAAE,CAAC,CAAC,CAAC;IACzB;IAEAxa,IAAI,CAAC6Z,KAAK,CAAC5R,OAAO,EAAE,CAACZ,IAAI,EAAE3E,CAAC,KAAK;MAC/BiC,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC+C,GAAG,CAACL,IAAI,CAAC;MAEpC,IAAIkT,UAAU,EAAE;QACdlT,IAAI,CAACgB,KAAK,CAAC;UACTvE,OAAO,EAAEuW;QACX,CAAC,CAAC;MACJ;MAEA,MAAMrU,MAAM,GAAG8T,OAAO,CAACpX,CAAC,CAAC;MAEzB,IAAIsD,MAAM,EAAE;QACVwB,UAAU,CAACH,IAAI,EAAErB,MAAM,CAACrB,GAAG,CAAC;QAE5B,IAAI0C,IAAI,CAAC1C,GAAG,EAAE;UACZ0C,IAAI,CAACc,KAAK,CAACS,IAAI,CAAC5C,MAAM,CAAC;QACzB,CAAC,MAAM;UACLqB,IAAI,CAACgB,KAAK,CAACrC,MAAM,CAAC;QACpB;MACF;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF7E,OAAO,CAAC,MAAM,MAAM;IAClBnB,IAAI,CAACqL,KAAK,CAACwO,KAAK,EAAExS,IAAI,IAAIA,IAAI,CAACgH,IAAI,CAAC,IAAI,CAAC,CAAC;EAC5C,CAAC,CAAC;EACF,MAAMrD,MAAM,GAAG6L,OAAO,CAAC/P,GAAG,CAAC2T,CAAC,IAAIpY,QAAQ,CAAC,CAAC,CAAC,EAAEoY,CAAC,CAAC,CAAC;EAChD,OAAO9V,GAAG,GAAG,CAACqG,MAAM,EAAErG,GAAG,CAAC,GAAGqG,MAAM;AACrC;AAEA,SAAS0P,SAASA,CAAC7W,KAAK,EAAE4V,IAAI,EAAE;EAC9B,MAAMkB,IAAI,GAAGjb,EAAE,CAAC4D,GAAG,CAACO,KAAK,CAAC;EAC1B,MAAM,CAAC,CAACmH,MAAM,CAAC,EAAErG,GAAG,CAAC,GAAG6U,UAAU,CAAC,CAAC,EAAEmB,IAAI,GAAG9W,KAAK,GAAG,CAACA,KAAK,CAAC,EAAE8W,IAAI,GAAGlB,IAAI,IAAI,EAAE,GAAGA,IAAI,CAAC;EACvF,OAAOkB,IAAI,IAAIhY,SAAS,CAACC,MAAM,IAAI,CAAC,GAAG,CAACoI,MAAM,EAAErG,GAAG,CAAC,GAAGqG,MAAM;AAC/D;AAEA,MAAM4P,aAAa,GAAGA,CAAA,KAAMxB,SAAS,CAAC,CAAC;AAEvC,MAAMyB,YAAY,GAAGA,CAAA,KAAM/Y,QAAQ,CAAC8Y,aAAa,CAAC,CAAC,CAAC,CAAC;AAErD,MAAME,cAAc,GAAGA,CAAChV,OAAO,EAAEjC,KAAK,KAAK;EACzC,MAAMkX,WAAW,GAAG3Z,WAAW,CAAC,MAAM,IAAIsP,WAAW,CAAC5K,OAAO,EAAEjC,KAAK,CAAC,CAAC;EACtE1C,OAAO,CAAC,MAAM,MAAM;IAClB4Z,WAAW,CAAC1M,IAAI,CAAC,CAAC;EACpB,CAAC,CAAC;EACF,OAAO0M,WAAW;AACpB,CAAC;AAED,SAASC,QAAQA,CAACpY,MAAM,EAAEqY,QAAQ,EAAExB,IAAI,EAAE;EACxC,MAAMC,OAAO,GAAGha,EAAE,CAAC4D,GAAG,CAAC2X,QAAQ,CAAC,IAAIA,QAAQ;EAC5C,IAAIvB,OAAO,IAAI,CAACD,IAAI,EAAEA,IAAI,GAAG,EAAE;EAC/B,IAAIzU,OAAO,GAAG,IAAI;EAClB,IAAIkW,SAAS,GAAGnX,SAAS;EACzB,MAAM0I,MAAM,GAAG+M,UAAU,CAAC5W,MAAM,EAAE,CAACF,CAAC,EAAE2E,IAAI,KAAK;IAC7C,MAAMxD,KAAK,GAAG6V,OAAO,GAAGA,OAAO,CAAChX,CAAC,EAAE2E,IAAI,CAAC,GAAG4T,QAAQ;IACnDC,SAAS,GAAGrX,KAAK,CAACc,GAAG;IACrBK,OAAO,GAAGA,OAAO,IAAInB,KAAK,CAACmB,OAAO;IAClC,OAAOnB,KAAK;EACd,CAAC,EAAE4V,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;EAChB1Z,yBAAyB,CAAC,MAAM;IAC9BC,IAAI,CAACyM,MAAM,CAAC,CAAC,CAAC,CAACxE,OAAO,EAAE,CAACZ,IAAI,EAAE3E,CAAC,KAAK;MACnC,MAAMmN,MAAM,GAAGpD,MAAM,CAAC,CAAC,CAAC,CAACxE,OAAO,CAACvF,CAAC,IAAIsC,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;MACxDwC,UAAU,CAACH,IAAI,EAAE6T,SAAS,CAAC;MAE3B,IAAI7T,IAAI,CAAC1C,GAAG,EAAE;QACZ,IAAIkL,MAAM,EAAE;UACVxI,IAAI,CAACrB,MAAM,CAAC;YACVtB,EAAE,EAAEmL,MAAM,CAACgH;UACb,CAAC,CAAC;QACJ;QAEA;MACF;MAEA,IAAIhH,MAAM,EAAE;QACVxI,IAAI,CAACgB,KAAK,CAAC;UACT3D,EAAE,EAAEmL,MAAM,CAACgH;QACb,CAAC,CAAC;MACJ,CAAC,MAAM;QACLxP,IAAI,CAACgB,KAAK,CAAC,CAAC;MACd;IACF,CAAC,CAAC;EACJ,CAAC,EAAEoR,IAAI,CAAC;EAER,IAAIC,OAAO,IAAI/W,SAAS,CAACC,MAAM,IAAI,CAAC,EAAE;IACpC,IAAIuY,UAAU;IAEd,MAAMxW,GAAG,GAAG,CAACwW,UAAU,GAAGD,SAAS,KAAK,IAAI,GAAGC,UAAU,GAAG1O,MAAM,CAAC,CAAC,CAAC;IAErE9H,GAAG,CAAC,WAAW,CAAC,GAAG,CAACsW,QAAQ,EAAE5T,IAAI,EAAE3E,CAAC,KAAK;MACxC,MAAMmB,KAAK,GAAGnE,EAAE,CAAC4D,GAAG,CAAC2X,QAAQ,CAAC,GAAGA,QAAQ,CAACvY,CAAC,EAAE2E,IAAI,CAAC,GAAG4T,QAAQ;MAE7D,IAAIpX,KAAK,EAAE;QACT,MAAMgM,MAAM,GAAGlL,GAAG,CAACsD,OAAO,CAACvF,CAAC,IAAImB,KAAK,CAACmB,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;QACxD,IAAI6K,MAAM,EAAEhM,KAAK,CAACa,EAAE,GAAGmL,MAAM,CAACgH,OAAO;QACrC,OAAOhT,KAAK;MACd;IACF,CAAC;IAED,OAAO4I,MAAM;EACf;EAEA,OAAOA,MAAM,CAAC,CAAC,CAAC;AAClB;AAEA,IAAI2O,eAAe;AAEnB,CAAC,UAAUA,eAAe,EAAE;EAC1BA,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO;EAClCA,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO;EAClCA,eAAe,CAAC,QAAQ,CAAC,GAAG,QAAQ;EACpCA,eAAe,CAAC,OAAO,CAAC,GAAG,OAAO;AACpC,CAAC,EAAEA,eAAe,KAAKA,eAAe,GAAG,CAAC,CAAC,CAAC,CAAC;AAE7C,SAASC,aAAaA,CAACC,IAAI,EAAEzX,KAAK,EAAE4V,IAAI,EAAE;EACxC,MAAMC,OAAO,GAAGha,EAAE,CAAC4D,GAAG,CAACO,KAAK,CAAC,IAAIA,KAAK;EACtC,MAAM;IACJgB,KAAK;IACLe,IAAI;IACJD,KAAK,GAAG,CAAC;IACTE,OAAO,GAAG,IAAI;IACd0V,eAAe,GAAG,KAAK;IACvBpV,WAAW;IACXxB,GAAG,EAAE6W,QAAQ;IACbhX,MAAM,EAAEiX;EACV,CAAC,GAAG/B,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAG7V,KAAK;EAC/B,MAAMc,GAAG,GAAG/C,OAAO,CAAC,MAAM8X,OAAO,IAAI/W,SAAS,CAACC,MAAM,IAAI,CAAC,GAAGwW,SAAS,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE,CAAC;EACtF,MAAM1T,KAAK,GAAG/F,OAAO,CAAC2b,IAAI,CAAC;EAC3B,MAAMI,WAAW,GAAG,EAAE;EACtB,MAAMC,eAAe,GAAG9Z,MAAM,CAAC,IAAI,CAAC;EACpC,MAAM+Z,eAAe,GAAG/W,KAAK,GAAG,IAAI,GAAG8W,eAAe,CAAC1T,OAAO;EAC9DlI,yBAAyB,CAAC,MAAM;IAC9B4b,eAAe,CAAC1T,OAAO,GAAGyT,WAAW;EACvC,CAAC,CAAC;EACFva,OAAO,CAAC,MAAM;IACZnB,IAAI,CAAC0b,WAAW,EAAElN,CAAC,IAAI;MACrB7J,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC+C,GAAG,CAAC8G,CAAC,CAACnH,IAAI,CAAC;MACtCmH,CAAC,CAACnH,IAAI,CAAC1C,GAAG,GAAGA,GAAG;IAClB,CAAC,CAAC;IACF,OAAO,MAAM;MACX3E,IAAI,CAAC2b,eAAe,CAAC1T,OAAO,EAAEuG,CAAC,IAAI;QACjC,IAAIA,CAAC,CAACqN,OAAO,EAAE;UACbC,YAAY,CAACtN,CAAC,CAACuN,YAAY,CAAC;QAC9B;QAEA3U,UAAU,CAACoH,CAAC,CAACnH,IAAI,EAAE1C,GAAG,CAAC;QACvB6J,CAAC,CAACnH,IAAI,CAACgH,IAAI,CAAC,IAAI,CAAC;MACnB,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,CAAC;EACF,MAAMlK,IAAI,GAAG6X,OAAO,CAACtW,KAAK,EAAEgU,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAG7V,KAAK,EAAE+X,eAAe,CAAC;EACzE,MAAMC,OAAO,GAAGhX,KAAK,IAAI8W,eAAe,CAAC1T,OAAO,IAAI,EAAE;EACtDlI,yBAAyB,CAAC,MAAMC,IAAI,CAAC6b,OAAO,EAAE,CAAC;IAC7CxU,IAAI;IACJkH,IAAI;IACJzL;EACF,CAAC,KAAK;IACJsE,UAAU,CAACC,IAAI,EAAE1C,GAAG,CAAC;IACrBxB,QAAQ,CAACgD,WAAW,EAAEoI,IAAI,EAAEzL,GAAG,CAAC;EAClC,CAAC,CAAC,CAAC;EACH,MAAMmZ,MAAM,GAAG,EAAE;EACjB,IAAIL,eAAe,EAAE5b,IAAI,CAAC4b,eAAe,EAAE,CAACpN,CAAC,EAAE9L,CAAC,KAAK;IACnD,IAAI8L,CAAC,CAACqN,OAAO,EAAE;MACbC,YAAY,CAACtN,CAAC,CAACuN,YAAY,CAAC;MAC5BF,OAAO,CAACjT,IAAI,CAAC4F,CAAC,CAAC;IACjB,CAAC,MAAM;MACL9L,CAAC,GAAGuZ,MAAM,CAACvZ,CAAC,CAAC,GAAGyB,IAAI,CAACqU,OAAO,CAAChK,CAAC,CAAC1L,GAAG,CAAC;MACnC,IAAI,CAACJ,CAAC,EAAEgZ,WAAW,CAAChZ,CAAC,CAAC,GAAG8L,CAAC;IAC5B;EACF,CAAC,CAAC;EACFxO,IAAI,CAAC0F,KAAK,EAAE,CAAC6I,IAAI,EAAE7L,CAAC,KAAK;IACvB,IAAI,CAACgZ,WAAW,CAAChZ,CAAC,CAAC,EAAE;MACnBgZ,WAAW,CAAChZ,CAAC,CAAC,GAAG;QACfI,GAAG,EAAEqB,IAAI,CAACzB,CAAC,CAAC;QACZ6L,IAAI;QACJ2N,KAAK,EAAEd,eAAe,CAACe,KAAK;QAC5B9U,IAAI,EAAE,IAAIuP,UAAU,CAAC;MACvB,CAAC;MACD8E,WAAW,CAAChZ,CAAC,CAAC,CAAC2E,IAAI,CAACkH,IAAI,GAAGA,IAAI;IACjC;EACF,CAAC,CAAC;EAEF,IAAI0N,MAAM,CAACrZ,MAAM,EAAE;IACjB,IAAIF,CAAC,GAAG,CAAC,CAAC;IACV,MAAM;MACJuD;IACF,CAAC,GAAGyT,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAG7V,KAAK;IAC/B7D,IAAI,CAACic,MAAM,EAAE,CAACG,QAAQ,EAAEC,SAAS,KAAK;MACpC,MAAM7N,CAAC,GAAGoN,eAAe,CAACS,SAAS,CAAC;MAEpC,IAAI,CAACD,QAAQ,EAAE;QACb1Z,CAAC,GAAGgZ,WAAW,CAAClD,OAAO,CAAChK,CAAC,CAAC;QAC1BkN,WAAW,CAAChZ,CAAC,CAAC,GAAGL,QAAQ,CAAC,CAAC,CAAC,EAAEmM,CAAC,EAAE;UAC/BD,IAAI,EAAE7I,KAAK,CAAC0W,QAAQ;QACtB,CAAC,CAAC;MACJ,CAAC,MAAM,IAAInW,KAAK,EAAE;QAChByV,WAAW,CAACpC,MAAM,CAAC,EAAE5W,CAAC,EAAE,CAAC,EAAE8L,CAAC,CAAC;MAC/B;IACF,CAAC,CAAC;EACJ;EAEA,IAAI9O,EAAE,CAAC4D,GAAG,CAACsC,IAAI,CAAC,EAAE;IAChB8V,WAAW,CAAC9V,IAAI,CAAC,CAAC0W,CAAC,EAAEC,CAAC,KAAK3W,IAAI,CAAC0W,CAAC,CAAC/N,IAAI,EAAEgO,CAAC,CAAChO,IAAI,CAAC,CAAC;EAClD;EAEA,IAAIrJ,KAAK,GAAG,CAACS,KAAK;EAClB,MAAMiU,WAAW,GAAG3Y,cAAc,CAAC,CAAC;EACpC,MAAMmK,YAAY,GAAGnH,eAAe,CAACJ,KAAK,CAAC;EAC3C,MAAM2Y,OAAO,GAAG,IAAIlF,GAAG,CAAC,CAAC;EACzB,MAAMmF,kBAAkB,GAAG5a,MAAM,CAAC,IAAIyV,GAAG,CAAC,CAAC,CAAC;EAC5C,MAAMoF,WAAW,GAAG7a,MAAM,CAAC,KAAK,CAAC;EACjC7B,IAAI,CAAC0b,WAAW,EAAE,CAAClN,CAAC,EAAE9L,CAAC,KAAK;IAC1B,MAAMI,GAAG,GAAG0L,CAAC,CAAC1L,GAAG;IACjB,MAAM6Z,SAAS,GAAGnO,CAAC,CAAC0N,KAAK;IACzB,MAAM5T,CAAC,GAAGoR,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAG7V,KAAK;IACrC,IAAIa,EAAE;IACN,IAAIwX,KAAK;IACT,IAAIU,UAAU,GAAGzZ,QAAQ,CAACmF,CAAC,CAACpD,KAAK,IAAI,CAAC,EAAEpC,GAAG,CAAC;IAE5C,IAAI6Z,SAAS,IAAIvB,eAAe,CAACe,KAAK,EAAE;MACtCzX,EAAE,GAAG4D,CAAC,CAACvC,KAAK;MACZmW,KAAK,GAAGd,eAAe,CAACyB,KAAK;IAC/B,CAAC,MAAM;MACL,MAAMC,OAAO,GAAG3Y,IAAI,CAACqU,OAAO,CAAC1V,GAAG,CAAC,GAAG,CAAC;MAErC,IAAI6Z,SAAS,IAAIvB,eAAe,CAAC2B,KAAK,EAAE;QACtC,IAAID,OAAO,EAAE;UACXpY,EAAE,GAAG4D,CAAC,CAACrC,KAAK;UACZiW,KAAK,GAAGd,eAAe,CAAC2B,KAAK;QAC/B,CAAC,MAAM,IAAIrY,EAAE,GAAG4D,CAAC,CAACtC,MAAM,EAAE;UACxBkW,KAAK,GAAGd,eAAe,CAAC4B,MAAM;QAChC,CAAC,MAAM;MACT,CAAC,MAAM,IAAI,CAACF,OAAO,EAAE;QACnBpY,EAAE,GAAG4D,CAAC,CAACvC,KAAK;QACZmW,KAAK,GAAGd,eAAe,CAACyB,KAAK;MAC/B,CAAC,MAAM;IACT;IAEAnY,EAAE,GAAGvB,QAAQ,CAACuB,EAAE,EAAE8J,CAAC,CAACD,IAAI,EAAE7L,CAAC,CAAC;IAC5BgC,EAAE,GAAGhF,EAAE,CAACiE,GAAG,CAACe,EAAE,CAAC,GAAG+B,OAAO,CAAC/B,EAAE,CAAC,GAAG;MAC9BA;IACF,CAAC;IAED,IAAI,CAACA,EAAE,CAACF,MAAM,EAAE;MACd,MAAMA,MAAM,GAAGiX,WAAW,IAAIrQ,YAAY,CAAC5G,MAAM;MACjDE,EAAE,CAACF,MAAM,GAAGrB,QAAQ,CAACqB,MAAM,EAAEgK,CAAC,CAACD,IAAI,EAAE7L,CAAC,EAAEwZ,KAAK,CAAC;IAChD;IAEAhX,KAAK,IAAIS,KAAK;IAEd,MAAM6L,OAAO,GAAGnP,QAAQ,CAAC,CAAC,CAAC,EAAE+I,YAAY,EAAE;MACzClG,KAAK,EAAE0X,UAAU,GAAG1X,KAAK;MACzBP,GAAG,EAAE6W,QAAQ;MACbvW,SAAS,EAAEqD,CAAC,CAACrD,SAAS;MACtBJ,KAAK,EAAE;IACT,CAAC,EAAEH,EAAE,CAAC;IAEN,IAAIwX,KAAK,IAAId,eAAe,CAACyB,KAAK,IAAInd,EAAE,CAAC4E,GAAG,CAACkN,OAAO,CAAC/M,IAAI,CAAC,EAAE;MAC1D,MAAMwY,EAAE,GAAGvD,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAG7V,KAAK;MAEtC,MAAMY,IAAI,GAAG/E,EAAE,CAAC4E,GAAG,CAAC2Y,EAAE,CAACnX,OAAO,CAAC,IAAI8V,eAAe,GAAGqB,EAAE,CAACxY,IAAI,GAAGwY,EAAE,CAACnX,OAAO;MACzE0L,OAAO,CAAC/M,IAAI,GAAGtB,QAAQ,CAACsB,IAAI,EAAE+J,CAAC,CAACD,IAAI,EAAE7L,CAAC,CAAC;IAC1C;IAEA,MAAM;MACJ+C;IACF,CAAC,GAAG+L,OAAO;IAEXA,OAAO,CAAC/L,SAAS,GAAGgH,MAAM,IAAI;MAC5BtJ,QAAQ,CAACsC,SAAS,EAAEgH,MAAM,CAAC;MAC3B,MAAMiP,WAAW,GAAGC,eAAe,CAAC1T,OAAO;MAC3C,MAAMuG,CAAC,GAAGkN,WAAW,CAACwB,IAAI,CAAC1O,CAAC,IAAIA,CAAC,CAAC1L,GAAG,KAAKA,GAAG,CAAC;MAC9C,IAAI,CAAC0L,CAAC,EAAE;MAER,IAAI/B,MAAM,CAACC,SAAS,IAAI8B,CAAC,CAAC0N,KAAK,IAAId,eAAe,CAAC4B,MAAM,EAAE;QACzD;MACF;MAEA,IAAIxO,CAAC,CAACnH,IAAI,CAACsI,IAAI,EAAE;QACf,MAAMA,IAAI,GAAG+L,WAAW,CAAC7O,KAAK,CAAC2B,CAAC,IAAIA,CAAC,CAACnH,IAAI,CAACsI,IAAI,CAAC;QAEhD,IAAInB,CAAC,CAAC0N,KAAK,IAAId,eAAe,CAAC2B,KAAK,EAAE;UACpC,MAAMI,MAAM,GAAGha,QAAQ,CAAC0C,OAAO,EAAE2I,CAAC,CAACD,IAAI,CAAC;UAExC,IAAI4O,MAAM,KAAK,KAAK,EAAE;YACpB,MAAMC,QAAQ,GAAGD,MAAM,KAAK,IAAI,GAAG,CAAC,GAAGA,MAAM;YAC7C3O,CAAC,CAACqN,OAAO,GAAG,IAAI;YAEhB,IAAI,CAAClM,IAAI,IAAIyN,QAAQ,GAAG,CAAC,EAAE;cACzB,IAAIA,QAAQ,IAAI,UAAU,EAAE5O,CAAC,CAACuN,YAAY,GAAG7P,UAAU,CAAC0N,WAAW,EAAEwD,QAAQ,CAAC;cAC9E;YACF;UACF;QACF;QAEA,IAAIzN,IAAI,IAAI+L,WAAW,CAAClP,IAAI,CAACgC,CAAC,IAAIA,CAAC,CAACqN,OAAO,CAAC,EAAE;UAC5CY,kBAAkB,CAACxU,OAAO,CAACV,MAAM,CAACiH,CAAC,CAAC;UAEpC,IAAI+M,eAAe,EAAE;YACnBmB,WAAW,CAACzU,OAAO,GAAG,IAAI;UAC5B;UAEA2R,WAAW,CAAC,CAAC;QACf;MACF;IACF,CAAC;IAED,MAAM/C,OAAO,GAAGmB,UAAU,CAACxJ,CAAC,CAACnH,IAAI,EAAEmK,OAAO,CAAC;IAE3C,IAAI0K,KAAK,KAAKd,eAAe,CAAC2B,KAAK,IAAIxB,eAAe,EAAE;MACtDkB,kBAAkB,CAACxU,OAAO,CAACuL,GAAG,CAAChF,CAAC,EAAE;QAChC0N,KAAK;QACLrF,OAAO;QACPrF;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACLgL,OAAO,CAAChJ,GAAG,CAAChF,CAAC,EAAE;QACb0N,KAAK;QACLrF,OAAO;QACPrF;MACF,CAAC,CAAC;IACJ;EACF,CAAC,CAAC;EACF,MAAM6I,OAAO,GAAG1Y,UAAU,CAAC+W,aAAa,CAAC;EACzC,MAAM4B,WAAW,GAAGpZ,OAAO,CAACmZ,OAAO,CAAC;EACpC,MAAME,UAAU,GAAGF,OAAO,KAAKC,WAAW,IAAIrT,QAAQ,CAACoT,OAAO,CAAC;EAC/Dta,yBAAyB,CAAC,MAAM;IAC9B,IAAIwa,UAAU,EAAE;MACdva,IAAI,CAAC0b,WAAW,EAAElN,CAAC,IAAI;QACrBA,CAAC,CAACnH,IAAI,CAACgB,KAAK,CAAC;UACXvE,OAAO,EAAEuW;QACX,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACA,OAAO,CAAC,CAAC;EACbra,IAAI,CAACwc,OAAO,EAAE,CAACtV,CAAC,EAAEsH,CAAC,KAAK;IACtB,IAAIiO,kBAAkB,CAACxU,OAAO,CAACkO,IAAI,EAAE;MACnC,MAAMkH,GAAG,GAAG3B,WAAW,CAAC4B,SAAS,CAACjS,KAAK,IAAIA,KAAK,CAACvI,GAAG,KAAK0L,CAAC,CAAC1L,GAAG,CAAC;MAC/D4Y,WAAW,CAACpC,MAAM,CAAC+D,GAAG,EAAE,CAAC,CAAC;IAC5B;EACF,CAAC,CAAC;EACFtd,yBAAyB,CAAC,MAAM;IAC9BC,IAAI,CAACyc,kBAAkB,CAACxU,OAAO,CAACkO,IAAI,GAAGsG,kBAAkB,CAACxU,OAAO,GAAGuU,OAAO,EAAE,CAAC;MAC5EN,KAAK;MACL1K;IACF,CAAC,EAAEhD,CAAC,KAAK;MACP,MAAM;QACJnH;MACF,CAAC,GAAGmH,CAAC;MACLA,CAAC,CAAC0N,KAAK,GAAGA,KAAK;MACfvX,GAAG,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC+C,GAAG,CAACL,IAAI,CAAC;MAEpC,IAAIkT,UAAU,IAAI2B,KAAK,IAAId,eAAe,CAACyB,KAAK,EAAE;QAChDxV,IAAI,CAACgB,KAAK,CAAC;UACTvE,OAAO,EAAEuW;QACX,CAAC,CAAC;MACJ;MAEA,IAAI7I,OAAO,EAAE;QACXhK,UAAU,CAACH,IAAI,EAAEmK,OAAO,CAAC7M,GAAG,CAAC;QAE7B,IAAI,CAAC0C,IAAI,CAAC1C,GAAG,IAAIA,GAAG,KAAK,CAAC+X,WAAW,CAACzU,OAAO,EAAE;UAC7CZ,IAAI,CAACrB,MAAM,CAACwL,OAAO,CAAC;QACtB,CAAC,MAAM;UACLnK,IAAI,CAACgB,KAAK,CAACmJ,OAAO,CAAC;UAEnB,IAAIkL,WAAW,CAACzU,OAAO,EAAE;YACvByU,WAAW,CAACzU,OAAO,GAAG,KAAK;UAC7B;QACF;MACF;IACF,CAAC,CAAC;EACJ,CAAC,EAAEpD,KAAK,GAAG,KAAK,CAAC,GAAG4U,IAAI,CAAC;EAEzB,MAAM8D,iBAAiB,GAAGC,MAAM,IAAI9b,KAAK,CAACoX,aAAa,CAACpX,KAAK,CAAC+b,QAAQ,EAAE,IAAI,EAAE/B,WAAW,CAAC5U,GAAG,CAAC,CAAC0H,CAAC,EAAE9L,CAAC,KAAK;IACtG,MAAM;MACJmU;IACF,CAAC,GAAG2F,OAAO,CAAC5P,GAAG,CAAC4B,CAAC,CAAC,IAAIA,CAAC,CAACnH,IAAI;IAC5B,MAAMqW,IAAI,GAAGF,MAAM,CAACnb,QAAQ,CAAC,CAAC,CAAC,EAAEwU,OAAO,CAAC,EAAErI,CAAC,CAACD,IAAI,EAAEC,CAAC,EAAE9L,CAAC,CAAC;IACxD,OAAOgb,IAAI,IAAIA,IAAI,CAAC9N,IAAI,GAAGlO,KAAK,CAACoX,aAAa,CAAC4E,IAAI,CAAC9N,IAAI,EAAEvN,QAAQ,CAAC,CAAC,CAAC,EAAEqb,IAAI,CAAC7Z,KAAK,EAAE;MACjFf,GAAG,EAAEpD,EAAE,CAACie,GAAG,CAACnP,CAAC,CAAC1L,GAAG,CAAC,IAAIpD,EAAE,CAAC2V,GAAG,CAAC7G,CAAC,CAAC1L,GAAG,CAAC,GAAG0L,CAAC,CAAC1L,GAAG,GAAG0L,CAAC,CAACnH,IAAI,CAACyH,EAAE;MACvDnK,GAAG,EAAE+Y,IAAI,CAAC/Y;IACZ,CAAC,CAAC,CAAC,GAAG+Y,IAAI;EACZ,CAAC,CAAC,CAAC;EAEH,OAAO/Y,GAAG,GAAG,CAAC4Y,iBAAiB,EAAE5Y,GAAG,CAAC,GAAG4Y,iBAAiB;AAC3D;AACA,IAAIK,OAAO,GAAG,CAAC;AAEf,SAAS5B,OAAOA,CAACtW,KAAK,EAAE;EACtB5C,GAAG;EACHqB,IAAI,GAAGrB;AACT,CAAC,EAAE8Y,eAAe,EAAE;EAClB,IAAIzX,IAAI,KAAK,IAAI,EAAE;IACjB,MAAM8X,MAAM,GAAG,IAAIpL,GAAG,CAAC,CAAC;IACxB,OAAOnL,KAAK,CAACoB,GAAG,CAACyH,IAAI,IAAI;MACvB,MAAMC,CAAC,GAAGoN,eAAe,IAAIA,eAAe,CAACsB,IAAI,CAAC1O,CAAC,IAAIA,CAAC,CAACD,IAAI,KAAKA,IAAI,IAAIC,CAAC,CAAC0N,KAAK,KAAKd,eAAe,CAAC2B,KAAK,IAAI,CAACd,MAAM,CAAC4B,GAAG,CAACrP,CAAC,CAAC,CAAC;MAE9H,IAAIA,CAAC,EAAE;QACLyN,MAAM,CAACvU,GAAG,CAAC8G,CAAC,CAAC;QACb,OAAOA,CAAC,CAAC1L,GAAG;MACd;MAEA,OAAO8a,OAAO,EAAE;IAClB,CAAC,CAAC;EACJ;EAEA,OAAOle,EAAE,CAAC4E,GAAG,CAACH,IAAI,CAAC,GAAGuB,KAAK,GAAGhG,EAAE,CAAC4D,GAAG,CAACa,IAAI,CAAC,GAAGuB,KAAK,CAACoB,GAAG,CAAC3C,IAAI,CAAC,GAAGxE,OAAO,CAACwE,IAAI,CAAC;AAC9E;AAEA,MAAM2Z,WAAW,GAAG,CAAC,WAAW,CAAC;AACjC,MAAMC,SAAS,GAAGA,CAAC5J,IAAI,GAAG,CAAC,CAAC,KAAK;EAC/B,IAAI;MACF6J;IACF,CAAC,GAAG7J,IAAI;IACJ8J,aAAa,GAAG5F,6BAA6B,CAAClE,IAAI,EAAE2J,WAAW,CAAC;EAEpE,MAAM,CAACI,YAAY,EAAEC,GAAG,CAAC,GAAGzD,SAAS,CAAC,MAAMrY,QAAQ,CAAC;IACnD+b,OAAO,EAAE,CAAC;IACVC,OAAO,EAAE,CAAC;IACVC,eAAe,EAAE,CAAC;IAClBC,eAAe,EAAE;EACnB,CAAC,EAAEN,aAAa,CAAC,EAAE,EAAE,CAAC;EACtBle,yBAAyB,CAAC,MAAM;IAC9B,MAAMye,aAAa,GAAGnd,QAAQ,CAAC,CAAC;MAC9BoZ,CAAC;MACDgE;IACF,CAAC,KAAK;MACJN,GAAG,CAAC9V,KAAK,CAAC;QACR+V,OAAO,EAAE3D,CAAC,CAACxS,OAAO;QAClBqW,eAAe,EAAE7D,CAAC,CAACzQ,QAAQ;QAC3BqU,OAAO,EAAEI,CAAC,CAACxW,OAAO;QAClBsW,eAAe,EAAEE,CAAC,CAACzU;MACrB,CAAC,CAAC;IACJ,CAAC,EAAE;MACDgU,SAAS,EAAE,CAACA,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC/V,OAAO,KAAKlE;IACjE,CAAC,CAAC;IACF,OAAO,MAAM;MACX/D,IAAI,CAACsC,MAAM,CAAC0I,MAAM,CAACkT,YAAY,CAAC,EAAE9a,KAAK,IAAIA,KAAK,CAACiL,IAAI,CAAC,CAAC,CAAC;MACxDmQ,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAON,YAAY;AACrB,CAAC;AAED,MAAMQ,WAAW,GAAG,CAAC,WAAW,CAAC;AACjC,MAAMC,SAAS,GAAGxK,IAAI,IAAI;EACxB,IAAI;MACF6J;IACF,CAAC,GAAG7J,IAAI;IACJ8J,aAAa,GAAG5F,6BAA6B,CAAClE,IAAI,EAAEuK,WAAW,CAAC;EAEpE,MAAM,CAACE,UAAU,EAAET,GAAG,CAAC,GAAGzD,SAAS,CAAC,MAAMrY,QAAQ,CAAC;IACjDwc,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV,CAAC,EAAEb,aAAa,CAAC,EAAE,EAAE,CAAC;EACtBle,yBAAyB,CAAC,MAAM;IAC9B,MAAMye,aAAa,GAAGld,QAAQ,CAAC,CAAC;MAC9Bud,KAAK;MACLC;IACF,CAAC,KAAK;MACJX,GAAG,CAAC9V,KAAK,CAAC;QACRwW,KAAK;QACLC,MAAM;QACN7Z,SAAS,EAAE2Z,UAAU,CAACC,KAAK,CAACjS,GAAG,CAAC,CAAC,KAAK,CAAC,IAAIgS,UAAU,CAACE,MAAM,CAAClS,GAAG,CAAC,CAAC,KAAK;MACzE,CAAC,CAAC;IACJ,CAAC,EAAE;MACDoR,SAAS,EAAE,CAACA,SAAS,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC/V,OAAO,KAAKlE;IACjE,CAAC,CAAC;IACF,OAAO,MAAM;MACX/D,IAAI,CAACsC,MAAM,CAAC0I,MAAM,CAAC4T,UAAU,CAAC,EAAExb,KAAK,IAAIA,KAAK,CAACiL,IAAI,CAAC,CAAC,CAAC;MACtDmQ,aAAa,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOI,UAAU;AACnB,CAAC;AAED,MAAMG,WAAW,GAAG,CAAC,IAAI,EAAE,MAAM,CAAC;EAC5BC,UAAU,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,QAAQ,CAAC;AAC7C,MAAMC,uBAAuB,GAAG;EAC9BC,GAAG,EAAE,CAAC;EACNrW,GAAG,EAAE;AACP,CAAC;AACD,SAASsW,SAASA,CAACtb,KAAK,EAAER,IAAI,EAAE;EAC9B,MAAM,CAAC+b,QAAQ,EAAEC,WAAW,CAAC,GAAGvd,QAAQ,CAAC,KAAK,CAAC;EAC/C,MAAM6C,GAAG,GAAG9C,MAAM,CAAC,CAAC;EACpB,MAAM6X,OAAO,GAAGha,EAAE,CAAC4D,GAAG,CAACO,KAAK,CAAC,IAAIA,KAAK;EACtC,MAAMyb,YAAY,GAAG5F,OAAO,GAAGA,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC;EAE7C,MAAM;MACJhV,EAAE,GAAG,CAAC,CAAC;MACPD,IAAI,GAAG,CAAC;IACV,CAAC,GAAG6a,YAAY;IACVC,eAAe,GAAGlH,6BAA6B,CAACiH,YAAY,EAAEP,WAAW,CAAC;EAEhF,MAAMS,qBAAqB,GAAG9F,OAAO,GAAGrW,IAAI,GAAGQ,KAAK;EACpD,MAAM,CAACgT,OAAO,EAAEsH,GAAG,CAAC,GAAGzD,SAAS,CAAC,MAAMrY,QAAQ,CAAC;IAC9CoC;EACF,CAAC,EAAE8a,eAAe,CAAC,EAAE,EAAE,CAAC;EACxBxf,yBAAyB,CAAC,MAAM;IAC9B,MAAM0f,OAAO,GAAG9a,GAAG,CAACsD,OAAO;IAE3B,MAAMkM,IAAI,GAAGqL,qBAAqB,IAAI,IAAI,GAAGA,qBAAqB,GAAG,CAAC,CAAC;MACjE;QACJE,IAAI;QACJC,IAAI;QACJC,MAAM,GAAG;MACX,CAAC,GAAGzL,IAAI;MACF0L,QAAQ,GAAGxH,6BAA6B,CAAClE,IAAI,EAAE6K,UAAU,CAAC;IAEhE,IAAI,CAACS,OAAO,IAAIE,IAAI,IAAIP,QAAQ,IAAI,OAAOU,oBAAoB,KAAK,WAAW,EAAE;IACjF,MAAMC,mBAAmB,GAAG,IAAIC,OAAO,CAAC,CAAC;IAEzC,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,IAAIvb,EAAE,EAAE;QACNyZ,GAAG,CAAC9V,KAAK,CAAC3D,EAAE,CAAC;MACf;MAEA2a,WAAW,CAAC,IAAI,CAAC;MAEjB,MAAMa,OAAO,GAAGA,CAAA,KAAM;QACpB,IAAIzb,IAAI,EAAE;UACR0Z,GAAG,CAAC9V,KAAK,CAAC5D,IAAI,CAAC;QACjB;QAEA4a,WAAW,CAAC,KAAK,CAAC;MACpB,CAAC;MAED,OAAOM,IAAI,GAAG5b,SAAS,GAAGmc,OAAO;IACnC,CAAC;IAED,MAAMC,kBAAkB,GAAGC,OAAO,IAAI;MACpCA,OAAO,CAAC3O,OAAO,CAAC4O,KAAK,IAAI;QACvB,MAAMC,OAAO,GAAGP,mBAAmB,CAACnT,GAAG,CAACyT,KAAK,CAAC5d,MAAM,CAAC;QAErD,IAAI4d,KAAK,CAACE,cAAc,KAAKC,OAAO,CAACF,OAAO,CAAC,EAAE;UAC7C;QACF;QAEA,IAAID,KAAK,CAACE,cAAc,EAAE;UACxB,MAAME,UAAU,GAAGR,OAAO,CAAC,CAAC;UAE5B,IAAIvgB,EAAE,CAAC4D,GAAG,CAACmd,UAAU,CAAC,EAAE;YACtBV,mBAAmB,CAACvM,GAAG,CAAC6M,KAAK,CAAC5d,MAAM,EAAEge,UAAU,CAAC;UACnD,CAAC,MAAM;YACLrI,QAAQ,CAACsI,SAAS,CAACL,KAAK,CAAC5d,MAAM,CAAC;UAClC;QACF,CAAC,MAAM,IAAI6d,OAAO,EAAE;UAClBA,OAAO,CAAC,CAAC;UACTP,mBAAmB,CAACxY,MAAM,CAAC8Y,KAAK,CAAC5d,MAAM,CAAC;QAC1C;MACF,CAAC,CAAC;IACJ,CAAC;IAED,MAAM2V,QAAQ,GAAG,IAAI0H,oBAAoB,CAACK,kBAAkB,EAAE9d,QAAQ,CAAC;MACrEqd,IAAI,EAAEA,IAAI,IAAIA,IAAI,CAACzX,OAAO,IAAIlE,SAAS;MACvC4c,SAAS,EAAE,OAAOf,MAAM,KAAK,QAAQ,IAAIxJ,KAAK,CAACwK,OAAO,CAAChB,MAAM,CAAC,GAAGA,MAAM,GAAGX,uBAAuB,CAACW,MAAM;IAC1G,CAAC,EAAEC,QAAQ,CAAC,CAAC;IACbzH,QAAQ,CAACyI,OAAO,CAACpB,OAAO,CAAC;IACzB,OAAO,MAAMrH,QAAQ,CAACsI,SAAS,CAACjB,OAAO,CAAC;EAC1C,CAAC,EAAE,CAACD,qBAAqB,CAAC,CAAC;EAE3B,IAAI9F,OAAO,EAAE;IACX,OAAO,CAAC/U,GAAG,EAAEkS,OAAO,CAAC;EACvB;EAEA,OAAO,CAAClS,GAAG,EAAEya,QAAQ,CAAC;AACxB;AAEA,MAAM0B,WAAW,GAAG,CAAC,UAAU,CAAC;AAChC,SAASC,MAAMA,CAAC5M,IAAI,EAAE;EACpB,IAAI;MACFjO;IACF,CAAC,GAAGiO,IAAI;IACJtQ,KAAK,GAAGwU,6BAA6B,CAAClE,IAAI,EAAE2M,WAAW,CAAC;EAE5D,OAAO5a,QAAQ,CAACwU,SAAS,CAAC7W,KAAK,CAAC,CAAC;AACnC;AAEA,MAAMmd,WAAW,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AACzC,SAASC,KAAKA,CAAC9M,IAAI,EAAE;EACnB,IAAI;MACFzO,KAAK;MACLQ;IACF,CAAC,GAAGiO,IAAI;IACJtQ,KAAK,GAAGwU,6BAA6B,CAAClE,IAAI,EAAE6M,WAAW,CAAC;EAE5D,MAAME,MAAM,GAAGlG,QAAQ,CAACtV,KAAK,CAAC9C,MAAM,EAAEiB,KAAK,CAAC;EAC5C,OAAO6B,KAAK,CAACoB,GAAG,CAAC,CAACyH,IAAI,EAAEgL,KAAK,KAAK;IAChC,MAAM9M,MAAM,GAAGvG,QAAQ,CAACqI,IAAI,EAAEgL,KAAK,CAAC;IACpC,OAAO7Z,EAAE,CAAC4D,GAAG,CAACmJ,MAAM,CAAC,GAAGA,MAAM,CAACyU,MAAM,CAAC3H,KAAK,CAAC,CAAC,GAAG9M,MAAM;EACxD,CAAC,CAAC;AACJ;AAEA,MAAM0U,SAAS,GAAG,CAAC,OAAO,EAAE,UAAU,CAAC;AACvC,SAASC,UAAUA,CAACjN,IAAI,EAAE;EACxB,IAAI;MACFzO,KAAK;MACLQ;IACF,CAAC,GAAGiO,IAAI;IACJtQ,KAAK,GAAGwU,6BAA6B,CAAClE,IAAI,EAAEgN,SAAS,CAAC;EAE1D,OAAO9F,aAAa,CAAC3V,KAAK,EAAE7B,KAAK,CAAC,CAACqC,QAAQ,CAAC;AAC9C;AAEA,MAAMmb,aAAa,SAASzS,UAAU,CAAC;EACrCjF,WAAWA,CAAC9G,MAAM,EAAEQ,IAAI,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,GAAG,GAAG,KAAK,CAAC;IACjB,IAAI,CAAC6M,IAAI,GAAG,IAAI;IAChB,IAAI,CAAC2R,IAAI,GAAG,KAAK,CAAC;IAClB,IAAI,CAACrK,OAAO,GAAG,IAAIpG,GAAG,CAAC,CAAC;IACxB,IAAI,CAAChO,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACye,IAAI,GAAG/f,kBAAkB,CAAC,GAAG8B,IAAI,CAAC;IAEvC,MAAMD,KAAK,GAAG,IAAI,CAACme,IAAI,CAAC,CAAC;IAEzB,MAAMjM,QAAQ,GAAGnT,eAAe,CAACiB,KAAK,CAAC;IACvChB,WAAW,CAAC,IAAI,EAAEkT,QAAQ,CAACQ,MAAM,CAAC1S,KAAK,CAAC,CAAC;EAC3C;EAEAiO,OAAOA,CAACmQ,GAAG,EAAE;IACX,MAAMpe,KAAK,GAAG,IAAI,CAACme,IAAI,CAAC,CAAC;IAEzB,MAAME,QAAQ,GAAG,IAAI,CAAC7U,GAAG,CAAC,CAAC;IAE3B,IAAI,CAAClM,OAAO,CAAC0C,KAAK,EAAEqe,QAAQ,CAAC,EAAE;MAC7B1f,WAAW,CAAC,IAAI,CAAC,CAACqR,QAAQ,CAAChQ,KAAK,CAAC;MAEjC,IAAI,CAACsM,SAAS,CAACtM,KAAK,EAAE,IAAI,CAACuM,IAAI,CAAC;IAClC;IAEA,IAAI,CAAC,IAAI,CAACA,IAAI,IAAI+R,SAAS,CAAC,IAAI,CAACzK,OAAO,CAAC,EAAE;MACzC0K,UAAU,CAAC,IAAI,CAAC;IAClB;EACF;EAEAJ,IAAIA,CAAA,EAAG;IACL,MAAMK,MAAM,GAAGliB,EAAE,CAACmH,GAAG,CAAC,IAAI,CAAChE,MAAM,CAAC,GAAG,IAAI,CAACA,MAAM,CAACiE,GAAG,CAAClH,aAAa,CAAC,GAAGD,OAAO,CAACC,aAAa,CAAC,IAAI,CAACiD,MAAM,CAAC,CAAC;IACzG,OAAO,IAAI,CAACye,IAAI,CAAC,GAAGM,MAAM,CAAC;EAC7B;EAEA3N,MAAMA,CAAA,EAAG;IACP,IAAI,IAAI,CAACtE,IAAI,IAAI,CAAC+R,SAAS,CAAC,IAAI,CAACzK,OAAO,CAAC,EAAE;MACzC,IAAI,CAACtH,IAAI,GAAG,KAAK;MACjB3P,IAAI,CAACiC,UAAU,CAAC,IAAI,CAAC,EAAEiN,IAAI,IAAI;QAC7BA,IAAI,CAACwC,IAAI,GAAG,KAAK;MACnB,CAAC,CAAC;MAEF,IAAI5R,OAAO,CAACkM,aAAa,EAAE;QACzB9L,GAAG,CAACoO,cAAc,CAAC,MAAM,IAAI,CAAC+C,OAAO,CAAC,CAAC,CAAC;QACxCsQ,UAAU,CAAC,IAAI,CAAC;MAClB,CAAC,MAAM;QACLphB,SAAS,CAAC8H,KAAK,CAAC,IAAI,CAAC;MACvB;IACF;EACF;EAEAkH,OAAOA,CAAA,EAAG;IACR,IAAIP,QAAQ,GAAG,CAAC;IAChBhP,IAAI,CAACL,OAAO,CAAC,IAAI,CAACkD,MAAM,CAAC,EAAEA,MAAM,IAAI;MACnC,IAAIrC,aAAa,CAACqC,MAAM,CAAC,EAAE;QACzBjC,gBAAgB,CAACiC,MAAM,EAAE,IAAI,CAAC;MAChC;MAEA,IAAI8L,YAAY,CAAC9L,MAAM,CAAC,EAAE;QACxB,IAAI,CAACA,MAAM,CAAC8M,IAAI,EAAE;UAChB,IAAI,CAACsH,OAAO,CAACvP,GAAG,CAAC7E,MAAM,CAAC;QAC1B;QAEAmM,QAAQ,GAAGvE,IAAI,CAACoX,GAAG,CAAC7S,QAAQ,EAAEnM,MAAM,CAACmM,QAAQ,GAAG,CAAC,CAAC;MACpD;IACF,CAAC,CAAC;IACF,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IAExB,IAAI,CAACiF,MAAM,CAAC,CAAC;EACf;EAEAxE,OAAOA,CAAA,EAAG;IACRzP,IAAI,CAACL,OAAO,CAAC,IAAI,CAACkD,MAAM,CAAC,EAAEA,MAAM,IAAI;MACnC,IAAIrC,aAAa,CAACqC,MAAM,CAAC,EAAE;QACzBhC,mBAAmB,CAACgC,MAAM,EAAE,IAAI,CAAC;MACnC;IACF,CAAC,CAAC;IAEF,IAAI,CAACoU,OAAO,CAACxI,KAAK,CAAC,CAAC;IAEpBkT,UAAU,CAAC,IAAI,CAAC;EAClB;EAEA5N,aAAaA,CAACC,KAAK,EAAE;IACnB,IAAIA,KAAK,CAACpE,IAAI,IAAI,QAAQ,EAAE;MAC1B,IAAIoE,KAAK,CAACrE,IAAI,EAAE;QACd,IAAI,CAAC0B,OAAO,CAAC,CAAC;MAChB,CAAC,MAAM;QACL,IAAI,CAAC4F,OAAO,CAACvP,GAAG,CAACsM,KAAK,CAACnE,MAAM,CAAC;QAE9B,IAAI,CAACoE,MAAM,CAAC,CAAC;MACf;IACF,CAAC,MAAM,IAAID,KAAK,CAACpE,IAAI,IAAI,MAAM,EAAE;MAC/B,IAAI,CAACqH,OAAO,CAAC1P,MAAM,CAACyM,KAAK,CAACnE,MAAM,CAAC;IACnC,CAAC,MAAM,IAAImE,KAAK,CAACpE,IAAI,IAAI,UAAU,EAAE;MACnC,IAAI,CAACZ,QAAQ,GAAGrP,OAAO,CAAC,IAAI,CAACkD,MAAM,CAAC,CAACif,MAAM,CAAC,CAACC,OAAO,EAAElS,MAAM,KAAKpF,IAAI,CAACoX,GAAG,CAACE,OAAO,EAAE,CAACpT,YAAY,CAACkB,MAAM,CAAC,GAAGA,MAAM,CAACb,QAAQ,GAAG,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,CAAC;IAC1I;EACF;AAEF;AAEA,SAASgT,MAAMA,CAACnf,MAAM,EAAE;EACtB,OAAOA,MAAM,CAAC8M,IAAI,KAAK,KAAK;AAC9B;AAEA,SAAS+R,SAASA,CAAClR,MAAM,EAAE;EACzB,OAAO,CAACA,MAAM,CAAC2F,IAAI,IAAIC,KAAK,CAAC3R,IAAI,CAAC+L,MAAM,CAAC,CAAC3D,KAAK,CAACmV,MAAM,CAAC;AACzD;AAEA,SAASL,UAAUA,CAACM,IAAI,EAAE;EACxB,IAAI,CAACA,IAAI,CAACtS,IAAI,EAAE;IACdsS,IAAI,CAACtS,IAAI,GAAG,IAAI;IAChB3P,IAAI,CAACiC,UAAU,CAACggB,IAAI,CAAC,EAAE/S,IAAI,IAAI;MAC7BA,IAAI,CAACwC,IAAI,GAAG,IAAI;IAClB,CAAC,CAAC;IACFpR,kBAAkB,CAAC2hB,IAAI,EAAE;MACvBrS,IAAI,EAAE,MAAM;MACZC,MAAM,EAAEoS;IACV,CAAC,CAAC;EACJ;AACF;AAEA,MAAMvd,EAAE,GAAGA,CAAC7B,MAAM,EAAE,GAAGQ,IAAI,KAAK,IAAIge,aAAa,CAACxe,MAAM,EAAEQ,IAAI,CAAC;AAC/D,MAAM+L,WAAW,GAAGA,CAACvM,MAAM,EAAE,GAAGQ,IAAI,MAAMhD,oBAAoB,CAAC,CAAC,EAAE,IAAIghB,aAAa,CAACxe,MAAM,EAAEQ,IAAI,CAAC,CAAC;AAElGvD,OAAO,CAACyC,MAAM,CAAC;EACbf,wBAAwB;EACxBkD,EAAE,EAAEA,CAAC7B,MAAM,EAAEQ,IAAI,KAAK,IAAIge,aAAa,CAACxe,MAAM,EAAEQ,IAAI;AACtD,CAAC,CAAC;AACF,MAAM2C,MAAM,GAAGzF,SAAS,CAAC8Q,OAAO;AAEhC,SAASrD,UAAU,EAAE4I,UAAU,EAAEhI,UAAU,EAAEyS,aAAa,EAAEN,MAAM,EAAErI,aAAa,EAAEU,SAAS,EAAE1I,WAAW,EAAEuQ,KAAK,EAAEG,UAAU,EAAE5c,MAAM,EAAEiC,OAAO,EAAE2I,WAAW,EAAE1K,EAAE,EAAEsB,MAAM,EAAE2B,QAAQ,EAAEwX,SAAS,EAAER,SAAS,EAAEZ,SAAS,EAAErD,SAAS,EAAEG,YAAY,EAAEC,cAAc,EAAEtB,UAAU,EAAEwB,QAAQ,EAAEK,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}