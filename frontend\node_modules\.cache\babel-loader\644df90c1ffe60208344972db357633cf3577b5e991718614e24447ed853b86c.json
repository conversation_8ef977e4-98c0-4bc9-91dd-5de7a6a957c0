{"ast": null, "code": "/**\n * 應用常量定義\n */\n\n// 名片欄位定義\nexport const CARD_FIELDS = {\n  name: '姓名',\n  company_name: '公司名稱',\n  position: '職位',\n  mobile_phone: '手機',\n  office_phone: '公司電話',\n  email: 'Email',\n  line_id: 'Line ID',\n  notes: '備註',\n  company_address_1: '公司地址一',\n  company_address_2: '公司地址二'\n};\n\n// 必填欄位\nexport const REQUIRED_FIELDS = ['name'];\n\n// 欄位驗證規則\nexport const FIELD_VALIDATION = {\n  name: {\n    required: true,\n    maxLength: 50,\n    pattern: null\n  },\n  company_name: {\n    required: false,\n    maxLength: 100,\n    pattern: null\n  },\n  position: {\n    required: false,\n    maxLength: 50,\n    pattern: null\n  },\n  mobile_phone: {\n    required: false,\n    maxLength: 20,\n    pattern: /^[\\d\\s\\-\\+\\(\\)]+$/\n  },\n  office_phone: {\n    required: false,\n    maxLength: 20,\n    pattern: /^[\\d\\s\\-\\+\\(\\)]+$/\n  },\n  email: {\n    required: false,\n    maxLength: 100,\n    pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  },\n  line_id: {\n    required: false,\n    maxLength: 50,\n    pattern: null\n  },\n  notes: {\n    required: false,\n    maxLength: 500,\n    pattern: null\n  },\n  company_address_1: {\n    required: false,\n    maxLength: 200,\n    pattern: null\n  },\n  company_address_2: {\n    required: false,\n    maxLength: 200,\n    pattern: null\n  }\n};\n\n// OCR處理狀態\nexport const OCR_STATUS = {\n  IDLE: 'idle',\n  PROCESSING: 'processing',\n  SUCCESS: 'success',\n  ERROR: 'error'\n};\n\n// 相機目標\nexport const CAMERA_TARGET = {\n  FRONT: 'front',\n  BACK: 'back'\n};\n\n// 相機狀態\nexport const CAMERA_STATUS = {\n  INACTIVE: 'inactive',\n  STARTING: 'starting',\n  ACTIVE: 'active',\n  ERROR: 'error'\n};\n\n// 圖片處理相關常量\nexport const IMAGE_CONFIG = {\n  MAX_FILE_SIZE: 10 * 1024 * 1024,\n  // 10MB\n  ACCEPTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],\n  COMPRESSION_QUALITY: 0.8,\n  MAX_WIDTH: 1920,\n  MAX_HEIGHT: 1080\n};\n\n// 路由路徑\nexport const ROUTES = {\n  HOME: '/',\n  SCAN: '/scan',\n  CARDS: '/cards',\n  CARD_NEW: '/cards/new',\n  CARD_VIEW: '/cards/:id',\n  CARD_EDIT: '/cards/:id/edit'\n};\n\n// 本地存儲鍵名\nexport const STORAGE_KEYS = {\n  CAMERA_SETTINGS: 'ocr_camera_settings',\n  USER_PREFERENCES: 'ocr_user_preferences',\n  LAST_SCAN_DATA: 'ocr_last_scan_data'\n};\n\n// 錯誤消息\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: '網絡連接失敗，請檢查網絡設置',\n  OCR_FAILED: 'OCR識別失敗，請重試',\n  CAMERA_PERMISSION_DENIED: '相機權限被拒絕，請在瀏覽器設置中允許相機訪問',\n  CAMERA_NOT_FOUND: '未找到可用的相機設備',\n  CAMERA_OCCUPIED: '相機被其他應用程序占用',\n  FILE_TOO_LARGE: '文件大小超過限制',\n  INVALID_FILE_FORMAT: '不支持的文件格式',\n  VALIDATION_FAILED: '數據驗證失敗',\n  SAVE_FAILED: '保存失敗，請重試',\n  DELETE_FAILED: '刪除失敗，請重試',\n  LOAD_FAILED: '加載失敗，請重試'\n};\n\n// 成功消息\nexport const SUCCESS_MESSAGES = {\n  OCR_SUCCESS: 'OCR識別完成',\n  CARD_SAVED: '名片保存成功',\n  CARD_UPDATED: '名片更新成功',\n  CARD_DELETED: '名片刪除成功',\n  EXPORT_SUCCESS: '導出成功'\n};\n\n// 主題色彩\nexport const THEME_COLORS = {\n  PRIMARY: '#1677ff',\n  SUCCESS: '#52c41a',\n  WARNING: '#faad14',\n  ERROR: '#ff4d4f',\n  INFO: '#13c2c2',\n  TEXT_PRIMARY: '#262626',\n  TEXT_SECONDARY: '#8c8c8c',\n  BORDER: '#d9d9d9',\n  BACKGROUND: '#f5f5f5'\n};\nexport default {\n  CARD_FIELDS,\n  REQUIRED_FIELDS,\n  FIELD_VALIDATION,\n  OCR_STATUS,\n  CAMERA_TARGET,\n  CAMERA_STATUS,\n  IMAGE_CONFIG,\n  ROUTES,\n  STORAGE_KEYS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  THEME_COLORS\n};", "map": {"version": 3, "names": ["CARD_FIELDS", "name", "company_name", "position", "mobile_phone", "office_phone", "email", "line_id", "notes", "company_address_1", "company_address_2", "REQUIRED_FIELDS", "FIELD_VALIDATION", "required", "max<PERSON><PERSON><PERSON>", "pattern", "OCR_STATUS", "IDLE", "PROCESSING", "SUCCESS", "ERROR", "CAMERA_TARGET", "FRONT", "BACK", "CAMERA_STATUS", "INACTIVE", "STARTING", "ACTIVE", "IMAGE_CONFIG", "MAX_FILE_SIZE", "ACCEPTED_FORMATS", "COMPRESSION_QUALITY", "MAX_WIDTH", "MAX_HEIGHT", "ROUTES", "HOME", "SCAN", "CARDS", "CARD_NEW", "CARD_VIEW", "CARD_EDIT", "STORAGE_KEYS", "CAMERA_SETTINGS", "USER_PREFERENCES", "LAST_SCAN_DATA", "ERROR_MESSAGES", "NETWORK_ERROR", "OCR_FAILED", "CAMERA_PERMISSION_DENIED", "CAMERA_NOT_FOUND", "CAMERA_OCCUPIED", "FILE_TOO_LARGE", "INVALID_FILE_FORMAT", "VALIDATION_FAILED", "SAVE_FAILED", "DELETE_FAILED", "LOAD_FAILED", "SUCCESS_MESSAGES", "OCR_SUCCESS", "CARD_SAVED", "CARD_UPDATED", "CARD_DELETED", "EXPORT_SUCCESS", "THEME_COLORS", "PRIMARY", "WARNING", "INFO", "TEXT_PRIMARY", "TEXT_SECONDARY", "BORDER", "BACKGROUND"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/utils/constants.js"], "sourcesContent": ["/**\n * 應用常量定義\n */\n\n// 名片欄位定義\nexport const CARD_FIELDS = {\n  name: '姓名',\n  company_name: '公司名稱',\n  position: '職位',\n  mobile_phone: '手機',\n  office_phone: '公司電話',\n  email: 'Email',\n  line_id: 'Line ID',\n  notes: '備註',\n  company_address_1: '公司地址一',\n  company_address_2: '公司地址二'\n};\n\n// 必填欄位\nexport const REQUIRED_FIELDS = ['name'];\n\n// 欄位驗證規則\nexport const FIELD_VALIDATION = {\n  name: {\n    required: true,\n    maxLength: 50,\n    pattern: null\n  },\n  company_name: {\n    required: false,\n    maxLength: 100,\n    pattern: null\n  },\n  position: {\n    required: false,\n    maxLength: 50,\n    pattern: null\n  },\n  mobile_phone: {\n    required: false,\n    maxLength: 20,\n    pattern: /^[\\d\\s\\-\\+\\(\\)]+$/\n  },\n  office_phone: {\n    required: false,\n    maxLength: 20,\n    pattern: /^[\\d\\s\\-\\+\\(\\)]+$/\n  },\n  email: {\n    required: false,\n    maxLength: 100,\n    pattern: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  },\n  line_id: {\n    required: false,\n    maxLength: 50,\n    pattern: null\n  },\n  notes: {\n    required: false,\n    maxLength: 500,\n    pattern: null\n  },\n  company_address_1: {\n    required: false,\n    maxLength: 200,\n    pattern: null\n  },\n  company_address_2: {\n    required: false,\n    maxLength: 200,\n    pattern: null\n  }\n};\n\n// OCR處理狀態\nexport const OCR_STATUS = {\n  IDLE: 'idle',\n  PROCESSING: 'processing',\n  SUCCESS: 'success',\n  ERROR: 'error'\n};\n\n// 相機目標\nexport const CAMERA_TARGET = {\n  FRONT: 'front',\n  BACK: 'back'\n};\n\n// 相機狀態\nexport const CAMERA_STATUS = {\n  INACTIVE: 'inactive',\n  STARTING: 'starting',\n  ACTIVE: 'active',\n  ERROR: 'error'\n};\n\n// 圖片處理相關常量\nexport const IMAGE_CONFIG = {\n  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB\n  ACCEPTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],\n  COMPRESSION_QUALITY: 0.8,\n  MAX_WIDTH: 1920,\n  MAX_HEIGHT: 1080\n};\n\n// 路由路徑\nexport const ROUTES = {\n  HOME: '/',\n  SCAN: '/scan',\n  CARDS: '/cards',\n  CARD_NEW: '/cards/new',\n  CARD_VIEW: '/cards/:id',\n  CARD_EDIT: '/cards/:id/edit'\n};\n\n// 本地存儲鍵名\nexport const STORAGE_KEYS = {\n  CAMERA_SETTINGS: 'ocr_camera_settings',\n  USER_PREFERENCES: 'ocr_user_preferences',\n  LAST_SCAN_DATA: 'ocr_last_scan_data'\n};\n\n// 錯誤消息\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: '網絡連接失敗，請檢查網絡設置',\n  OCR_FAILED: 'OCR識別失敗，請重試',\n  CAMERA_PERMISSION_DENIED: '相機權限被拒絕，請在瀏覽器設置中允許相機訪問',\n  CAMERA_NOT_FOUND: '未找到可用的相機設備',\n  CAMERA_OCCUPIED: '相機被其他應用程序占用',\n  FILE_TOO_LARGE: '文件大小超過限制',\n  INVALID_FILE_FORMAT: '不支持的文件格式',\n  VALIDATION_FAILED: '數據驗證失敗',\n  SAVE_FAILED: '保存失敗，請重試',\n  DELETE_FAILED: '刪除失敗，請重試',\n  LOAD_FAILED: '加載失敗，請重試'\n};\n\n// 成功消息\nexport const SUCCESS_MESSAGES = {\n  OCR_SUCCESS: 'OCR識別完成',\n  CARD_SAVED: '名片保存成功',\n  CARD_UPDATED: '名片更新成功',\n  CARD_DELETED: '名片刪除成功',\n  EXPORT_SUCCESS: '導出成功'\n};\n\n// 主題色彩\nexport const THEME_COLORS = {\n  PRIMARY: '#1677ff',\n  SUCCESS: '#52c41a',\n  WARNING: '#faad14',\n  ERROR: '#ff4d4f',\n  INFO: '#13c2c2',\n  TEXT_PRIMARY: '#262626',\n  TEXT_SECONDARY: '#8c8c8c',\n  BORDER: '#d9d9d9',\n  BACKGROUND: '#f5f5f5'\n};\n\nexport default {\n  CARD_FIELDS,\n  REQUIRED_FIELDS,\n  FIELD_VALIDATION,\n  OCR_STATUS,\n  CAMERA_TARGET,\n  CAMERA_STATUS,\n  IMAGE_CONFIG,\n  ROUTES,\n  STORAGE_KEYS,\n  ERROR_MESSAGES,\n  SUCCESS_MESSAGES,\n  THEME_COLORS\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA,OAAO,MAAMA,WAAW,GAAG;EACzBC,IAAI,EAAE,IAAI;EACVC,YAAY,EAAE,MAAM;EACpBC,QAAQ,EAAE,IAAI;EACdC,YAAY,EAAE,IAAI;EAClBC,YAAY,EAAE,MAAM;EACpBC,KAAK,EAAE,OAAO;EACdC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE,IAAI;EACXC,iBAAiB,EAAE,OAAO;EAC1BC,iBAAiB,EAAE;AACrB,CAAC;;AAED;AACA,OAAO,MAAMC,eAAe,GAAG,CAAC,MAAM,CAAC;;AAEvC;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9BX,IAAI,EAAE;IACJY,QAAQ,EAAE,IAAI;IACdC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC;EACDb,YAAY,EAAE;IACZW,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE;EACX,CAAC;EACDZ,QAAQ,EAAE;IACRU,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC;EACDX,YAAY,EAAE;IACZS,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC;EACDV,YAAY,EAAE;IACZQ,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC;EACDT,KAAK,EAAE;IACLO,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE;EACX,CAAC;EACDR,OAAO,EAAE;IACPM,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,EAAE;IACbC,OAAO,EAAE;EACX,CAAC;EACDP,KAAK,EAAE;IACLK,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE;EACX,CAAC;EACDN,iBAAiB,EAAE;IACjBI,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE;EACX,CAAC;EACDL,iBAAiB,EAAE;IACjBG,QAAQ,EAAE,KAAK;IACfC,SAAS,EAAE,GAAG;IACdC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,IAAI,EAAE,MAAM;EACZC,UAAU,EAAE,YAAY;EACxBC,OAAO,EAAE,SAAS;EAClBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,MAAM,EAAE,QAAQ;EAChBP,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMQ,YAAY,GAAG;EAC1BC,aAAa,EAAE,EAAE,GAAG,IAAI,GAAG,IAAI;EAAE;EACjCC,gBAAgB,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,YAAY,CAAC;EACxEC,mBAAmB,EAAE,GAAG;EACxBC,SAAS,EAAE,IAAI;EACfC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,GAAG;EACTC,IAAI,EAAE,OAAO;EACbC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,YAAY;EACtBC,SAAS,EAAE,YAAY;EACvBC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,eAAe,EAAE,qBAAqB;EACtCC,gBAAgB,EAAE,sBAAsB;EACxCC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,aAAa,EAAE,gBAAgB;EAC/BC,UAAU,EAAE,aAAa;EACzBC,wBAAwB,EAAE,wBAAwB;EAClDC,gBAAgB,EAAE,YAAY;EAC9BC,eAAe,EAAE,aAAa;EAC9BC,cAAc,EAAE,UAAU;EAC1BC,mBAAmB,EAAE,UAAU;EAC/BC,iBAAiB,EAAE,QAAQ;EAC3BC,WAAW,EAAE,UAAU;EACvBC,aAAa,EAAE,UAAU;EACzBC,WAAW,EAAE;AACf,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,WAAW,EAAE,SAAS;EACtBC,UAAU,EAAE,QAAQ;EACpBC,YAAY,EAAE,QAAQ;EACtBC,YAAY,EAAE,QAAQ;EACtBC,cAAc,EAAE;AAClB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,OAAO,EAAE,SAAS;EAClB7C,OAAO,EAAE,SAAS;EAClB8C,OAAO,EAAE,SAAS;EAClB7C,KAAK,EAAE,SAAS;EAChB8C,IAAI,EAAE,SAAS;EACfC,YAAY,EAAE,SAAS;EACvBC,cAAc,EAAE,SAAS;EACzBC,MAAM,EAAE,SAAS;EACjBC,UAAU,EAAE;AACd,CAAC;AAED,eAAe;EACbtE,WAAW;EACXW,eAAe;EACfC,gBAAgB;EAChBI,UAAU;EACVK,aAAa;EACbG,aAAa;EACbI,YAAY;EACZM,MAAM;EACNO,YAAY;EACZI,cAAc;EACdY,gBAAgB;EAChBM;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}