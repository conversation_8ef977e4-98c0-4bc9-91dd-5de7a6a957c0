{"ast": null, "code": "import { useRef } from 'react';\nimport { depsEqual } from '../utils/depsEqual';\nexport var createDeepCompareEffect = function (hook) {\n  return function (effect, deps) {\n    var ref = useRef();\n    var signalRef = useRef(0);\n    if (deps === undefined || !depsEqual(deps, ref.current)) {\n      signalRef.current += 1;\n    }\n    ref.current = deps;\n    hook(effect, [signalRef.current]);\n  };\n};", "map": {"version": 3, "names": ["useRef", "depsEqual", "createDeepCompareEffect", "hook", "effect", "deps", "ref", "signalRef", "undefined", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/createDeepCompareEffect/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport { depsEqual } from '../utils/depsEqual';\nexport var createDeepCompareEffect = function (hook) {\n  return function (effect, deps) {\n    var ref = useRef();\n    var signalRef = useRef(0);\n    if (deps === undefined || !depsEqual(deps, ref.current)) {\n      signalRef.current += 1;\n    }\n    ref.current = deps;\n    hook(effect, [signalRef.current]);\n  };\n};"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,QAAQ,oBAAoB;AAC9C,OAAO,IAAIC,uBAAuB,GAAG,SAAAA,CAAUC,IAAI,EAAE;EACnD,OAAO,UAAUC,MAAM,EAAEC,IAAI,EAAE;IAC7B,IAAIC,GAAG,GAAGN,MAAM,CAAC,CAAC;IAClB,IAAIO,SAAS,GAAGP,MAAM,CAAC,CAAC,CAAC;IACzB,IAAIK,IAAI,KAAKG,SAAS,IAAI,CAACP,SAAS,CAACI,IAAI,EAAEC,GAAG,CAACG,OAAO,CAAC,EAAE;MACvDF,SAAS,CAACE,OAAO,IAAI,CAAC;IACxB;IACAH,GAAG,CAACG,OAAO,GAAGJ,IAAI;IAClBF,IAAI,CAACC,MAAM,EAAE,CAACG,SAAS,CAACE,OAAO,CAAC,CAAC;EACnC,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}