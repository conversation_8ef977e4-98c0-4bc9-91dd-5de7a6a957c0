{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nfunction getTargetValue(val, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var target = val;\n  if (isNumber(max)) {\n    target = Math.min(max, target);\n  }\n  if (isNumber(min)) {\n    target = Math.max(min, target);\n  }\n  return target;\n}\nfunction useCounter(initialValue, options) {\n  if (initialValue === void 0) {\n    initialValue = 0;\n  }\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var _a = __read(useState(function () {\n      return getTargetValue(initialValue, {\n        min: min,\n        max: max\n      });\n    }), 2),\n    current = _a[0],\n    setCurrent = _a[1];\n  var setValue = function (value) {\n    setCurrent(function (c) {\n      var target = isNumber(value) ? value : value(c);\n      return getTargetValue(target, {\n        max: max,\n        min: min\n      });\n    });\n  };\n  var inc = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c + delta;\n    });\n  };\n  var dec = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c - delta;\n    });\n  };\n  var set = function (value) {\n    setValue(value);\n  };\n  var reset = function () {\n    setValue(initialValue);\n  };\n  return [current, {\n    inc: useMemoizedFn(inc),\n    dec: useMemoizedFn(dec),\n    set: useMemoizedFn(set),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useCounter;", "map": {"version": 3, "names": ["__read", "useState", "useMemoizedFn", "isNumber", "getTargetValue", "val", "options", "min", "max", "target", "Math", "useCounter", "initialValue", "_a", "current", "setCurrent", "setValue", "value", "c", "inc", "delta", "dec", "set", "reset"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useCounter/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nfunction getTargetValue(val, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var target = val;\n  if (isNumber(max)) {\n    target = Math.min(max, target);\n  }\n  if (isNumber(min)) {\n    target = Math.max(min, target);\n  }\n  return target;\n}\nfunction useCounter(initialValue, options) {\n  if (initialValue === void 0) {\n    initialValue = 0;\n  }\n  if (options === void 0) {\n    options = {};\n  }\n  var min = options.min,\n    max = options.max;\n  var _a = __read(useState(function () {\n      return getTargetValue(initialValue, {\n        min: min,\n        max: max\n      });\n    }), 2),\n    current = _a[0],\n    setCurrent = _a[1];\n  var setValue = function (value) {\n    setCurrent(function (c) {\n      var target = isNumber(value) ? value : value(c);\n      return getTargetValue(target, {\n        max: max,\n        min: min\n      });\n    });\n  };\n  var inc = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c + delta;\n    });\n  };\n  var dec = function (delta) {\n    if (delta === void 0) {\n      delta = 1;\n    }\n    setValue(function (c) {\n      return c - delta;\n    });\n  };\n  var set = function (value) {\n    setValue(value);\n  };\n  var reset = function () {\n    setValue(initialValue);\n  };\n  return [current, {\n    inc: useMemoizedFn(inc),\n    dec: useMemoizedFn(dec),\n    set: useMemoizedFn(set),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useCounter;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,UAAU;AACnC,SAASC,cAAcA,CAACC,GAAG,EAAEC,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACnBC,GAAG,GAAGF,OAAO,CAACE,GAAG;EACnB,IAAIC,MAAM,GAAGJ,GAAG;EAChB,IAAIF,QAAQ,CAACK,GAAG,CAAC,EAAE;IACjBC,MAAM,GAAGC,IAAI,CAACH,GAAG,CAACC,GAAG,EAAEC,MAAM,CAAC;EAChC;EACA,IAAIN,QAAQ,CAACI,GAAG,CAAC,EAAE;IACjBE,MAAM,GAAGC,IAAI,CAACF,GAAG,CAACD,GAAG,EAAEE,MAAM,CAAC;EAChC;EACA,OAAOA,MAAM;AACf;AACA,SAASE,UAAUA,CAACC,YAAY,EAAEN,OAAO,EAAE;EACzC,IAAIM,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,CAAC;EAClB;EACA,IAAIN,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,GAAG,GAAGD,OAAO,CAACC,GAAG;IACnBC,GAAG,GAAGF,OAAO,CAACE,GAAG;EACnB,IAAIK,EAAE,GAAGb,MAAM,CAACC,QAAQ,CAAC,YAAY;MACjC,OAAOG,cAAc,CAACQ,YAAY,EAAE;QAClCL,GAAG,EAAEA,GAAG;QACRC,GAAG,EAAEA;MACP,CAAC,CAAC;IACJ,CAAC,CAAC,EAAE,CAAC,CAAC;IACNM,OAAO,GAAGD,EAAE,CAAC,CAAC,CAAC;IACfE,UAAU,GAAGF,EAAE,CAAC,CAAC,CAAC;EACpB,IAAIG,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAE;IAC9BF,UAAU,CAAC,UAAUG,CAAC,EAAE;MACtB,IAAIT,MAAM,GAAGN,QAAQ,CAACc,KAAK,CAAC,GAAGA,KAAK,GAAGA,KAAK,CAACC,CAAC,CAAC;MAC/C,OAAOd,cAAc,CAACK,MAAM,EAAE;QAC5BD,GAAG,EAAEA,GAAG;QACRD,GAAG,EAAEA;MACP,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,IAAIY,GAAG,GAAG,SAAAA,CAAUC,KAAK,EAAE;IACzB,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAG,CAAC;IACX;IACAJ,QAAQ,CAAC,UAAUE,CAAC,EAAE;MACpB,OAAOA,CAAC,GAAGE,KAAK;IAClB,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,GAAG,GAAG,SAAAA,CAAUD,KAAK,EAAE;IACzB,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;MACpBA,KAAK,GAAG,CAAC;IACX;IACAJ,QAAQ,CAAC,UAAUE,CAAC,EAAE;MACpB,OAAOA,CAAC,GAAGE,KAAK;IAClB,CAAC,CAAC;EACJ,CAAC;EACD,IAAIE,GAAG,GAAG,SAAAA,CAAUL,KAAK,EAAE;IACzBD,QAAQ,CAACC,KAAK,CAAC;EACjB,CAAC;EACD,IAAIM,KAAK,GAAG,SAAAA,CAAA,EAAY;IACtBP,QAAQ,CAACJ,YAAY,CAAC;EACxB,CAAC;EACD,OAAO,CAACE,OAAO,EAAE;IACfK,GAAG,EAAEjB,aAAa,CAACiB,GAAG,CAAC;IACvBE,GAAG,EAAEnB,aAAa,CAACmB,GAAG,CAAC;IACvBC,GAAG,EAAEpB,aAAa,CAACoB,GAAG,CAAC;IACvBC,KAAK,EAAErB,aAAa,CAACqB,KAAK;EAC5B,CAAC,CAAC;AACJ;AACA,eAAeZ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}