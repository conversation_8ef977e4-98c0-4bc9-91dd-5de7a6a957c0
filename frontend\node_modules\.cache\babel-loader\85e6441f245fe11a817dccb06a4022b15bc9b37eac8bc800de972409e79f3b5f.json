{"ast": null, "code": "import { useEffect, useLayoutEffect } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;", "map": {"version": 3, "names": ["useEffect", "useLayoutEffect", "<PERSON><PERSON><PERSON><PERSON>", "useIsomorphicLayoutEffect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useIsomorphicLayoutEffect/index.js"], "sourcesContent": ["import { useEffect, useLayoutEffect } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar useIsomorphicLayoutEffect = isBrowser ? useLayoutEffect : useEffect;\nexport default useIsomorphicLayoutEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,eAAe,QAAQ,OAAO;AAClD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,yBAAyB,GAAGD,SAAS,GAAGD,eAAe,GAAGD,SAAS;AACvE,eAAeG,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}