{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * 自定義Hooks統一導出\n */\n\nexport { useCardData } from './useCardData';\nexport { useOCRState } from './useOCRState';\nexport { useCameraState } from './useCameraState';\n\n// 便捷的組合Hook\nexport const useOCRApp = () => {\n  _s();\n  const cardData = useCardData();\n  const ocrState = useOCRState();\n  const cameraState = useCameraState();\n  return {\n    cardData,\n    ocrState,\n    cameraState\n  };\n};\n_s(useOCRApp, \"QucbUf37As9cskG3QiEQKjJs6QI=\", true);\nexport default {\n  useCardData,\n  useOCRState,\n  useCameraState,\n  useOCRApp\n};", "map": {"version": 3, "names": ["useCardData", "useOCRState", "useCameraState", "useOCRApp", "_s", "cardData", "ocrState", "cameraState"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/hooks/index.js"], "sourcesContent": ["/**\n * 自定義Hooks統一導出\n */\n\nexport { useCardData } from './useCardData';\nexport { useOCRState } from './useOCRState';\nexport { useCameraState } from './useCameraState';\n\n// 便捷的組合Hook\nexport const useOCRApp = () => {\n  const cardData = useCardData();\n  const ocrState = useOCRState();\n  const cameraState = useCameraState();\n  \n  return {\n    cardData,\n    ocrState,\n    cameraState\n  };\n};\n\nexport default {\n  useCardData,\n  useOCRState,\n  useCameraState,\n  useOCRApp\n};\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,cAAc,QAAQ,kBAAkB;;AAEjD;AACA,OAAO,MAAMC,SAAS,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAMC,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAC9B,MAAMM,QAAQ,GAAGL,WAAW,CAAC,CAAC;EAC9B,MAAMM,WAAW,GAAGL,cAAc,CAAC,CAAC;EAEpC,OAAO;IACLG,QAAQ;IACRC,QAAQ;IACRC;EACF,CAAC;AACH,CAAC;AAACH,EAAA,CAVWD,SAAS;AAYtB,eAAe;EACbH,WAAW;EACXC,WAAW;EACXC,cAAc;EACdC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}