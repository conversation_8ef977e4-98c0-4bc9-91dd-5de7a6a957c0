{"ast": null, "code": "/**\n * 相機組件統一導出\n */\n\nexport { default as CameraCapture } from './CameraCapture';\nexport { default as ImagePreview } from './ImagePreview';\nexport { default as CameraControls } from './CameraControls';\nexport default {\n  CameraCapture,\n  ImagePreview,\n  CameraControls\n};", "map": {"version": 3, "names": ["default", "CameraCapture", "ImagePreview", "CameraControls"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Camera/index.js"], "sourcesContent": ["/**\n * 相機組件統一導出\n */\n\nexport { default as CameraCapture } from './CameraCapture';\nexport { default as ImagePreview } from './ImagePreview';\nexport { default as CameraControls } from './CameraControls';\n\nexport default {\n  CameraCapture,\n  ImagePreview,\n  CameraControls\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB;AACxD,SAASF,OAAO,IAAIG,cAAc,QAAQ,kBAAkB;AAE5D,eAAe;EACbF,aAAa;EACbC,YAAY;EACZC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}