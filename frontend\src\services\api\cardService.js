/**
 * 名片相關API服務
 * 處理名片的CRUD操作和相關功能
 */

import apiClient from './apiClient';

export const cardService = {
  /**
   * 獲取所有名片
   * @param {Object} params - 查詢參數
   * @returns {Promise<Object[]>} 名片列表
   */
  getCards: async (params = {}) => {
    try {
      const response = await apiClient.get('/cards/', { params });
      return response.data;
    } catch (error) {
      console.error('獲取名片列表失敗:', error);
      throw error;
    }
  },

  /**
   * 獲取單張名片
   * @param {number} id - 名片ID
   * @returns {Promise<Object>} 名片詳情
   */
  getCard: async (id) => {
    try {
      const response = await apiClient.get(`/cards/${id}`);
      return response.data;
    } catch (error) {
      console.error('獲取名片詳情失敗:', error);
      throw error;
    }
  },

  /**
   * 創建名片
   * @param {Object} cardData - 名片數據
   * @param {Object} images - 圖片數據 { front: {file, ocrText}, back: {file, ocrText} }
   * @returns {Promise<Object>} 創建的名片
   */
  createCard: async (cardData, images = {}) => {
    try {
      const formData = new FormData();
      
      // 添加名片數據
      Object.keys(cardData).forEach(key => {
        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {
          formData.append(key, cardData[key]);
        }
      });
      
      // 添加圖片文件和OCR數據
      if (images.front) {
        if (images.front.file) {
          formData.append('front_image', images.front.file);
        }
        if (images.front.ocrText) {
          formData.append('front_ocr_text', images.front.ocrText);
        }
      }
      
      if (images.back) {
        if (images.back.file) {
          formData.append('back_image', images.back.file);
        }
        if (images.back.ocrText) {
          formData.append('back_ocr_text', images.back.ocrText);
        }
      }
      
      const response = await apiClient.post('/cards/', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('創建名片失敗:', error);
      throw error;
    }
  },

  /**
   * 更新名片
   * @param {number} id - 名片ID
   * @param {Object} cardData - 更新的名片數據
   * @param {Object} images - 圖片數據
   * @returns {Promise<Object>} 更新後的名片
   */
  updateCard: async (id, cardData, images = {}) => {
    try {
      const formData = new FormData();
      
      // 添加名片數據
      Object.keys(cardData).forEach(key => {
        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {
          formData.append(key, cardData[key]);
        }
      });
      
      // 添加圖片文件和OCR數據
      if (images.front) {
        if (images.front.file) {
          formData.append('front_image', images.front.file);
        }
        if (images.front.ocrText) {
          formData.append('front_ocr_text', images.front.ocrText);
        }
      }
      
      if (images.back) {
        if (images.back.file) {
          formData.append('back_image', images.back.file);
        }
        if (images.back.ocrText) {
          formData.append('back_ocr_text', images.back.ocrText);
        }
      }
      
      const response = await apiClient.put(`/cards/${id}`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('更新名片失敗:', error);
      throw error;
    }
  },

  /**
   * 刪除名片
   * @param {number} id - 名片ID
   * @returns {Promise<Object>} 刪除結果
   */
  deleteCard: async (id) => {
    try {
      const response = await apiClient.delete(`/cards/${id}`);
      return response.data;
    } catch (error) {
      console.error('刪除名片失敗:', error);
      throw error;
    }
  },

  /**
   * 導出名片
   * @param {string} format - 導出格式 ('csv' 或 'excel')
   * @returns {Promise<Blob>} 導出文件
   */
  exportCards: async (format = 'csv') => {
    try {
      const response = await apiClient.get(`/cards/export?format=${format}`, {
        responseType: 'blob',
      });
      return response.data;
    } catch (error) {
      console.error('導出名片失敗:', error);
      throw error;
    }
  }
};

export default cardService;
