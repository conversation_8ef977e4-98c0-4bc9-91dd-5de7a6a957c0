{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nimport useUpdateEffect from '../useUpdateEffect';\nfunction useDebounceEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n    flag = _a[0],\n    setFlag = _a[1];\n  var run = useDebounceFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\nexport default useDebounceEffect;", "map": {"version": 3, "names": ["__read", "useEffect", "useState", "useDebounceFn", "useUpdateEffect", "useDebounceEffect", "effect", "deps", "options", "_a", "flag", "setFlag", "run"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDebounceEffect/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nimport useUpdateEffect from '../useUpdateEffect';\nfunction useDebounceEffect(effect, deps, options) {\n  var _a = __read(useState({}), 2),\n    flag = _a[0],\n    setFlag = _a[1];\n  var run = useDebounceFn(function () {\n    setFlag({});\n  }, options).run;\n  useEffect(function () {\n    return run();\n  }, deps);\n  useUpdateEffect(effect, [flag]);\n}\nexport default useDebounceEffect;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE;EAChD,IAAIC,EAAE,GAAGT,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9BQ,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IACZE,OAAO,GAAGF,EAAE,CAAC,CAAC,CAAC;EACjB,IAAIG,GAAG,GAAGT,aAAa,CAAC,YAAY;IAClCQ,OAAO,CAAC,CAAC,CAAC,CAAC;EACb,CAAC,EAAEH,OAAO,CAAC,CAACI,GAAG;EACfX,SAAS,CAAC,YAAY;IACpB,OAAOW,GAAG,CAAC,CAAC;EACd,CAAC,EAAEL,IAAI,CAAC;EACRH,eAAe,CAACE,MAAM,EAAE,CAACI,IAAI,CAAC,CAAC;AACjC;AACA,eAAeL,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}