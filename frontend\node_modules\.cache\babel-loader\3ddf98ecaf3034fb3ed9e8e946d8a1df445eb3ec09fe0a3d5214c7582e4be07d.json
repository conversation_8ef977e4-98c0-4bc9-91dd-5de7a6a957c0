{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport canUseDOM from \"rc-util/es/Dom/canUseDom\";\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nexport function getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes(canUseDOM(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif (canUseDOM()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nexport function getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nexport var supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nexport var animationEndName = internalAnimationEndName || 'animationend';\nexport var transitionEndName = internalTransitionEndName || 'transitionend';\nexport function getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if (_typeof(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}", "map": {"version": 3, "names": ["_typeof", "canUseDOM", "makePrefixMap", "styleProp", "eventName", "prefixes", "toLowerCase", "concat", "getVendorPrefixes", "domSupport", "win", "animationend", "transitionend", "animation", "transition", "vendorPrefixes", "window", "style", "_document$createEleme", "document", "createElement", "prefixedEventNames", "getVendorPrefixedEventName", "prefixMap", "stylePropList", "Object", "keys", "len", "length", "i", "prototype", "hasOwnProperty", "call", "internalAnimationEndName", "internalTransitionEndName", "supportTransition", "animationEndName", "transitionEndName", "getTransitionName", "transitionName", "transitionType", "type", "replace", "match", "toUpperCase"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-motion/es/util/motion.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport canUseDOM from \"rc-util/es/Dom/canUseDom\";\n// ================= Transition =================\n// Event wrapper. Copy from react source code\nfunction makePrefixMap(styleProp, eventName) {\n  var prefixes = {};\n  prefixes[styleProp.toLowerCase()] = eventName.toLowerCase();\n  prefixes[\"Webkit\".concat(styleProp)] = \"webkit\".concat(eventName);\n  prefixes[\"Moz\".concat(styleProp)] = \"moz\".concat(eventName);\n  prefixes[\"ms\".concat(styleProp)] = \"MS\".concat(eventName);\n  prefixes[\"O\".concat(styleProp)] = \"o\".concat(eventName.toLowerCase());\n  return prefixes;\n}\nexport function getVendorPrefixes(domSupport, win) {\n  var prefixes = {\n    animationend: makePrefixMap('Animation', 'AnimationEnd'),\n    transitionend: makePrefixMap('Transition', 'TransitionEnd')\n  };\n  if (domSupport) {\n    if (!('AnimationEvent' in win)) {\n      delete prefixes.animationend.animation;\n    }\n    if (!('TransitionEvent' in win)) {\n      delete prefixes.transitionend.transition;\n    }\n  }\n  return prefixes;\n}\nvar vendorPrefixes = getVendorPrefixes(canUseDOM(), typeof window !== 'undefined' ? window : {});\nvar style = {};\nif (canUseDOM()) {\n  var _document$createEleme = document.createElement('div');\n  style = _document$createEleme.style;\n}\nvar prefixedEventNames = {};\nexport function getVendorPrefixedEventName(eventName) {\n  if (prefixedEventNames[eventName]) {\n    return prefixedEventNames[eventName];\n  }\n  var prefixMap = vendorPrefixes[eventName];\n  if (prefixMap) {\n    var stylePropList = Object.keys(prefixMap);\n    var len = stylePropList.length;\n    for (var i = 0; i < len; i += 1) {\n      var styleProp = stylePropList[i];\n      if (Object.prototype.hasOwnProperty.call(prefixMap, styleProp) && styleProp in style) {\n        prefixedEventNames[eventName] = prefixMap[styleProp];\n        return prefixedEventNames[eventName];\n      }\n    }\n  }\n  return '';\n}\nvar internalAnimationEndName = getVendorPrefixedEventName('animationend');\nvar internalTransitionEndName = getVendorPrefixedEventName('transitionend');\nexport var supportTransition = !!(internalAnimationEndName && internalTransitionEndName);\nexport var animationEndName = internalAnimationEndName || 'animationend';\nexport var transitionEndName = internalTransitionEndName || 'transitionend';\nexport function getTransitionName(transitionName, transitionType) {\n  if (!transitionName) return null;\n  if (_typeof(transitionName) === 'object') {\n    var type = transitionType.replace(/-\\w/g, function (match) {\n      return match[1].toUpperCase();\n    });\n    return transitionName[type];\n  }\n  return \"\".concat(transitionName, \"-\").concat(transitionType);\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,SAAS,MAAM,0BAA0B;AAChD;AACA;AACA,SAASC,aAAaA,CAACC,SAAS,EAAEC,SAAS,EAAE;EAC3C,IAAIC,QAAQ,GAAG,CAAC,CAAC;EACjBA,QAAQ,CAACF,SAAS,CAACG,WAAW,CAAC,CAAC,CAAC,GAAGF,SAAS,CAACE,WAAW,CAAC,CAAC;EAC3DD,QAAQ,CAAC,QAAQ,CAACE,MAAM,CAACJ,SAAS,CAAC,CAAC,GAAG,QAAQ,CAACI,MAAM,CAACH,SAAS,CAAC;EACjEC,QAAQ,CAAC,KAAK,CAACE,MAAM,CAACJ,SAAS,CAAC,CAAC,GAAG,KAAK,CAACI,MAAM,CAACH,SAAS,CAAC;EAC3DC,QAAQ,CAAC,IAAI,CAACE,MAAM,CAACJ,SAAS,CAAC,CAAC,GAAG,IAAI,CAACI,MAAM,CAACH,SAAS,CAAC;EACzDC,QAAQ,CAAC,GAAG,CAACE,MAAM,CAACJ,SAAS,CAAC,CAAC,GAAG,GAAG,CAACI,MAAM,CAACH,SAAS,CAACE,WAAW,CAAC,CAAC,CAAC;EACrE,OAAOD,QAAQ;AACjB;AACA,OAAO,SAASG,iBAAiBA,CAACC,UAAU,EAAEC,GAAG,EAAE;EACjD,IAAIL,QAAQ,GAAG;IACbM,YAAY,EAAET,aAAa,CAAC,WAAW,EAAE,cAAc,CAAC;IACxDU,aAAa,EAAEV,aAAa,CAAC,YAAY,EAAE,eAAe;EAC5D,CAAC;EACD,IAAIO,UAAU,EAAE;IACd,IAAI,EAAE,gBAAgB,IAAIC,GAAG,CAAC,EAAE;MAC9B,OAAOL,QAAQ,CAACM,YAAY,CAACE,SAAS;IACxC;IACA,IAAI,EAAE,iBAAiB,IAAIH,GAAG,CAAC,EAAE;MAC/B,OAAOL,QAAQ,CAACO,aAAa,CAACE,UAAU;IAC1C;EACF;EACA,OAAOT,QAAQ;AACjB;AACA,IAAIU,cAAc,GAAGP,iBAAiB,CAACP,SAAS,CAAC,CAAC,EAAE,OAAOe,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,CAAC,CAAC,CAAC;AAChG,IAAIC,KAAK,GAAG,CAAC,CAAC;AACd,IAAIhB,SAAS,CAAC,CAAC,EAAE;EACf,IAAIiB,qBAAqB,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EACzDH,KAAK,GAAGC,qBAAqB,CAACD,KAAK;AACrC;AACA,IAAII,kBAAkB,GAAG,CAAC,CAAC;AAC3B,OAAO,SAASC,0BAA0BA,CAAClB,SAAS,EAAE;EACpD,IAAIiB,kBAAkB,CAACjB,SAAS,CAAC,EAAE;IACjC,OAAOiB,kBAAkB,CAACjB,SAAS,CAAC;EACtC;EACA,IAAImB,SAAS,GAAGR,cAAc,CAACX,SAAS,CAAC;EACzC,IAAImB,SAAS,EAAE;IACb,IAAIC,aAAa,GAAGC,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC;IAC1C,IAAII,GAAG,GAAGH,aAAa,CAACI,MAAM;IAC9B,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,EAAEE,CAAC,IAAI,CAAC,EAAE;MAC/B,IAAI1B,SAAS,GAAGqB,aAAa,CAACK,CAAC,CAAC;MAChC,IAAIJ,MAAM,CAACK,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,SAAS,EAAEpB,SAAS,CAAC,IAAIA,SAAS,IAAIc,KAAK,EAAE;QACpFI,kBAAkB,CAACjB,SAAS,CAAC,GAAGmB,SAAS,CAACpB,SAAS,CAAC;QACpD,OAAOkB,kBAAkB,CAACjB,SAAS,CAAC;MACtC;IACF;EACF;EACA,OAAO,EAAE;AACX;AACA,IAAI6B,wBAAwB,GAAGX,0BAA0B,CAAC,cAAc,CAAC;AACzE,IAAIY,yBAAyB,GAAGZ,0BAA0B,CAAC,eAAe,CAAC;AAC3E,OAAO,IAAIa,iBAAiB,GAAG,CAAC,EAAEF,wBAAwB,IAAIC,yBAAyB,CAAC;AACxF,OAAO,IAAIE,gBAAgB,GAAGH,wBAAwB,IAAI,cAAc;AACxE,OAAO,IAAII,iBAAiB,GAAGH,yBAAyB,IAAI,eAAe;AAC3E,OAAO,SAASI,iBAAiBA,CAACC,cAAc,EAAEC,cAAc,EAAE;EAChE,IAAI,CAACD,cAAc,EAAE,OAAO,IAAI;EAChC,IAAIvC,OAAO,CAACuC,cAAc,CAAC,KAAK,QAAQ,EAAE;IACxC,IAAIE,IAAI,GAAGD,cAAc,CAACE,OAAO,CAAC,MAAM,EAAE,UAAUC,KAAK,EAAE;MACzD,OAAOA,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF,OAAOL,cAAc,CAACE,IAAI,CAAC;EAC7B;EACA,OAAO,EAAE,CAAClC,MAAM,CAACgC,cAAc,EAAE,GAAG,CAAC,CAAChC,MAAM,CAACiC,cAAc,CAAC;AAC9D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}