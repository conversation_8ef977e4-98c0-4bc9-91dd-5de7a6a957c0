{"ast": null, "code": "import isFragment from \"../React/isFragment\";\nimport React from 'react';\nexport default function toArray(children) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var ret = [];\n  React.Children.forEach(children, function (child) {\n    if ((child === undefined || child === null) && !option.keepEmpty) {\n      return;\n    }\n    if (Array.isArray(child)) {\n      ret = ret.concat(toArray(child));\n    } else if (isFragment(child) && child.props) {\n      ret = ret.concat(toArray(child.props.children, option));\n    } else {\n      ret.push(child);\n    }\n  });\n  return ret;\n}", "map": {"version": 3, "names": ["isFragment", "React", "toArray", "children", "option", "arguments", "length", "undefined", "ret", "Children", "for<PERSON>ach", "child", "keepEmpty", "Array", "isArray", "concat", "props", "push"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-util/es/Children/toArray.js"], "sourcesContent": ["import isFragment from \"../React/isFragment\";\nimport React from 'react';\nexport default function toArray(children) {\n  var option = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  var ret = [];\n  React.Children.forEach(children, function (child) {\n    if ((child === undefined || child === null) && !option.keepEmpty) {\n      return;\n    }\n    if (Array.isArray(child)) {\n      ret = ret.concat(toArray(child));\n    } else if (isFragment(child) && child.props) {\n      ret = ret.concat(toArray(child.props.children, option));\n    } else {\n      ret.push(child);\n    }\n  });\n  return ret;\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,KAAK,MAAM,OAAO;AACzB,eAAe,SAASC,OAAOA,CAACC,QAAQ,EAAE;EACxC,IAAIC,MAAM,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACnF,IAAIG,GAAG,GAAG,EAAE;EACZP,KAAK,CAACQ,QAAQ,CAACC,OAAO,CAACP,QAAQ,EAAE,UAAUQ,KAAK,EAAE;IAChD,IAAI,CAACA,KAAK,KAAKJ,SAAS,IAAII,KAAK,KAAK,IAAI,KAAK,CAACP,MAAM,CAACQ,SAAS,EAAE;MAChE;IACF;IACA,IAAIC,KAAK,CAACC,OAAO,CAACH,KAAK,CAAC,EAAE;MACxBH,GAAG,GAAGA,GAAG,CAACO,MAAM,CAACb,OAAO,CAACS,KAAK,CAAC,CAAC;IAClC,CAAC,MAAM,IAAIX,UAAU,CAACW,KAAK,CAAC,IAAIA,KAAK,CAACK,KAAK,EAAE;MAC3CR,GAAG,GAAGA,GAAG,CAACO,MAAM,CAACb,OAAO,CAACS,KAAK,CAACK,KAAK,CAACb,QAAQ,EAAEC,MAAM,CAAC,CAAC;IACzD,CAAC,MAAM;MACLI,GAAG,CAACS,IAAI,CAACN,KAAK,CAAC;IACjB;EACF,CAAC,CAAC;EACF,OAAOH,GAAG;AACZ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}