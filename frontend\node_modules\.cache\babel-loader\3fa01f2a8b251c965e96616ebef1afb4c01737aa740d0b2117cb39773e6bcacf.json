{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\OCR\\\\OCRProcessor.js\",\n  _s = $RefreshSig$();\n/**\n * OCR處理邏輯組件\n */\n\nimport React, { useEffect } from 'react';\nimport { useOCRState } from '../../hooks';\nimport OCRStatus from './OCRStatus';\nimport ParsedFieldsDisplay from './ParsedFieldsDisplay';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OCRProcessor = ({\n  frontImage,\n  backImage,\n  onFieldsParsed,\n  onOCRCompleted,\n  autoProcess = true,\n  showStatus = true,\n  showResults = true,\n  style = {},\n  className = ''\n}) => {\n  _s();\n  const {\n    frontOCR,\n    backOCR,\n    processImage,\n    parseFields,\n    getOCRStatus,\n    getMergedFields,\n    isProcessing,\n    hasError,\n    hasResults\n  } = useOCRState();\n\n  // 自動處理圖片OCR\n  useEffect(() => {\n    if (autoProcess && frontImage && frontImage.file && !frontOCR.text) {\n      processImage(frontImage.file, 'front').catch(console.error);\n    }\n  }, [autoProcess, frontImage, frontOCR.text, processImage]);\n  useEffect(() => {\n    if (autoProcess && backImage && backImage.file && !backOCR.text) {\n      processImage(backImage.file, 'back').catch(console.error);\n    }\n  }, [autoProcess, backImage, backOCR.text, processImage]);\n\n  // 當解析完成時通知父組件\n  useEffect(() => {\n    if (onFieldsParsed) {\n      const mergedFields = getMergedFields();\n      if (Object.keys(mergedFields).length > 0) {\n        onFieldsParsed(mergedFields, frontOCR.parsedFields, backOCR.parsedFields);\n      }\n    }\n  }, [frontOCR.parsedFields, backOCR.parsedFields, onFieldsParsed, getMergedFields]);\n\n  // 當OCR完成時通知父組件\n  useEffect(() => {\n    if (onOCRCompleted && hasResults && !isProcessing) {\n      onOCRCompleted({\n        frontText: frontOCR.text,\n        backText: backOCR.text,\n        frontFields: frontOCR.parsedFields,\n        backFields: backOCR.parsedFields,\n        mergedFields: getMergedFields()\n      });\n    }\n  }, [hasResults, isProcessing, onOCRCompleted, frontOCR, backOCR, getMergedFields]);\n\n  // 手動處理OCR\n  const handleProcessImage = async (imageFile, side) => {\n    try {\n      await processImage(imageFile, side);\n    } catch (error) {\n      console.error('OCR處理失敗:', error);\n    }\n  };\n\n  // 重新處理OCR\n  const handleRetryOCR = async side => {\n    const image = side === 'front' ? frontImage : backImage;\n    if (image && image.file) {\n      await handleProcessImage(image.file, side);\n    }\n  };\n\n  // 應用解析結果到表單\n  const handleApplyToForm = fields => {\n    if (onFieldsParsed) {\n      onFieldsParsed(fields, frontOCR.parsedFields, backOCR.parsedFields);\n    }\n  };\n  const overallStatus = getOCRStatus();\n  const mergedFields = getMergedFields();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `ocr-processor ${className}`,\n    style: style,\n    children: [showStatus && /*#__PURE__*/_jsxDEV(OCRStatus, {\n      status: overallStatus,\n      error: frontOCR.error || backOCR.error,\n      frontStatus: frontOCR.status,\n      backStatus: backOCR.status,\n      showDetails: true,\n      style: {\n        marginBottom: '16px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 9\n    }, this), showResults && hasResults && /*#__PURE__*/_jsxDEV(ParsedFieldsDisplay, {\n      frontFields: frontOCR.parsedFields,\n      backFields: backOCR.parsedFields,\n      mergedFields: mergedFields,\n      onApplyToForm: handleApplyToForm,\n      showMerged: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 9\n    }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '16px',\n        fontSize: '12px',\n        color: '#8c8c8c'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"details\", {\n        children: [/*#__PURE__*/_jsxDEV(\"summary\", {\n          children: \"OCR\\u8ABF\\u8A66\\u4FE1\\u606F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 125,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"pre\", {\n          style: {\n            fontSize: '10px',\n            overflow: 'auto'\n          },\n          children: JSON.stringify({\n            frontOCR: {\n              status: frontOCR.status,\n              hasText: !!frontOCR.text,\n              fieldsCount: Object.keys(frontOCR.parsedFields).length,\n              error: frontOCR.error\n            },\n            backOCR: {\n              status: backOCR.status,\n              hasText: !!backOCR.text,\n              fieldsCount: Object.keys(backOCR.parsedFields).length,\n              error: backOCR.error\n            },\n            merged: {\n              fieldsCount: Object.keys(mergedFields).length\n            }\n          }, null, 2)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 126,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 124,\n        columnNumber: 11\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 123,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 97,\n    columnNumber: 5\n  }, this);\n};\n_s(OCRProcessor, \"wtsc/n2QoPngppcJgPGq1vZ1138=\", false, function () {\n  return [useOCRState];\n});\n_c = OCRProcessor;\nexport default OCRProcessor;\nvar _c;\n$RefreshReg$(_c, \"OCRProcessor\");", "map": {"version": 3, "names": ["React", "useEffect", "useOCRState", "OCRStatus", "ParsedFieldsDisplay", "jsxDEV", "_jsxDEV", "OCRProcessor", "frontImage", "backImage", "onFieldsParsed", "onOCRCompleted", "autoProcess", "showStatus", "showResults", "style", "className", "_s", "frontOCR", "backOCR", "processImage", "parseFields", "getOCRStatus", "getMergedFields", "isProcessing", "<PERSON><PERSON><PERSON><PERSON>", "hasResults", "file", "text", "catch", "console", "error", "mergedFields", "Object", "keys", "length", "parsedFields", "frontText", "backText", "frontFields", "backFields", "handleProcessImage", "imageFile", "side", "handleRetryOCR", "image", "handleApplyToForm", "fields", "overallStatus", "children", "status", "frontStatus", "backStatus", "showDetails", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onApplyToForm", "showMerged", "process", "env", "NODE_ENV", "marginTop", "fontSize", "color", "overflow", "JSON", "stringify", "hasText", "fieldsCount", "merged", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/OCR/OCRProcessor.js"], "sourcesContent": ["/**\n * OCR處理邏輯組件\n */\n\nimport React, { useEffect } from 'react';\nimport { useOCRState } from '../../hooks';\nimport OCRStatus from './OCRStatus';\nimport ParsedFieldsDisplay from './ParsedFieldsDisplay';\n\nconst OCRProcessor = ({\n  frontImage,\n  backImage,\n  onFieldsParsed,\n  onOCRCompleted,\n  autoProcess = true,\n  showStatus = true,\n  showResults = true,\n  style = {},\n  className = ''\n}) => {\n  const {\n    frontOCR,\n    backOCR,\n    processImage,\n    parseFields,\n    getOCRStatus,\n    getMergedFields,\n    isProcessing,\n    hasError,\n    hasResults\n  } = useOCRState();\n\n  // 自動處理圖片OCR\n  useEffect(() => {\n    if (autoProcess && frontImage && frontImage.file && !frontOCR.text) {\n      processImage(frontImage.file, 'front').catch(console.error);\n    }\n  }, [autoProcess, frontImage, frontOCR.text, processImage]);\n\n  useEffect(() => {\n    if (autoProcess && backImage && backImage.file && !backOCR.text) {\n      processImage(backImage.file, 'back').catch(console.error);\n    }\n  }, [autoProcess, backImage, backOCR.text, processImage]);\n\n  // 當解析完成時通知父組件\n  useEffect(() => {\n    if (onFieldsParsed) {\n      const mergedFields = getMergedFields();\n      if (Object.keys(mergedFields).length > 0) {\n        onFieldsParsed(mergedFields, frontOCR.parsedFields, backOCR.parsedFields);\n      }\n    }\n  }, [frontOCR.parsedFields, backOCR.parsedFields, onFieldsParsed, getMergedFields]);\n\n  // 當OCR完成時通知父組件\n  useEffect(() => {\n    if (onOCRCompleted && hasResults && !isProcessing) {\n      onOCRCompleted({\n        frontText: frontOCR.text,\n        backText: backOCR.text,\n        frontFields: frontOCR.parsedFields,\n        backFields: backOCR.parsedFields,\n        mergedFields: getMergedFields()\n      });\n    }\n  }, [hasResults, isProcessing, onOCRCompleted, frontOCR, backOCR, getMergedFields]);\n\n  // 手動處理OCR\n  const handleProcessImage = async (imageFile, side) => {\n    try {\n      await processImage(imageFile, side);\n    } catch (error) {\n      console.error('OCR處理失敗:', error);\n    }\n  };\n\n  // 重新處理OCR\n  const handleRetryOCR = async (side) => {\n    const image = side === 'front' ? frontImage : backImage;\n    if (image && image.file) {\n      await handleProcessImage(image.file, side);\n    }\n  };\n\n  // 應用解析結果到表單\n  const handleApplyToForm = (fields) => {\n    if (onFieldsParsed) {\n      onFieldsParsed(fields, frontOCR.parsedFields, backOCR.parsedFields);\n    }\n  };\n\n  const overallStatus = getOCRStatus();\n  const mergedFields = getMergedFields();\n\n  return (\n    <div className={`ocr-processor ${className}`} style={style}>\n      {/* OCR狀態顯示 */}\n      {showStatus && (\n        <OCRStatus\n          status={overallStatus}\n          error={frontOCR.error || backOCR.error}\n          frontStatus={frontOCR.status}\n          backStatus={backOCR.status}\n          showDetails={true}\n          style={{ marginBottom: '16px' }}\n        />\n      )}\n\n      {/* 解析結果顯示 */}\n      {showResults && hasResults && (\n        <ParsedFieldsDisplay\n          frontFields={frontOCR.parsedFields}\n          backFields={backOCR.parsedFields}\n          mergedFields={mergedFields}\n          onApplyToForm={handleApplyToForm}\n          showMerged={true}\n        />\n      )}\n\n      {/* 調試信息（開發環境） */}\n      {process.env.NODE_ENV === 'development' && (\n        <div style={{ marginTop: '16px', fontSize: '12px', color: '#8c8c8c' }}>\n          <details>\n            <summary>OCR調試信息</summary>\n            <pre style={{ fontSize: '10px', overflow: 'auto' }}>\n              {JSON.stringify({\n                frontOCR: {\n                  status: frontOCR.status,\n                  hasText: !!frontOCR.text,\n                  fieldsCount: Object.keys(frontOCR.parsedFields).length,\n                  error: frontOCR.error\n                },\n                backOCR: {\n                  status: backOCR.status,\n                  hasText: !!backOCR.text,\n                  fieldsCount: Object.keys(backOCR.parsedFields).length,\n                  error: backOCR.error\n                },\n                merged: {\n                  fieldsCount: Object.keys(mergedFields).length\n                }\n              }, null, 2)}\n            </pre>\n          </details>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default OCRProcessor;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAOC,mBAAmB,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,YAAY,GAAGA,CAAC;EACpBC,UAAU;EACVC,SAAS;EACTC,cAAc;EACdC,cAAc;EACdC,WAAW,GAAG,IAAI;EAClBC,UAAU,GAAG,IAAI;EACjBC,WAAW,GAAG,IAAI;EAClBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IACJC,QAAQ;IACRC,OAAO;IACPC,YAAY;IACZC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,YAAY;IACZC,QAAQ;IACRC;EACF,CAAC,GAAGxB,WAAW,CAAC,CAAC;;EAEjB;EACAD,SAAS,CAAC,MAAM;IACd,IAAIW,WAAW,IAAIJ,UAAU,IAAIA,UAAU,CAACmB,IAAI,IAAI,CAACT,QAAQ,CAACU,IAAI,EAAE;MAClER,YAAY,CAACZ,UAAU,CAACmB,IAAI,EAAE,OAAO,CAAC,CAACE,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;IAC7D;EACF,CAAC,EAAE,CAACnB,WAAW,EAAEJ,UAAU,EAAEU,QAAQ,CAACU,IAAI,EAAER,YAAY,CAAC,CAAC;EAE1DnB,SAAS,CAAC,MAAM;IACd,IAAIW,WAAW,IAAIH,SAAS,IAAIA,SAAS,CAACkB,IAAI,IAAI,CAACR,OAAO,CAACS,IAAI,EAAE;MAC/DR,YAAY,CAACX,SAAS,CAACkB,IAAI,EAAE,MAAM,CAAC,CAACE,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC;IAC3D;EACF,CAAC,EAAE,CAACnB,WAAW,EAAEH,SAAS,EAAEU,OAAO,CAACS,IAAI,EAAER,YAAY,CAAC,CAAC;;EAExD;EACAnB,SAAS,CAAC,MAAM;IACd,IAAIS,cAAc,EAAE;MAClB,MAAMsB,YAAY,GAAGT,eAAe,CAAC,CAAC;MACtC,IAAIU,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACG,MAAM,GAAG,CAAC,EAAE;QACxCzB,cAAc,CAACsB,YAAY,EAAEd,QAAQ,CAACkB,YAAY,EAAEjB,OAAO,CAACiB,YAAY,CAAC;MAC3E;IACF;EACF,CAAC,EAAE,CAAClB,QAAQ,CAACkB,YAAY,EAAEjB,OAAO,CAACiB,YAAY,EAAE1B,cAAc,EAAEa,eAAe,CAAC,CAAC;;EAElF;EACAtB,SAAS,CAAC,MAAM;IACd,IAAIU,cAAc,IAAIe,UAAU,IAAI,CAACF,YAAY,EAAE;MACjDb,cAAc,CAAC;QACb0B,SAAS,EAAEnB,QAAQ,CAACU,IAAI;QACxBU,QAAQ,EAAEnB,OAAO,CAACS,IAAI;QACtBW,WAAW,EAAErB,QAAQ,CAACkB,YAAY;QAClCI,UAAU,EAAErB,OAAO,CAACiB,YAAY;QAChCJ,YAAY,EAAET,eAAe,CAAC;MAChC,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAACG,UAAU,EAAEF,YAAY,EAAEb,cAAc,EAAEO,QAAQ,EAAEC,OAAO,EAAEI,eAAe,CAAC,CAAC;;EAElF;EACA,MAAMkB,kBAAkB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,IAAI,KAAK;IACpD,IAAI;MACF,MAAMvB,YAAY,CAACsB,SAAS,EAAEC,IAAI,CAAC;IACrC,CAAC,CAAC,OAAOZ,KAAK,EAAE;MACdD,OAAO,CAACC,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;IAClC;EACF,CAAC;;EAED;EACA,MAAMa,cAAc,GAAG,MAAOD,IAAI,IAAK;IACrC,MAAME,KAAK,GAAGF,IAAI,KAAK,OAAO,GAAGnC,UAAU,GAAGC,SAAS;IACvD,IAAIoC,KAAK,IAAIA,KAAK,CAAClB,IAAI,EAAE;MACvB,MAAMc,kBAAkB,CAACI,KAAK,CAAClB,IAAI,EAAEgB,IAAI,CAAC;IAC5C;EACF,CAAC;;EAED;EACA,MAAMG,iBAAiB,GAAIC,MAAM,IAAK;IACpC,IAAIrC,cAAc,EAAE;MAClBA,cAAc,CAACqC,MAAM,EAAE7B,QAAQ,CAACkB,YAAY,EAAEjB,OAAO,CAACiB,YAAY,CAAC;IACrE;EACF,CAAC;EAED,MAAMY,aAAa,GAAG1B,YAAY,CAAC,CAAC;EACpC,MAAMU,YAAY,GAAGT,eAAe,CAAC,CAAC;EAEtC,oBACEjB,OAAA;IAAKU,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IAACD,KAAK,EAAEA,KAAM;IAAAkC,QAAA,GAExDpC,UAAU,iBACTP,OAAA,CAACH,SAAS;MACR+C,MAAM,EAAEF,aAAc;MACtBjB,KAAK,EAAEb,QAAQ,CAACa,KAAK,IAAIZ,OAAO,CAACY,KAAM;MACvCoB,WAAW,EAAEjC,QAAQ,CAACgC,MAAO;MAC7BE,UAAU,EAAEjC,OAAO,CAAC+B,MAAO;MAC3BG,WAAW,EAAE,IAAK;MAClBtC,KAAK,EAAE;QAAEuC,YAAY,EAAE;MAAO;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CACF,EAGA5C,WAAW,IAAIY,UAAU,iBACxBpB,OAAA,CAACF,mBAAmB;MAClBmC,WAAW,EAAErB,QAAQ,CAACkB,YAAa;MACnCI,UAAU,EAAErB,OAAO,CAACiB,YAAa;MACjCJ,YAAY,EAAEA,YAAa;MAC3B2B,aAAa,EAAEb,iBAAkB;MACjCc,UAAU,EAAE;IAAK;MAAAL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB,CACF,EAGAG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBACrCzD,OAAA;MAAKS,KAAK,EAAE;QAAEiD,SAAS,EAAE,MAAM;QAAEC,QAAQ,EAAE,MAAM;QAAEC,KAAK,EAAE;MAAU,CAAE;MAAAjB,QAAA,eACpE3C,OAAA;QAAA2C,QAAA,gBACE3C,OAAA;UAAA2C,QAAA,EAAS;QAAO;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1BpD,OAAA;UAAKS,KAAK,EAAE;YAAEkD,QAAQ,EAAE,MAAM;YAAEE,QAAQ,EAAE;UAAO,CAAE;UAAAlB,QAAA,EAChDmB,IAAI,CAACC,SAAS,CAAC;YACdnD,QAAQ,EAAE;cACRgC,MAAM,EAAEhC,QAAQ,CAACgC,MAAM;cACvBoB,OAAO,EAAE,CAAC,CAACpD,QAAQ,CAACU,IAAI;cACxB2C,WAAW,EAAEtC,MAAM,CAACC,IAAI,CAAChB,QAAQ,CAACkB,YAAY,CAAC,CAACD,MAAM;cACtDJ,KAAK,EAAEb,QAAQ,CAACa;YAClB,CAAC;YACDZ,OAAO,EAAE;cACP+B,MAAM,EAAE/B,OAAO,CAAC+B,MAAM;cACtBoB,OAAO,EAAE,CAAC,CAACnD,OAAO,CAACS,IAAI;cACvB2C,WAAW,EAAEtC,MAAM,CAACC,IAAI,CAACf,OAAO,CAACiB,YAAY,CAAC,CAACD,MAAM;cACrDJ,KAAK,EAAEZ,OAAO,CAACY;YACjB,CAAC;YACDyC,MAAM,EAAE;cACND,WAAW,EAAEtC,MAAM,CAACC,IAAI,CAACF,YAAY,CAAC,CAACG;YACzC;UACF,CAAC,EAAE,IAAI,EAAE,CAAC;QAAC;UAAAoB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACzC,EAAA,CA5IIV,YAAY;EAAA,QAqBZL,WAAW;AAAA;AAAAuE,EAAA,GArBXlE,YAAY;AA8IlB,eAAeA,YAAY;AAAC,IAAAkE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}