/**
 * 組件測試文件
 * 用於驗證重構後的組件是否正常工作
 */

import React from 'react';
import { createRoot } from 'react-dom/client';

// 測試基本組件導入
try {
  console.log('🧪 開始測試組件導入...');
  
  // 測試UI組件
  const { LoadingSpinner, ErrorMessage, SuccessMessage } = require('./components/UI');
  console.log('✅ UI組件導入成功');
  
  // 測試佈局組件
  const { PageHeader, PageContainer } = require('./components/Layout');
  console.log('✅ 佈局組件導入成功');
  
  // 測試表單組件
  const { CardForm, FormField } = require('./components/Form');
  console.log('✅ 表單組件導入成功');
  
  // 測試Hooks
  const { useCardData, useOCRState, useCameraState } = require('./hooks');
  console.log('✅ Hooks導入成功');
  
  // 測試服務
  const { cardService, ocrService } = require('./services/api/cardService');
  console.log('✅ 服務導入成功');
  
  // 測試工具函數
  const { validateForm } = require('./utils/validation');
  const { formatFormData } = require('./utils/formatters');
  console.log('✅ 工具函數導入成功');
  
  console.log('🎉 所有組件測試通過！');
  
} catch (error) {
  console.error('❌ 組件測試失敗:', error);
}

// 簡單的React組件測試
const TestComponent = () => {
  return React.createElement('div', null, 'Test Component');
};

// 導出測試組件
export default TestComponent;
