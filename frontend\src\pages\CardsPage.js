/**
 * 名片管理頁面 - 重構版本
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  SearchBar, 
  Card, 
  Button, 
  Space, 
  Tag, 
  Empty,
  Modal,
  Divider
} from 'antd-mobile';
import { 
  AddOutline,
  ScanningOutline,
  DeleteOutline,
  EditSOutline,
  EyeOutline,
  PhoneFill,
  MailOutline,
  EnvironmentOutline,
  DownlandOutline
} from 'antd-mobile-icons';
import { PageContainer } from '../components/Layout';
import { LoadingSpinner, ErrorMessage } from '../components/UI';
import { useCardData } from '../hooks';
import { formatDateTime, truncateText } from '../utils/formatters';

const CardsPage = () => {
  const navigate = useNavigate();
  const {
    filteredCards,
    loading,
    error,
    searchText,
    searchCards,
    clearSearch,
    deleteCard,
    exportCards,
    totalCards,
    filteredCount
  } = useCardData();

  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [cardToDelete, setCardToDelete] = useState(null);

  // 處理搜索
  const handleSearch = (value) => {
    searchCards(value);
  };

  // 處理刪除確認
  const handleDeleteConfirm = (card) => {
    setCardToDelete(card);
    setDeleteModalVisible(true);
  };

  // 執行刪除
  const handleDeleteExecute = async () => {
    if (cardToDelete) {
      try {
        await deleteCard(cardToDelete.id);
        setDeleteModalVisible(false);
        setCardToDelete(null);
      } catch (error) {
        console.error('刪除失敗:', error);
      }
    }
  };

  // 處理導出
  const handleExport = async (format) => {
    try {
      await exportCards(format);
    } catch (error) {
      console.error('導出失敗:', error);
    }
  };

  // 渲染名片項目
  const renderCardItem = (card) => (
    <Card 
      key={card.id}
      style={{ marginBottom: '12px' }}
      onClick={() => navigate(`/cards/${card.id}`)}
    >
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
        <div style={{ flex: 1 }}>
          {/* 基本信息 */}
          <div style={{ marginBottom: '8px' }}>
            <div style={{ fontSize: '16px', fontWeight: 'bold', marginBottom: '4px' }}>
              {card.name}
            </div>
            {card.company_name && (
              <div style={{ fontSize: '14px', color: '#8c8c8c' }}>
                {card.company_name}
                {card.position && ` · ${card.position}`}
              </div>
            )}
          </div>

          {/* 聯絡信息 */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '4px' }}>
            {card.mobile_phone && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <PhoneFill style={{ fontSize: '12px', color: '#52c41a' }} />
                <span style={{ fontSize: '13px', color: '#595959' }}>
                  {card.mobile_phone}
                </span>
              </div>
            )}
            
            {card.email && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <MailOutline style={{ fontSize: '12px', color: '#1677ff' }} />
                <span style={{ fontSize: '13px', color: '#595959' }}>
                  {truncateText(card.email, 25)}
                </span>
              </div>
            )}
            
            {(card.company_address_1 || card.company_address_2) && (
              <div style={{ display: 'flex', alignItems: 'center', gap: '6px' }}>
                <EnvironmentOutline style={{ fontSize: '12px', color: '#fa8c16' }} />
                <span style={{ fontSize: '13px', color: '#595959' }}>
                  {truncateText(card.company_address_1 || card.company_address_2, 30)}
                </span>
              </div>
            )}
          </div>

          {/* 時間信息 */}
          <div style={{ marginTop: '8px', fontSize: '12px', color: '#bfbfbf' }}>
            {formatDateTime(card.created_at, 'date')}
          </div>
        </div>

        {/* 操作按鈕 */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '4px', marginLeft: '12px' }}>
          <Button
            size="mini"
            fill="none"
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/cards/${card.id}`);
            }}
          >
            <EyeOutline />
          </Button>
          
          <Button
            size="mini"
            fill="none"
            onClick={(e) => {
              e.stopPropagation();
              navigate(`/cards/${card.id}/edit`);
            }}
          >
            <EditSOutline />
          </Button>
          
          <Button
            size="mini"
            fill="none"
            color="danger"
            onClick={(e) => {
              e.stopPropagation();
              handleDeleteConfirm(card);
            }}
          >
            <DeleteOutline />
          </Button>
        </div>
      </div>
    </Card>
  );

  if (loading && filteredCards.length === 0) {
    return (
      <PageContainer title="名片管理">
        <LoadingSpinner text="載入名片中..." />
      </PageContainer>
    );
  }

  if (error) {
    return (
      <PageContainer title="名片管理">
        <ErrorMessage 
          error={error}
          onRetry={() => window.location.reload()}
        />
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="名片管理"
      onBack={() => navigate('/')}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 搜索欄 */}
        <SearchBar
          placeholder="搜索名片（姓名、公司、電話、郵箱）"
          value={searchText}
          onChange={handleSearch}
          onClear={clearSearch}
        />

        {/* 操作按鈕 */}
        <Card>
          <Space direction="vertical" style={{ width: '100%' }}>
            <Space style={{ width: '100%' }}>
              <Button 
                color="primary" 
                size="large" 
                style={{ flex: 1 }}
                onClick={() => navigate('/cards/new')}
              >
                <AddOutline /> 手動新增
              </Button>
              <Button 
                color="default" 
                size="large" 
                style={{ flex: 1 }}
                onClick={() => navigate('/scan')}
              >
                <ScanningOutline /> OCR掃描
              </Button>
            </Space>
            
            <Space style={{ width: '100%' }}>
              <Button 
                color="default" 
                fill="outline"
                style={{ flex: 1 }}
                onClick={() => handleExport('csv')}
              >
                <DownlandOutline /> 導出CSV
              </Button>
              <Button 
                color="default" 
                fill="outline"
                style={{ flex: 1 }}
                onClick={() => handleExport('excel')}
              >
                <DownlandOutline /> 導出Excel
              </Button>
            </Space>
          </Space>
        </Card>

        {/* 統計信息 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{ fontSize: '14px', color: '#8c8c8c' }}>
            {searchText ? (
              <>找到 <Tag color="primary">{filteredCount}</Tag> 張名片</>
            ) : (
              <>共 <Tag color="primary">{totalCards}</Tag> 張名片</>
            )}
          </div>
          
          {searchText && (
            <Button size="mini" fill="none" onClick={clearSearch}>
              清除搜索
            </Button>
          )}
        </div>

        {/* 名片列表 */}
        {filteredCards.length === 0 ? (
          <Empty
            style={{ padding: '40px' }}
            description={
              searchText 
                ? `沒有找到包含 "${searchText}" 的名片` 
                : "還沒有名片，點擊上方按鈕新增"
            }
          />
        ) : (
          <div>
            {filteredCards.map(renderCardItem)}
          </div>
        )}
      </Space>

      {/* 刪除確認對話框 */}
      <Modal
        visible={deleteModalVisible}
        content={
          <div style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ marginBottom: '16px' }}>
              確定要刪除名片 <strong>{cardToDelete?.name}</strong> 嗎？
            </div>
            <div style={{ fontSize: '14px', color: '#8c8c8c' }}>
              此操作無法撤銷
            </div>
          </div>
        }
        closeOnAction
        onClose={() => setDeleteModalVisible(false)}
        actions={[
          {
            key: 'cancel',
            text: '取消',
            onClick: () => setDeleteModalVisible(false)
          },
          {
            key: 'delete',
            text: '刪除',
            danger: true,
            onClick: handleDeleteExecute
          }
        ]}
      />
    </PageContainer>
  );
};

export default CardsPage;
