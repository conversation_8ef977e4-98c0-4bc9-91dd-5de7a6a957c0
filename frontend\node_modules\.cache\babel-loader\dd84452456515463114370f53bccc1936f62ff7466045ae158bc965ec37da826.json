{"ast": null, "code": "import { useRef } from 'react';\nconst MIN_DISTANCE = 10;\nfunction getDirection(x, y) {\n  if (x > y && x > MIN_DISTANCE) {\n    return 'horizontal';\n  }\n  if (y > x && y > MIN_DISTANCE) {\n    return 'vertical';\n  }\n  return '';\n}\nexport function useTouch() {\n  const startX = useRef(0);\n  const startY = useRef(0);\n  const deltaX = useRef(0);\n  const deltaY = useRef(0);\n  const offsetX = useRef(0);\n  const offsetY = useRef(0);\n  const direction = useRef('');\n  const isVertical = () => direction.current === 'vertical';\n  const isHorizontal = () => direction.current === 'horizontal';\n  const reset = () => {\n    deltaX.current = 0;\n    deltaY.current = 0;\n    offsetX.current = 0;\n    offsetY.current = 0;\n    direction.current = '';\n  };\n  const start = event => {\n    reset();\n    startX.current = event.touches[0].clientX;\n    startY.current = event.touches[0].clientY;\n  };\n  const move = event => {\n    const touch = event.touches[0];\n    // Fix: <PERSON><PERSON> back will set clientX to negative number\n    deltaX.current = touch.clientX < 0 ? 0 : touch.clientX - startX.current;\n    deltaY.current = touch.clientY - startY.current;\n    offsetX.current = Math.abs(deltaX.current);\n    offsetY.current = Math.abs(deltaY.current);\n    if (!direction.current) {\n      direction.current = getDirection(offsetX.current, offsetY.current);\n    }\n  };\n  return {\n    move,\n    start,\n    reset,\n    startX,\n    startY,\n    deltaX,\n    deltaY,\n    offsetX,\n    offsetY,\n    direction,\n    isVertical,\n    isHorizontal\n  };\n}", "map": {"version": 3, "names": ["useRef", "MIN_DISTANCE", "getDirection", "x", "y", "useTouch", "startX", "startY", "deltaX", "deltaY", "offsetX", "offsetY", "direction", "isVertical", "current", "isHorizontal", "reset", "start", "event", "touches", "clientX", "clientY", "move", "touch", "Math", "abs"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/use-touch.js"], "sourcesContent": ["import { useRef } from 'react';\nconst MIN_DISTANCE = 10;\nfunction getDirection(x, y) {\n  if (x > y && x > MIN_DISTANCE) {\n    return 'horizontal';\n  }\n  if (y > x && y > MIN_DISTANCE) {\n    return 'vertical';\n  }\n  return '';\n}\nexport function useTouch() {\n  const startX = useRef(0);\n  const startY = useRef(0);\n  const deltaX = useRef(0);\n  const deltaY = useRef(0);\n  const offsetX = useRef(0);\n  const offsetY = useRef(0);\n  const direction = useRef('');\n  const isVertical = () => direction.current === 'vertical';\n  const isHorizontal = () => direction.current === 'horizontal';\n  const reset = () => {\n    deltaX.current = 0;\n    deltaY.current = 0;\n    offsetX.current = 0;\n    offsetY.current = 0;\n    direction.current = '';\n  };\n  const start = event => {\n    reset();\n    startX.current = event.touches[0].clientX;\n    startY.current = event.touches[0].clientY;\n  };\n  const move = event => {\n    const touch = event.touches[0];\n    // Fix: <PERSON><PERSON> back will set clientX to negative number\n    deltaX.current = touch.clientX < 0 ? 0 : touch.clientX - startX.current;\n    deltaY.current = touch.clientY - startY.current;\n    offsetX.current = Math.abs(deltaX.current);\n    offsetY.current = Math.abs(deltaY.current);\n    if (!direction.current) {\n      direction.current = getDirection(offsetX.current, offsetY.current);\n    }\n  };\n  return {\n    move,\n    start,\n    reset,\n    startX,\n    startY,\n    deltaX,\n    deltaY,\n    offsetX,\n    offsetY,\n    direction,\n    isVertical,\n    isHorizontal\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,MAAMC,YAAY,GAAG,EAAE;AACvB,SAASC,YAAYA,CAACC,CAAC,EAAEC,CAAC,EAAE;EAC1B,IAAID,CAAC,GAAGC,CAAC,IAAID,CAAC,GAAGF,YAAY,EAAE;IAC7B,OAAO,YAAY;EACrB;EACA,IAAIG,CAAC,GAAGD,CAAC,IAAIC,CAAC,GAAGH,YAAY,EAAE;IAC7B,OAAO,UAAU;EACnB;EACA,OAAO,EAAE;AACX;AACA,OAAO,SAASI,QAAQA,CAAA,EAAG;EACzB,MAAMC,MAAM,GAAGN,MAAM,CAAC,CAAC,CAAC;EACxB,MAAMO,MAAM,GAAGP,MAAM,CAAC,CAAC,CAAC;EACxB,MAAMQ,MAAM,GAAGR,MAAM,CAAC,CAAC,CAAC;EACxB,MAAMS,MAAM,GAAGT,MAAM,CAAC,CAAC,CAAC;EACxB,MAAMU,OAAO,GAAGV,MAAM,CAAC,CAAC,CAAC;EACzB,MAAMW,OAAO,GAAGX,MAAM,CAAC,CAAC,CAAC;EACzB,MAAMY,SAAS,GAAGZ,MAAM,CAAC,EAAE,CAAC;EAC5B,MAAMa,UAAU,GAAGA,CAAA,KAAMD,SAAS,CAACE,OAAO,KAAK,UAAU;EACzD,MAAMC,YAAY,GAAGA,CAAA,KAAMH,SAAS,CAACE,OAAO,KAAK,YAAY;EAC7D,MAAME,KAAK,GAAGA,CAAA,KAAM;IAClBR,MAAM,CAACM,OAAO,GAAG,CAAC;IAClBL,MAAM,CAACK,OAAO,GAAG,CAAC;IAClBJ,OAAO,CAACI,OAAO,GAAG,CAAC;IACnBH,OAAO,CAACG,OAAO,GAAG,CAAC;IACnBF,SAAS,CAACE,OAAO,GAAG,EAAE;EACxB,CAAC;EACD,MAAMG,KAAK,GAAGC,KAAK,IAAI;IACrBF,KAAK,CAAC,CAAC;IACPV,MAAM,CAACQ,OAAO,GAAGI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACC,OAAO;IACzCb,MAAM,CAACO,OAAO,GAAGI,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC,CAACE,OAAO;EAC3C,CAAC;EACD,MAAMC,IAAI,GAAGJ,KAAK,IAAI;IACpB,MAAMK,KAAK,GAAGL,KAAK,CAACC,OAAO,CAAC,CAAC,CAAC;IAC9B;IACAX,MAAM,CAACM,OAAO,GAAGS,KAAK,CAACH,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGG,KAAK,CAACH,OAAO,GAAGd,MAAM,CAACQ,OAAO;IACvEL,MAAM,CAACK,OAAO,GAAGS,KAAK,CAACF,OAAO,GAAGd,MAAM,CAACO,OAAO;IAC/CJ,OAAO,CAACI,OAAO,GAAGU,IAAI,CAACC,GAAG,CAACjB,MAAM,CAACM,OAAO,CAAC;IAC1CH,OAAO,CAACG,OAAO,GAAGU,IAAI,CAACC,GAAG,CAAChB,MAAM,CAACK,OAAO,CAAC;IAC1C,IAAI,CAACF,SAAS,CAACE,OAAO,EAAE;MACtBF,SAAS,CAACE,OAAO,GAAGZ,YAAY,CAACQ,OAAO,CAACI,OAAO,EAAEH,OAAO,CAACG,OAAO,CAAC;IACpE;EACF,CAAC;EACD,OAAO;IACLQ,IAAI;IACJL,KAAK;IACLD,KAAK;IACLV,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,SAAS;IACTC,UAAU;IACVE;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}