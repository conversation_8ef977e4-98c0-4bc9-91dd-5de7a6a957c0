{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nexport default function useSelections(items, options) {\n  var _a, _b;\n  var defaultSelected = [];\n  var itemKey;\n  if (Array.isArray(options)) {\n    defaultSelected = options;\n  } else if (isPlainObject(options)) {\n    defaultSelected = (_a = options === null || options === void 0 ? void 0 : options.defaultSelected) !== null && _a !== void 0 ? _a : defaultSelected;\n    itemKey = (_b = options === null || options === void 0 ? void 0 : options.itemKey) !== null && _b !== void 0 ? _b : itemKey;\n  }\n  var getKey = function (item) {\n    if (isFunction(itemKey)) {\n      return itemKey(item);\n    }\n    if (isString(itemKey) && isPlainObject(item)) {\n      return item[itemKey];\n    }\n    return item;\n  };\n  var _c = __read(useState(defaultSelected), 2),\n    selected = _c[0],\n    setSelected = _c[1];\n  var selectedMap = useMemo(function () {\n    var keyToItemMap = new Map();\n    if (!Array.isArray(selected)) {\n      return keyToItemMap;\n    }\n    selected.forEach(function (item) {\n      keyToItemMap.set(getKey(item), item);\n    });\n    return keyToItemMap;\n  }, [selected]);\n  var isSelected = function (item) {\n    return selectedMap.has(getKey(item));\n  };\n  var select = function (item) {\n    selectedMap.set(getKey(item), item);\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelect = function (item) {\n    selectedMap.delete(getKey(item));\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var toggle = function (item) {\n    if (isSelected(item)) {\n      unSelect(item);\n    } else {\n      select(item);\n    }\n  };\n  var selectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.set(getKey(item), item);\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.delete(getKey(item));\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var noneSelected = useMemo(function () {\n    return items.every(function (item) {\n      return !selectedMap.has(getKey(item));\n    });\n  }, [items, selectedMap]);\n  var allSelected = useMemo(function () {\n    return items.every(function (item) {\n      return selectedMap.has(getKey(item));\n    }) && !noneSelected;\n  }, [items, selectedMap, noneSelected]);\n  var partiallySelected = useMemo(function () {\n    return !noneSelected && !allSelected;\n  }, [noneSelected, allSelected]);\n  var toggleAll = function () {\n    return allSelected ? unSelectAll() : selectAll();\n  };\n  var clearAll = function () {\n    selectedMap.clear();\n    setSelected([]);\n  };\n  return {\n    selected: selected,\n    noneSelected: noneSelected,\n    allSelected: allSelected,\n    partiallySelected: partiallySelected,\n    setSelected: setSelected,\n    isSelected: isSelected,\n    select: useMemoizedFn(select),\n    unSelect: useMemoizedFn(unSelect),\n    toggle: useMemoizedFn(toggle),\n    selectAll: useMemoizedFn(selectAll),\n    unSelectAll: useMemoizedFn(unSelectAll),\n    clearAll: useMemoizedFn(clearAll),\n    toggleAll: useMemoizedFn(toggleAll)\n  };\n}", "map": {"version": 3, "names": ["__read", "useMemo", "useState", "isPlainObject", "useMemoizedFn", "isFunction", "isString", "useSelections", "items", "options", "_a", "_b", "defaultSelected", "itemKey", "Array", "isArray", "<PERSON><PERSON><PERSON>", "item", "_c", "selected", "setSelected", "selectedMap", "keyToItemMap", "Map", "for<PERSON>ach", "set", "isSelected", "has", "select", "from", "values", "unSelect", "delete", "toggle", "selectAll", "unSelectAll", "noneSelected", "every", "allSelected", "partiallySelected", "toggleAll", "clearAll", "clear"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useSelections/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isFunction, isString } from '../utils';\nexport default function useSelections(items, options) {\n  var _a, _b;\n  var defaultSelected = [];\n  var itemKey;\n  if (Array.isArray(options)) {\n    defaultSelected = options;\n  } else if (isPlainObject(options)) {\n    defaultSelected = (_a = options === null || options === void 0 ? void 0 : options.defaultSelected) !== null && _a !== void 0 ? _a : defaultSelected;\n    itemKey = (_b = options === null || options === void 0 ? void 0 : options.itemKey) !== null && _b !== void 0 ? _b : itemKey;\n  }\n  var getKey = function (item) {\n    if (isFunction(itemKey)) {\n      return itemKey(item);\n    }\n    if (isString(itemKey) && isPlainObject(item)) {\n      return item[itemKey];\n    }\n    return item;\n  };\n  var _c = __read(useState(defaultSelected), 2),\n    selected = _c[0],\n    setSelected = _c[1];\n  var selectedMap = useMemo(function () {\n    var keyToItemMap = new Map();\n    if (!Array.isArray(selected)) {\n      return keyToItemMap;\n    }\n    selected.forEach(function (item) {\n      keyToItemMap.set(getKey(item), item);\n    });\n    return keyToItemMap;\n  }, [selected]);\n  var isSelected = function (item) {\n    return selectedMap.has(getKey(item));\n  };\n  var select = function (item) {\n    selectedMap.set(getKey(item), item);\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelect = function (item) {\n    selectedMap.delete(getKey(item));\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var toggle = function (item) {\n    if (isSelected(item)) {\n      unSelect(item);\n    } else {\n      select(item);\n    }\n  };\n  var selectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.set(getKey(item), item);\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var unSelectAll = function () {\n    items.forEach(function (item) {\n      selectedMap.delete(getKey(item));\n    });\n    setSelected(Array.from(selectedMap.values()));\n  };\n  var noneSelected = useMemo(function () {\n    return items.every(function (item) {\n      return !selectedMap.has(getKey(item));\n    });\n  }, [items, selectedMap]);\n  var allSelected = useMemo(function () {\n    return items.every(function (item) {\n      return selectedMap.has(getKey(item));\n    }) && !noneSelected;\n  }, [items, selectedMap, noneSelected]);\n  var partiallySelected = useMemo(function () {\n    return !noneSelected && !allSelected;\n  }, [noneSelected, allSelected]);\n  var toggleAll = function () {\n    return allSelected ? unSelectAll() : selectAll();\n  };\n  var clearAll = function () {\n    selectedMap.clear();\n    setSelected([]);\n  };\n  return {\n    selected: selected,\n    noneSelected: noneSelected,\n    allSelected: allSelected,\n    partiallySelected: partiallySelected,\n    setSelected: setSelected,\n    isSelected: isSelected,\n    select: useMemoizedFn(select),\n    unSelect: useMemoizedFn(unSelect),\n    toggle: useMemoizedFn(toggle),\n    selectAll: useMemoizedFn(selectAll),\n    unSelectAll: useMemoizedFn(unSelectAll),\n    clearAll: useMemoizedFn(clearAll),\n    toggleAll: useMemoizedFn(toggleAll)\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACzC,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,UAAU,EAAEC,QAAQ,QAAQ,UAAU;AAC/C,eAAe,SAASC,aAAaA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACpD,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,OAAO;EACX,IAAIC,KAAK,CAACC,OAAO,CAACN,OAAO,CAAC,EAAE;IAC1BG,eAAe,GAAGH,OAAO;EAC3B,CAAC,MAAM,IAAIN,aAAa,CAACM,OAAO,CAAC,EAAE;IACjCG,eAAe,GAAG,CAACF,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,eAAe,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,eAAe;IACnJC,OAAO,GAAG,CAACF,EAAE,GAAGF,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACI,OAAO,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGE,OAAO;EAC7H;EACA,IAAIG,MAAM,GAAG,SAAAA,CAAUC,IAAI,EAAE;IAC3B,IAAIZ,UAAU,CAACQ,OAAO,CAAC,EAAE;MACvB,OAAOA,OAAO,CAACI,IAAI,CAAC;IACtB;IACA,IAAIX,QAAQ,CAACO,OAAO,CAAC,IAAIV,aAAa,CAACc,IAAI,CAAC,EAAE;MAC5C,OAAOA,IAAI,CAACJ,OAAO,CAAC;IACtB;IACA,OAAOI,IAAI;EACb,CAAC;EACD,IAAIC,EAAE,GAAGlB,MAAM,CAACE,QAAQ,CAACU,eAAe,CAAC,EAAE,CAAC,CAAC;IAC3CO,QAAQ,GAAGD,EAAE,CAAC,CAAC,CAAC;IAChBE,WAAW,GAAGF,EAAE,CAAC,CAAC,CAAC;EACrB,IAAIG,WAAW,GAAGpB,OAAO,CAAC,YAAY;IACpC,IAAIqB,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC5B,IAAI,CAACT,KAAK,CAACC,OAAO,CAACI,QAAQ,CAAC,EAAE;MAC5B,OAAOG,YAAY;IACrB;IACAH,QAAQ,CAACK,OAAO,CAAC,UAAUP,IAAI,EAAE;MAC/BK,YAAY,CAACG,GAAG,CAACT,MAAM,CAACC,IAAI,CAAC,EAAEA,IAAI,CAAC;IACtC,CAAC,CAAC;IACF,OAAOK,YAAY;EACrB,CAAC,EAAE,CAACH,QAAQ,CAAC,CAAC;EACd,IAAIO,UAAU,GAAG,SAAAA,CAAUT,IAAI,EAAE;IAC/B,OAAOI,WAAW,CAACM,GAAG,CAACX,MAAM,CAACC,IAAI,CAAC,CAAC;EACtC,CAAC;EACD,IAAIW,MAAM,GAAG,SAAAA,CAAUX,IAAI,EAAE;IAC3BI,WAAW,CAACI,GAAG,CAACT,MAAM,CAACC,IAAI,CAAC,EAAEA,IAAI,CAAC;IACnCG,WAAW,CAACN,KAAK,CAACe,IAAI,CAACR,WAAW,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACD,IAAIC,QAAQ,GAAG,SAAAA,CAAUd,IAAI,EAAE;IAC7BI,WAAW,CAACW,MAAM,CAAChB,MAAM,CAACC,IAAI,CAAC,CAAC;IAChCG,WAAW,CAACN,KAAK,CAACe,IAAI,CAACR,WAAW,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACD,IAAIG,MAAM,GAAG,SAAAA,CAAUhB,IAAI,EAAE;IAC3B,IAAIS,UAAU,CAACT,IAAI,CAAC,EAAE;MACpBc,QAAQ,CAACd,IAAI,CAAC;IAChB,CAAC,MAAM;MACLW,MAAM,CAACX,IAAI,CAAC;IACd;EACF,CAAC;EACD,IAAIiB,SAAS,GAAG,SAAAA,CAAA,EAAY;IAC1B1B,KAAK,CAACgB,OAAO,CAAC,UAAUP,IAAI,EAAE;MAC5BI,WAAW,CAACI,GAAG,CAACT,MAAM,CAACC,IAAI,CAAC,EAAEA,IAAI,CAAC;IACrC,CAAC,CAAC;IACFG,WAAW,CAACN,KAAK,CAACe,IAAI,CAACR,WAAW,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACD,IAAIK,WAAW,GAAG,SAAAA,CAAA,EAAY;IAC5B3B,KAAK,CAACgB,OAAO,CAAC,UAAUP,IAAI,EAAE;MAC5BI,WAAW,CAACW,MAAM,CAAChB,MAAM,CAACC,IAAI,CAAC,CAAC;IAClC,CAAC,CAAC;IACFG,WAAW,CAACN,KAAK,CAACe,IAAI,CAACR,WAAW,CAACS,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/C,CAAC;EACD,IAAIM,YAAY,GAAGnC,OAAO,CAAC,YAAY;IACrC,OAAOO,KAAK,CAAC6B,KAAK,CAAC,UAAUpB,IAAI,EAAE;MACjC,OAAO,CAACI,WAAW,CAACM,GAAG,CAACX,MAAM,CAACC,IAAI,CAAC,CAAC;IACvC,CAAC,CAAC;EACJ,CAAC,EAAE,CAACT,KAAK,EAAEa,WAAW,CAAC,CAAC;EACxB,IAAIiB,WAAW,GAAGrC,OAAO,CAAC,YAAY;IACpC,OAAOO,KAAK,CAAC6B,KAAK,CAAC,UAAUpB,IAAI,EAAE;MACjC,OAAOI,WAAW,CAACM,GAAG,CAACX,MAAM,CAACC,IAAI,CAAC,CAAC;IACtC,CAAC,CAAC,IAAI,CAACmB,YAAY;EACrB,CAAC,EAAE,CAAC5B,KAAK,EAAEa,WAAW,EAAEe,YAAY,CAAC,CAAC;EACtC,IAAIG,iBAAiB,GAAGtC,OAAO,CAAC,YAAY;IAC1C,OAAO,CAACmC,YAAY,IAAI,CAACE,WAAW;EACtC,CAAC,EAAE,CAACF,YAAY,EAAEE,WAAW,CAAC,CAAC;EAC/B,IAAIE,SAAS,GAAG,SAAAA,CAAA,EAAY;IAC1B,OAAOF,WAAW,GAAGH,WAAW,CAAC,CAAC,GAAGD,SAAS,CAAC,CAAC;EAClD,CAAC;EACD,IAAIO,QAAQ,GAAG,SAAAA,CAAA,EAAY;IACzBpB,WAAW,CAACqB,KAAK,CAAC,CAAC;IACnBtB,WAAW,CAAC,EAAE,CAAC;EACjB,CAAC;EACD,OAAO;IACLD,QAAQ,EAAEA,QAAQ;IAClBiB,YAAY,EAAEA,YAAY;IAC1BE,WAAW,EAAEA,WAAW;IACxBC,iBAAiB,EAAEA,iBAAiB;IACpCnB,WAAW,EAAEA,WAAW;IACxBM,UAAU,EAAEA,UAAU;IACtBE,MAAM,EAAExB,aAAa,CAACwB,MAAM,CAAC;IAC7BG,QAAQ,EAAE3B,aAAa,CAAC2B,QAAQ,CAAC;IACjCE,MAAM,EAAE7B,aAAa,CAAC6B,MAAM,CAAC;IAC7BC,SAAS,EAAE9B,aAAa,CAAC8B,SAAS,CAAC;IACnCC,WAAW,EAAE/B,aAAa,CAAC+B,WAAW,CAAC;IACvCM,QAAQ,EAAErC,aAAa,CAACqC,QAAQ,CAAC;IACjCD,SAAS,EAAEpC,aAAa,CAACoC,SAAS;EACpC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}