{"ast": null, "code": "/**\n * OCR組件統一導出\n */\n\nexport { default as OCRProcessor } from './OCRProcessor';\nexport { default as OCRStatus } from './OCRStatus';\nexport { default as ParsedFieldsDisplay } from './ParsedFieldsDisplay';\nexport default {\n  OCRProcessor,\n  OCRStatus,\n  ParsedFieldsDisplay\n};", "map": {"version": 3, "names": ["default", "OCRProcessor", "OCRStatus", "ParsedFieldsDisplay"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/OCR/index.js"], "sourcesContent": ["/**\n * OCR組件統一導出\n */\n\nexport { default as OCRProcessor } from './OCRProcessor';\nexport { default as OCRStatus } from './OCRStatus';\nexport { default as ParsedFieldsDisplay } from './ParsedFieldsDisplay';\n\nexport default {\n  OCRProcessor,\n  OCRStatus,\n  ParsedFieldsDisplay\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,SAASD,OAAO,IAAIE,SAAS,QAAQ,aAAa;AAClD,SAASF,OAAO,IAAIG,mBAAmB,QAAQ,uBAAuB;AAEtE,eAAe;EACbF,YAAY;EACZC,SAAS;EACTC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}