{"ast": null, "code": "import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useScroll(target, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = function () {\n      return true;\n    };\n  }\n  var _a = __read(useRafState(), 2),\n    position = _a[0],\n    setPosition = _a[1];\n  var shouldUpdateRef = useLatest(shouldUpdate);\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var updatePosition = function () {\n      var newPosition;\n      if (el === document) {\n        if (document.scrollingElement) {\n          newPosition = {\n            left: document.scrollingElement.scrollLeft,\n            top: document.scrollingElement.scrollTop\n          };\n        } else {\n          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.\n          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement\n          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js\n          newPosition = {\n            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),\n            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)\n          };\n        }\n      } else {\n        newPosition = {\n          left: el.scrollLeft,\n          top: el.scrollTop\n        };\n      }\n      if (shouldUpdateRef.current(newPosition)) {\n        setPosition(newPosition);\n      }\n    };\n    updatePosition();\n    el.addEventListener('scroll', updatePosition);\n    return function () {\n      el.removeEventListener('scroll', updatePosition);\n    };\n  }, [], target);\n  return position;\n}\nexport default useScroll;", "map": {"version": 3, "names": ["__read", "useRafState", "useLatest", "getTargetElement", "useEffectWithTarget", "useScroll", "target", "shouldUpdate", "_a", "position", "setPosition", "shouldUpdateRef", "el", "document", "updatePosition", "newPosition", "scrollingElement", "left", "scrollLeft", "top", "scrollTop", "Math", "max", "window", "pageXOffset", "documentElement", "body", "pageYOffset", "current", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useScroll/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useScroll(target, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = function () {\n      return true;\n    };\n  }\n  var _a = __read(useRafState(), 2),\n    position = _a[0],\n    setPosition = _a[1];\n  var shouldUpdateRef = useLatest(shouldUpdate);\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var updatePosition = function () {\n      var newPosition;\n      if (el === document) {\n        if (document.scrollingElement) {\n          newPosition = {\n            left: document.scrollingElement.scrollLeft,\n            top: document.scrollingElement.scrollTop\n          };\n        } else {\n          // When in quirks mode, the scrollingElement attribute returns the HTML body element if it exists and is potentially scrollable, otherwise it returns null.\n          // https://developer.mozilla.org/zh-CN/docs/Web/API/Document/scrollingElement\n          // https://stackoverflow.com/questions/28633221/document-body-scrolltop-firefox-returns-0-only-js\n          newPosition = {\n            left: Math.max(window.pageXOffset, document.documentElement.scrollLeft, document.body.scrollLeft),\n            top: Math.max(window.pageYOffset, document.documentElement.scrollTop, document.body.scrollTop)\n          };\n        }\n      } else {\n        newPosition = {\n          left: el.scrollLeft,\n          top: el.scrollTop\n        };\n      }\n      if (shouldUpdateRef.current(newPosition)) {\n        setPosition(newPosition);\n      }\n    };\n    updatePosition();\n    el.addEventListener('scroll', updatePosition);\n    return function () {\n      el.removeEventListener('scroll', updatePosition);\n    };\n  }, [], target);\n  return position;\n}\nexport default useScroll;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,SAASC,SAASA,CAACC,MAAM,EAAEC,YAAY,EAAE;EACvC,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,SAAAA,CAAA,EAAY;MACzB,OAAO,IAAI;IACb,CAAC;EACH;EACA,IAAIC,EAAE,GAAGR,MAAM,CAACC,WAAW,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/BQ,QAAQ,GAAGD,EAAE,CAAC,CAAC,CAAC;IAChBE,WAAW,GAAGF,EAAE,CAAC,CAAC,CAAC;EACrB,IAAIG,eAAe,GAAGT,SAAS,CAACK,YAAY,CAAC;EAC7CH,mBAAmB,CAAC,YAAY;IAC9B,IAAIQ,EAAE,GAAGT,gBAAgB,CAACG,MAAM,EAAEO,QAAQ,CAAC;IAC3C,IAAI,CAACD,EAAE,EAAE;MACP;IACF;IACA,IAAIE,cAAc,GAAG,SAAAA,CAAA,EAAY;MAC/B,IAAIC,WAAW;MACf,IAAIH,EAAE,KAAKC,QAAQ,EAAE;QACnB,IAAIA,QAAQ,CAACG,gBAAgB,EAAE;UAC7BD,WAAW,GAAG;YACZE,IAAI,EAAEJ,QAAQ,CAACG,gBAAgB,CAACE,UAAU;YAC1CC,GAAG,EAAEN,QAAQ,CAACG,gBAAgB,CAACI;UACjC,CAAC;QACH,CAAC,MAAM;UACL;UACA;UACA;UACAL,WAAW,GAAG;YACZE,IAAI,EAAEI,IAAI,CAACC,GAAG,CAACC,MAAM,CAACC,WAAW,EAAEX,QAAQ,CAACY,eAAe,CAACP,UAAU,EAAEL,QAAQ,CAACa,IAAI,CAACR,UAAU,CAAC;YACjGC,GAAG,EAAEE,IAAI,CAACC,GAAG,CAACC,MAAM,CAACI,WAAW,EAAEd,QAAQ,CAACY,eAAe,CAACL,SAAS,EAAEP,QAAQ,CAACa,IAAI,CAACN,SAAS;UAC/F,CAAC;QACH;MACF,CAAC,MAAM;QACLL,WAAW,GAAG;UACZE,IAAI,EAAEL,EAAE,CAACM,UAAU;UACnBC,GAAG,EAAEP,EAAE,CAACQ;QACV,CAAC;MACH;MACA,IAAIT,eAAe,CAACiB,OAAO,CAACb,WAAW,CAAC,EAAE;QACxCL,WAAW,CAACK,WAAW,CAAC;MAC1B;IACF,CAAC;IACDD,cAAc,CAAC,CAAC;IAChBF,EAAE,CAACiB,gBAAgB,CAAC,QAAQ,EAAEf,cAAc,CAAC;IAC7C,OAAO,YAAY;MACjBF,EAAE,CAACkB,mBAAmB,CAAC,QAAQ,EAAEhB,cAAc,CAAC;IAClD,CAAC;EACH,CAAC,EAAE,EAAE,EAAER,MAAM,CAAC;EACd,OAAOG,QAAQ;AACjB;AACA,eAAeJ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}