{"ast": null, "code": "/**\n * 統一的API客戶端\n * 提供統一的請求配置、錯誤處理和日誌記錄\n */\n\nimport axios from 'axios';\n\n// 創建API客戶端實例\nconst apiClient = axios.create({\n  baseURL: '/api/v1',\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// 請求攔截器\napiClient.interceptors.request.use(config => {\n  var _config$method;\n  // 添加請求日誌\n  console.log(`🚀 API Request: ${(_config$method = config.method) === null || _config$method === void 0 ? void 0 : _config$method.toUpperCase()} ${config.url}`);\n\n  // 如果是FormData，移除Content-Type讓瀏覽器自動設置\n  if (config.data instanceof FormData) {\n    delete config.headers['Content-Type'];\n  }\n  return config;\n}, error => {\n  console.error('❌ API Request Error:', error);\n  return Promise.reject(error);\n});\n\n// 響應攔截器\napiClient.interceptors.response.use(response => {\n  var _response$config$meth;\n  console.log(`✅ API Response: ${(_response$config$meth = response.config.method) === null || _response$config$meth === void 0 ? void 0 : _response$config$meth.toUpperCase()} ${response.config.url}`, response.data);\n  return response;\n}, error => {\n  var _error$response, _error$response$data, _error$response2, _error$response2$data, _error$response3, _error$response4, _error$response5;\n  console.error('❌ API Response Error:', error);\n\n  // 統一錯誤處理\n  const errorMessage = ((_error$response = error.response) === null || _error$response === void 0 ? void 0 : (_error$response$data = _error$response.data) === null || _error$response$data === void 0 ? void 0 : _error$response$data.detail) || ((_error$response2 = error.response) === null || _error$response2 === void 0 ? void 0 : (_error$response2$data = _error$response2.data) === null || _error$response2$data === void 0 ? void 0 : _error$response2$data.message) || error.message || '網絡請求失敗';\n\n  // 創建統一的錯誤對象\n  const apiError = {\n    message: errorMessage,\n    status: (_error$response3 = error.response) === null || _error$response3 === void 0 ? void 0 : _error$response3.status,\n    statusText: (_error$response4 = error.response) === null || _error$response4 === void 0 ? void 0 : _error$response4.statusText,\n    data: (_error$response5 = error.response) === null || _error$response5 === void 0 ? void 0 : _error$response5.data,\n    originalError: error\n  };\n  return Promise.reject(apiError);\n});\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "apiClient", "create", "baseURL", "timeout", "headers", "interceptors", "request", "use", "config", "_config$method", "console", "log", "method", "toUpperCase", "url", "data", "FormData", "error", "Promise", "reject", "response", "_response$config$meth", "_error$response", "_error$response$data", "_error$response2", "_error$response2$data", "_error$response3", "_error$response4", "_error$response5", "errorMessage", "detail", "message", "apiError", "status", "statusText", "originalError"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/services/api/apiClient.js"], "sourcesContent": ["/**\n * 統一的API客戶端\n * 提供統一的請求配置、錯誤處理和日誌記錄\n */\n\nimport axios from 'axios';\n\n// 創建API客戶端實例\nconst apiClient = axios.create({\n  baseURL: '/api/v1',\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// 請求攔截器\napiClient.interceptors.request.use(\n  (config) => {\n    // 添加請求日誌\n    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);\n    \n    // 如果是FormData，移除Content-Type讓瀏覽器自動設置\n    if (config.data instanceof FormData) {\n      delete config.headers['Content-Type'];\n    }\n    \n    return config;\n  },\n  (error) => {\n    console.error('❌ API Request Error:', error);\n    return Promise.reject(error);\n  }\n);\n\n// 響應攔截器\napiClient.interceptors.response.use(\n  (response) => {\n    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);\n    return response;\n  },\n  (error) => {\n    console.error('❌ API Response Error:', error);\n    \n    // 統一錯誤處理\n    const errorMessage = error.response?.data?.detail || \n                        error.response?.data?.message || \n                        error.message || \n                        '網絡請求失敗';\n    \n    // 創建統一的錯誤對象\n    const apiError = {\n      message: errorMessage,\n      status: error.response?.status,\n      statusText: error.response?.statusText,\n      data: error.response?.data,\n      originalError: error\n    };\n    \n    return Promise.reject(apiError);\n  }\n);\n\nexport default apiClient;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,MAAMC,SAAS,GAAGD,KAAK,CAACE,MAAM,CAAC;EAC7BC,OAAO,EAAE,SAAS;EAClBC,OAAO,EAAE,KAAK;EACdC,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAJ,SAAS,CAACK,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EAAA,IAAAC,cAAA;EACV;EACAC,OAAO,CAACC,GAAG,CAAC,oBAAAF,cAAA,GAAmBD,MAAM,CAACI,MAAM,cAAAH,cAAA,uBAAbA,cAAA,CAAeI,WAAW,CAAC,CAAC,IAAIL,MAAM,CAACM,GAAG,EAAE,CAAC;;EAE5E;EACA,IAAIN,MAAM,CAACO,IAAI,YAAYC,QAAQ,EAAE;IACnC,OAAOR,MAAM,CAACJ,OAAO,CAAC,cAAc,CAAC;EACvC;EAEA,OAAOI,MAAM;AACf,CAAC,EACAS,KAAK,IAAK;EACTP,OAAO,CAACO,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;EAC5C,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAjB,SAAS,CAACK,YAAY,CAACe,QAAQ,CAACb,GAAG,CAChCa,QAAQ,IAAK;EAAA,IAAAC,qBAAA;EACZX,OAAO,CAACC,GAAG,CAAC,oBAAAU,qBAAA,GAAmBD,QAAQ,CAACZ,MAAM,CAACI,MAAM,cAAAS,qBAAA,uBAAtBA,qBAAA,CAAwBR,WAAW,CAAC,CAAC,IAAIO,QAAQ,CAACZ,MAAM,CAACM,GAAG,EAAE,EAAEM,QAAQ,CAACL,IAAI,CAAC;EAC7G,OAAOK,QAAQ;AACjB,CAAC,EACAH,KAAK,IAAK;EAAA,IAAAK,eAAA,EAAAC,oBAAA,EAAAC,gBAAA,EAAAC,qBAAA,EAAAC,gBAAA,EAAAC,gBAAA,EAAAC,gBAAA;EACTlB,OAAO,CAACO,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;;EAE7C;EACA,MAAMY,YAAY,GAAG,EAAAP,eAAA,GAAAL,KAAK,CAACG,QAAQ,cAAAE,eAAA,wBAAAC,oBAAA,GAAdD,eAAA,CAAgBP,IAAI,cAAAQ,oBAAA,uBAApBA,oBAAA,CAAsBO,MAAM,OAAAN,gBAAA,GAC7BP,KAAK,CAACG,QAAQ,cAAAI,gBAAA,wBAAAC,qBAAA,GAAdD,gBAAA,CAAgBT,IAAI,cAAAU,qBAAA,uBAApBA,qBAAA,CAAsBM,OAAO,KAC7Bd,KAAK,CAACc,OAAO,IACb,QAAQ;;EAE5B;EACA,MAAMC,QAAQ,GAAG;IACfD,OAAO,EAAEF,YAAY;IACrBI,MAAM,GAAAP,gBAAA,GAAET,KAAK,CAACG,QAAQ,cAAAM,gBAAA,uBAAdA,gBAAA,CAAgBO,MAAM;IAC9BC,UAAU,GAAAP,gBAAA,GAAEV,KAAK,CAACG,QAAQ,cAAAO,gBAAA,uBAAdA,gBAAA,CAAgBO,UAAU;IACtCnB,IAAI,GAAAa,gBAAA,GAAEX,KAAK,CAACG,QAAQ,cAAAQ,gBAAA,uBAAdA,gBAAA,CAAgBb,IAAI;IAC1BoB,aAAa,EAAElB;EACjB,CAAC;EAED,OAAOC,OAAO,CAACC,MAAM,CAACa,QAAQ,CAAC;AACjC,CACF,CAAC;AAED,eAAehC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}