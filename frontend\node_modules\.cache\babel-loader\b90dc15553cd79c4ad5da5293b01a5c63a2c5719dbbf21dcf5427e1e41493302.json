{"ast": null, "code": "import { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\nimport isDocumentVisible from '../utils/isDocumentVisible';\nimport subscribeReVisible from '../utils/subscribeReVisible';\nvar usePollingPlugin = function (fetchInstance, _a) {\n  var pollingInterval = _a.pollingInterval,\n    _b = _a.pollingWhenHidden,\n    pollingWhenHidden = _b === void 0 ? true : _b,\n    _c = _a.pollingErrorRetryCount,\n    pollingErrorRetryCount = _c === void 0 ? -1 : _c;\n  var timerRef = useRef();\n  var unsubscribeRef = useRef();\n  var countRef = useRef(0);\n  var stopPolling = function () {\n    var _a;\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useUpdateEffect(function () {\n    if (!pollingInterval) {\n      stopPolling();\n    }\n  }, [pollingInterval]);\n  if (!pollingInterval) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      stopPolling();\n    },\n    onError: function () {\n      countRef.current += 1;\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onFinally: function () {\n      if (pollingErrorRetryCount === -1 ||\n      // When an error occurs, the request is not repeated after pollingErrorRetryCount retries\n      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {\n        timerRef.current = setTimeout(function () {\n          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible\n          if (!pollingWhenHidden && !isDocumentVisible()) {\n            unsubscribeRef.current = subscribeReVisible(function () {\n              fetchInstance.refresh();\n            });\n          } else {\n            fetchInstance.refresh();\n          }\n        }, pollingInterval);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      stopPolling();\n    }\n  };\n};\nexport default usePollingPlugin;", "map": {"version": 3, "names": ["useRef", "useUpdateEffect", "isDocumentVisible", "subscribeReVisible", "usePollingPlugin", "fetchInstance", "_a", "pollingInterval", "_b", "pollingWhenHidden", "_c", "pollingErrorRetryCount", "timerRef", "unsubscribeRef", "countRef", "stopPolling", "current", "clearTimeout", "call", "onBefore", "onError", "onSuccess", "onFinally", "setTimeout", "refresh", "onCancel"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/src/plugins/usePollingPlugin.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useUpdateEffect from '../../../useUpdateEffect';\nimport isDocumentVisible from '../utils/isDocumentVisible';\nimport subscribeReVisible from '../utils/subscribeReVisible';\nvar usePollingPlugin = function (fetchInstance, _a) {\n  var pollingInterval = _a.pollingInterval,\n    _b = _a.pollingWhenHidden,\n    pollingWhenHidden = _b === void 0 ? true : _b,\n    _c = _a.pollingErrorRetryCount,\n    pollingErrorRetryCount = _c === void 0 ? -1 : _c;\n  var timerRef = useRef();\n  var unsubscribeRef = useRef();\n  var countRef = useRef(0);\n  var stopPolling = function () {\n    var _a;\n    if (timerRef.current) {\n      clearTimeout(timerRef.current);\n    }\n    (_a = unsubscribeRef.current) === null || _a === void 0 ? void 0 : _a.call(unsubscribeRef);\n  };\n  useUpdateEffect(function () {\n    if (!pollingInterval) {\n      stopPolling();\n    }\n  }, [pollingInterval]);\n  if (!pollingInterval) {\n    return {};\n  }\n  return {\n    onBefore: function () {\n      stopPolling();\n    },\n    onError: function () {\n      countRef.current += 1;\n    },\n    onSuccess: function () {\n      countRef.current = 0;\n    },\n    onFinally: function () {\n      if (pollingErrorRetryCount === -1 ||\n      // When an error occurs, the request is not repeated after pollingErrorRetryCount retries\n      pollingErrorRetryCount !== -1 && countRef.current <= pollingErrorRetryCount) {\n        timerRef.current = setTimeout(function () {\n          // if pollingWhenHidden = false && document is hidden, then stop polling and subscribe revisible\n          if (!pollingWhenHidden && !isDocumentVisible()) {\n            unsubscribeRef.current = subscribeReVisible(function () {\n              fetchInstance.refresh();\n            });\n          } else {\n            fetchInstance.refresh();\n          }\n        }, pollingInterval);\n      } else {\n        countRef.current = 0;\n      }\n    },\n    onCancel: function () {\n      stopPolling();\n    }\n  };\n};\nexport default usePollingPlugin;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,eAAe,MAAM,0BAA0B;AACtD,OAAOC,iBAAiB,MAAM,4BAA4B;AAC1D,OAAOC,kBAAkB,MAAM,6BAA6B;AAC5D,IAAIC,gBAAgB,GAAG,SAAAA,CAAUC,aAAa,EAAEC,EAAE,EAAE;EAClD,IAAIC,eAAe,GAAGD,EAAE,CAACC,eAAe;IACtCC,EAAE,GAAGF,EAAE,CAACG,iBAAiB;IACzBA,iBAAiB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IAC7CE,EAAE,GAAGJ,EAAE,CAACK,sBAAsB;IAC9BA,sBAAsB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,GAAGA,EAAE;EAClD,IAAIE,QAAQ,GAAGZ,MAAM,CAAC,CAAC;EACvB,IAAIa,cAAc,GAAGb,MAAM,CAAC,CAAC;EAC7B,IAAIc,QAAQ,GAAGd,MAAM,CAAC,CAAC,CAAC;EACxB,IAAIe,WAAW,GAAG,SAAAA,CAAA,EAAY;IAC5B,IAAIT,EAAE;IACN,IAAIM,QAAQ,CAACI,OAAO,EAAE;MACpBC,YAAY,CAACL,QAAQ,CAACI,OAAO,CAAC;IAChC;IACA,CAACV,EAAE,GAAGO,cAAc,CAACG,OAAO,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACY,IAAI,CAACL,cAAc,CAAC;EAC5F,CAAC;EACDZ,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACM,eAAe,EAAE;MACpBQ,WAAW,CAAC,CAAC;IACf;EACF,CAAC,EAAE,CAACR,eAAe,CAAC,CAAC;EACrB,IAAI,CAACA,eAAe,EAAE;IACpB,OAAO,CAAC,CAAC;EACX;EACA,OAAO;IACLY,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpBJ,WAAW,CAAC,CAAC;IACf,CAAC;IACDK,OAAO,EAAE,SAAAA,CAAA,EAAY;MACnBN,QAAQ,CAACE,OAAO,IAAI,CAAC;IACvB,CAAC;IACDK,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrBP,QAAQ,CAACE,OAAO,GAAG,CAAC;IACtB,CAAC;IACDM,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAIX,sBAAsB,KAAK,CAAC,CAAC;MACjC;MACAA,sBAAsB,KAAK,CAAC,CAAC,IAAIG,QAAQ,CAACE,OAAO,IAAIL,sBAAsB,EAAE;QAC3EC,QAAQ,CAACI,OAAO,GAAGO,UAAU,CAAC,YAAY;UACxC;UACA,IAAI,CAACd,iBAAiB,IAAI,CAACP,iBAAiB,CAAC,CAAC,EAAE;YAC9CW,cAAc,CAACG,OAAO,GAAGb,kBAAkB,CAAC,YAAY;cACtDE,aAAa,CAACmB,OAAO,CAAC,CAAC;YACzB,CAAC,CAAC;UACJ,CAAC,MAAM;YACLnB,aAAa,CAACmB,OAAO,CAAC,CAAC;UACzB;QACF,CAAC,EAAEjB,eAAe,CAAC;MACrB,CAAC,MAAM;QACLO,QAAQ,CAACE,OAAO,GAAG,CAAC;MACtB;IACF,CAAC;IACDS,QAAQ,EAAE,SAAAA,CAAA,EAAY;MACpBV,WAAW,CAAC,CAAC;IACf;EACF,CAAC;AACH,CAAC;AACD,eAAeX,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}