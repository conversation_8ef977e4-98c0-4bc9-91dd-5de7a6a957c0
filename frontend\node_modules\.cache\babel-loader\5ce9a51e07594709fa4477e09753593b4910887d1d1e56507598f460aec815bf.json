{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\pages\\\\CardEditPage.js\",\n  _s = $RefreshSig$();\n/**\n * 名片編輯頁面 - 統一的新增/編輯頁面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { PageContainer } from '../components/Layout';\nimport { CardForm } from '../components/Form';\nimport { LoadingSpinner, ErrorMessage } from '../components/UI';\nimport { useCardData } from '../hooks';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CardEditPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    id\n  } = useParams();\n  const isEditing = !!id;\n  const {\n    getCard,\n    createCard,\n    updateCard,\n    loading\n  } = useCardData();\n  const [cardData, setCardData] = useState({});\n  const [loadError, setLoadError] = useState(null);\n  const [initialLoading, setInitialLoading] = useState(isEditing);\n\n  // 載入名片數據（編輯模式）\n  useEffect(() => {\n    if (isEditing) {\n      loadCardData();\n    }\n  }, [id, isEditing]);\n  const loadCardData = async () => {\n    try {\n      setInitialLoading(true);\n      setLoadError(null);\n      const data = await getCard(id);\n      setCardData(data);\n    } catch (error) {\n      console.error('載入名片失敗:', error);\n      setLoadError(error.message || '載入名片失敗');\n    } finally {\n      setInitialLoading(false);\n    }\n  };\n\n  // 處理表單提交\n  const handleSubmit = async formData => {\n    try {\n      if (isEditing) {\n        await updateCard(id, formData);\n        navigate(`/cards/${id}`);\n      } else {\n        const newCard = await createCard(formData);\n        navigate(`/cards/${newCard.id}`);\n      }\n    } catch (error) {\n      console.error('保存失敗:', error);\n      throw error;\n    }\n  };\n\n  // 處理取消\n  const handleCancel = () => {\n    if (isEditing) {\n      navigate(`/cards/${id}`);\n    } else {\n      navigate('/cards');\n    }\n  };\n  const pageTitle = isEditing ? '編輯名片' : '新增名片';\n  if (initialLoading) {\n    return /*#__PURE__*/_jsxDEV(PageContainer, {\n      title: pageTitle,\n      children: /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        text: \"\\u8F09\\u5165\\u4E2D...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 79,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 78,\n      columnNumber: 7\n    }, this);\n  }\n  if (loadError) {\n    return /*#__PURE__*/_jsxDEV(PageContainer, {\n      title: pageTitle,\n      children: /*#__PURE__*/_jsxDEV(ErrorMessage, {\n        error: loadError,\n        onRetry: loadCardData\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 87,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 86,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    title: pageTitle,\n    onBack: handleCancel,\n    children: /*#__PURE__*/_jsxDEV(CardForm, {\n      initialData: cardData,\n      onSubmit: handleSubmit,\n      onCancel: handleCancel,\n      loading: loading,\n      submitText: isEditing ? '更新名片' : '創建名片',\n      title: pageTitle,\n      showCancel: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 100,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 96,\n    columnNumber: 5\n  }, this);\n};\n_s(CardEditPage, \"rAqaKULbvEdspQlAPlhU8MOwwFU=\", false, function () {\n  return [useNavigate, useParams, useCardData];\n});\n_c = CardEditPage;\nexport default CardEditPage;\nvar _c;\n$RefreshReg$(_c, \"CardEditPage\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "useParams", "<PERSON><PERSON><PERSON><PERSON>", "CardForm", "LoadingSpinner", "ErrorMessage", "useCardData", "jsxDEV", "_jsxDEV", "CardEditPage", "_s", "navigate", "id", "isEditing", "getCard", "createCard", "updateCard", "loading", "cardData", "setCardData", "loadError", "setLoadError", "initialLoading", "setInitialLoading", "loadCardData", "data", "error", "console", "message", "handleSubmit", "formData", "newCard", "handleCancel", "pageTitle", "title", "children", "text", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onRetry", "onBack", "initialData", "onSubmit", "onCancel", "submitText", "showCancel", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/pages/CardEditPage.js"], "sourcesContent": ["/**\n * 名片編輯頁面 - 統一的新增/編輯頁面\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport { PageContainer } from '../components/Layout';\nimport { CardForm } from '../components/Form';\nimport { LoadingSpinner, ErrorMessage } from '../components/UI';\nimport { useCardData } from '../hooks';\n\nconst CardEditPage = () => {\n  const navigate = useNavigate();\n  const { id } = useParams();\n  const isEditing = !!id;\n  \n  const { \n    getCard, \n    createCard, \n    updateCard, \n    loading \n  } = useCardData();\n  \n  const [cardData, setCardData] = useState({});\n  const [loadError, setLoadError] = useState(null);\n  const [initialLoading, setInitialLoading] = useState(isEditing);\n\n  // 載入名片數據（編輯模式）\n  useEffect(() => {\n    if (isEditing) {\n      loadCardData();\n    }\n  }, [id, isEditing]);\n\n  const loadCardData = async () => {\n    try {\n      setInitialLoading(true);\n      setLoadError(null);\n      const data = await getCard(id);\n      setCardData(data);\n    } catch (error) {\n      console.error('載入名片失敗:', error);\n      setLoadError(error.message || '載入名片失敗');\n    } finally {\n      setInitialLoading(false);\n    }\n  };\n\n  // 處理表單提交\n  const handleSubmit = async (formData) => {\n    try {\n      if (isEditing) {\n        await updateCard(id, formData);\n        navigate(`/cards/${id}`);\n      } else {\n        const newCard = await createCard(formData);\n        navigate(`/cards/${newCard.id}`);\n      }\n    } catch (error) {\n      console.error('保存失敗:', error);\n      throw error;\n    }\n  };\n\n  // 處理取消\n  const handleCancel = () => {\n    if (isEditing) {\n      navigate(`/cards/${id}`);\n    } else {\n      navigate('/cards');\n    }\n  };\n\n  const pageTitle = isEditing ? '編輯名片' : '新增名片';\n\n  if (initialLoading) {\n    return (\n      <PageContainer title={pageTitle}>\n        <LoadingSpinner text=\"載入中...\" />\n      </PageContainer>\n    );\n  }\n\n  if (loadError) {\n    return (\n      <PageContainer title={pageTitle}>\n        <ErrorMessage \n          error={loadError}\n          onRetry={loadCardData}\n        />\n      </PageContainer>\n    );\n  }\n\n  return (\n    <PageContainer\n      title={pageTitle}\n      onBack={handleCancel}\n    >\n      <CardForm\n        initialData={cardData}\n        onSubmit={handleSubmit}\n        onCancel={handleCancel}\n        loading={loading}\n        submitText={isEditing ? '更新名片' : '創建名片'}\n        title={pageTitle}\n        showCancel={true}\n      />\n    </PageContainer>\n  );\n};\n\nexport default CardEditPage;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,EAAEC,SAAS,QAAQ,kBAAkB;AACzD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,cAAc,EAAEC,YAAY,QAAQ,kBAAkB;AAC/D,SAASC,WAAW,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvC,MAAMC,YAAY,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACzB,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEY;EAAG,CAAC,GAAGX,SAAS,CAAC,CAAC;EAC1B,MAAMY,SAAS,GAAG,CAAC,CAACD,EAAE;EAEtB,MAAM;IACJE,OAAO;IACPC,UAAU;IACVC,UAAU;IACVC;EACF,CAAC,GAAGX,WAAW,CAAC,CAAC;EAEjB,MAAM,CAACY,QAAQ,EAAEC,WAAW,CAAC,GAAGrB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5C,MAAM,CAACsB,SAAS,EAAEC,YAAY,CAAC,GAAGvB,QAAQ,CAAC,IAAI,CAAC;EAChD,MAAM,CAACwB,cAAc,EAAEC,iBAAiB,CAAC,GAAGzB,QAAQ,CAACe,SAAS,CAAC;;EAE/D;EACAd,SAAS,CAAC,MAAM;IACd,IAAIc,SAAS,EAAE;MACbW,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAACZ,EAAE,EAAEC,SAAS,CAAC,CAAC;EAEnB,MAAMW,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACFD,iBAAiB,CAAC,IAAI,CAAC;MACvBF,YAAY,CAAC,IAAI,CAAC;MAClB,MAAMI,IAAI,GAAG,MAAMX,OAAO,CAACF,EAAE,CAAC;MAC9BO,WAAW,CAACM,IAAI,CAAC;IACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/BL,YAAY,CAACK,KAAK,CAACE,OAAO,IAAI,QAAQ,CAAC;IACzC,CAAC,SAAS;MACRL,iBAAiB,CAAC,KAAK,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMM,YAAY,GAAG,MAAOC,QAAQ,IAAK;IACvC,IAAI;MACF,IAAIjB,SAAS,EAAE;QACb,MAAMG,UAAU,CAACJ,EAAE,EAAEkB,QAAQ,CAAC;QAC9BnB,QAAQ,CAAC,UAAUC,EAAE,EAAE,CAAC;MAC1B,CAAC,MAAM;QACL,MAAMmB,OAAO,GAAG,MAAMhB,UAAU,CAACe,QAAQ,CAAC;QAC1CnB,QAAQ,CAAC,UAAUoB,OAAO,CAACnB,EAAE,EAAE,CAAC;MAClC;IACF,CAAC,CAAC,OAAOc,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,OAAO,EAAEA,KAAK,CAAC;MAC7B,MAAMA,KAAK;IACb;EACF,CAAC;;EAED;EACA,MAAMM,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAInB,SAAS,EAAE;MACbF,QAAQ,CAAC,UAAUC,EAAE,EAAE,CAAC;IAC1B,CAAC,MAAM;MACLD,QAAQ,CAAC,QAAQ,CAAC;IACpB;EACF,CAAC;EAED,MAAMsB,SAAS,GAAGpB,SAAS,GAAG,MAAM,GAAG,MAAM;EAE7C,IAAIS,cAAc,EAAE;IAClB,oBACEd,OAAA,CAACN,aAAa;MAACgC,KAAK,EAAED,SAAU;MAAAE,QAAA,eAC9B3B,OAAA,CAACJ,cAAc;QAACgC,IAAI,EAAC;MAAQ;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACnB,CAAC;EAEpB;EAEA,IAAIpB,SAAS,EAAE;IACb,oBACEZ,OAAA,CAACN,aAAa;MAACgC,KAAK,EAAED,SAAU;MAAAE,QAAA,eAC9B3B,OAAA,CAACH,YAAY;QACXqB,KAAK,EAAEN,SAAU;QACjBqB,OAAO,EAAEjB;MAAa;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvB;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACW,CAAC;EAEpB;EAEA,oBACEhC,OAAA,CAACN,aAAa;IACZgC,KAAK,EAAED,SAAU;IACjBS,MAAM,EAAEV,YAAa;IAAAG,QAAA,eAErB3B,OAAA,CAACL,QAAQ;MACPwC,WAAW,EAAEzB,QAAS;MACtB0B,QAAQ,EAAEf,YAAa;MACvBgB,QAAQ,EAAEb,YAAa;MACvBf,OAAO,EAAEA,OAAQ;MACjB6B,UAAU,EAAEjC,SAAS,GAAG,MAAM,GAAG,MAAO;MACxCqB,KAAK,EAAED,SAAU;MACjBc,UAAU,EAAE;IAAK;MAAAV,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACW,CAAC;AAEpB,CAAC;AAAC9B,EAAA,CAnGID,YAAY;EAAA,QACCT,WAAW,EACbC,SAAS,EAQpBK,WAAW;AAAA;AAAA0C,EAAA,GAVXvC,YAAY;AAqGlB,eAAeA,YAAY;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}