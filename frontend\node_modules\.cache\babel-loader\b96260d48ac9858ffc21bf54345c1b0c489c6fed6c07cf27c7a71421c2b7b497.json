{"ast": null, "code": "/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign(target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent);\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(/%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g, decodeURIComponent);\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init(converter, defaultAttributes) {\n  function set(name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return;\n    }\n    attributes = assign({}, defaultAttributes, attributes);\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n    name = encodeURIComponent(name).replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent).replace(/[()]/g, escape);\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue;\n      }\n      stringifiedAttributes += '; ' + attributeName;\n      if (attributes[attributeName] === true) {\n        continue;\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n    return document.cookie = name + '=' + converter.write(value, name) + stringifiedAttributes;\n  }\n  function get(name) {\n    if (typeof document === 'undefined' || arguments.length && !name) {\n      return;\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n        if (name === found) {\n          break;\n        }\n      } catch (e) {}\n    }\n    return name ? jar[name] : jar;\n  }\n  return Object.create({\n    set,\n    get,\n    remove: function (name, attributes) {\n      set(name, '', assign({}, attributes, {\n        expires: -1\n      }));\n    },\n    withAttributes: function (attributes) {\n      return init(this.converter, assign({}, this.attributes, attributes));\n    },\n    withConverter: function (converter) {\n      return init(assign({}, this.converter, converter), this.attributes);\n    }\n  }, {\n    attributes: {\n      value: Object.freeze(defaultAttributes)\n    },\n    converter: {\n      value: Object.freeze(converter)\n    }\n  });\n}\nvar api = init(defaultConverter, {\n  path: '/'\n});\n/* eslint-enable no-var */\n\nexport { api as default };", "map": {"version": 3, "names": ["assign", "target", "i", "arguments", "length", "source", "key", "defaultConverter", "read", "value", "slice", "replace", "decodeURIComponent", "write", "encodeURIComponent", "init", "converter", "defaultAttributes", "set", "name", "attributes", "document", "expires", "Date", "now", "toUTCString", "escape", "stringifiedAttributes", "attributeName", "split", "cookie", "get", "cookies", "jar", "parts", "join", "found", "e", "Object", "create", "remove", "withAttributes", "withConverter", "freeze", "api", "path", "default"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/js-cookie/dist/js.cookie.mjs"], "sourcesContent": ["/*! js-cookie v3.0.5 | MIT */\n/* eslint-disable no-var */\nfunction assign (target) {\n  for (var i = 1; i < arguments.length; i++) {\n    var source = arguments[i];\n    for (var key in source) {\n      target[key] = source[key];\n    }\n  }\n  return target\n}\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\nvar defaultConverter = {\n  read: function (value) {\n    if (value[0] === '\"') {\n      value = value.slice(1, -1);\n    }\n    return value.replace(/(%[\\dA-F]{2})+/gi, decodeURIComponent)\n  },\n  write: function (value) {\n    return encodeURIComponent(value).replace(\n      /%(2[346BF]|3[AC-F]|40|5[BDE]|60|7[BCD])/g,\n      decodeURIComponent\n    )\n  }\n};\n/* eslint-enable no-var */\n\n/* eslint-disable no-var */\n\nfunction init (converter, defaultAttributes) {\n  function set (name, value, attributes) {\n    if (typeof document === 'undefined') {\n      return\n    }\n\n    attributes = assign({}, defaultAttributes, attributes);\n\n    if (typeof attributes.expires === 'number') {\n      attributes.expires = new Date(Date.now() + attributes.expires * 864e5);\n    }\n    if (attributes.expires) {\n      attributes.expires = attributes.expires.toUTCString();\n    }\n\n    name = encodeURIComponent(name)\n      .replace(/%(2[346B]|5E|60|7C)/g, decodeURIComponent)\n      .replace(/[()]/g, escape);\n\n    var stringifiedAttributes = '';\n    for (var attributeName in attributes) {\n      if (!attributes[attributeName]) {\n        continue\n      }\n\n      stringifiedAttributes += '; ' + attributeName;\n\n      if (attributes[attributeName] === true) {\n        continue\n      }\n\n      // Considers RFC 6265 section 5.2:\n      // ...\n      // 3.  If the remaining unparsed-attributes contains a %x3B (\";\")\n      //     character:\n      // Consume the characters of the unparsed-attributes up to,\n      // not including, the first %x3B (\";\") character.\n      // ...\n      stringifiedAttributes += '=' + attributes[attributeName].split(';')[0];\n    }\n\n    return (document.cookie =\n      name + '=' + converter.write(value, name) + stringifiedAttributes)\n  }\n\n  function get (name) {\n    if (typeof document === 'undefined' || (arguments.length && !name)) {\n      return\n    }\n\n    // To prevent the for loop in the first place assign an empty array\n    // in case there are no cookies at all.\n    var cookies = document.cookie ? document.cookie.split('; ') : [];\n    var jar = {};\n    for (var i = 0; i < cookies.length; i++) {\n      var parts = cookies[i].split('=');\n      var value = parts.slice(1).join('=');\n\n      try {\n        var found = decodeURIComponent(parts[0]);\n        jar[found] = converter.read(value, found);\n\n        if (name === found) {\n          break\n        }\n      } catch (e) {}\n    }\n\n    return name ? jar[name] : jar\n  }\n\n  return Object.create(\n    {\n      set,\n      get,\n      remove: function (name, attributes) {\n        set(\n          name,\n          '',\n          assign({}, attributes, {\n            expires: -1\n          })\n        );\n      },\n      withAttributes: function (attributes) {\n        return init(this.converter, assign({}, this.attributes, attributes))\n      },\n      withConverter: function (converter) {\n        return init(assign({}, this.converter, converter), this.attributes)\n      }\n    },\n    {\n      attributes: { value: Object.freeze(defaultAttributes) },\n      converter: { value: Object.freeze(converter) }\n    }\n  )\n}\n\nvar api = init(defaultConverter, { path: '/' });\n/* eslint-enable no-var */\n\nexport { api as default };\n"], "mappings": "AAAA;AACA;AACA,SAASA,MAAMA,CAAEC,MAAM,EAAE;EACvB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGC,SAAS,CAACC,MAAM,EAAEF,CAAC,EAAE,EAAE;IACzC,IAAIG,MAAM,GAAGF,SAAS,CAACD,CAAC,CAAC;IACzB,KAAK,IAAII,GAAG,IAAID,MAAM,EAAE;MACtBJ,MAAM,CAACK,GAAG,CAAC,GAAGD,MAAM,CAACC,GAAG,CAAC;IAC3B;EACF;EACA,OAAOL,MAAM;AACf;AACA;;AAEA;AACA,IAAIM,gBAAgB,GAAG;EACrBC,IAAI,EAAE,SAAAA,CAAUC,KAAK,EAAE;IACrB,IAAIA,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACpBA,KAAK,GAAGA,KAAK,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC5B;IACA,OAAOD,KAAK,CAACE,OAAO,CAAC,kBAAkB,EAAEC,kBAAkB,CAAC;EAC9D,CAAC;EACDC,KAAK,EAAE,SAAAA,CAAUJ,KAAK,EAAE;IACtB,OAAOK,kBAAkB,CAACL,KAAK,CAAC,CAACE,OAAO,CACtC,0CAA0C,EAC1CC,kBACF,CAAC;EACH;AACF,CAAC;AACD;;AAEA;;AAEA,SAASG,IAAIA,CAAEC,SAAS,EAAEC,iBAAiB,EAAE;EAC3C,SAASC,GAAGA,CAAEC,IAAI,EAAEV,KAAK,EAAEW,UAAU,EAAE;IACrC,IAAI,OAAOC,QAAQ,KAAK,WAAW,EAAE;MACnC;IACF;IAEAD,UAAU,GAAGpB,MAAM,CAAC,CAAC,CAAC,EAAEiB,iBAAiB,EAAEG,UAAU,CAAC;IAEtD,IAAI,OAAOA,UAAU,CAACE,OAAO,KAAK,QAAQ,EAAE;MAC1CF,UAAU,CAACE,OAAO,GAAG,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGJ,UAAU,CAACE,OAAO,GAAG,KAAK,CAAC;IACxE;IACA,IAAIF,UAAU,CAACE,OAAO,EAAE;MACtBF,UAAU,CAACE,OAAO,GAAGF,UAAU,CAACE,OAAO,CAACG,WAAW,CAAC,CAAC;IACvD;IAEAN,IAAI,GAAGL,kBAAkB,CAACK,IAAI,CAAC,CAC5BR,OAAO,CAAC,sBAAsB,EAAEC,kBAAkB,CAAC,CACnDD,OAAO,CAAC,OAAO,EAAEe,MAAM,CAAC;IAE3B,IAAIC,qBAAqB,GAAG,EAAE;IAC9B,KAAK,IAAIC,aAAa,IAAIR,UAAU,EAAE;MACpC,IAAI,CAACA,UAAU,CAACQ,aAAa,CAAC,EAAE;QAC9B;MACF;MAEAD,qBAAqB,IAAI,IAAI,GAAGC,aAAa;MAE7C,IAAIR,UAAU,CAACQ,aAAa,CAAC,KAAK,IAAI,EAAE;QACtC;MACF;;MAEA;MACA;MACA;MACA;MACA;MACA;MACA;MACAD,qBAAqB,IAAI,GAAG,GAAGP,UAAU,CAACQ,aAAa,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;IACxE;IAEA,OAAQR,QAAQ,CAACS,MAAM,GACrBX,IAAI,GAAG,GAAG,GAAGH,SAAS,CAACH,KAAK,CAACJ,KAAK,EAAEU,IAAI,CAAC,GAAGQ,qBAAqB;EACrE;EAEA,SAASI,GAAGA,CAAEZ,IAAI,EAAE;IAClB,IAAI,OAAOE,QAAQ,KAAK,WAAW,IAAKlB,SAAS,CAACC,MAAM,IAAI,CAACe,IAAK,EAAE;MAClE;IACF;;IAEA;IACA;IACA,IAAIa,OAAO,GAAGX,QAAQ,CAACS,MAAM,GAAGT,QAAQ,CAACS,MAAM,CAACD,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE;IAChE,IAAII,GAAG,GAAG,CAAC,CAAC;IACZ,KAAK,IAAI/B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8B,OAAO,CAAC5B,MAAM,EAAEF,CAAC,EAAE,EAAE;MACvC,IAAIgC,KAAK,GAAGF,OAAO,CAAC9B,CAAC,CAAC,CAAC2B,KAAK,CAAC,GAAG,CAAC;MACjC,IAAIpB,KAAK,GAAGyB,KAAK,CAACxB,KAAK,CAAC,CAAC,CAAC,CAACyB,IAAI,CAAC,GAAG,CAAC;MAEpC,IAAI;QACF,IAAIC,KAAK,GAAGxB,kBAAkB,CAACsB,KAAK,CAAC,CAAC,CAAC,CAAC;QACxCD,GAAG,CAACG,KAAK,CAAC,GAAGpB,SAAS,CAACR,IAAI,CAACC,KAAK,EAAE2B,KAAK,CAAC;QAEzC,IAAIjB,IAAI,KAAKiB,KAAK,EAAE;UAClB;QACF;MACF,CAAC,CAAC,OAAOC,CAAC,EAAE,CAAC;IACf;IAEA,OAAOlB,IAAI,GAAGc,GAAG,CAACd,IAAI,CAAC,GAAGc,GAAG;EAC/B;EAEA,OAAOK,MAAM,CAACC,MAAM,CAClB;IACErB,GAAG;IACHa,GAAG;IACHS,MAAM,EAAE,SAAAA,CAAUrB,IAAI,EAAEC,UAAU,EAAE;MAClCF,GAAG,CACDC,IAAI,EACJ,EAAE,EACFnB,MAAM,CAAC,CAAC,CAAC,EAAEoB,UAAU,EAAE;QACrBE,OAAO,EAAE,CAAC;MACZ,CAAC,CACH,CAAC;IACH,CAAC;IACDmB,cAAc,EAAE,SAAAA,CAAUrB,UAAU,EAAE;MACpC,OAAOL,IAAI,CAAC,IAAI,CAACC,SAAS,EAAEhB,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACoB,UAAU,EAAEA,UAAU,CAAC,CAAC;IACtE,CAAC;IACDsB,aAAa,EAAE,SAAAA,CAAU1B,SAAS,EAAE;MAClC,OAAOD,IAAI,CAACf,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,CAACgB,SAAS,EAAEA,SAAS,CAAC,EAAE,IAAI,CAACI,UAAU,CAAC;IACrE;EACF,CAAC,EACD;IACEA,UAAU,EAAE;MAAEX,KAAK,EAAE6B,MAAM,CAACK,MAAM,CAAC1B,iBAAiB;IAAE,CAAC;IACvDD,SAAS,EAAE;MAAEP,KAAK,EAAE6B,MAAM,CAACK,MAAM,CAAC3B,SAAS;IAAE;EAC/C,CACF,CAAC;AACH;AAEA,IAAI4B,GAAG,GAAG7B,IAAI,CAACR,gBAAgB,EAAE;EAAEsC,IAAI,EAAE;AAAI,CAAC,CAAC;AAC/C;;AAEA,SAASD,GAAG,IAAIE,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}