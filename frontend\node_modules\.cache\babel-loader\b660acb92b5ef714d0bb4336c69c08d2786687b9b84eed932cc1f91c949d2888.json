{"ast": null, "code": "import { useRef } from 'react';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport { depsEqual } from './depsEqual';\nvar useDeepCompareEffectWithTarget = function (effect, deps, target) {\n  var ref = useRef();\n  var signalRef = useRef(0);\n  if (!depsEqual(deps, ref.current)) {\n    signalRef.current += 1;\n  }\n  ref.current = deps;\n  useEffectWithTarget(effect, [signalRef.current], target);\n};\nexport default useDeepCompareEffectWithTarget;", "map": {"version": 3, "names": ["useRef", "useEffectWithTarget", "depsEqual", "useDeepCompareEffectWithTarget", "effect", "deps", "target", "ref", "signalRef", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/useDeepCompareWithTarget.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport { depsEqual } from './depsEqual';\nvar useDeepCompareEffectWithTarget = function (effect, deps, target) {\n  var ref = useRef();\n  var signalRef = useRef(0);\n  if (!depsEqual(deps, ref.current)) {\n    signalRef.current += 1;\n  }\n  ref.current = deps;\n  useEffectWithTarget(effect, [signalRef.current], target);\n};\nexport default useDeepCompareEffectWithTarget;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,SAASC,SAAS,QAAQ,aAAa;AACvC,IAAIC,8BAA8B,GAAG,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE;EACnE,IAAIC,GAAG,GAAGP,MAAM,CAAC,CAAC;EAClB,IAAIQ,SAAS,GAAGR,MAAM,CAAC,CAAC,CAAC;EACzB,IAAI,CAACE,SAAS,CAACG,IAAI,EAAEE,GAAG,CAACE,OAAO,CAAC,EAAE;IACjCD,SAAS,CAACC,OAAO,IAAI,CAAC;EACxB;EACAF,GAAG,CAACE,OAAO,GAAGJ,IAAI;EAClBJ,mBAAmB,CAACG,MAAM,EAAE,CAACI,SAAS,CAACC,OAAO,CAAC,EAAEH,MAAM,CAAC;AAC1D,CAAC;AACD,eAAeH,8BAA8B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}