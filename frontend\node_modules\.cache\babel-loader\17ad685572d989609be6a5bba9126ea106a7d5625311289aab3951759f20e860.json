{"ast": null, "code": "import { createUseGesture, dragAction, pinchAction } from '@use-gesture/react';\nexport const useDragAndPinch = createUseGesture([dragAction, pinchAction]);", "map": {"version": 3, "names": ["createUseGesture", "dragAction", "pinchAction", "useDragAndPinch"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/use-drag-and-pinch.js"], "sourcesContent": ["import { createUseGesture, dragAction, pinchAction } from '@use-gesture/react';\nexport const useDragAndPinch = createUseGesture([dragAction, pinchAction]);"], "mappings": "AAAA,SAASA,gBAAgB,EAAEC,UAAU,EAAEC,WAAW,QAAQ,oBAAoB;AAC9E,OAAO,MAAMC,eAAe,GAAGH,gBAAgB,CAAC,CAACC,UAAU,EAAEC,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}