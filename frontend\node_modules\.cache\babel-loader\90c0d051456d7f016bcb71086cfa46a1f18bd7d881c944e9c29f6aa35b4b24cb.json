{"ast": null, "code": "import { __read } from \"tslib\";\nimport dayjs from 'dayjs';\nimport { useEffect, useMemo, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils/index';\nvar calcLeft = function (target) {\n  if (!target) {\n    return 0;\n  }\n  // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n  var left = dayjs(target).valueOf() - Date.now();\n  return left < 0 ? 0 : left;\n};\nvar parseMs = function (milliseconds) {\n  return {\n    days: Math.floor(milliseconds / 86400000),\n    hours: Math.floor(milliseconds / 3600000) % 24,\n    minutes: Math.floor(milliseconds / 60000) % 60,\n    seconds: Math.floor(milliseconds / 1000) % 60,\n    milliseconds: Math.floor(milliseconds) % 1000\n  };\n};\nvar useCountdown = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options || {},\n    leftTime = _a.leftTime,\n    targetDate = _a.targetDate,\n    _b = _a.interval,\n    interval = _b === void 0 ? 1000 : _b,\n    onEnd = _a.onEnd;\n  var memoLeftTime = useMemo(function () {\n    return isNumber(leftTime) && leftTime > 0 ? Date.now() + leftTime : undefined;\n  }, [leftTime]);\n  var target = 'leftTime' in options ? memoLeftTime : targetDate;\n  var _c = __read(useState(function () {\n      return calcLeft(target);\n    }), 2),\n    timeLeft = _c[0],\n    setTimeLeft = _c[1];\n  var onEndRef = useLatest(onEnd);\n  useEffect(function () {\n    if (!target) {\n      // for stop\n      setTimeLeft(0);\n      return;\n    }\n    // 立即执行一次\n    setTimeLeft(calcLeft(target));\n    var timer = setInterval(function () {\n      var _a;\n      var targetLeft = calcLeft(target);\n      setTimeLeft(targetLeft);\n      if (targetLeft === 0) {\n        clearInterval(timer);\n        (_a = onEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onEndRef);\n      }\n    }, interval);\n    return function () {\n      return clearInterval(timer);\n    };\n  }, [target, interval]);\n  var formattedRes = useMemo(function () {\n    return parseMs(timeLeft);\n  }, [timeLeft]);\n  return [timeLeft, formattedRes];\n};\nexport default useCountdown;", "map": {"version": 3, "names": ["__read", "dayjs", "useEffect", "useMemo", "useState", "useLatest", "isNumber", "calcLeft", "target", "left", "valueOf", "Date", "now", "parseMs", "milliseconds", "days", "Math", "floor", "hours", "minutes", "seconds", "useCountdown", "options", "_a", "leftTime", "targetDate", "_b", "interval", "onEnd", "memoLeftTime", "undefined", "_c", "timeLeft", "setTimeLeft", "onEndRef", "timer", "setInterval", "targetLeft", "clearInterval", "current", "call", "formattedRes"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useCountDown/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport dayjs from 'dayjs';\nimport { useEffect, useMemo, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils/index';\nvar calcLeft = function (target) {\n  if (!target) {\n    return 0;\n  }\n  // https://stackoverflow.com/questions/4310953/invalid-date-in-safari\n  var left = dayjs(target).valueOf() - Date.now();\n  return left < 0 ? 0 : left;\n};\nvar parseMs = function (milliseconds) {\n  return {\n    days: Math.floor(milliseconds / 86400000),\n    hours: Math.floor(milliseconds / 3600000) % 24,\n    minutes: Math.floor(milliseconds / 60000) % 60,\n    seconds: Math.floor(milliseconds / 1000) % 60,\n    milliseconds: Math.floor(milliseconds) % 1000\n  };\n};\nvar useCountdown = function (options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options || {},\n    leftTime = _a.leftTime,\n    targetDate = _a.targetDate,\n    _b = _a.interval,\n    interval = _b === void 0 ? 1000 : _b,\n    onEnd = _a.onEnd;\n  var memoLeftTime = useMemo(function () {\n    return isNumber(leftTime) && leftTime > 0 ? Date.now() + leftTime : undefined;\n  }, [leftTime]);\n  var target = 'leftTime' in options ? memoLeftTime : targetDate;\n  var _c = __read(useState(function () {\n      return calcLeft(target);\n    }), 2),\n    timeLeft = _c[0],\n    setTimeLeft = _c[1];\n  var onEndRef = useLatest(onEnd);\n  useEffect(function () {\n    if (!target) {\n      // for stop\n      setTimeLeft(0);\n      return;\n    }\n    // 立即执行一次\n    setTimeLeft(calcLeft(target));\n    var timer = setInterval(function () {\n      var _a;\n      var targetLeft = calcLeft(target);\n      setTimeLeft(targetLeft);\n      if (targetLeft === 0) {\n        clearInterval(timer);\n        (_a = onEndRef.current) === null || _a === void 0 ? void 0 : _a.call(onEndRef);\n      }\n    }, interval);\n    return function () {\n      return clearInterval(timer);\n    };\n  }, [target, interval]);\n  var formattedRes = useMemo(function () {\n    return parseMs(timeLeft);\n  }, [timeLeft]);\n  return [timeLeft, formattedRes];\n};\nexport default useCountdown;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,KAAK,MAAM,OAAO;AACzB,SAASC,SAAS,EAAEC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACpD,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,QAAQ,QAAQ,gBAAgB;AACzC,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,MAAM,EAAE;EAC/B,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,CAAC;EACV;EACA;EACA,IAAIC,IAAI,GAAGR,KAAK,CAACO,MAAM,CAAC,CAACE,OAAO,CAAC,CAAC,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC;EAC/C,OAAOH,IAAI,GAAG,CAAC,GAAG,CAAC,GAAGA,IAAI;AAC5B,CAAC;AACD,IAAII,OAAO,GAAG,SAAAA,CAAUC,YAAY,EAAE;EACpC,OAAO;IACLC,IAAI,EAAEC,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,QAAQ,CAAC;IACzCI,KAAK,EAAEF,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,OAAO,CAAC,GAAG,EAAE;IAC9CK,OAAO,EAAEH,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,KAAK,CAAC,GAAG,EAAE;IAC9CM,OAAO,EAAEJ,IAAI,CAACC,KAAK,CAACH,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE;IAC7CA,YAAY,EAAEE,IAAI,CAACC,KAAK,CAACH,YAAY,CAAC,GAAG;EAC3C,CAAC;AACH,CAAC;AACD,IAAIO,YAAY,GAAG,SAAAA,CAAUC,OAAO,EAAE;EACpC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,EAAE,GAAGD,OAAO,IAAI,CAAC,CAAC;IACpBE,QAAQ,GAAGD,EAAE,CAACC,QAAQ;IACtBC,UAAU,GAAGF,EAAE,CAACE,UAAU;IAC1BC,EAAE,GAAGH,EAAE,CAACI,QAAQ;IAChBA,QAAQ,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IACpCE,KAAK,GAAGL,EAAE,CAACK,KAAK;EAClB,IAAIC,YAAY,GAAG1B,OAAO,CAAC,YAAY;IACrC,OAAOG,QAAQ,CAACkB,QAAQ,CAAC,IAAIA,QAAQ,GAAG,CAAC,GAAGb,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGY,QAAQ,GAAGM,SAAS;EAC/E,CAAC,EAAE,CAACN,QAAQ,CAAC,CAAC;EACd,IAAIhB,MAAM,GAAG,UAAU,IAAIc,OAAO,GAAGO,YAAY,GAAGJ,UAAU;EAC9D,IAAIM,EAAE,GAAG/B,MAAM,CAACI,QAAQ,CAAC,YAAY;MACjC,OAAOG,QAAQ,CAACC,MAAM,CAAC;IACzB,CAAC,CAAC,EAAE,CAAC,CAAC;IACNwB,QAAQ,GAAGD,EAAE,CAAC,CAAC,CAAC;IAChBE,WAAW,GAAGF,EAAE,CAAC,CAAC,CAAC;EACrB,IAAIG,QAAQ,GAAG7B,SAAS,CAACuB,KAAK,CAAC;EAC/B1B,SAAS,CAAC,YAAY;IACpB,IAAI,CAACM,MAAM,EAAE;MACX;MACAyB,WAAW,CAAC,CAAC,CAAC;MACd;IACF;IACA;IACAA,WAAW,CAAC1B,QAAQ,CAACC,MAAM,CAAC,CAAC;IAC7B,IAAI2B,KAAK,GAAGC,WAAW,CAAC,YAAY;MAClC,IAAIb,EAAE;MACN,IAAIc,UAAU,GAAG9B,QAAQ,CAACC,MAAM,CAAC;MACjCyB,WAAW,CAACI,UAAU,CAAC;MACvB,IAAIA,UAAU,KAAK,CAAC,EAAE;QACpBC,aAAa,CAACH,KAAK,CAAC;QACpB,CAACZ,EAAE,GAAGW,QAAQ,CAACK,OAAO,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiB,IAAI,CAACN,QAAQ,CAAC;MAChF;IACF,CAAC,EAAEP,QAAQ,CAAC;IACZ,OAAO,YAAY;MACjB,OAAOW,aAAa,CAACH,KAAK,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAAC3B,MAAM,EAAEmB,QAAQ,CAAC,CAAC;EACtB,IAAIc,YAAY,GAAGtC,OAAO,CAAC,YAAY;IACrC,OAAOU,OAAO,CAACmB,QAAQ,CAAC;EAC1B,CAAC,EAAE,CAACA,QAAQ,CAAC,CAAC;EACd,OAAO,CAACA,QAAQ,EAAES,YAAY,CAAC;AACjC,CAAC;AACD,eAAepB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}