{"ast": null, "code": "import { useEffect } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useMount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMount: parameter `fn` expected to be a function, but got \\\"\".concat(typeof fn, \"\\\".\"));\n    }\n  }\n  useEffect(function () {\n    fn === null || fn === void 0 ? void 0 : fn();\n  }, []);\n};\nexport default useMount;", "map": {"version": 3, "names": ["useEffect", "isFunction", "isDev", "useMount", "fn", "console", "error", "concat"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useMount/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useMount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useMount: parameter `fn` expected to be a function, but got \\\"\".concat(typeof fn, \"\\\".\"));\n    }\n  }\n  useEffect(function () {\n    fn === null || fn === void 0 ? void 0 : fn();\n  }, []);\n};\nexport default useMount;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,KAAK,MAAM,gBAAgB;AAClC,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC3B,IAAIF,KAAK,EAAE;IACT,IAAI,CAACD,UAAU,CAACG,EAAE,CAAC,EAAE;MACnBC,OAAO,CAACC,KAAK,CAAC,gEAAgE,CAACC,MAAM,CAAC,OAAOH,EAAE,EAAE,KAAK,CAAC,CAAC;IAC1G;EACF;EACAJ,SAAS,CAAC,YAAY;IACpBI,EAAE,KAAK,IAAI,IAAIA,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC;EAC9C,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,eAAeD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}