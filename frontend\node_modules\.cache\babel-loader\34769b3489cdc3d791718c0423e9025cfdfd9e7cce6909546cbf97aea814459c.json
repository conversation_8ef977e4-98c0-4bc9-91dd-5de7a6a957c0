{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useMemo } from 'react';\nimport useToggle from '../useToggle';\nexport default function useBoolean(defaultValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useToggle(!!defaultValue), 2),\n    state = _a[0],\n    _b = _a[1],\n    toggle = _b.toggle,\n    set = _b.set;\n  var actions = useMemo(function () {\n    var setTrue = function () {\n      return set(true);\n    };\n    var setFalse = function () {\n      return set(false);\n    };\n    return {\n      toggle: toggle,\n      set: function (v) {\n        return set(!!v);\n      },\n      setTrue: setTrue,\n      setFalse: setFalse\n    };\n  }, []);\n  return [state, actions];\n}", "map": {"version": 3, "names": ["__read", "useMemo", "useToggle", "useBoolean", "defaultValue", "_a", "state", "_b", "toggle", "set", "actions", "setTrue", "setFalse", "v"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useBoolean/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useMemo } from 'react';\nimport useToggle from '../useToggle';\nexport default function useBoolean(defaultValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useToggle(!!defaultValue), 2),\n    state = _a[0],\n    _b = _a[1],\n    toggle = _b.toggle,\n    set = _b.set;\n  var actions = useMemo(function () {\n    var setTrue = function () {\n      return set(true);\n    };\n    var setFalse = function () {\n      return set(false);\n    };\n    return {\n      toggle: toggle,\n      set: function (v) {\n        return set(!!v);\n      },\n      setTrue: setTrue,\n      setFalse: setFalse\n    };\n  }, []);\n  return [state, actions];\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,SAAS,MAAM,cAAc;AACpC,eAAe,SAASC,UAAUA,CAACC,YAAY,EAAE;EAC/C,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EACA,IAAIC,EAAE,GAAGL,MAAM,CAACE,SAAS,CAAC,CAAC,CAACE,YAAY,CAAC,EAAE,CAAC,CAAC;IAC3CE,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;IACVG,MAAM,GAAGD,EAAE,CAACC,MAAM;IAClBC,GAAG,GAAGF,EAAE,CAACE,GAAG;EACd,IAAIC,OAAO,GAAGT,OAAO,CAAC,YAAY;IAChC,IAAIU,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,OAAOF,GAAG,CAAC,IAAI,CAAC;IAClB,CAAC;IACD,IAAIG,QAAQ,GAAG,SAAAA,CAAA,EAAY;MACzB,OAAOH,GAAG,CAAC,KAAK,CAAC;IACnB,CAAC;IACD,OAAO;MACLD,MAAM,EAAEA,MAAM;MACdC,GAAG,EAAE,SAAAA,CAAUI,CAAC,EAAE;QAChB,OAAOJ,GAAG,CAAC,CAAC,CAACI,CAAC,CAAC;MACjB,CAAC;MACDF,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACN,KAAK,EAAEI,OAAO,CAAC;AACzB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}