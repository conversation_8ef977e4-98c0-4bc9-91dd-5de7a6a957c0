{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nexport default function limit(fn, timespan) {\n  var pending = false;\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (pending) return;\n    pending = true;\n    fn.apply(void 0, __spreadArray([], __read(args), false));\n    setTimeout(function () {\n      pending = false;\n    }, timespan);\n  };\n}", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "limit", "fn", "timespan", "pending", "args", "_i", "arguments", "length", "apply", "setTimeout"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/src/utils/limit.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nexport default function limit(fn, timespan) {\n  var pending = false;\n  return function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    if (pending) return;\n    pending = true;\n    fn.apply(void 0, __spreadArray([], __read(args), false));\n    setTimeout(function () {\n      pending = false;\n    }, timespan);\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,eAAe,SAASC,KAAKA,CAACC,EAAE,EAAEC,QAAQ,EAAE;EAC1C,IAAIC,OAAO,GAAG,KAAK;EACnB,OAAO,YAAY;IACjB,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC1B;IACA,IAAIF,OAAO,EAAE;IACbA,OAAO,GAAG,IAAI;IACdF,EAAE,CAACO,KAAK,CAAC,KAAK,CAAC,EAAET,aAAa,CAAC,EAAE,EAAED,MAAM,CAACM,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACxDK,UAAU,CAAC,YAAY;MACrBN,OAAO,GAAG,KAAK;IACjB,CAAC,EAAED,QAAQ,CAAC;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}