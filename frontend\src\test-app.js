/**
 * 簡單的應用測試腳本
 * 用於驗證重構後的應用是否正常工作
 */

// 測試API服務
import { cardService, ocrService } from './services/api/cardService';
import { validateForm } from './utils/validation';
import { formatFormData } from './utils/formatters';

console.log('🧪 開始測試重構後的OCR應用...');

// 測試1: 驗證API服務導入
console.log('✅ API服務導入成功');
console.log('- cardService:', typeof cardService);
console.log('- ocrService:', typeof ocrService);

// 測試2: 驗證工具函數
console.log('✅ 工具函數導入成功');
console.log('- validateForm:', typeof validateForm);
console.log('- formatFormData:', typeof formatFormData);

// 測試3: 驗證表單驗證
const testFormData = {
  name: 'Test User',
  company_name: 'Test Company',
  email: '<EMAIL>',
  mobile_phone: '0912345678'
};

const validation = validateForm(testFormData);
console.log('✅ 表單驗證測試:', validation.isValid ? '通過' : '失敗');

// 測試4: 驗證數據格式化
const formatted = formatFormData(testFormData);
console.log('✅ 數據格式化測試:', formatted);

console.log('🎉 所有測試完成！');

export default {
  cardService,
  ocrService,
  validateForm,
  formatFormData
};
