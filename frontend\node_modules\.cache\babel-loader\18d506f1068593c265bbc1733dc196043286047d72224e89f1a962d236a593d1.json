{"ast": null, "code": "var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default isBrowser;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "window", "document", "createElement"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/isBrowser.js"], "sourcesContent": ["var isBrowser = !!(typeof window !== 'undefined' && window.document && window.document.createElement);\nexport default isBrowser;"], "mappings": "AAAA,IAAIA,SAAS,GAAG,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC;AACrG,eAAeH,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}