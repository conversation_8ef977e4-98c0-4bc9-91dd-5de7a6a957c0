{"ast": null, "code": "export var isObject = function (value) {\n  return value !== null && typeof value === 'object';\n};\nexport var isFunction = function (value) {\n  return typeof value === 'function';\n};\nexport var isString = function (value) {\n  return typeof value === 'string';\n};\nexport var isBoolean = function (value) {\n  return typeof value === 'boolean';\n};\nexport var isNumber = function (value) {\n  return typeof value === 'number';\n};\nexport var isUndef = function (value) {\n  return typeof value === 'undefined';\n};", "map": {"version": 3, "names": ["isObject", "value", "isFunction", "isString", "isBoolean", "isNumber", "isUndef"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/index.js"], "sourcesContent": ["export var isObject = function (value) {\n  return value !== null && typeof value === 'object';\n};\nexport var isFunction = function (value) {\n  return typeof value === 'function';\n};\nexport var isString = function (value) {\n  return typeof value === 'string';\n};\nexport var isBoolean = function (value) {\n  return typeof value === 'boolean';\n};\nexport var isNumber = function (value) {\n  return typeof value === 'number';\n};\nexport var isUndef = function (value) {\n  return typeof value === 'undefined';\n};"], "mappings": "AAAA,OAAO,IAAIA,QAAQ,GAAG,SAAAA,CAAUC,KAAK,EAAE;EACrC,OAAOA,KAAK,KAAK,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ;AACpD,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,SAAAA,CAAUD,KAAK,EAAE;EACvC,OAAO,OAAOA,KAAK,KAAK,UAAU;AACpC,CAAC;AACD,OAAO,IAAIE,QAAQ,GAAG,SAAAA,CAAUF,KAAK,EAAE;EACrC,OAAO,OAAOA,KAAK,KAAK,QAAQ;AAClC,CAAC;AACD,OAAO,IAAIG,SAAS,GAAG,SAAAA,CAAUH,KAAK,EAAE;EACtC,OAAO,OAAOA,KAAK,KAAK,SAAS;AACnC,CAAC;AACD,OAAO,IAAII,QAAQ,GAAG,SAAAA,CAAUJ,KAAK,EAAE;EACrC,OAAO,OAAOA,KAAK,KAAK,QAAQ;AAClC,CAAC;AACD,OAAO,IAAIK,OAAO,GAAG,SAAAA,CAAUL,KAAK,EAAE;EACpC,OAAO,OAAOA,KAAK,KAAK,WAAW;AACrC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}