{"ast": null, "code": "import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useLocalStorageState = createUseStorageState(function () {\n  return isBrowser ? localStorage : undefined;\n});\nexport default useLocalStorageState;", "map": {"version": 3, "names": ["createUseStorageState", "<PERSON><PERSON><PERSON><PERSON>", "useLocalStorageState", "localStorage", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useLocalStorageState/index.js"], "sourcesContent": ["import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useLocalStorageState = createUseStorageState(function () {\n  return isBrowser ? localStorage : undefined;\n});\nexport default useLocalStorageState;"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,0BAA0B;AAChE,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,oBAAoB,GAAGF,qBAAqB,CAAC,YAAY;EAC3D,OAAOC,SAAS,GAAGE,YAAY,GAAGC,SAAS;AAC7C,CAAC,CAAC;AACF,eAAeF,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}