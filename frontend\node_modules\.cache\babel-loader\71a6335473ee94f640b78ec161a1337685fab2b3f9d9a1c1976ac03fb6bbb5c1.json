{"ast": null, "code": "var raf = function raf(callback) {\n  return +setTimeout(callback, 16);\n};\nvar caf = function caf(num) {\n  return clearTimeout(num);\n};\nif (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {\n  raf = function raf(callback) {\n    return window.requestAnimationFrame(callback);\n  };\n  caf = function caf(handle) {\n    return window.cancelAnimationFrame(handle);\n  };\n}\nvar rafUUID = 0;\nvar rafIds = new Map();\nfunction cleanup(id) {\n  rafIds.delete(id);\n}\nvar wrapperRaf = function wrapperRaf(callback) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  rafUUID += 1;\n  var id = rafUUID;\n  function callRef(leftTimes) {\n    if (leftTimes === 0) {\n      // Clean up\n      cleanup(id);\n\n      // Trigger\n      callback();\n    } else {\n      // Next raf\n      var realId = raf(function () {\n        callRef(leftTimes - 1);\n      });\n\n      // Bind real raf id\n      rafIds.set(id, realId);\n    }\n  }\n  callRef(times);\n  return id;\n};\nwrapperRaf.cancel = function (id) {\n  var realId = rafIds.get(id);\n  cleanup(id);\n  return caf(realId);\n};\nif (process.env.NODE_ENV !== 'production') {\n  wrapperRaf.ids = function () {\n    return rafIds;\n  };\n}\nexport default wrapperRaf;", "map": {"version": 3, "names": ["raf", "callback", "setTimeout", "caf", "num", "clearTimeout", "window", "requestAnimationFrame", "handle", "cancelAnimationFrame", "rafUUID", "rafIds", "Map", "cleanup", "id", "delete", "wrapperRaf", "times", "arguments", "length", "undefined", "callRef", "leftTimes", "realId", "set", "cancel", "get", "process", "env", "NODE_ENV", "ids"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-util/es/raf.js"], "sourcesContent": ["var raf = function raf(callback) {\n  return +setTimeout(callback, 16);\n};\nvar caf = function caf(num) {\n  return clearTimeout(num);\n};\nif (typeof window !== 'undefined' && 'requestAnimationFrame' in window) {\n  raf = function raf(callback) {\n    return window.requestAnimationFrame(callback);\n  };\n  caf = function caf(handle) {\n    return window.cancelAnimationFrame(handle);\n  };\n}\nvar rafUUID = 0;\nvar rafIds = new Map();\nfunction cleanup(id) {\n  rafIds.delete(id);\n}\nvar wrapperRaf = function wrapperRaf(callback) {\n  var times = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 1;\n  rafUUID += 1;\n  var id = rafUUID;\n  function callRef(leftTimes) {\n    if (leftTimes === 0) {\n      // Clean up\n      cleanup(id);\n\n      // Trigger\n      callback();\n    } else {\n      // Next raf\n      var realId = raf(function () {\n        callRef(leftTimes - 1);\n      });\n\n      // Bind real raf id\n      rafIds.set(id, realId);\n    }\n  }\n  callRef(times);\n  return id;\n};\nwrapperRaf.cancel = function (id) {\n  var realId = rafIds.get(id);\n  cleanup(id);\n  return caf(realId);\n};\nif (process.env.NODE_ENV !== 'production') {\n  wrapperRaf.ids = function () {\n    return rafIds;\n  };\n}\nexport default wrapperRaf;"], "mappings": "AAAA,IAAIA,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;EAC/B,OAAO,CAACC,UAAU,CAACD,QAAQ,EAAE,EAAE,CAAC;AAClC,CAAC;AACD,IAAIE,GAAG,GAAG,SAASA,GAAGA,CAACC,GAAG,EAAE;EAC1B,OAAOC,YAAY,CAACD,GAAG,CAAC;AAC1B,CAAC;AACD,IAAI,OAAOE,MAAM,KAAK,WAAW,IAAI,uBAAuB,IAAIA,MAAM,EAAE;EACtEN,GAAG,GAAG,SAASA,GAAGA,CAACC,QAAQ,EAAE;IAC3B,OAAOK,MAAM,CAACC,qBAAqB,CAACN,QAAQ,CAAC;EAC/C,CAAC;EACDE,GAAG,GAAG,SAASA,GAAGA,CAACK,MAAM,EAAE;IACzB,OAAOF,MAAM,CAACG,oBAAoB,CAACD,MAAM,CAAC;EAC5C,CAAC;AACH;AACA,IAAIE,OAAO,GAAG,CAAC;AACf,IAAIC,MAAM,GAAG,IAAIC,GAAG,CAAC,CAAC;AACtB,SAASC,OAAOA,CAACC,EAAE,EAAE;EACnBH,MAAM,CAACI,MAAM,CAACD,EAAE,CAAC;AACnB;AACA,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAACf,QAAQ,EAAE;EAC7C,IAAIgB,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;EACjFR,OAAO,IAAI,CAAC;EACZ,IAAII,EAAE,GAAGJ,OAAO;EAChB,SAASW,OAAOA,CAACC,SAAS,EAAE;IAC1B,IAAIA,SAAS,KAAK,CAAC,EAAE;MACnB;MACAT,OAAO,CAACC,EAAE,CAAC;;MAEX;MACAb,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA,IAAIsB,MAAM,GAAGvB,GAAG,CAAC,YAAY;QAC3BqB,OAAO,CAACC,SAAS,GAAG,CAAC,CAAC;MACxB,CAAC,CAAC;;MAEF;MACAX,MAAM,CAACa,GAAG,CAACV,EAAE,EAAES,MAAM,CAAC;IACxB;EACF;EACAF,OAAO,CAACJ,KAAK,CAAC;EACd,OAAOH,EAAE;AACX,CAAC;AACDE,UAAU,CAACS,MAAM,GAAG,UAAUX,EAAE,EAAE;EAChC,IAAIS,MAAM,GAAGZ,MAAM,CAACe,GAAG,CAACZ,EAAE,CAAC;EAC3BD,OAAO,CAACC,EAAE,CAAC;EACX,OAAOX,GAAG,CAACoB,MAAM,CAAC;AACpB,CAAC;AACD,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCb,UAAU,CAACc,GAAG,GAAG,YAAY;IAC3B,OAAOnB,MAAM;EACf,CAAC;AACH;AACA,eAAeK,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}