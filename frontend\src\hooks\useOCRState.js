/**
 * OCR狀態管理Hook
 * 處理OCR識別和智能解析功能
 */

import { useState, useCallback } from 'react';
import { Toast } from 'antd-mobile';
import { ocrService } from '../services/api/ocrService';
import { OCR_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';

export const useOCRState = () => {
  const [frontOCR, setFrontOCR] = useState({
    text: '',
    parsedFields: {},
    status: OCR_STATUS.IDLE,
    error: null
  });
  
  const [backOCR, setBackOCR] = useState({
    text: '',
    parsedFields: {},
    status: OCR_STATUS.IDLE,
    error: null
  });

  /**
   * 更新OCR狀態
   */
  const updateOCRState = useCallback((side, updates) => {
    const setter = side === 'front' ? setFrontOCR : setBackOCR;
    setter(prev => ({ ...prev, ...updates }));
  }, []);

  /**
   * 處理圖片OCR識別
   */
  const processImage = useCallback(async (imageFile, side = 'front') => {
    console.log(`🔍 開始OCR處理 - ${side}面:`, imageFile.name);
    
    updateOCRState(side, {
      status: OCR_STATUS.PROCESSING,
      error: null
    });

    try {
      const result = await ocrService.processImage(imageFile);
      
      if (result.success && result.text) {
        updateOCRState(side, {
          text: result.text,
          status: OCR_STATUS.SUCCESS,
          error: null
        });
        
        console.log(`✅ OCR識別成功 - ${side}面:`, result.text);
        
        // 自動進行智能解析
        await parseFields(result.text, side);
        
        return result.text;
      } else {
        throw new Error(result.message || 'OCR識別失敗');
      }
    } catch (error) {
      console.error(`❌ OCR識別失敗 - ${side}面:`, error);
      
      updateOCRState(side, {
        status: OCR_STATUS.ERROR,
        error: error.message || ERROR_MESSAGES.OCR_FAILED
      });
      
      Toast.show({
        content: error.message || ERROR_MESSAGES.OCR_FAILED,
        position: 'center',
      });
      
      throw error;
    }
  }, [updateOCRState]);

  /**
   * 智能解析OCR文字到標準化欄位
   */
  const parseFields = useCallback(async (ocrText, side = 'front') => {
    if (!ocrText || !ocrText.trim()) {
      console.warn('⚠️ OCR文字為空，跳過解析');
      return {};
    }

    console.log(`🧠 開始智能解析 - ${side}面:`, ocrText.substring(0, 100) + '...');

    try {
      const result = await ocrService.parseFields(ocrText, side);
      
      if (result.success && result.parsed_fields) {
        updateOCRState(side, {
          parsedFields: result.parsed_fields
        });
        
        console.log(`✅ 智能解析成功 - ${side}面:`, result.parsed_fields);
        
        Toast.show({
          content: `${side === 'front' ? '正面' : '反面'}${SUCCESS_MESSAGES.OCR_SUCCESS}`,
          position: 'center',
        });
        
        return result.parsed_fields;
      } else {
        console.warn('⚠️ 智能解析無結果');
        return {};
      }
    } catch (error) {
      console.error(`❌ 智能解析失敗 - ${side}面:`, error);
      
      // 解析失敗不影響OCR結果，只記錄錯誤
      updateOCRState(side, {
        parsedFields: {}
      });
      
      return {};
    }
  }, [updateOCRState]);

  /**
   * 重新處理OCR
   */
  const retryOCR = useCallback(async (imageFile, side = 'front') => {
    console.log(`🔄 重新處理OCR - ${side}面`);
    return await processImage(imageFile, side);
  }, [processImage]);

  /**
   * 清空OCR結果
   */
  const clearOCR = useCallback((side = 'both') => {
    const emptyState = {
      text: '',
      parsedFields: {},
      status: OCR_STATUS.IDLE,
      error: null
    };

    if (side === 'both') {
      setFrontOCR(emptyState);
      setBackOCR(emptyState);
    } else if (side === 'front') {
      setFrontOCR(emptyState);
    } else if (side === 'back') {
      setBackOCR(emptyState);
    }
    
    console.log(`🧹 清空OCR結果 - ${side}`);
  }, []);

  /**
   * 合併正反面解析結果
   */
  const getMergedFields = useCallback(() => {
    const merged = { ...frontOCR.parsedFields };
    
    // 反面的欄位可以補充或覆蓋正面的欄位
    Object.keys(backOCR.parsedFields).forEach(key => {
      const backValue = backOCR.parsedFields[key];
      const frontValue = merged[key];
      
      // 如果正面沒有該欄位，或者反面的值更完整，則使用反面的值
      if (!frontValue || (backValue && backValue.length > frontValue.length)) {
        merged[key] = backValue;
      }
    });
    
    return merged;
  }, [frontOCR.parsedFields, backOCR.parsedFields]);

  /**
   * 獲取OCR處理狀態
   */
  const getOCRStatus = useCallback((side = 'both') => {
    if (side === 'front') {
      return frontOCR.status;
    } else if (side === 'back') {
      return backOCR.status;
    } else {
      // 返回整體狀態
      if (frontOCR.status === OCR_STATUS.PROCESSING || backOCR.status === OCR_STATUS.PROCESSING) {
        return OCR_STATUS.PROCESSING;
      } else if (frontOCR.status === OCR_STATUS.ERROR || backOCR.status === OCR_STATUS.ERROR) {
        return OCR_STATUS.ERROR;
      } else if (frontOCR.status === OCR_STATUS.SUCCESS || backOCR.status === OCR_STATUS.SUCCESS) {
        return OCR_STATUS.SUCCESS;
      } else {
        return OCR_STATUS.IDLE;
      }
    }
  }, [frontOCR.status, backOCR.status]);

  /**
   * 檢查是否有OCR結果
   */
  const hasOCRResults = useCallback((side = 'both') => {
    if (side === 'front') {
      return !!frontOCR.text;
    } else if (side === 'back') {
      return !!backOCR.text;
    } else {
      return !!(frontOCR.text || backOCR.text);
    }
  }, [frontOCR.text, backOCR.text]);

  /**
   * 檢查是否有解析結果
   */
  const hasParsedFields = useCallback((side = 'both') => {
    if (side === 'front') {
      return Object.keys(frontOCR.parsedFields).length > 0;
    } else if (side === 'back') {
      return Object.keys(backOCR.parsedFields).length > 0;
    } else {
      return Object.keys(frontOCR.parsedFields).length > 0 || 
             Object.keys(backOCR.parsedFields).length > 0;
    }
  }, [frontOCR.parsedFields, backOCR.parsedFields]);

  return {
    // OCR狀態
    frontOCR,
    backOCR,
    
    // 操作方法
    processImage,
    parseFields,
    retryOCR,
    clearOCR,
    
    // 工具方法
    getMergedFields,
    getOCRStatus,
    hasOCRResults,
    hasParsedFields,
    
    // 便捷屬性
    isProcessing: getOCRStatus() === OCR_STATUS.PROCESSING,
    hasError: getOCRStatus() === OCR_STATUS.ERROR,
    hasResults: hasOCRResults(),
    mergedFields: getMergedFields()
  };
};

export default useOCRState;
