/**
 * 錯誤消息組件
 */

import React from 'react';
import { Card, Button } from 'antd-mobile';
import { ExclamationCircleOutline, ReloadOutline } from 'antd-mobile-icons';

const ErrorMessage = ({ 
  error, 
  onRetry, 
  title = '發生錯誤',
  showRetry = true,
  style = {},
  className = '' 
}) => {
  const errorMessage = typeof error === 'string' ? error : error?.message || '未知錯誤';

  return (
    <Card 
      className={`error-message ${className}`}
      style={{
        margin: '16px',
        textAlign: 'center',
        ...style
      }}
    >
      <div style={{ padding: '20px' }}>
        <ExclamationCircleOutline 
          style={{ 
            fontSize: '48px', 
            color: '#ff4d4f',
            marginBottom: '16px'
          }} 
        />
        
        <div 
          style={{
            fontSize: '16px',
            fontWeight: 'bold',
            color: '#262626',
            marginBottom: '8px'
          }}
        >
          {title}
        </div>
        
        <div 
          style={{
            fontSize: '14px',
            color: '#8c8c8c',
            marginBottom: showRetry ? '20px' : '0',
            lineHeight: '1.5'
          }}
        >
          {errorMessage}
        </div>
        
        {showRetry && onRetry && (
          <Button 
            color="primary" 
            fill="outline"
            onClick={onRetry}
            style={{ minWidth: '100px' }}
          >
            <ReloadOutline /> 重試
          </Button>
        )}
      </div>
    </Card>
  );
};

export default ErrorMessage;
