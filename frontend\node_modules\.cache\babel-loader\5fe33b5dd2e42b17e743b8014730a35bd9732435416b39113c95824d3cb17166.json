{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar dumpIndex = function (step, arr) {\n  var index = step > 0 ? step - 1 // move forward\n  : arr.length + step; // move backward\n  if (index >= arr.length - 1) {\n    index = arr.length - 1;\n  }\n  if (index < 0) {\n    index = 0;\n  }\n  return index;\n};\nvar split = function (step, targetArr) {\n  var index = dumpIndex(step, targetArr);\n  return {\n    _current: targetArr[index],\n    _before: targetArr.slice(0, index),\n    _after: targetArr.slice(index + 1)\n  };\n};\nexport default function useHistoryTravel(initialValue, maxLength) {\n  if (maxLength === void 0) {\n    maxLength = 0;\n  }\n  var _a = __read(useState({\n      present: initialValue,\n      past: [],\n      future: []\n    }), 2),\n    history = _a[0],\n    setHistory = _a[1];\n  var present = history.present,\n    past = history.past,\n    future = history.future;\n  var initialValueRef = useRef(initialValue);\n  var reset = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    var _initial = params.length > 0 ? params[0] : initialValueRef.current;\n    initialValueRef.current = _initial;\n    setHistory({\n      present: _initial,\n      future: [],\n      past: []\n    });\n  };\n  var updateValue = function (val) {\n    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);\n    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength);\n    // maximum number of records exceeded\n    if (maxLengthNum > 0 && _past.length > maxLengthNum) {\n      //delete first\n      _past.splice(0, 1);\n    }\n    setHistory({\n      present: val,\n      future: [],\n      past: _past\n    });\n  };\n  var _forward = function (step) {\n    if (step === void 0) {\n      step = 1;\n    }\n    if (future.length === 0) {\n      return;\n    }\n    var _a = split(step, future),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),\n      present: _current,\n      future: _after\n    });\n  };\n  var _backward = function (step) {\n    if (step === void 0) {\n      step = -1;\n    }\n    if (past.length === 0) {\n      return;\n    }\n    var _a = split(step, past),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: _before,\n      present: _current,\n      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)\n    });\n  };\n  var go = function (step) {\n    var stepNum = isNumber(step) ? step : Number(step);\n    if (stepNum === 0) {\n      return;\n    }\n    if (stepNum > 0) {\n      return _forward(stepNum);\n    }\n    _backward(stepNum);\n  };\n  return {\n    value: present,\n    backLength: past.length,\n    forwardLength: future.length,\n    setValue: useMemoizedFn(updateValue),\n    go: useMemoizedFn(go),\n    back: useMemoizedFn(function () {\n      go(-1);\n    }),\n    forward: useMemoizedFn(function () {\n      go(1);\n    }),\n    reset: useMemoizedFn(reset)\n  };\n}", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "useRef", "useState", "useMemoizedFn", "isNumber", "dumpIndex", "step", "arr", "index", "length", "split", "targetArr", "_current", "_before", "slice", "_after", "useHistoryTravel", "initialValue", "max<PERSON><PERSON><PERSON>", "_a", "present", "past", "future", "history", "setHistory", "initialValueRef", "reset", "params", "_i", "arguments", "_initial", "current", "updateValue", "val", "_past", "maxLength<PERSON>um", "Number", "splice", "_forward", "_backward", "go", "<PERSON><PERSON><PERSON>", "value", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "setValue", "back", "forward"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useHistoryTravel/index.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { isNumber } from '../utils';\nvar dumpIndex = function (step, arr) {\n  var index = step > 0 ? step - 1 // move forward\n  : arr.length + step; // move backward\n  if (index >= arr.length - 1) {\n    index = arr.length - 1;\n  }\n  if (index < 0) {\n    index = 0;\n  }\n  return index;\n};\nvar split = function (step, targetArr) {\n  var index = dumpIndex(step, targetArr);\n  return {\n    _current: targetArr[index],\n    _before: targetArr.slice(0, index),\n    _after: targetArr.slice(index + 1)\n  };\n};\nexport default function useHistoryTravel(initialValue, maxLength) {\n  if (maxLength === void 0) {\n    maxLength = 0;\n  }\n  var _a = __read(useState({\n      present: initialValue,\n      past: [],\n      future: []\n    }), 2),\n    history = _a[0],\n    setHistory = _a[1];\n  var present = history.present,\n    past = history.past,\n    future = history.future;\n  var initialValueRef = useRef(initialValue);\n  var reset = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    var _initial = params.length > 0 ? params[0] : initialValueRef.current;\n    initialValueRef.current = _initial;\n    setHistory({\n      present: _initial,\n      future: [],\n      past: []\n    });\n  };\n  var updateValue = function (val) {\n    var _past = __spreadArray(__spreadArray([], __read(past), false), [present], false);\n    var maxLengthNum = isNumber(maxLength) ? maxLength : Number(maxLength);\n    // maximum number of records exceeded\n    if (maxLengthNum > 0 && _past.length > maxLengthNum) {\n      //delete first\n      _past.splice(0, 1);\n    }\n    setHistory({\n      present: val,\n      future: [],\n      past: _past\n    });\n  };\n  var _forward = function (step) {\n    if (step === void 0) {\n      step = 1;\n    }\n    if (future.length === 0) {\n      return;\n    }\n    var _a = split(step, future),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: __spreadArray(__spreadArray(__spreadArray([], __read(past), false), [present], false), __read(_before), false),\n      present: _current,\n      future: _after\n    });\n  };\n  var _backward = function (step) {\n    if (step === void 0) {\n      step = -1;\n    }\n    if (past.length === 0) {\n      return;\n    }\n    var _a = split(step, past),\n      _before = _a._before,\n      _current = _a._current,\n      _after = _a._after;\n    setHistory({\n      past: _before,\n      present: _current,\n      future: __spreadArray(__spreadArray(__spreadArray([], __read(_after), false), [present], false), __read(future), false)\n    });\n  };\n  var go = function (step) {\n    var stepNum = isNumber(step) ? step : Number(step);\n    if (stepNum === 0) {\n      return;\n    }\n    if (stepNum > 0) {\n      return _forward(stepNum);\n    }\n    _backward(stepNum);\n  };\n  return {\n    value: present,\n    backLength: past.length,\n    forwardLength: future.length,\n    setValue: useMemoizedFn(updateValue),\n    go: useMemoizedFn(go),\n    back: useMemoizedFn(function () {\n      go(-1);\n    }),\n    forward: useMemoizedFn(function () {\n      go(1);\n    }),\n    reset: useMemoizedFn(reset)\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,QAAQ,QAAQ,UAAU;AACnC,IAAIC,SAAS,GAAG,SAAAA,CAAUC,IAAI,EAAEC,GAAG,EAAE;EACnC,IAAIC,KAAK,GAAGF,IAAI,GAAG,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;EAAA,EAC9BC,GAAG,CAACE,MAAM,GAAGH,IAAI,CAAC,CAAC;EACrB,IAAIE,KAAK,IAAID,GAAG,CAACE,MAAM,GAAG,CAAC,EAAE;IAC3BD,KAAK,GAAGD,GAAG,CAACE,MAAM,GAAG,CAAC;EACxB;EACA,IAAID,KAAK,GAAG,CAAC,EAAE;IACbA,KAAK,GAAG,CAAC;EACX;EACA,OAAOA,KAAK;AACd,CAAC;AACD,IAAIE,KAAK,GAAG,SAAAA,CAAUJ,IAAI,EAAEK,SAAS,EAAE;EACrC,IAAIH,KAAK,GAAGH,SAAS,CAACC,IAAI,EAAEK,SAAS,CAAC;EACtC,OAAO;IACLC,QAAQ,EAAED,SAAS,CAACH,KAAK,CAAC;IAC1BK,OAAO,EAAEF,SAAS,CAACG,KAAK,CAAC,CAAC,EAAEN,KAAK,CAAC;IAClCO,MAAM,EAAEJ,SAAS,CAACG,KAAK,CAACN,KAAK,GAAG,CAAC;EACnC,CAAC;AACH,CAAC;AACD,eAAe,SAASQ,gBAAgBA,CAACC,YAAY,EAAEC,SAAS,EAAE;EAChE,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,CAAC;EACf;EACA,IAAIC,EAAE,GAAGpB,MAAM,CAACG,QAAQ,CAAC;MACrBkB,OAAO,EAAEH,YAAY;MACrBI,IAAI,EAAE,EAAE;MACRC,MAAM,EAAE;IACV,CAAC,CAAC,EAAE,CAAC,CAAC;IACNC,OAAO,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACfK,UAAU,GAAGL,EAAE,CAAC,CAAC,CAAC;EACpB,IAAIC,OAAO,GAAGG,OAAO,CAACH,OAAO;IAC3BC,IAAI,GAAGE,OAAO,CAACF,IAAI;IACnBC,MAAM,GAAGC,OAAO,CAACD,MAAM;EACzB,IAAIG,eAAe,GAAGxB,MAAM,CAACgB,YAAY,CAAC;EAC1C,IAAIS,KAAK,GAAG,SAAAA,CAAA,EAAY;IACtB,IAAIC,MAAM,GAAG,EAAE;IACf,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACpB,MAAM,EAAEmB,EAAE,EAAE,EAAE;MAC5CD,MAAM,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA,IAAIE,QAAQ,GAAGH,MAAM,CAAClB,MAAM,GAAG,CAAC,GAAGkB,MAAM,CAAC,CAAC,CAAC,GAAGF,eAAe,CAACM,OAAO;IACtEN,eAAe,CAACM,OAAO,GAAGD,QAAQ;IAClCN,UAAU,CAAC;MACTJ,OAAO,EAAEU,QAAQ;MACjBR,MAAM,EAAE,EAAE;MACVD,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;EACD,IAAIW,WAAW,GAAG,SAAAA,CAAUC,GAAG,EAAE;IAC/B,IAAIC,KAAK,GAAGlC,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACsB,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,CAACD,OAAO,CAAC,EAAE,KAAK,CAAC;IACnF,IAAIe,YAAY,GAAG/B,QAAQ,CAACc,SAAS,CAAC,GAAGA,SAAS,GAAGkB,MAAM,CAAClB,SAAS,CAAC;IACtE;IACA,IAAIiB,YAAY,GAAG,CAAC,IAAID,KAAK,CAACzB,MAAM,GAAG0B,YAAY,EAAE;MACnD;MACAD,KAAK,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;IACpB;IACAb,UAAU,CAAC;MACTJ,OAAO,EAAEa,GAAG;MACZX,MAAM,EAAE,EAAE;MACVD,IAAI,EAAEa;IACR,CAAC,CAAC;EACJ,CAAC;EACD,IAAII,QAAQ,GAAG,SAAAA,CAAUhC,IAAI,EAAE;IAC7B,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,CAAC;IACV;IACA,IAAIgB,MAAM,CAACb,MAAM,KAAK,CAAC,EAAE;MACvB;IACF;IACA,IAAIU,EAAE,GAAGT,KAAK,CAACJ,IAAI,EAAEgB,MAAM,CAAC;MAC1BT,OAAO,GAAGM,EAAE,CAACN,OAAO;MACpBD,QAAQ,GAAGO,EAAE,CAACP,QAAQ;MACtBG,MAAM,GAAGI,EAAE,CAACJ,MAAM;IACpBS,UAAU,CAAC;MACTH,IAAI,EAAErB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACsB,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE,CAACD,OAAO,CAAC,EAAE,KAAK,CAAC,EAAErB,MAAM,CAACc,OAAO,CAAC,EAAE,KAAK,CAAC;MACpHO,OAAO,EAAER,QAAQ;MACjBU,MAAM,EAAEP;IACV,CAAC,CAAC;EACJ,CAAC;EACD,IAAIwB,SAAS,GAAG,SAAAA,CAAUjC,IAAI,EAAE;IAC9B,IAAIA,IAAI,KAAK,KAAK,CAAC,EAAE;MACnBA,IAAI,GAAG,CAAC,CAAC;IACX;IACA,IAAIe,IAAI,CAACZ,MAAM,KAAK,CAAC,EAAE;MACrB;IACF;IACA,IAAIU,EAAE,GAAGT,KAAK,CAACJ,IAAI,EAAEe,IAAI,CAAC;MACxBR,OAAO,GAAGM,EAAE,CAACN,OAAO;MACpBD,QAAQ,GAAGO,EAAE,CAACP,QAAQ;MACtBG,MAAM,GAAGI,EAAE,CAACJ,MAAM;IACpBS,UAAU,CAAC;MACTH,IAAI,EAAER,OAAO;MACbO,OAAO,EAAER,QAAQ;MACjBU,MAAM,EAAEtB,aAAa,CAACA,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACgB,MAAM,CAAC,EAAE,KAAK,CAAC,EAAE,CAACK,OAAO,CAAC,EAAE,KAAK,CAAC,EAAErB,MAAM,CAACuB,MAAM,CAAC,EAAE,KAAK;IACxH,CAAC,CAAC;EACJ,CAAC;EACD,IAAIkB,EAAE,GAAG,SAAAA,CAAUlC,IAAI,EAAE;IACvB,IAAImC,OAAO,GAAGrC,QAAQ,CAACE,IAAI,CAAC,GAAGA,IAAI,GAAG8B,MAAM,CAAC9B,IAAI,CAAC;IAClD,IAAImC,OAAO,KAAK,CAAC,EAAE;MACjB;IACF;IACA,IAAIA,OAAO,GAAG,CAAC,EAAE;MACf,OAAOH,QAAQ,CAACG,OAAO,CAAC;IAC1B;IACAF,SAAS,CAACE,OAAO,CAAC;EACpB,CAAC;EACD,OAAO;IACLC,KAAK,EAAEtB,OAAO;IACduB,UAAU,EAAEtB,IAAI,CAACZ,MAAM;IACvBmC,aAAa,EAAEtB,MAAM,CAACb,MAAM;IAC5BoC,QAAQ,EAAE1C,aAAa,CAAC6B,WAAW,CAAC;IACpCQ,EAAE,EAAErC,aAAa,CAACqC,EAAE,CAAC;IACrBM,IAAI,EAAE3C,aAAa,CAAC,YAAY;MAC9BqC,EAAE,CAAC,CAAC,CAAC,CAAC;IACR,CAAC,CAAC;IACFO,OAAO,EAAE5C,aAAa,CAAC,YAAY;MACjCqC,EAAE,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACFd,KAAK,EAAEvB,aAAa,CAACuB,KAAK;EAC5B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}