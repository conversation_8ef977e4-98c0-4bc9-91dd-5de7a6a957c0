{"ast": null, "code": "var isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';\nexport default isDev;", "map": {"version": 3, "names": ["isDev", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/isDev.js"], "sourcesContent": ["var isDev = process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test';\nexport default isDev;"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAIF,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,MAAM;AACrF,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}