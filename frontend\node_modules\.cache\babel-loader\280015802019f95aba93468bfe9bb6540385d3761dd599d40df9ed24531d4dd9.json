{"ast": null, "code": "/**\n * 統一相機管理器\n * 根據環境自動選擇合適的拍照策略，提供統一的API接口\n */\n\nimport { Toast } from 'antd-mobile';\nimport { getRecommendedCameraMode, getCameraCapabilities } from './deviceDetector';\nimport { createCameraStrategy } from './cameraStrategies';\n\n/**\n * 相機管理器類\n */\nclass CameraManager {\n  constructor() {\n    this.strategy = null;\n    this.mode = null;\n    this.isInitialized = false;\n    this.callbacks = {};\n    this.capabilities = null;\n  }\n\n  /**\n   * 初始化相機管理器\n   */\n  async initialize() {\n    try {\n      // 檢測相機功能\n      this.capabilities = await getCameraCapabilities();\n      if (!this.capabilities.hasCamera) {\n        throw new Error('設備不支持相機功能');\n      }\n\n      // 獲取推薦的相機模式\n      this.mode = await getRecommendedCameraMode();\n      if (this.mode === 'none') {\n        throw new Error('無可用的相機模式');\n      }\n\n      // 創建相應的策略\n      this.strategy = createCameraStrategy(this.mode);\n\n      // 設置策略回調\n      this.strategy.setCallbacks({\n        cameraStart: data => this.emit('cameraStart', data),\n        cameraStop: () => this.emit('cameraStop'),\n        cameraError: error => this.emit('cameraError', error),\n        photoTaken: data => this.emit('photoTaken', data),\n        photoError: error => this.emit('photoError', error),\n        cameraSwitch: data => this.emit('cameraSwitch', data),\n        cameraSwitchError: error => this.emit('cameraSwitchError', error)\n      });\n      this.isInitialized = true;\n      console.log(`相機管理器初始化完成，模式: ${this.mode}`);\n    } catch (error) {\n      console.error('相機管理器初始化失敗:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 設置事件回調\n   */\n  setCallbacks(callbacks) {\n    this.callbacks = {\n      ...this.callbacks,\n      ...callbacks\n    };\n  }\n\n  /**\n   * 觸發事件回調\n   */\n  emit(event, data) {\n    if (this.callbacks[event]) {\n      this.callbacks[event](data);\n    }\n  }\n\n  /**\n   * 啟動相機\n   */\n  async startCamera(target = 'back', options = {}) {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n    try {\n      // 根據目標設置約束\n      const constraints = this.buildConstraints(target, options);\n\n      // 設置視頻和畫布元素（如果提供）\n      if (options.videoElement && options.canvasElement) {\n        this.strategy.setElements(options.videoElement, options.canvasElement);\n      }\n      const stream = await this.strategy.startCamera(constraints);\n      this.emit('cameraStart', {\n        stream,\n        mode: this.mode,\n        target,\n        capabilities: this.capabilities\n      });\n      return stream;\n    } catch (error) {\n      console.error('啟動相機失敗:', error);\n\n      // 用戶友好的錯誤提示\n      let message = '無法啟動相機';\n      if (error.name === 'NotAllowedError') {\n        message = '相機權限被拒絕，請在瀏覽器設置中允許相機訪問';\n      } else if (error.name === 'NotFoundError') {\n        message = '未找到可用的相機設備';\n      } else if (error.name === 'NotReadableError') {\n        message = '相機被其他應用程序占用';\n      }\n      Toast.show({\n        content: message,\n        position: 'center'\n      });\n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 停止相機\n   */\n  stopCamera() {\n    if (this.strategy) {\n      this.strategy.stopCamera();\n    }\n  }\n\n  /**\n   * 拍照\n   */\n  async takePhoto() {\n    // 詳細的狀態檢查\n    if (!this.isInitialized) {\n      throw new Error('相機管理器未初始化');\n    }\n    if (!this.strategy) {\n      throw new Error('相機策略未創建');\n    }\n    const strategyStatus = this.strategy.getStatus();\n    if (!strategyStatus.isActive) {\n      throw new Error('相機未啟動');\n    }\n    console.log('相機管理器開始拍照', {\n      mode: this.mode,\n      strategyStatus,\n      capabilities: this.capabilities\n    });\n    try {\n      const result = await this.strategy.takePhoto();\n      if (!result || !result.file) {\n        throw new Error('拍照結果無效');\n      }\n      const enhancedResult = {\n        ...result,\n        mode: this.mode,\n        timestamp: new Date().toISOString(),\n        deviceInfo: {\n          userAgent: navigator.userAgent,\n          screenSize: {\n            width: window.innerWidth,\n            height: window.innerHeight\n          }\n        }\n      };\n      console.log('相機管理器拍照成功', {\n        fileSize: result.file.size,\n        mode: this.mode,\n        timestamp: enhancedResult.timestamp\n      });\n      this.emit('photoTaken', enhancedResult);\n      return enhancedResult;\n    } catch (error) {\n      console.error('相機管理器拍照失敗:', error);\n      Toast.show({\n        content: `拍照失敗：${error.message || '請重試'}`,\n        position: 'center'\n      });\n      this.emit('photoError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 切換攝像頭（僅移動端支持）\n   */\n  async switchCamera() {\n    if (!this.strategy) {\n      throw new Error('相機未初始化');\n    }\n    if (this.mode !== 'mobile') {\n      Toast.show({\n        content: '當前模式不支持切換攝像頭',\n        position: 'center'\n      });\n      return;\n    }\n    try {\n      await this.strategy.switchCamera();\n    } catch (error) {\n      console.error('切換攝像頭失敗:', error);\n      this.emit('cameraSwitchError', error);\n    }\n  }\n\n  /**\n   * 構建相機約束\n   */\n  buildConstraints(target, options) {\n    const constraints = {\n      video: {\n        facingMode: target === 'front' ? 'user' : 'environment'\n      }\n    };\n\n    // 根據模式調整約束\n    if (this.mode === 'mobile') {\n      // 移動端優化：嘗試獲取最高可用解析度\n      constraints.video = {\n        ...constraints.video,\n        width: {\n          ideal: 3840,\n          min: 1920\n        },\n        // 4K優先，最低1080p\n        height: {\n          ideal: 2160,\n          min: 1080\n        },\n        frameRate: {\n          ideal: 30,\n          min: 15\n        },\n        aspectRatio: {\n          ideal: 16 / 9\n        }\n      };\n    } else {\n      // Web端保持原有設置\n      constraints.video = {\n        ...constraints.video,\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      };\n    }\n\n    // 合併用戶提供的約束\n    if (options.constraints) {\n      constraints.video = {\n        ...constraints.video,\n        ...options.constraints.video\n      };\n    }\n    return constraints;\n  }\n\n  /**\n   * 獲取相機狀態\n   */\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      mode: this.mode,\n      capabilities: this.capabilities,\n      strategy: this.strategy ? this.strategy.getStatus() : null\n    };\n  }\n\n  /**\n   * 獲取當前模式\n   */\n  getMode() {\n    return this.mode;\n  }\n\n  /**\n   * 獲取相機功能\n   */\n  getCapabilities() {\n    return this.capabilities;\n  }\n\n  /**\n   * 檢查是否支持攝像頭切換\n   */\n  supportsCameraSwitch() {\n    return this.mode === 'mobile' && this.strategy && typeof this.strategy.supportsCameraSwitch === 'function' && this.strategy.supportsCameraSwitch();\n  }\n\n  /**\n   * 銷毀管理器\n   */\n  destroy() {\n    this.stopCamera();\n    this.strategy = null;\n    this.callbacks = {};\n    this.isInitialized = false;\n  }\n}\n\n// 創建單例實例\nconst cameraManager = new CameraManager();\n\n/**\n * 獲取相機管理器實例\n */\nexport const getCameraManager = () => cameraManager;\n\n/**\n * 便捷函數：初始化並啟動相機\n */\nexport const startCameraWithAutoDetection = async (target = 'back', options = {}) => {\n  const manager = getCameraManager();\n  return await manager.startCamera(target, options);\n};\n\n/**\n * 便捷函數：拍照\n */\nexport const takePhotoWithManager = async () => {\n  const manager = getCameraManager();\n  return await manager.takePhoto();\n};\n\n/**\n * 便捷函數：停止相機\n */\nexport const stopCameraWithManager = () => {\n  const manager = getCameraManager();\n  manager.stopCamera();\n};\n\n// 導出對象\nconst cameraManagerExports = {\n  CameraManager,\n  getCameraManager,\n  startCameraWithAutoDetection,\n  takePhotoWithManager,\n  stopCameraWithManager\n};\nexport default cameraManagerExports;", "map": {"version": 3, "names": ["Toast", "getRecommendedCameraMode", "getCameraCapabilities", "createCameraStrategy", "CameraManager", "constructor", "strategy", "mode", "isInitialized", "callbacks", "capabilities", "initialize", "hasCamera", "Error", "setCallbacks", "cameraStart", "data", "emit", "cameraStop", "cameraError", "error", "photoTaken", "photoError", "cameraSwitch", "cameraSwitchError", "console", "log", "event", "startCamera", "target", "options", "constraints", "buildConstraints", "videoElement", "canvasElement", "setElements", "stream", "message", "name", "show", "content", "position", "stopCamera", "<PERSON><PERSON><PERSON><PERSON>", "strategyStatus", "getStatus", "isActive", "result", "file", "enhancedResult", "timestamp", "Date", "toISOString", "deviceInfo", "userAgent", "navigator", "screenSize", "width", "window", "innerWidth", "height", "innerHeight", "fileSize", "size", "switchCamera", "video", "facingMode", "ideal", "min", "frameRate", "aspectRatio", "getMode", "getCapabilities", "supportsCameraSwitch", "destroy", "cameraManager", "getCameraManager", "startCameraWithAutoDetection", "manager", "takePhotoWithManager", "stopCameraWithManager", "cameraManagerExports"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/utils/cameraManager.js"], "sourcesContent": ["/**\n * 統一相機管理器\n * 根據環境自動選擇合適的拍照策略，提供統一的API接口\n */\n\nimport { Toast } from 'antd-mobile';\nimport { getRecommendedCameraMode, getCameraCapabilities } from './deviceDetector';\nimport { createCameraStrategy } from './cameraStrategies';\n\n/**\n * 相機管理器類\n */\nclass CameraManager {\n  constructor() {\n    this.strategy = null;\n    this.mode = null;\n    this.isInitialized = false;\n    this.callbacks = {};\n    this.capabilities = null;\n  }\n\n  /**\n   * 初始化相機管理器\n   */\n  async initialize() {\n    try {\n      // 檢測相機功能\n      this.capabilities = await getCameraCapabilities();\n      \n      if (!this.capabilities.hasCamera) {\n        throw new Error('設備不支持相機功能');\n      }\n\n      // 獲取推薦的相機模式\n      this.mode = await getRecommendedCameraMode();\n      \n      if (this.mode === 'none') {\n        throw new Error('無可用的相機模式');\n      }\n\n      // 創建相應的策略\n      this.strategy = createCameraStrategy(this.mode);\n      \n      // 設置策略回調\n      this.strategy.setCallbacks({\n        cameraStart: (data) => this.emit('cameraStart', data),\n        cameraStop: () => this.emit('cameraStop'),\n        cameraError: (error) => this.emit('cameraError', error),\n        photoTaken: (data) => this.emit('photoTaken', data),\n        photoError: (error) => this.emit('photoError', error),\n        cameraSwitch: (data) => this.emit('cameraSwitch', data),\n        cameraSwitchError: (error) => this.emit('cameraSwitchError', error)\n      });\n\n      this.isInitialized = true;\n      console.log(`相機管理器初始化完成，模式: ${this.mode}`);\n      \n    } catch (error) {\n      console.error('相機管理器初始化失敗:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 設置事件回調\n   */\n  setCallbacks(callbacks) {\n    this.callbacks = { ...this.callbacks, ...callbacks };\n  }\n\n  /**\n   * 觸發事件回調\n   */\n  emit(event, data) {\n    if (this.callbacks[event]) {\n      this.callbacks[event](data);\n    }\n  }\n\n  /**\n   * 啟動相機\n   */\n  async startCamera(target = 'back', options = {}) {\n    if (!this.isInitialized) {\n      await this.initialize();\n    }\n\n    try {\n      // 根據目標設置約束\n      const constraints = this.buildConstraints(target, options);\n      \n      // 設置視頻和畫布元素（如果提供）\n      if (options.videoElement && options.canvasElement) {\n        this.strategy.setElements(options.videoElement, options.canvasElement);\n      }\n\n      const stream = await this.strategy.startCamera(constraints);\n      \n      this.emit('cameraStart', { \n        stream, \n        mode: this.mode, \n        target,\n        capabilities: this.capabilities \n      });\n      \n      return stream;\n    } catch (error) {\n      console.error('啟動相機失敗:', error);\n      \n      // 用戶友好的錯誤提示\n      let message = '無法啟動相機';\n      if (error.name === 'NotAllowedError') {\n        message = '相機權限被拒絕，請在瀏覽器設置中允許相機訪問';\n      } else if (error.name === 'NotFoundError') {\n        message = '未找到可用的相機設備';\n      } else if (error.name === 'NotReadableError') {\n        message = '相機被其他應用程序占用';\n      }\n      \n      Toast.show({\n        content: message,\n        position: 'center'\n      });\n      \n      this.emit('cameraError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 停止相機\n   */\n  stopCamera() {\n    if (this.strategy) {\n      this.strategy.stopCamera();\n    }\n  }\n\n  /**\n   * 拍照\n   */\n  async takePhoto() {\n    // 詳細的狀態檢查\n    if (!this.isInitialized) {\n      throw new Error('相機管理器未初始化');\n    }\n\n    if (!this.strategy) {\n      throw new Error('相機策略未創建');\n    }\n\n    const strategyStatus = this.strategy.getStatus();\n    if (!strategyStatus.isActive) {\n      throw new Error('相機未啟動');\n    }\n\n    console.log('相機管理器開始拍照', {\n      mode: this.mode,\n      strategyStatus,\n      capabilities: this.capabilities\n    });\n\n    try {\n      const result = await this.strategy.takePhoto();\n\n      if (!result || !result.file) {\n        throw new Error('拍照結果無效');\n      }\n\n      const enhancedResult = {\n        ...result,\n        mode: this.mode,\n        timestamp: new Date().toISOString(),\n        deviceInfo: {\n          userAgent: navigator.userAgent,\n          screenSize: {\n            width: window.innerWidth,\n            height: window.innerHeight\n          }\n        }\n      };\n\n      console.log('相機管理器拍照成功', {\n        fileSize: result.file.size,\n        mode: this.mode,\n        timestamp: enhancedResult.timestamp\n      });\n\n      this.emit('photoTaken', enhancedResult);\n\n      return enhancedResult;\n    } catch (error) {\n      console.error('相機管理器拍照失敗:', error);\n\n      Toast.show({\n        content: `拍照失敗：${error.message || '請重試'}`,\n        position: 'center'\n      });\n\n      this.emit('photoError', error);\n      throw error;\n    }\n  }\n\n  /**\n   * 切換攝像頭（僅移動端支持）\n   */\n  async switchCamera() {\n    if (!this.strategy) {\n      throw new Error('相機未初始化');\n    }\n\n    if (this.mode !== 'mobile') {\n      Toast.show({\n        content: '當前模式不支持切換攝像頭',\n        position: 'center'\n      });\n      return;\n    }\n\n    try {\n      await this.strategy.switchCamera();\n    } catch (error) {\n      console.error('切換攝像頭失敗:', error);\n      this.emit('cameraSwitchError', error);\n    }\n  }\n\n  /**\n   * 構建相機約束\n   */\n  buildConstraints(target, options) {\n    const constraints = {\n      video: {\n        facingMode: target === 'front' ? 'user' : 'environment'\n      }\n    };\n\n    // 根據模式調整約束\n    if (this.mode === 'mobile') {\n      // 移動端優化：嘗試獲取最高可用解析度\n      constraints.video = {\n        ...constraints.video,\n        width: { ideal: 3840, min: 1920 }, // 4K優先，最低1080p\n        height: { ideal: 2160, min: 1080 },\n        frameRate: { ideal: 30, min: 15 },\n        aspectRatio: { ideal: 16/9 }\n      };\n    } else {\n      // Web端保持原有設置\n      constraints.video = {\n        ...constraints.video,\n        width: { ideal: 1280 },\n        height: { ideal: 720 }\n      };\n    }\n\n    // 合併用戶提供的約束\n    if (options.constraints) {\n      constraints.video = {\n        ...constraints.video,\n        ...options.constraints.video\n      };\n    }\n\n    return constraints;\n  }\n\n  /**\n   * 獲取相機狀態\n   */\n  getStatus() {\n    return {\n      isInitialized: this.isInitialized,\n      mode: this.mode,\n      capabilities: this.capabilities,\n      strategy: this.strategy ? this.strategy.getStatus() : null\n    };\n  }\n\n  /**\n   * 獲取當前模式\n   */\n  getMode() {\n    return this.mode;\n  }\n\n  /**\n   * 獲取相機功能\n   */\n  getCapabilities() {\n    return this.capabilities;\n  }\n\n  /**\n   * 檢查是否支持攝像頭切換\n   */\n  supportsCameraSwitch() {\n    return this.mode === 'mobile' && \n           this.strategy && \n           typeof this.strategy.supportsCameraSwitch === 'function' && \n           this.strategy.supportsCameraSwitch();\n  }\n\n  /**\n   * 銷毀管理器\n   */\n  destroy() {\n    this.stopCamera();\n    this.strategy = null;\n    this.callbacks = {};\n    this.isInitialized = false;\n  }\n}\n\n// 創建單例實例\nconst cameraManager = new CameraManager();\n\n/**\n * 獲取相機管理器實例\n */\nexport const getCameraManager = () => cameraManager;\n\n/**\n * 便捷函數：初始化並啟動相機\n */\nexport const startCameraWithAutoDetection = async (target = 'back', options = {}) => {\n  const manager = getCameraManager();\n  return await manager.startCamera(target, options);\n};\n\n/**\n * 便捷函數：拍照\n */\nexport const takePhotoWithManager = async () => {\n  const manager = getCameraManager();\n  return await manager.takePhoto();\n};\n\n/**\n * 便捷函數：停止相機\n */\nexport const stopCameraWithManager = () => {\n  const manager = getCameraManager();\n  manager.stopCamera();\n};\n\n// 導出對象\nconst cameraManagerExports = {\n  CameraManager,\n  getCameraManager,\n  startCameraWithAutoDetection,\n  takePhotoWithManager,\n  stopCameraWithManager\n};\n\nexport default cameraManagerExports;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SAASA,KAAK,QAAQ,aAAa;AACnC,SAASC,wBAAwB,EAAEC,qBAAqB,QAAQ,kBAAkB;AAClF,SAASC,oBAAoB,QAAQ,oBAAoB;;AAEzD;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B,IAAI,CAACC,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACC,YAAY,GAAG,IAAI;EAC1B;;EAEA;AACF;AACA;EACE,MAAMC,UAAUA,CAAA,EAAG;IACjB,IAAI;MACF;MACA,IAAI,CAACD,YAAY,GAAG,MAAMR,qBAAqB,CAAC,CAAC;MAEjD,IAAI,CAAC,IAAI,CAACQ,YAAY,CAACE,SAAS,EAAE;QAChC,MAAM,IAAIC,KAAK,CAAC,WAAW,CAAC;MAC9B;;MAEA;MACA,IAAI,CAACN,IAAI,GAAG,MAAMN,wBAAwB,CAAC,CAAC;MAE5C,IAAI,IAAI,CAACM,IAAI,KAAK,MAAM,EAAE;QACxB,MAAM,IAAIM,KAAK,CAAC,UAAU,CAAC;MAC7B;;MAEA;MACA,IAAI,CAACP,QAAQ,GAAGH,oBAAoB,CAAC,IAAI,CAACI,IAAI,CAAC;;MAE/C;MACA,IAAI,CAACD,QAAQ,CAACQ,YAAY,CAAC;QACzBC,WAAW,EAAGC,IAAI,IAAK,IAAI,CAACC,IAAI,CAAC,aAAa,EAAED,IAAI,CAAC;QACrDE,UAAU,EAAEA,CAAA,KAAM,IAAI,CAACD,IAAI,CAAC,YAAY,CAAC;QACzCE,WAAW,EAAGC,KAAK,IAAK,IAAI,CAACH,IAAI,CAAC,aAAa,EAAEG,KAAK,CAAC;QACvDC,UAAU,EAAGL,IAAI,IAAK,IAAI,CAACC,IAAI,CAAC,YAAY,EAAED,IAAI,CAAC;QACnDM,UAAU,EAAGF,KAAK,IAAK,IAAI,CAACH,IAAI,CAAC,YAAY,EAAEG,KAAK,CAAC;QACrDG,YAAY,EAAGP,IAAI,IAAK,IAAI,CAACC,IAAI,CAAC,cAAc,EAAED,IAAI,CAAC;QACvDQ,iBAAiB,EAAGJ,KAAK,IAAK,IAAI,CAACH,IAAI,CAAC,mBAAmB,EAAEG,KAAK;MACpE,CAAC,CAAC;MAEF,IAAI,CAACZ,aAAa,GAAG,IAAI;MACzBiB,OAAO,CAACC,GAAG,CAAC,kBAAkB,IAAI,CAACnB,IAAI,EAAE,CAAC;IAE5C,CAAC,CAAC,OAAOa,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,aAAa,EAAEA,KAAK,CAAC;MACnC,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEN,YAAYA,CAACL,SAAS,EAAE;IACtB,IAAI,CAACA,SAAS,GAAG;MAAE,GAAG,IAAI,CAACA,SAAS;MAAE,GAAGA;IAAU,CAAC;EACtD;;EAEA;AACF;AACA;EACEQ,IAAIA,CAACU,KAAK,EAAEX,IAAI,EAAE;IAChB,IAAI,IAAI,CAACP,SAAS,CAACkB,KAAK,CAAC,EAAE;MACzB,IAAI,CAAClB,SAAS,CAACkB,KAAK,CAAC,CAACX,IAAI,CAAC;IAC7B;EACF;;EAEA;AACF;AACA;EACE,MAAMY,WAAWA,CAACC,MAAM,GAAG,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IAC/C,IAAI,CAAC,IAAI,CAACtB,aAAa,EAAE;MACvB,MAAM,IAAI,CAACG,UAAU,CAAC,CAAC;IACzB;IAEA,IAAI;MACF;MACA,MAAMoB,WAAW,GAAG,IAAI,CAACC,gBAAgB,CAACH,MAAM,EAAEC,OAAO,CAAC;;MAE1D;MACA,IAAIA,OAAO,CAACG,YAAY,IAAIH,OAAO,CAACI,aAAa,EAAE;QACjD,IAAI,CAAC5B,QAAQ,CAAC6B,WAAW,CAACL,OAAO,CAACG,YAAY,EAAEH,OAAO,CAACI,aAAa,CAAC;MACxE;MAEA,MAAME,MAAM,GAAG,MAAM,IAAI,CAAC9B,QAAQ,CAACsB,WAAW,CAACG,WAAW,CAAC;MAE3D,IAAI,CAACd,IAAI,CAAC,aAAa,EAAE;QACvBmB,MAAM;QACN7B,IAAI,EAAE,IAAI,CAACA,IAAI;QACfsB,MAAM;QACNnB,YAAY,EAAE,IAAI,CAACA;MACrB,CAAC,CAAC;MAEF,OAAO0B,MAAM;IACf,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;;MAE/B;MACA,IAAIiB,OAAO,GAAG,QAAQ;MACtB,IAAIjB,KAAK,CAACkB,IAAI,KAAK,iBAAiB,EAAE;QACpCD,OAAO,GAAG,wBAAwB;MACpC,CAAC,MAAM,IAAIjB,KAAK,CAACkB,IAAI,KAAK,eAAe,EAAE;QACzCD,OAAO,GAAG,YAAY;MACxB,CAAC,MAAM,IAAIjB,KAAK,CAACkB,IAAI,KAAK,kBAAkB,EAAE;QAC5CD,OAAO,GAAG,aAAa;MACzB;MAEArC,KAAK,CAACuC,IAAI,CAAC;QACTC,OAAO,EAAEH,OAAO;QAChBI,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAI,CAACxB,IAAI,CAAC,aAAa,EAAEG,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACEsB,UAAUA,CAAA,EAAG;IACX,IAAI,IAAI,CAACpC,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACoC,UAAU,CAAC,CAAC;IAC5B;EACF;;EAEA;AACF;AACA;EACE,MAAMC,SAASA,CAAA,EAAG;IAChB;IACA,IAAI,CAAC,IAAI,CAACnC,aAAa,EAAE;MACvB,MAAM,IAAIK,KAAK,CAAC,WAAW,CAAC;IAC9B;IAEA,IAAI,CAAC,IAAI,CAACP,QAAQ,EAAE;MAClB,MAAM,IAAIO,KAAK,CAAC,SAAS,CAAC;IAC5B;IAEA,MAAM+B,cAAc,GAAG,IAAI,CAACtC,QAAQ,CAACuC,SAAS,CAAC,CAAC;IAChD,IAAI,CAACD,cAAc,CAACE,QAAQ,EAAE;MAC5B,MAAM,IAAIjC,KAAK,CAAC,OAAO,CAAC;IAC1B;IAEAY,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;MACvBnB,IAAI,EAAE,IAAI,CAACA,IAAI;MACfqC,cAAc;MACdlC,YAAY,EAAE,IAAI,CAACA;IACrB,CAAC,CAAC;IAEF,IAAI;MACF,MAAMqC,MAAM,GAAG,MAAM,IAAI,CAACzC,QAAQ,CAACqC,SAAS,CAAC,CAAC;MAE9C,IAAI,CAACI,MAAM,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE;QAC3B,MAAM,IAAInC,KAAK,CAAC,QAAQ,CAAC;MAC3B;MAEA,MAAMoC,cAAc,GAAG;QACrB,GAAGF,MAAM;QACTxC,IAAI,EAAE,IAAI,CAACA,IAAI;QACf2C,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QACnCC,UAAU,EAAE;UACVC,SAAS,EAAEC,SAAS,CAACD,SAAS;UAC9BE,UAAU,EAAE;YACVC,KAAK,EAAEC,MAAM,CAACC,UAAU;YACxBC,MAAM,EAAEF,MAAM,CAACG;UACjB;QACF;MACF,CAAC;MAEDpC,OAAO,CAACC,GAAG,CAAC,WAAW,EAAE;QACvBoC,QAAQ,EAAEf,MAAM,CAACC,IAAI,CAACe,IAAI;QAC1BxD,IAAI,EAAE,IAAI,CAACA,IAAI;QACf2C,SAAS,EAAED,cAAc,CAACC;MAC5B,CAAC,CAAC;MAEF,IAAI,CAACjC,IAAI,CAAC,YAAY,EAAEgC,cAAc,CAAC;MAEvC,OAAOA,cAAc;IACvB,CAAC,CAAC,OAAO7B,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAElCpB,KAAK,CAACuC,IAAI,CAAC;QACTC,OAAO,EAAE,QAAQpB,KAAK,CAACiB,OAAO,IAAI,KAAK,EAAE;QACzCI,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,IAAI,CAACxB,IAAI,CAAC,YAAY,EAAEG,KAAK,CAAC;MAC9B,MAAMA,KAAK;IACb;EACF;;EAEA;AACF;AACA;EACE,MAAM4C,YAAYA,CAAA,EAAG;IACnB,IAAI,CAAC,IAAI,CAAC1D,QAAQ,EAAE;MAClB,MAAM,IAAIO,KAAK,CAAC,QAAQ,CAAC;IAC3B;IAEA,IAAI,IAAI,CAACN,IAAI,KAAK,QAAQ,EAAE;MAC1BP,KAAK,CAACuC,IAAI,CAAC;QACTC,OAAO,EAAE,cAAc;QACvBC,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF;IACF;IAEA,IAAI;MACF,MAAM,IAAI,CAACnC,QAAQ,CAAC0D,YAAY,CAAC,CAAC;IACpC,CAAC,CAAC,OAAO5C,KAAK,EAAE;MACdK,OAAO,CAACL,KAAK,CAAC,UAAU,EAAEA,KAAK,CAAC;MAChC,IAAI,CAACH,IAAI,CAAC,mBAAmB,EAAEG,KAAK,CAAC;IACvC;EACF;;EAEA;AACF;AACA;EACEY,gBAAgBA,CAACH,MAAM,EAAEC,OAAO,EAAE;IAChC,MAAMC,WAAW,GAAG;MAClBkC,KAAK,EAAE;QACLC,UAAU,EAAErC,MAAM,KAAK,OAAO,GAAG,MAAM,GAAG;MAC5C;IACF,CAAC;;IAED;IACA,IAAI,IAAI,CAACtB,IAAI,KAAK,QAAQ,EAAE;MAC1B;MACAwB,WAAW,CAACkC,KAAK,GAAG;QAClB,GAAGlC,WAAW,CAACkC,KAAK;QACpBR,KAAK,EAAE;UAAEU,KAAK,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAK,CAAC;QAAE;QACnCR,MAAM,EAAE;UAAEO,KAAK,EAAE,IAAI;UAAEC,GAAG,EAAE;QAAK,CAAC;QAClCC,SAAS,EAAE;UAAEF,KAAK,EAAE,EAAE;UAAEC,GAAG,EAAE;QAAG,CAAC;QACjCE,WAAW,EAAE;UAAEH,KAAK,EAAE,EAAE,GAAC;QAAE;MAC7B,CAAC;IACH,CAAC,MAAM;MACL;MACApC,WAAW,CAACkC,KAAK,GAAG;QAClB,GAAGlC,WAAW,CAACkC,KAAK;QACpBR,KAAK,EAAE;UAAEU,KAAK,EAAE;QAAK,CAAC;QACtBP,MAAM,EAAE;UAAEO,KAAK,EAAE;QAAI;MACvB,CAAC;IACH;;IAEA;IACA,IAAIrC,OAAO,CAACC,WAAW,EAAE;MACvBA,WAAW,CAACkC,KAAK,GAAG;QAClB,GAAGlC,WAAW,CAACkC,KAAK;QACpB,GAAGnC,OAAO,CAACC,WAAW,CAACkC;MACzB,CAAC;IACH;IAEA,OAAOlC,WAAW;EACpB;;EAEA;AACF;AACA;EACEc,SAASA,CAAA,EAAG;IACV,OAAO;MACLrC,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCD,IAAI,EAAE,IAAI,CAACA,IAAI;MACfG,YAAY,EAAE,IAAI,CAACA,YAAY;MAC/BJ,QAAQ,EAAE,IAAI,CAACA,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACuC,SAAS,CAAC,CAAC,GAAG;IACxD,CAAC;EACH;;EAEA;AACF;AACA;EACE0B,OAAOA,CAAA,EAAG;IACR,OAAO,IAAI,CAAChE,IAAI;EAClB;;EAEA;AACF;AACA;EACEiE,eAAeA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC9D,YAAY;EAC1B;;EAEA;AACF;AACA;EACE+D,oBAAoBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAClE,IAAI,KAAK,QAAQ,IACtB,IAAI,CAACD,QAAQ,IACb,OAAO,IAAI,CAACA,QAAQ,CAACmE,oBAAoB,KAAK,UAAU,IACxD,IAAI,CAACnE,QAAQ,CAACmE,oBAAoB,CAAC,CAAC;EAC7C;;EAEA;AACF;AACA;EACEC,OAAOA,CAAA,EAAG;IACR,IAAI,CAAChC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACpC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACG,SAAS,GAAG,CAAC,CAAC;IACnB,IAAI,CAACD,aAAa,GAAG,KAAK;EAC5B;AACF;;AAEA;AACA,MAAMmE,aAAa,GAAG,IAAIvE,aAAa,CAAC,CAAC;;AAEzC;AACA;AACA;AACA,OAAO,MAAMwE,gBAAgB,GAAGA,CAAA,KAAMD,aAAa;;AAEnD;AACA;AACA;AACA,OAAO,MAAME,4BAA4B,GAAG,MAAAA,CAAOhD,MAAM,GAAG,MAAM,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EACnF,MAAMgD,OAAO,GAAGF,gBAAgB,CAAC,CAAC;EAClC,OAAO,MAAME,OAAO,CAAClD,WAAW,CAACC,MAAM,EAAEC,OAAO,CAAC;AACnD,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMiD,oBAAoB,GAAG,MAAAA,CAAA,KAAY;EAC9C,MAAMD,OAAO,GAAGF,gBAAgB,CAAC,CAAC;EAClC,OAAO,MAAME,OAAO,CAACnC,SAAS,CAAC,CAAC;AAClC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMqC,qBAAqB,GAAGA,CAAA,KAAM;EACzC,MAAMF,OAAO,GAAGF,gBAAgB,CAAC,CAAC;EAClCE,OAAO,CAACpC,UAAU,CAAC,CAAC;AACtB,CAAC;;AAED;AACA,MAAMuC,oBAAoB,GAAG;EAC3B7E,aAAa;EACbwE,gBAAgB;EAChBC,4BAA4B;EAC5BE,oBAAoB;EACpBC;AACF,CAAC;AAED,eAAeC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}