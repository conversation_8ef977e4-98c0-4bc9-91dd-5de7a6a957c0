{"ast": null, "code": "import isBrowser from '../../../utils/isBrowser';\nexport default function isOnline() {\n  if (isBrowser && typeof navigator.onLine !== 'undefined') {\n    return navigator.onLine;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isOnline", "navigator", "onLine"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/src/utils/isOnline.js"], "sourcesContent": ["import isBrowser from '../../../utils/isBrowser';\nexport default function isOnline() {\n  if (isBrowser && typeof navigator.onLine !== 'undefined') {\n    return navigator.onLine;\n  }\n  return true;\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,0BAA0B;AAChD,eAAe,SAASC,QAAQA,CAAA,EAAG;EACjC,IAAID,SAAS,IAAI,OAAOE,SAAS,CAACC,MAAM,KAAK,WAAW,EAAE;IACxD,OAAOD,SAAS,CAACC,MAAM;EACzB;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}