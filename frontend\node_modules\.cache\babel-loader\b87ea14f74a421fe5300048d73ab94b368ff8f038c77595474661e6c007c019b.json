{"ast": null, "code": "import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport useMount from '../useMount';\nimport { isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar useDrag = function (data, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  var dataRef = useLatest(data);\n  var imageElementRef = useRef();\n  var dragImage = optionsRef.current.dragImage;\n  useMount(function () {\n    if (dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) {\n      var image = dragImage.image;\n      if (isString(image)) {\n        var imageElement = new Image();\n        imageElement.src = image;\n        imageElementRef.current = imageElement;\n      } else {\n        imageElementRef.current = image;\n      }\n    }\n  });\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onDragStart = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      event.dataTransfer.setData('custom', JSON.stringify(dataRef.current));\n      if ((dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) && imageElementRef.current) {\n        var _c = dragImage.offsetX,\n          offsetX = _c === void 0 ? 0 : _c,\n          _d = dragImage.offsetY,\n          offsetY = _d === void 0 ? 0 : _d;\n        event.dataTransfer.setDragImage(imageElementRef.current, offsetX, offsetY);\n      }\n    };\n    var onDragEnd = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.setAttribute('draggable', 'true');\n    targetElement.addEventListener('dragstart', onDragStart);\n    targetElement.addEventListener('dragend', onDragEnd);\n    return function () {\n      targetElement.removeEventListener('dragstart', onDragStart);\n      targetElement.removeEventListener('dragend', onDragEnd);\n    };\n  }, [], target);\n};\nexport default useDrag;", "map": {"version": 3, "names": ["useRef", "useLatest", "useMount", "isString", "getTargetElement", "useEffectWithTarget", "useDrag", "data", "target", "options", "optionsRef", "dataRef", "imageElementRef", "dragImage", "current", "image", "imageElement", "Image", "src", "targetElement", "addEventListener", "onDragStart", "event", "_a", "_b", "call", "dataTransfer", "setData", "JSON", "stringify", "_c", "offsetX", "_d", "offsetY", "setDragImage", "onDragEnd", "setAttribute", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDrag/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useLatest from '../useLatest';\nimport useMount from '../useMount';\nimport { isString } from '../utils';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar useDrag = function (data, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  var dataRef = useLatest(data);\n  var imageElementRef = useRef();\n  var dragImage = optionsRef.current.dragImage;\n  useMount(function () {\n    if (dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) {\n      var image = dragImage.image;\n      if (isString(image)) {\n        var imageElement = new Image();\n        imageElement.src = image;\n        imageElementRef.current = imageElement;\n      } else {\n        imageElementRef.current = image;\n      }\n    }\n  });\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onDragStart = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragStart) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      event.dataTransfer.setData('custom', JSON.stringify(dataRef.current));\n      if ((dragImage === null || dragImage === void 0 ? void 0 : dragImage.image) && imageElementRef.current) {\n        var _c = dragImage.offsetX,\n          offsetX = _c === void 0 ? 0 : _c,\n          _d = dragImage.offsetY,\n          offsetY = _d === void 0 ? 0 : _d;\n        event.dataTransfer.setDragImage(imageElementRef.current, offsetX, offsetY);\n      }\n    };\n    var onDragEnd = function (event) {\n      var _a, _b;\n      (_b = (_a = optionsRef.current).onDragEnd) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.setAttribute('draggable', 'true');\n    targetElement.addEventListener('dragstart', onDragStart);\n    targetElement.addEventListener('dragend', onDragEnd);\n    return function () {\n      targetElement.removeEventListener('dragstart', onDragStart);\n      targetElement.removeEventListener('dragend', onDragEnd);\n    };\n  }, [], target);\n};\nexport default useDrag;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,QAAQ,MAAM,aAAa;AAClC,SAASC,QAAQ,QAAQ,UAAU;AACnC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,IAAIC,OAAO,GAAG,SAAAA,CAAUC,IAAI,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC7C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,UAAU,GAAGT,SAAS,CAACQ,OAAO,CAAC;EACnC,IAAIE,OAAO,GAAGV,SAAS,CAACM,IAAI,CAAC;EAC7B,IAAIK,eAAe,GAAGZ,MAAM,CAAC,CAAC;EAC9B,IAAIa,SAAS,GAAGH,UAAU,CAACI,OAAO,CAACD,SAAS;EAC5CX,QAAQ,CAAC,YAAY;IACnB,IAAIW,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,KAAK,EAAE;MACzE,IAAIA,KAAK,GAAGF,SAAS,CAACE,KAAK;MAC3B,IAAIZ,QAAQ,CAACY,KAAK,CAAC,EAAE;QACnB,IAAIC,YAAY,GAAG,IAAIC,KAAK,CAAC,CAAC;QAC9BD,YAAY,CAACE,GAAG,GAAGH,KAAK;QACxBH,eAAe,CAACE,OAAO,GAAGE,YAAY;MACxC,CAAC,MAAM;QACLJ,eAAe,CAACE,OAAO,GAAGC,KAAK;MACjC;IACF;EACF,CAAC,CAAC;EACFV,mBAAmB,CAAC,YAAY;IAC9B,IAAIc,aAAa,GAAGf,gBAAgB,CAACI,MAAM,CAAC;IAC5C,IAAI,EAAEW,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,gBAAgB,CAAC,EAAE;MACnG;IACF;IACA,IAAIC,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACjC,IAAIC,EAAE,EAAEC,EAAE;MACV,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGb,UAAU,CAACI,OAAO,EAAEO,WAAW,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACF,EAAE,EAAED,KAAK,CAAC;MACpGA,KAAK,CAACI,YAAY,CAACC,OAAO,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAClB,OAAO,CAACG,OAAO,CAAC,CAAC;MACrE,IAAI,CAACD,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACE,KAAK,KAAKH,eAAe,CAACE,OAAO,EAAE;QACtG,IAAIgB,EAAE,GAAGjB,SAAS,CAACkB,OAAO;UACxBA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;UAChCE,EAAE,GAAGnB,SAAS,CAACoB,OAAO;UACtBA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;QAClCV,KAAK,CAACI,YAAY,CAACQ,YAAY,CAACtB,eAAe,CAACE,OAAO,EAAEiB,OAAO,EAAEE,OAAO,CAAC;MAC5E;IACF,CAAC;IACD,IAAIE,SAAS,GAAG,SAAAA,CAAUb,KAAK,EAAE;MAC/B,IAAIC,EAAE,EAAEC,EAAE;MACV,CAACA,EAAE,GAAG,CAACD,EAAE,GAAGb,UAAU,CAACI,OAAO,EAAEqB,SAAS,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACF,EAAE,EAAED,KAAK,CAAC;IACpG,CAAC;IACDH,aAAa,CAACiB,YAAY,CAAC,WAAW,EAAE,MAAM,CAAC;IAC/CjB,aAAa,CAACC,gBAAgB,CAAC,WAAW,EAAEC,WAAW,CAAC;IACxDF,aAAa,CAACC,gBAAgB,CAAC,SAAS,EAAEe,SAAS,CAAC;IACpD,OAAO,YAAY;MACjBhB,aAAa,CAACkB,mBAAmB,CAAC,WAAW,EAAEhB,WAAW,CAAC;MAC3DF,aAAa,CAACkB,mBAAmB,CAAC,SAAS,EAAEF,SAAS,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,EAAE3B,MAAM,CAAC;AAChB,CAAC;AACD,eAAeF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}