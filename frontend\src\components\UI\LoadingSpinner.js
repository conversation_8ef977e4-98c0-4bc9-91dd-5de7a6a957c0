/**
 * 加載動畫組件
 */

import React from 'react';
import { Loading } from 'antd-mobile';

const LoadingSpinner = ({ 
  size = 'medium', 
  color = 'primary', 
  text = '載入中...', 
  style = {},
  className = '',
  showText = true 
}) => {
  return (
    <div 
      className={`loading-spinner ${className}`}
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '20px',
        ...style
      }}
    >
      <Loading color={color} />
      {showText && text && (
        <div 
          style={{
            marginTop: '12px',
            fontSize: '14px',
            color: '#8c8c8c',
            textAlign: 'center'
          }}
        >
          {text}
        </div>
      )}
    </div>
  );
};

export default LoadingSpinner;
