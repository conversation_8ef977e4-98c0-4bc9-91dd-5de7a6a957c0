{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { debounce } from '../utils/lodash-polyfill';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useDebounceFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useDebounceFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var debounced = useMemo(function () {\n    return debounce(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    debounced.cancel();\n  });\n  return {\n    run: debounced,\n    cancel: debounced.cancel,\n    flush: debounced.flush\n  };\n}\nexport default useDebounceFn;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "debounce", "useMemo", "useLatest", "useUnmount", "isFunction", "isDev", "useDebounceFn", "fn", "options", "_a", "console", "error", "concat", "fnRef", "wait", "debounced", "args", "_i", "arguments", "length", "current", "apply", "cancel", "run", "flush"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDebounceFn/index.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { debounce } from '../utils/lodash-polyfill';\nimport { useMemo } from 'react';\nimport useLatest from '../useLatest';\nimport useUnmount from '../useUnmount';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nfunction useDebounceFn(fn, options) {\n  var _a;\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useDebounceFn expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  var wait = (_a = options === null || options === void 0 ? void 0 : options.wait) !== null && _a !== void 0 ? _a : 1000;\n  var debounced = useMemo(function () {\n    return debounce(function () {\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      return fnRef.current.apply(fnRef, __spreadArray([], __read(args), false));\n    }, wait, options);\n  }, []);\n  useUnmount(function () {\n    debounced.cancel();\n  });\n  return {\n    run: debounced,\n    cancel: debounced.cancel,\n    flush: debounced.flush\n  };\n}\nexport default useDebounceFn;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,OAAO,QAAQ,OAAO;AAC/B,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,KAAK,MAAM,gBAAgB;AAClC,SAASC,aAAaA,CAACC,EAAE,EAAEC,OAAO,EAAE;EAClC,IAAIC,EAAE;EACN,IAAIJ,KAAK,EAAE;IACT,IAAI,CAACD,UAAU,CAACG,EAAE,CAAC,EAAE;MACnBG,OAAO,CAACC,KAAK,CAAC,sDAAsD,CAACC,MAAM,CAAC,OAAOL,EAAE,CAAC,CAAC;IACzF;EACF;EACA,IAAIM,KAAK,GAAGX,SAAS,CAACK,EAAE,CAAC;EACzB,IAAIO,IAAI,GAAG,CAACL,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACM,IAAI,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI;EACtH,IAAIM,SAAS,GAAGd,OAAO,CAAC,YAAY;IAClC,OAAOD,QAAQ,CAAC,YAAY;MAC1B,IAAIgB,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC1B;MACA,OAAOJ,KAAK,CAACO,OAAO,CAACC,KAAK,CAACR,KAAK,EAAEd,aAAa,CAAC,EAAE,EAAED,MAAM,CAACkB,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAC3E,CAAC,EAAEF,IAAI,EAAEN,OAAO,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EACNL,UAAU,CAAC,YAAY;IACrBY,SAAS,CAACO,MAAM,CAAC,CAAC;EACpB,CAAC,CAAC;EACF,OAAO;IACLC,GAAG,EAAER,SAAS;IACdO,MAAM,EAAEP,SAAS,CAACO,MAAM;IACxBE,KAAK,EAAET,SAAS,CAACS;EACnB,CAAC;AACH;AACA,eAAelB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}