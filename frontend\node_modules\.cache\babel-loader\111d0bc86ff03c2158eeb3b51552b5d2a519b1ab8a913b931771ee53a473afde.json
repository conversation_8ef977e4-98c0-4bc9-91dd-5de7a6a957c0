{"ast": null, "code": "import { useRef } from 'react';\nvar defaultShouldUpdate = function (a, b) {\n  return !Object.is(a, b);\n};\nfunction usePrevious(state, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = defaultShouldUpdate;\n  }\n  var prevRef = useRef();\n  var curRef = useRef();\n  if (shouldUpdate(curRef.current, state)) {\n    prevRef.current = curRef.current;\n    curRef.current = state;\n  }\n  return prevRef.current;\n}\nexport default usePrevious;", "map": {"version": 3, "names": ["useRef", "defaultShouldUpdate", "a", "b", "Object", "is", "usePrevious", "state", "shouldUpdate", "prevRef", "curRef", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/usePrevious/index.js"], "sourcesContent": ["import { useRef } from 'react';\nvar defaultShouldUpdate = function (a, b) {\n  return !Object.is(a, b);\n};\nfunction usePrevious(state, shouldUpdate) {\n  if (shouldUpdate === void 0) {\n    shouldUpdate = defaultShouldUpdate;\n  }\n  var prevRef = useRef();\n  var curRef = useRef();\n  if (shouldUpdate(curRef.current, state)) {\n    prevRef.current = curRef.current;\n    curRef.current = state;\n  }\n  return prevRef.current;\n}\nexport default usePrevious;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,IAAIC,mBAAmB,GAAG,SAAAA,CAAUC,CAAC,EAAEC,CAAC,EAAE;EACxC,OAAO,CAACC,MAAM,CAACC,EAAE,CAACH,CAAC,EAAEC,CAAC,CAAC;AACzB,CAAC;AACD,SAASG,WAAWA,CAACC,KAAK,EAAEC,YAAY,EAAE;EACxC,IAAIA,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAGP,mBAAmB;EACpC;EACA,IAAIQ,OAAO,GAAGT,MAAM,CAAC,CAAC;EACtB,IAAIU,MAAM,GAAGV,MAAM,CAAC,CAAC;EACrB,IAAIQ,YAAY,CAACE,MAAM,CAACC,OAAO,EAAEJ,KAAK,CAAC,EAAE;IACvCE,OAAO,CAACE,OAAO,GAAGD,MAAM,CAACC,OAAO;IAChCD,MAAM,CAACC,OAAO,GAAGJ,KAAK;EACxB;EACA,OAAOE,OAAO,CAACE,OAAO;AACxB;AACA,eAAeL,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}