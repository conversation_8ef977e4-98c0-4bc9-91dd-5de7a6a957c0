{"ast": null, "code": "import { __assign } from \"tslib\";\nimport { useEffect, useRef } from 'react';\nexport default function useWhyDidYouUpdate(componentName, props) {\n  var prevProps = useRef({});\n  useEffect(function () {\n    if (prevProps.current) {\n      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));\n      var changedProps_1 = {};\n      allKeys.forEach(function (key) {\n        if (!Object.is(prevProps.current[key], props[key])) {\n          changedProps_1[key] = {\n            from: prevProps.current[key],\n            to: props[key]\n          };\n        }\n      });\n      if (Object.keys(changedProps_1).length) {\n        console.log('[why-did-you-update]', componentName, changedProps_1);\n      }\n    }\n    prevProps.current = props;\n  });\n}", "map": {"version": 3, "names": ["__assign", "useEffect", "useRef", "useWhyDidYouUpdate", "componentName", "props", "prevProps", "current", "allKeys", "Object", "keys", "changedProps_1", "for<PERSON>ach", "key", "is", "from", "to", "length", "console", "log"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useWhyDidYouUpdate/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport { useEffect, useRef } from 'react';\nexport default function useWhyDidYouUpdate(componentName, props) {\n  var prevProps = useRef({});\n  useEffect(function () {\n    if (prevProps.current) {\n      var allKeys = Object.keys(__assign(__assign({}, prevProps.current), props));\n      var changedProps_1 = {};\n      allKeys.forEach(function (key) {\n        if (!Object.is(prevProps.current[key], props[key])) {\n          changedProps_1[key] = {\n            from: prevProps.current[key],\n            to: props[key]\n          };\n        }\n      });\n      if (Object.keys(changedProps_1).length) {\n        console.log('[why-did-you-update]', componentName, changedProps_1);\n      }\n    }\n    prevProps.current = props;\n  });\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,eAAe,SAASC,kBAAkBA,CAACC,aAAa,EAAEC,KAAK,EAAE;EAC/D,IAAIC,SAAS,GAAGJ,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1BD,SAAS,CAAC,YAAY;IACpB,IAAIK,SAAS,CAACC,OAAO,EAAE;MACrB,IAAIC,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACV,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEM,SAAS,CAACC,OAAO,CAAC,EAAEF,KAAK,CAAC,CAAC;MAC3E,IAAIM,cAAc,GAAG,CAAC,CAAC;MACvBH,OAAO,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;QAC7B,IAAI,CAACJ,MAAM,CAACK,EAAE,CAACR,SAAS,CAACC,OAAO,CAACM,GAAG,CAAC,EAAER,KAAK,CAACQ,GAAG,CAAC,CAAC,EAAE;UAClDF,cAAc,CAACE,GAAG,CAAC,GAAG;YACpBE,IAAI,EAAET,SAAS,CAACC,OAAO,CAACM,GAAG,CAAC;YAC5BG,EAAE,EAAEX,KAAK,CAACQ,GAAG;UACf,CAAC;QACH;MACF,CAAC,CAAC;MACF,IAAIJ,MAAM,CAACC,IAAI,CAACC,cAAc,CAAC,CAACM,MAAM,EAAE;QACtCC,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAEf,aAAa,EAAEO,cAAc,CAAC;MACpE;IACF;IACAL,SAAS,CAACC,OAAO,GAAGF,KAAK;EAC3B,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}