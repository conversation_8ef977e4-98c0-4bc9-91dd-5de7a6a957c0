{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport getDocumentOrShadow from '../utils/getDocumentOrShadow';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nexport default function useClickAway(onClickAway, target, eventName) {\n  if (eventName === void 0) {\n    eventName = 'click';\n  }\n  var onClickAwayRef = useLatest(onClickAway);\n  useEffectWithTarget(function () {\n    var handler = function (event) {\n      var targets = Array.isArray(target) ? target : [target];\n      if (targets.some(function (item) {\n        var targetElement = getTargetElement(item);\n        return !targetElement || targetElement.contains(event.target);\n      })) {\n        return;\n      }\n      onClickAwayRef.current(event);\n    };\n    var documentOrShadow = getDocumentOrShadow(target);\n    var eventNames = Array.isArray(eventName) ? eventName : [eventName];\n    eventNames.forEach(function (event) {\n      return documentOrShadow.addEventListener(event, handler);\n    });\n    return function () {\n      eventNames.forEach(function (event) {\n        return documentOrShadow.removeEventListener(event, handler);\n      });\n    };\n  }, Array.isArray(eventName) ? eventName : [eventName], target);\n}", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "getDocumentOrShadow", "useEffectWithTarget", "useClickAway", "onClickAway", "target", "eventName", "onClickAwayRef", "handler", "event", "targets", "Array", "isArray", "some", "item", "targetElement", "contains", "current", "documentOrShadow", "eventNames", "for<PERSON>ach", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useClickAway/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport getDocumentOrShadow from '../utils/getDocumentOrShadow';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nexport default function useClickAway(onClickAway, target, eventName) {\n  if (eventName === void 0) {\n    eventName = 'click';\n  }\n  var onClickAwayRef = useLatest(onClickAway);\n  useEffectWithTarget(function () {\n    var handler = function (event) {\n      var targets = Array.isArray(target) ? target : [target];\n      if (targets.some(function (item) {\n        var targetElement = getTargetElement(item);\n        return !targetElement || targetElement.contains(event.target);\n      })) {\n        return;\n      }\n      onClickAwayRef.current(event);\n    };\n    var documentOrShadow = getDocumentOrShadow(target);\n    var eventNames = Array.isArray(eventName) ? eventName : [eventName];\n    eventNames.forEach(function (event) {\n      return documentOrShadow.addEventListener(event, handler);\n    });\n    return function () {\n      eventNames.forEach(function (event) {\n        return documentOrShadow.removeEventListener(event, handler);\n      });\n    };\n  }, Array.isArray(eventName) ? eventName : [eventName], target);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,cAAc;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,eAAe,SAASC,YAAYA,CAACC,WAAW,EAAEC,MAAM,EAAEC,SAAS,EAAE;EACnE,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;IACxBA,SAAS,GAAG,OAAO;EACrB;EACA,IAAIC,cAAc,GAAGR,SAAS,CAACK,WAAW,CAAC;EAC3CF,mBAAmB,CAAC,YAAY;IAC9B,IAAIM,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;MAC7B,IAAIC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACP,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;MACvD,IAAIK,OAAO,CAACG,IAAI,CAAC,UAAUC,IAAI,EAAE;QAC/B,IAAIC,aAAa,GAAGf,gBAAgB,CAACc,IAAI,CAAC;QAC1C,OAAO,CAACC,aAAa,IAAIA,aAAa,CAACC,QAAQ,CAACP,KAAK,CAACJ,MAAM,CAAC;MAC/D,CAAC,CAAC,EAAE;QACF;MACF;MACAE,cAAc,CAACU,OAAO,CAACR,KAAK,CAAC;IAC/B,CAAC;IACD,IAAIS,gBAAgB,GAAGjB,mBAAmB,CAACI,MAAM,CAAC;IAClD,IAAIc,UAAU,GAAGR,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IACnEa,UAAU,CAACC,OAAO,CAAC,UAAUX,KAAK,EAAE;MAClC,OAAOS,gBAAgB,CAACG,gBAAgB,CAACZ,KAAK,EAAED,OAAO,CAAC;IAC1D,CAAC,CAAC;IACF,OAAO,YAAY;MACjBW,UAAU,CAACC,OAAO,CAAC,UAAUX,KAAK,EAAE;QAClC,OAAOS,gBAAgB,CAACI,mBAAmB,CAACb,KAAK,EAAED,OAAO,CAAC;MAC7D,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAEG,KAAK,CAACC,OAAO,CAACN,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC,EAAED,MAAM,CAAC;AAChE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}