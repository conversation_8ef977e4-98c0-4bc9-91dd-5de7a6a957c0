{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\UI\\\\LoadingSpinner.js\";\n/**\n * 加載動畫組件\n */\n\nimport React from 'react';\nimport { Loading } from 'antd-mobile';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst LoadingSpinner = ({\n  size = 'medium',\n  color = 'primary',\n  text = '載入中...',\n  style = {},\n  className = '',\n  showText = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `loading-spinner ${className}`,\n    style: {\n      display: 'flex',\n      flexDirection: 'column',\n      alignItems: 'center',\n      justifyContent: 'center',\n      padding: '20px',\n      ...style\n    },\n    children: [/*#__PURE__*/_jsxDEV(Loading, {\n      color: color\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), showText && text && /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '12px',\n        fontSize: '14px',\n        color: '#8c8c8c',\n        textAlign: 'center'\n      },\n      children: text\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 30,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = LoadingSpinner;\nexport default LoadingSpinner;\nvar _c;\n$RefreshReg$(_c, \"LoadingSpinner\");", "map": {"version": 3, "names": ["React", "Loading", "jsxDEV", "_jsxDEV", "LoadingSpinner", "size", "color", "text", "style", "className", "showText", "display", "flexDirection", "alignItems", "justifyContent", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginTop", "fontSize", "textAlign", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/UI/LoadingSpinner.js"], "sourcesContent": ["/**\n * 加載動畫組件\n */\n\nimport React from 'react';\nimport { Loading } from 'antd-mobile';\n\nconst LoadingSpinner = ({ \n  size = 'medium', \n  color = 'primary', \n  text = '載入中...', \n  style = {},\n  className = '',\n  showText = true \n}) => {\n  return (\n    <div \n      className={`loading-spinner ${className}`}\n      style={{\n        display: 'flex',\n        flexDirection: 'column',\n        alignItems: 'center',\n        justifyContent: 'center',\n        padding: '20px',\n        ...style\n      }}\n    >\n      <Loading color={color} />\n      {showText && text && (\n        <div \n          style={{\n            marginTop: '12px',\n            fontSize: '14px',\n            color: '#8c8c8c',\n            textAlign: 'center'\n          }}\n        >\n          {text}\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,cAAc,GAAGA,CAAC;EACtBC,IAAI,GAAG,QAAQ;EACfC,KAAK,GAAG,SAAS;EACjBC,IAAI,GAAG,QAAQ;EACfC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,oBACEP,OAAA;IACEM,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAC1CD,KAAK,EAAE;MACLG,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,UAAU,EAAE,QAAQ;MACpBC,cAAc,EAAE,QAAQ;MACxBC,OAAO,EAAE,MAAM;MACf,GAAGP;IACL,CAAE;IAAAQ,QAAA,gBAEFb,OAAA,CAACF,OAAO;MAACK,KAAK,EAAEA;IAAM;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,EACxBV,QAAQ,IAAIH,IAAI,iBACfJ,OAAA;MACEK,KAAK,EAAE;QACLa,SAAS,EAAE,MAAM;QACjBC,QAAQ,EAAE,MAAM;QAChBhB,KAAK,EAAE,SAAS;QAChBiB,SAAS,EAAE;MACb,CAAE;MAAAP,QAAA,EAEDT;IAAI;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CACN;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACE,CAAC;AAEV,CAAC;AAACI,EAAA,GAnCIpB,cAAc;AAqCpB,eAAeA,cAAc;AAAC,IAAAoB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}