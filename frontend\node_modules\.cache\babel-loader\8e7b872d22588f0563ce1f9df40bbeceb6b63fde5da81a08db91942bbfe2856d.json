{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useUnmountedRef from '../useUnmountedRef';\nfunction useSafeState(initialState) {\n  var unmountedRef = useUnmountedRef();\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setCurrentState = useCallback(function (currentState) {\n    /** if component is unmounted, stop update */\n    if (unmountedRef.current) return;\n    setState(currentState);\n  }, []);\n  return [state, setCurrentState];\n}\nexport default useSafeState;", "map": {"version": 3, "names": ["__read", "useCallback", "useState", "useUnmountedRef", "useSafeState", "initialState", "unmountedRef", "_a", "state", "setState", "setCurrentState", "currentState", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useSafeState/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useUnmountedRef from '../useUnmountedRef';\nfunction useSafeState(initialState) {\n  var unmountedRef = useUnmountedRef();\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setCurrentState = useCallback(function (currentState) {\n    /** if component is unmounted, stop update */\n    if (unmountedRef.current) return;\n    setState(currentState);\n  }, []);\n  return [state, setCurrentState];\n}\nexport default useSafeState;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAC7C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,YAAYA,CAACC,YAAY,EAAE;EAClC,IAAIC,YAAY,GAAGH,eAAe,CAAC,CAAC;EACpC,IAAII,EAAE,GAAGP,MAAM,CAACE,QAAQ,CAACG,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCG,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,eAAe,GAAGT,WAAW,CAAC,UAAUU,YAAY,EAAE;IACxD;IACA,IAAIL,YAAY,CAACM,OAAO,EAAE;IAC1BH,QAAQ,CAACE,YAAY,CAAC;EACxB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACH,KAAK,EAAEE,eAAe,CAAC;AACjC;AACA,eAAeN,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}