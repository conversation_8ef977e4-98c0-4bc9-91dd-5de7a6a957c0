{"ast": null, "code": "import { __assign, __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef, useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { getTargetElement } from '../utils/domTarget';\nimport { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';\nvar useInfiniteScroll = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var target = options.target,\n    isNoMore = options.isNoMore,\n    _a = options.threshold,\n    threshold = _a === void 0 ? 100 : _a,\n    _b = options.direction,\n    direction = _b === void 0 ? 'bottom' : _b,\n    _c = options.reloadDeps,\n    reloadDeps = _c === void 0 ? [] : _c,\n    manual = options.manual,\n    onBefore = options.onBefore,\n    onSuccess = options.onSuccess,\n    onError = options.onError,\n    onFinally = options.onFinally;\n  var _d = __read(useState(), 2),\n    finalData = _d[0],\n    setFinalData = _d[1];\n  var _e = __read(useState(false), 2),\n    loadingMore = _e[0],\n    setLoadingMore = _e[1];\n  var isScrollToTop = direction === 'top';\n  // lastScrollTop is used to determine whether the scroll direction is up or down\n  var lastScrollTop = useRef();\n  // scrollBottom is used to record the distance from the bottom of the scroll bar\n  var scrollBottom = useRef(0);\n  var noMore = useMemo(function () {\n    if (!isNoMore) return false;\n    return isNoMore(finalData);\n  }, [finalData]);\n  var _f = useRequest(function (lastData) {\n      return __awaiter(void 0, void 0, void 0, function () {\n        var currentData;\n        var _a, _b, _c;\n        return __generator(this, function (_d) {\n          switch (_d.label) {\n            case 0:\n              return [4 /*yield*/, service(lastData)];\n            case 1:\n              currentData = _d.sent();\n              if (!lastData) {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: __spreadArray([], __read((_a = currentData.list) !== null && _a !== void 0 ? _a : []), false)\n                }));\n              } else {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: isScrollToTop ? __spreadArray(__spreadArray([], __read(currentData.list), false), __read((_b = lastData.list) !== null && _b !== void 0 ? _b : []), false) : __spreadArray(__spreadArray([], __read((_c = lastData.list) !== null && _c !== void 0 ? _c : []), false), __read(currentData.list), false)\n                }));\n              }\n              return [2 /*return*/, currentData];\n          }\n        });\n      });\n    }, {\n      manual: manual,\n      onFinally: function (_, d, e) {\n        setLoadingMore(false);\n        onFinally === null || onFinally === void 0 ? void 0 : onFinally(d, e);\n      },\n      onBefore: function () {\n        return onBefore === null || onBefore === void 0 ? void 0 : onBefore();\n      },\n      onSuccess: function (d) {\n        setTimeout(function () {\n          if (isScrollToTop) {\n            var el = getTargetElement(target);\n            el = el === document ? document.documentElement : el;\n            if (el) {\n              var scrollHeight = getScrollHeight(el);\n              el.scrollTo(0, scrollHeight - scrollBottom.current);\n            }\n          } else {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            scrollMethod();\n          }\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(d);\n      },\n      onError: function (e) {\n        return onError === null || onError === void 0 ? void 0 : onError(e);\n      }\n    }),\n    loading = _f.loading,\n    error = _f.error,\n    run = _f.run,\n    runAsync = _f.runAsync,\n    cancel = _f.cancel;\n  var loadMore = useMemoizedFn(function () {\n    if (noMore) return;\n    setLoadingMore(true);\n    run(finalData);\n  });\n  var loadMoreAsync = useMemoizedFn(function () {\n    if (noMore) return Promise.reject();\n    setLoadingMore(true);\n    return runAsync(finalData);\n  });\n  var reload = function () {\n    setLoadingMore(false);\n    return run();\n  };\n  var reloadAsync = function () {\n    setLoadingMore(false);\n    return runAsync();\n  };\n  var scrollMethod = function () {\n    var el = getTargetElement(target);\n    if (!el) return;\n    var targetEl = el === document ? document.documentElement : el;\n    var scrollTop = getScrollTop(targetEl);\n    var scrollHeight = getScrollHeight(targetEl);\n    var clientHeight = getClientHeight(targetEl);\n    if (isScrollToTop) {\n      if (lastScrollTop.current !== undefined && lastScrollTop.current > scrollTop && scrollTop <= threshold) {\n        loadMore();\n      }\n      lastScrollTop.current = scrollTop;\n      scrollBottom.current = scrollHeight - scrollTop;\n    } else if (scrollHeight - scrollTop <= clientHeight + threshold) {\n      loadMore();\n    }\n  };\n  useEventListener('scroll', function () {\n    if (loading || loadingMore) {\n      return;\n    }\n    scrollMethod();\n  }, {\n    target: target\n  });\n  useUpdateEffect(function () {\n    run();\n  }, __spreadArray([], __read(reloadDeps), false));\n  return {\n    data: finalData,\n    loading: !loadingMore && loading,\n    error: error,\n    loadingMore: loadingMore,\n    noMore: noMore,\n    loadMore: loadMore,\n    loadMoreAsync: loadMoreAsync,\n    reload: useMemoizedFn(reload),\n    reloadAsync: useMemoizedFn(reloadAsync),\n    mutate: setFinalData,\n    cancel: cancel\n  };\n};\nexport default useInfiniteScroll;", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "__read", "__spread<PERSON><PERSON>y", "useMemo", "useRef", "useState", "useEventListener", "useMemoizedFn", "useRequest", "useUpdateEffect", "getTargetElement", "getClientHeight", "getScrollHeight", "getScrollTop", "useInfiniteScroll", "service", "options", "target", "isNoMore", "_a", "threshold", "_b", "direction", "_c", "reloadDeps", "manual", "onBefore", "onSuccess", "onError", "onFinally", "_d", "finalData", "setFinalData", "_e", "loadingMore", "setLoadingMore", "isScrollToTop", "lastScrollTop", "scrollBottom", "noMore", "_f", "lastData", "currentData", "label", "sent", "list", "_", "d", "e", "setTimeout", "el", "document", "documentElement", "scrollHeight", "scrollTo", "current", "scrollMethod", "loading", "error", "run", "runAsync", "cancel", "loadMore", "loadMoreAsync", "Promise", "reject", "reload", "reloadAsync", "targetEl", "scrollTop", "clientHeight", "undefined", "data", "mutate"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useInfiniteScroll/index.js"], "sourcesContent": ["import { __assign, __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef, useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useRequest from '../useRequest';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { getTargetElement } from '../utils/domTarget';\nimport { getClientHeight, getScrollHeight, getScrollTop } from '../utils/rect';\nvar useInfiniteScroll = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var target = options.target,\n    isNoMore = options.isNoMore,\n    _a = options.threshold,\n    threshold = _a === void 0 ? 100 : _a,\n    _b = options.direction,\n    direction = _b === void 0 ? 'bottom' : _b,\n    _c = options.reloadDeps,\n    reloadDeps = _c === void 0 ? [] : _c,\n    manual = options.manual,\n    onBefore = options.onBefore,\n    onSuccess = options.onSuccess,\n    onError = options.onError,\n    onFinally = options.onFinally;\n  var _d = __read(useState(), 2),\n    finalData = _d[0],\n    setFinalData = _d[1];\n  var _e = __read(useState(false), 2),\n    loadingMore = _e[0],\n    setLoadingMore = _e[1];\n  var isScrollToTop = direction === 'top';\n  // lastScrollTop is used to determine whether the scroll direction is up or down\n  var lastScrollTop = useRef();\n  // scrollBottom is used to record the distance from the bottom of the scroll bar\n  var scrollBottom = useRef(0);\n  var noMore = useMemo(function () {\n    if (!isNoMore) return false;\n    return isNoMore(finalData);\n  }, [finalData]);\n  var _f = useRequest(function (lastData) {\n      return __awaiter(void 0, void 0, void 0, function () {\n        var currentData;\n        var _a, _b, _c;\n        return __generator(this, function (_d) {\n          switch (_d.label) {\n            case 0:\n              return [4 /*yield*/, service(lastData)];\n            case 1:\n              currentData = _d.sent();\n              if (!lastData) {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: __spreadArray([], __read((_a = currentData.list) !== null && _a !== void 0 ? _a : []), false)\n                }));\n              } else {\n                setFinalData(__assign(__assign({}, currentData), {\n                  list: isScrollToTop ? __spreadArray(__spreadArray([], __read(currentData.list), false), __read((_b = lastData.list) !== null && _b !== void 0 ? _b : []), false) : __spreadArray(__spreadArray([], __read((_c = lastData.list) !== null && _c !== void 0 ? _c : []), false), __read(currentData.list), false)\n                }));\n              }\n              return [2 /*return*/, currentData];\n          }\n        });\n      });\n    }, {\n      manual: manual,\n      onFinally: function (_, d, e) {\n        setLoadingMore(false);\n        onFinally === null || onFinally === void 0 ? void 0 : onFinally(d, e);\n      },\n      onBefore: function () {\n        return onBefore === null || onBefore === void 0 ? void 0 : onBefore();\n      },\n      onSuccess: function (d) {\n        setTimeout(function () {\n          if (isScrollToTop) {\n            var el = getTargetElement(target);\n            el = el === document ? document.documentElement : el;\n            if (el) {\n              var scrollHeight = getScrollHeight(el);\n              el.scrollTo(0, scrollHeight - scrollBottom.current);\n            }\n          } else {\n            // eslint-disable-next-line @typescript-eslint/no-use-before-define\n            scrollMethod();\n          }\n        });\n        onSuccess === null || onSuccess === void 0 ? void 0 : onSuccess(d);\n      },\n      onError: function (e) {\n        return onError === null || onError === void 0 ? void 0 : onError(e);\n      }\n    }),\n    loading = _f.loading,\n    error = _f.error,\n    run = _f.run,\n    runAsync = _f.runAsync,\n    cancel = _f.cancel;\n  var loadMore = useMemoizedFn(function () {\n    if (noMore) return;\n    setLoadingMore(true);\n    run(finalData);\n  });\n  var loadMoreAsync = useMemoizedFn(function () {\n    if (noMore) return Promise.reject();\n    setLoadingMore(true);\n    return runAsync(finalData);\n  });\n  var reload = function () {\n    setLoadingMore(false);\n    return run();\n  };\n  var reloadAsync = function () {\n    setLoadingMore(false);\n    return runAsync();\n  };\n  var scrollMethod = function () {\n    var el = getTargetElement(target);\n    if (!el) return;\n    var targetEl = el === document ? document.documentElement : el;\n    var scrollTop = getScrollTop(targetEl);\n    var scrollHeight = getScrollHeight(targetEl);\n    var clientHeight = getClientHeight(targetEl);\n    if (isScrollToTop) {\n      if (lastScrollTop.current !== undefined && lastScrollTop.current > scrollTop && scrollTop <= threshold) {\n        loadMore();\n      }\n      lastScrollTop.current = scrollTop;\n      scrollBottom.current = scrollHeight - scrollTop;\n    } else if (scrollHeight - scrollTop <= clientHeight + threshold) {\n      loadMore();\n    }\n  };\n  useEventListener('scroll', function () {\n    if (loading || loadingMore) {\n      return;\n    }\n    scrollMethod();\n  }, {\n    target: target\n  });\n  useUpdateEffect(function () {\n    run();\n  }, __spreadArray([], __read(reloadDeps), false));\n  return {\n    data: finalData,\n    loading: !loadingMore && loading,\n    error: error,\n    loadingMore: loadingMore,\n    noMore: noMore,\n    loadMore: loadMore,\n    loadMoreAsync: loadMoreAsync,\n    reload: useMemoizedFn(reload),\n    reloadAsync: useMemoizedFn(reloadAsync),\n    mutate: setFinalData,\n    cancel: cancel\n  };\n};\nexport default useInfiniteScroll;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC/E,SAASC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACjD,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,eAAe,EAAEC,eAAe,EAAEC,YAAY,QAAQ,eAAe;AAC9E,IAAIC,iBAAiB,GAAG,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;EAClD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,MAAM,GAAGD,OAAO,CAACC,MAAM;IACzBC,QAAQ,GAAGF,OAAO,CAACE,QAAQ;IAC3BC,EAAE,GAAGH,OAAO,CAACI,SAAS;IACtBA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,GAAG,GAAGA,EAAE;IACpCE,EAAE,GAAGL,OAAO,CAACM,SAAS;IACtBA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,EAAE;IACzCE,EAAE,GAAGP,OAAO,CAACQ,UAAU;IACvBA,UAAU,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACpCE,MAAM,GAAGT,OAAO,CAACS,MAAM;IACvBC,QAAQ,GAAGV,OAAO,CAACU,QAAQ;IAC3BC,SAAS,GAAGX,OAAO,CAACW,SAAS;IAC7BC,OAAO,GAAGZ,OAAO,CAACY,OAAO;IACzBC,SAAS,GAAGb,OAAO,CAACa,SAAS;EAC/B,IAAIC,EAAE,GAAG7B,MAAM,CAACI,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B0B,SAAS,GAAGD,EAAE,CAAC,CAAC,CAAC;IACjBE,YAAY,GAAGF,EAAE,CAAC,CAAC,CAAC;EACtB,IAAIG,EAAE,GAAGhC,MAAM,CAACI,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACjC6B,WAAW,GAAGD,EAAE,CAAC,CAAC,CAAC;IACnBE,cAAc,GAAGF,EAAE,CAAC,CAAC,CAAC;EACxB,IAAIG,aAAa,GAAGd,SAAS,KAAK,KAAK;EACvC;EACA,IAAIe,aAAa,GAAGjC,MAAM,CAAC,CAAC;EAC5B;EACA,IAAIkC,YAAY,GAAGlC,MAAM,CAAC,CAAC,CAAC;EAC5B,IAAImC,MAAM,GAAGpC,OAAO,CAAC,YAAY;IAC/B,IAAI,CAACe,QAAQ,EAAE,OAAO,KAAK;IAC3B,OAAOA,QAAQ,CAACa,SAAS,CAAC;EAC5B,CAAC,EAAE,CAACA,SAAS,CAAC,CAAC;EACf,IAAIS,EAAE,GAAGhC,UAAU,CAAC,UAAUiC,QAAQ,EAAE;MACpC,OAAO1C,SAAS,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;QACnD,IAAI2C,WAAW;QACf,IAAIvB,EAAE,EAAEE,EAAE,EAAEE,EAAE;QACd,OAAOvB,WAAW,CAAC,IAAI,EAAE,UAAU8B,EAAE,EAAE;UACrC,QAAQA,EAAE,CAACa,KAAK;YACd,KAAK,CAAC;cACJ,OAAO,CAAC,CAAC,CAAC,WAAW5B,OAAO,CAAC0B,QAAQ,CAAC,CAAC;YACzC,KAAK,CAAC;cACJC,WAAW,GAAGZ,EAAE,CAACc,IAAI,CAAC,CAAC;cACvB,IAAI,CAACH,QAAQ,EAAE;gBACbT,YAAY,CAAClC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4C,WAAW,CAAC,EAAE;kBAC/CG,IAAI,EAAE3C,aAAa,CAAC,EAAE,EAAED,MAAM,CAAC,CAACkB,EAAE,GAAGuB,WAAW,CAACG,IAAI,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK;gBACpG,CAAC,CAAC,CAAC;cACL,CAAC,MAAM;gBACLa,YAAY,CAAClC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE4C,WAAW,CAAC,EAAE;kBAC/CG,IAAI,EAAET,aAAa,GAAGlC,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAACyC,WAAW,CAACG,IAAI,CAAC,EAAE,KAAK,CAAC,EAAE5C,MAAM,CAAC,CAACoB,EAAE,GAAGoB,QAAQ,CAACI,IAAI,MAAM,IAAI,IAAIxB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,GAAGnB,aAAa,CAACA,aAAa,CAAC,EAAE,EAAED,MAAM,CAAC,CAACsB,EAAE,GAAGkB,QAAQ,CAACI,IAAI,MAAM,IAAI,IAAItB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,EAAE,CAAC,EAAE,KAAK,CAAC,EAAEtB,MAAM,CAACyC,WAAW,CAACG,IAAI,CAAC,EAAE,KAAK;gBAC9S,CAAC,CAAC,CAAC;cACL;cACA,OAAO,CAAC,CAAC,CAAC,YAAYH,WAAW,CAAC;UACtC;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,EAAE;MACDjB,MAAM,EAAEA,MAAM;MACdI,SAAS,EAAE,SAAAA,CAAUiB,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAE;QAC5Bb,cAAc,CAAC,KAAK,CAAC;QACrBN,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACkB,CAAC,EAAEC,CAAC,CAAC;MACvE,CAAC;MACDtB,QAAQ,EAAE,SAAAA,CAAA,EAAY;QACpB,OAAOA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,CAAC;MACvE,CAAC;MACDC,SAAS,EAAE,SAAAA,CAAUoB,CAAC,EAAE;QACtBE,UAAU,CAAC,YAAY;UACrB,IAAIb,aAAa,EAAE;YACjB,IAAIc,EAAE,GAAGxC,gBAAgB,CAACO,MAAM,CAAC;YACjCiC,EAAE,GAAGA,EAAE,KAAKC,QAAQ,GAAGA,QAAQ,CAACC,eAAe,GAAGF,EAAE;YACpD,IAAIA,EAAE,EAAE;cACN,IAAIG,YAAY,GAAGzC,eAAe,CAACsC,EAAE,CAAC;cACtCA,EAAE,CAACI,QAAQ,CAAC,CAAC,EAAED,YAAY,GAAGf,YAAY,CAACiB,OAAO,CAAC;YACrD;UACF,CAAC,MAAM;YACL;YACAC,YAAY,CAAC,CAAC;UAChB;QACF,CAAC,CAAC;QACF7B,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACoB,CAAC,CAAC;MACpE,CAAC;MACDnB,OAAO,EAAE,SAAAA,CAAUoB,CAAC,EAAE;QACpB,OAAOpB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoB,CAAC,CAAC;MACrE;IACF,CAAC,CAAC;IACFS,OAAO,GAAGjB,EAAE,CAACiB,OAAO;IACpBC,KAAK,GAAGlB,EAAE,CAACkB,KAAK;IAChBC,GAAG,GAAGnB,EAAE,CAACmB,GAAG;IACZC,QAAQ,GAAGpB,EAAE,CAACoB,QAAQ;IACtBC,MAAM,GAAGrB,EAAE,CAACqB,MAAM;EACpB,IAAIC,QAAQ,GAAGvD,aAAa,CAAC,YAAY;IACvC,IAAIgC,MAAM,EAAE;IACZJ,cAAc,CAAC,IAAI,CAAC;IACpBwB,GAAG,CAAC5B,SAAS,CAAC;EAChB,CAAC,CAAC;EACF,IAAIgC,aAAa,GAAGxD,aAAa,CAAC,YAAY;IAC5C,IAAIgC,MAAM,EAAE,OAAOyB,OAAO,CAACC,MAAM,CAAC,CAAC;IACnC9B,cAAc,CAAC,IAAI,CAAC;IACpB,OAAOyB,QAAQ,CAAC7B,SAAS,CAAC;EAC5B,CAAC,CAAC;EACF,IAAImC,MAAM,GAAG,SAAAA,CAAA,EAAY;IACvB/B,cAAc,CAAC,KAAK,CAAC;IACrB,OAAOwB,GAAG,CAAC,CAAC;EACd,CAAC;EACD,IAAIQ,WAAW,GAAG,SAAAA,CAAA,EAAY;IAC5BhC,cAAc,CAAC,KAAK,CAAC;IACrB,OAAOyB,QAAQ,CAAC,CAAC;EACnB,CAAC;EACD,IAAIJ,YAAY,GAAG,SAAAA,CAAA,EAAY;IAC7B,IAAIN,EAAE,GAAGxC,gBAAgB,CAACO,MAAM,CAAC;IACjC,IAAI,CAACiC,EAAE,EAAE;IACT,IAAIkB,QAAQ,GAAGlB,EAAE,KAAKC,QAAQ,GAAGA,QAAQ,CAACC,eAAe,GAAGF,EAAE;IAC9D,IAAImB,SAAS,GAAGxD,YAAY,CAACuD,QAAQ,CAAC;IACtC,IAAIf,YAAY,GAAGzC,eAAe,CAACwD,QAAQ,CAAC;IAC5C,IAAIE,YAAY,GAAG3D,eAAe,CAACyD,QAAQ,CAAC;IAC5C,IAAIhC,aAAa,EAAE;MACjB,IAAIC,aAAa,CAACkB,OAAO,KAAKgB,SAAS,IAAIlC,aAAa,CAACkB,OAAO,GAAGc,SAAS,IAAIA,SAAS,IAAIjD,SAAS,EAAE;QACtG0C,QAAQ,CAAC,CAAC;MACZ;MACAzB,aAAa,CAACkB,OAAO,GAAGc,SAAS;MACjC/B,YAAY,CAACiB,OAAO,GAAGF,YAAY,GAAGgB,SAAS;IACjD,CAAC,MAAM,IAAIhB,YAAY,GAAGgB,SAAS,IAAIC,YAAY,GAAGlD,SAAS,EAAE;MAC/D0C,QAAQ,CAAC,CAAC;IACZ;EACF,CAAC;EACDxD,gBAAgB,CAAC,QAAQ,EAAE,YAAY;IACrC,IAAImD,OAAO,IAAIvB,WAAW,EAAE;MAC1B;IACF;IACAsB,YAAY,CAAC,CAAC;EAChB,CAAC,EAAE;IACDvC,MAAM,EAAEA;EACV,CAAC,CAAC;EACFR,eAAe,CAAC,YAAY;IAC1BkD,GAAG,CAAC,CAAC;EACP,CAAC,EAAEzD,aAAa,CAAC,EAAE,EAAED,MAAM,CAACuB,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;EAChD,OAAO;IACLgD,IAAI,EAAEzC,SAAS;IACf0B,OAAO,EAAE,CAACvB,WAAW,IAAIuB,OAAO;IAChCC,KAAK,EAAEA,KAAK;IACZxB,WAAW,EAAEA,WAAW;IACxBK,MAAM,EAAEA,MAAM;IACduB,QAAQ,EAAEA,QAAQ;IAClBC,aAAa,EAAEA,aAAa;IAC5BG,MAAM,EAAE3D,aAAa,CAAC2D,MAAM,CAAC;IAC7BC,WAAW,EAAE5D,aAAa,CAAC4D,WAAW,CAAC;IACvCM,MAAM,EAAEzC,YAAY;IACpB6B,MAAM,EAAEA;EACV,CAAC;AACH,CAAC;AACD,eAAe/C,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}