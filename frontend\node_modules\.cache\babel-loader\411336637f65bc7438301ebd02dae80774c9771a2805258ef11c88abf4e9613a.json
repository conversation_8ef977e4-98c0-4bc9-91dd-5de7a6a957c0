{"ast": null, "code": "export default function depsAreSame(oldDeps, deps) {\n  if (oldDeps === deps) return true;\n  for (var i = 0; i < oldDeps.length; i++) {\n    if (!Object.is(oldDeps[i], deps[i])) return false;\n  }\n  return true;\n}", "map": {"version": 3, "names": ["depsAreSame", "oldDeps", "deps", "i", "length", "Object", "is"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/depsAreSame.js"], "sourcesContent": ["export default function depsAreSame(oldDeps, deps) {\n  if (oldDeps === deps) return true;\n  for (var i = 0; i < oldDeps.length; i++) {\n    if (!Object.is(oldDeps[i], deps[i])) return false;\n  }\n  return true;\n}"], "mappings": "AAAA,eAAe,SAASA,WAAWA,CAACC,OAAO,EAAEC,IAAI,EAAE;EACjD,IAAID,OAAO,KAAKC,IAAI,EAAE,OAAO,IAAI;EACjC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;IACvC,IAAI,CAACE,MAAM,CAACC,EAAE,CAACL,OAAO,CAACE,CAAC,CAAC,EAAED,IAAI,CAACC,CAAC,CAAC,CAAC,EAAE,OAAO,KAAK;EACnD;EACA,OAAO,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}