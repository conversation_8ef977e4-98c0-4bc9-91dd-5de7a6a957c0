{"ast": null, "code": "import { useEffect } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useUnmount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useUnmount expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  useEffect(function () {\n    return function () {\n      fnRef.current();\n    };\n  }, []);\n};\nexport default useUnmount;", "map": {"version": 3, "names": ["useEffect", "useLatest", "isFunction", "isDev", "useUnmount", "fn", "console", "error", "concat", "fnRef", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useUnmount/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nimport isDev from '../utils/isDev';\nvar useUnmount = function (fn) {\n  if (isDev) {\n    if (!isFunction(fn)) {\n      console.error(\"useUnmount expected parameter is a function, got \".concat(typeof fn));\n    }\n  }\n  var fnRef = useLatest(fn);\n  useEffect(function () {\n    return function () {\n      fnRef.current();\n    };\n  }, []);\n};\nexport default useUnmount;"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,KAAK,MAAM,gBAAgB;AAClC,IAAIC,UAAU,GAAG,SAAAA,CAAUC,EAAE,EAAE;EAC7B,IAAIF,KAAK,EAAE;IACT,IAAI,CAACD,UAAU,CAACG,EAAE,CAAC,EAAE;MACnBC,OAAO,CAACC,KAAK,CAAC,mDAAmD,CAACC,MAAM,CAAC,OAAOH,EAAE,CAAC,CAAC;IACtF;EACF;EACA,IAAII,KAAK,GAAGR,SAAS,CAACI,EAAE,CAAC;EACzBL,SAAS,CAAC,YAAY;IACpB,OAAO,YAAY;MACjBS,KAAK,CAACC,OAAO,CAAC,CAAC;IACjB,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,eAAeN,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}