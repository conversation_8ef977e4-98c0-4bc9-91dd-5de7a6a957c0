{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\Layout\\\\PageHeader.js\",\n  _s = $RefreshSig$();\n/**\n * 頁面標題欄組件\n */\n\nimport React from 'react';\nimport { NavBar } from 'antd-mobile';\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageHeader = ({\n  title,\n  onBack,\n  backText = '',\n  right = null,\n  showBack = true,\n  style = {},\n  className = ''\n}) => {\n  _s();\n  const navigate = useNavigate();\n  const handleBack = () => {\n    if (onBack) {\n      onBack();\n    } else {\n      navigate(-1);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(NavBar, {\n    className: `page-header ${className}`,\n    style: style,\n    onBack: showBack ? handleBack : undefined,\n    backText: backText,\n    right: right,\n    children: title\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 29,\n    columnNumber: 5\n  }, this);\n};\n_s(PageHeader, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = PageHeader;\nexport default PageHeader;\nvar _c;\n$RefreshReg$(_c, \"PageHeader\");", "map": {"version": 3, "names": ["React", "NavBar", "useNavigate", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "title", "onBack", "backText", "right", "showBack", "style", "className", "_s", "navigate", "handleBack", "undefined", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Layout/PageHeader.js"], "sourcesContent": ["/**\n * 頁面標題欄組件\n */\n\nimport React from 'react';\nimport { NavBar } from 'antd-mobile';\nimport { useNavigate } from 'react-router-dom';\n\nconst PageHeader = ({ \n  title, \n  onBack, \n  backText = '',\n  right = null,\n  showBack = true,\n  style = {},\n  className = '' \n}) => {\n  const navigate = useNavigate();\n\n  const handleBack = () => {\n    if (onBack) {\n      onBack();\n    } else {\n      navigate(-1);\n    }\n  };\n\n  return (\n    <NavBar\n      className={`page-header ${className}`}\n      style={style}\n      onBack={showBack ? handleBack : undefined}\n      backText={backText}\n      right={right}\n    >\n      {title}\n    </NavBar>\n  );\n};\n\nexport default PageHeader;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,MAAM;EACNC,QAAQ,GAAG,EAAE;EACbC,KAAK,GAAG,IAAI;EACZC,QAAQ,GAAG,IAAI;EACfC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAMC,QAAQ,GAAGZ,WAAW,CAAC,CAAC;EAE9B,MAAMa,UAAU,GAAGA,CAAA,KAAM;IACvB,IAAIR,MAAM,EAAE;MACVA,MAAM,CAAC,CAAC;IACV,CAAC,MAAM;MACLO,QAAQ,CAAC,CAAC,CAAC,CAAC;IACd;EACF,CAAC;EAED,oBACEV,OAAA,CAACH,MAAM;IACLW,SAAS,EAAE,eAAeA,SAAS,EAAG;IACtCD,KAAK,EAAEA,KAAM;IACbJ,MAAM,EAAEG,QAAQ,GAAGK,UAAU,GAAGC,SAAU;IAC1CR,QAAQ,EAAEA,QAAS;IACnBC,KAAK,EAAEA,KAAM;IAAAQ,QAAA,EAEZX;EAAK;IAAAY,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACR,EAAA,CA9BIR,UAAU;EAAA,QASGH,WAAW;AAAA;AAAAoB,EAAA,GATxBjB,UAAU;AAgChB,eAAeA,UAAU;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}