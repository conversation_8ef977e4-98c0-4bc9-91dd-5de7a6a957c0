{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\UI\\\\SuccessMessage.js\";\n/**\n * 成功消息組件\n */\n\nimport React from 'react';\nimport { Card } from 'antd-mobile';\nimport { CheckCircleOutline } from 'antd-mobile-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst SuccessMessage = ({\n  message,\n  title = '操作成功',\n  style = {},\n  className = '',\n  showIcon = true\n}) => {\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: `success-message ${className}`,\n    style: {\n      margin: '16px',\n      textAlign: 'center',\n      border: '1px solid #52c41a',\n      backgroundColor: '#f6ffed',\n      ...style\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px'\n      },\n      children: [showIcon && /*#__PURE__*/_jsxDEV(CheckCircleOutline, {\n        style: {\n          fontSize: '48px',\n          color: '#52c41a',\n          marginBottom: '16px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '16px',\n          fontWeight: 'bold',\n          color: '#262626',\n          marginBottom: message ? '8px' : '0'\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this), message && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          color: '#52c41a',\n          lineHeight: '1.5'\n        },\n        children: message\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 27,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 17,\n    columnNumber: 5\n  }, this);\n};\n_c = SuccessMessage;\nexport default SuccessMessage;\nvar _c;\n$RefreshReg$(_c, \"SuccessMessage\");", "map": {"version": 3, "names": ["React", "Card", "CheckCircleOutline", "jsxDEV", "_jsxDEV", "SuccessMessage", "message", "title", "style", "className", "showIcon", "margin", "textAlign", "border", "backgroundColor", "children", "padding", "fontSize", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "lineHeight", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/UI/SuccessMessage.js"], "sourcesContent": ["/**\n * 成功消息組件\n */\n\nimport React from 'react';\nimport { Card } from 'antd-mobile';\nimport { CheckCircleOutline } from 'antd-mobile-icons';\n\nconst SuccessMessage = ({ \n  message, \n  title = '操作成功',\n  style = {},\n  className = '',\n  showIcon = true \n}) => {\n  return (\n    <Card \n      className={`success-message ${className}`}\n      style={{\n        margin: '16px',\n        textAlign: 'center',\n        border: '1px solid #52c41a',\n        backgroundColor: '#f6ffed',\n        ...style\n      }}\n    >\n      <div style={{ padding: '20px' }}>\n        {showIcon && (\n          <CheckCircleOutline \n            style={{ \n              fontSize: '48px', \n              color: '#52c41a',\n              marginBottom: '16px'\n            }} \n          />\n        )}\n        \n        <div \n          style={{\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: '#262626',\n            marginBottom: message ? '8px' : '0'\n          }}\n        >\n          {title}\n        </div>\n        \n        {message && (\n          <div \n            style={{\n              fontSize: '14px',\n              color: '#52c41a',\n              lineHeight: '1.5'\n            }}\n          >\n            {message}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n};\n\nexport default SuccessMessage;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,QAAQ,aAAa;AAClC,SAASC,kBAAkB,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEvD,MAAMC,cAAc,GAAGA,CAAC;EACtBC,OAAO;EACPC,KAAK,GAAG,MAAM;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,QAAQ,GAAG;AACb,CAAC,KAAK;EACJ,oBACEN,OAAA,CAACH,IAAI;IACHQ,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAC1CD,KAAK,EAAE;MACLG,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,QAAQ;MACnBC,MAAM,EAAE,mBAAmB;MAC3BC,eAAe,EAAE,SAAS;MAC1B,GAAGN;IACL,CAAE;IAAAO,QAAA,eAEFX,OAAA;MAAKI,KAAK,EAAE;QAAEQ,OAAO,EAAE;MAAO,CAAE;MAAAD,QAAA,GAC7BL,QAAQ,iBACPN,OAAA,CAACF,kBAAkB;QACjBM,KAAK,EAAE;UACLS,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,SAAS;UAChBC,YAAY,EAAE;QAChB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACF,eAEDnB,OAAA;QACEI,KAAK,EAAE;UACLS,QAAQ,EAAE,MAAM;UAChBO,UAAU,EAAE,MAAM;UAClBN,KAAK,EAAE,SAAS;UAChBC,YAAY,EAAEb,OAAO,GAAG,KAAK,GAAG;QAClC,CAAE;QAAAS,QAAA,EAEDR;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELjB,OAAO,iBACNF,OAAA;QACEI,KAAK,EAAE;UACLS,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,SAAS;UAChBO,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,EAEDT;MAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACL,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACG,EAAA,GAtDIrB,cAAc;AAwDpB,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}