{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useEventListener(eventName, handler, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.enable,\n    enable = _a === void 0 ? true : _a;\n  var handlerRef = useLatest(handler);\n  useEffectWithTarget(function () {\n    if (!enable) {\n      return;\n    }\n    var targetElement = getTargetElement(options.target, window);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var eventListener = function (event) {\n      return handlerRef.current(event);\n    };\n    var eventNameArray = Array.isArray(eventName) ? eventName : [eventName];\n    eventNameArray.forEach(function (event) {\n      targetElement.addEventListener(event, eventListener, {\n        capture: options.capture,\n        once: options.once,\n        passive: options.passive\n      });\n    });\n    return function () {\n      eventNameArray.forEach(function (event) {\n        targetElement.removeEventListener(event, eventListener, {\n          capture: options.capture\n        });\n      });\n    };\n  }, [eventName, options.capture, options.once, options.passive, enable], options.target);\n}\nexport default useEventListener;", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "useEffectWithTarget", "useEventListener", "eventName", "handler", "options", "_a", "enable", "handler<PERSON>ef", "targetElement", "target", "window", "addEventListener", "eventListener", "event", "current", "eventNameArray", "Array", "isArray", "for<PERSON>ach", "capture", "once", "passive", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useEventListener/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nfunction useEventListener(eventName, handler, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.enable,\n    enable = _a === void 0 ? true : _a;\n  var handlerRef = useLatest(handler);\n  useEffectWithTarget(function () {\n    if (!enable) {\n      return;\n    }\n    var targetElement = getTargetElement(options.target, window);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var eventListener = function (event) {\n      return handlerRef.current(event);\n    };\n    var eventNameArray = Array.isArray(eventName) ? eventName : [eventName];\n    eventNameArray.forEach(function (event) {\n      targetElement.addEventListener(event, eventListener, {\n        capture: options.capture,\n        once: options.once,\n        passive: options.passive\n      });\n    });\n    return function () {\n      eventNameArray.forEach(function (event) {\n        targetElement.removeEventListener(event, eventListener, {\n          capture: options.capture\n        });\n      });\n    };\n  }, [eventName, options.capture, options.once, options.passive, enable], options.target);\n}\nexport default useEventListener;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,cAAc;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,SAASC,gBAAgBA,CAACC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EACrD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,EAAE,GAAGD,OAAO,CAACE,MAAM;IACrBA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;EACpC,IAAIE,UAAU,GAAGT,SAAS,CAACK,OAAO,CAAC;EACnCH,mBAAmB,CAAC,YAAY;IAC9B,IAAI,CAACM,MAAM,EAAE;MACX;IACF;IACA,IAAIE,aAAa,GAAGT,gBAAgB,CAACK,OAAO,CAACK,MAAM,EAAEC,MAAM,CAAC;IAC5D,IAAI,EAAEF,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACG,gBAAgB,CAAC,EAAE;MACnG;IACF;IACA,IAAIC,aAAa,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACnC,OAAON,UAAU,CAACO,OAAO,CAACD,KAAK,CAAC;IAClC,CAAC;IACD,IAAIE,cAAc,GAAGC,KAAK,CAACC,OAAO,CAACf,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;IACvEa,cAAc,CAACG,OAAO,CAAC,UAAUL,KAAK,EAAE;MACtCL,aAAa,CAACG,gBAAgB,CAACE,KAAK,EAAED,aAAa,EAAE;QACnDO,OAAO,EAAEf,OAAO,CAACe,OAAO;QACxBC,IAAI,EAAEhB,OAAO,CAACgB,IAAI;QAClBC,OAAO,EAAEjB,OAAO,CAACiB;MACnB,CAAC,CAAC;IACJ,CAAC,CAAC;IACF,OAAO,YAAY;MACjBN,cAAc,CAACG,OAAO,CAAC,UAAUL,KAAK,EAAE;QACtCL,aAAa,CAACc,mBAAmB,CAACT,KAAK,EAAED,aAAa,EAAE;UACtDO,OAAO,EAAEf,OAAO,CAACe;QACnB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC;EACH,CAAC,EAAE,CAACjB,SAAS,EAAEE,OAAO,CAACe,OAAO,EAAEf,OAAO,CAACgB,IAAI,EAAEhB,OAAO,CAACiB,OAAO,EAAEf,MAAM,CAAC,EAAEF,OAAO,CAACK,MAAM,CAAC;AACzF;AACA,eAAeR,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}