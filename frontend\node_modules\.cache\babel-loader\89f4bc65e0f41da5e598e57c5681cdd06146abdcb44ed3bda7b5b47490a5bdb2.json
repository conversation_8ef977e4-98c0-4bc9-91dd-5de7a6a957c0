{"ast": null, "code": "export function resolveContainer(getContainer) {\n  const container = typeof getContainer === 'function' ? getContainer() : getContainer;\n  return container || document.body;\n}", "map": {"version": 3, "names": ["resolveContainer", "getContainer", "container", "document", "body"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/get-container.js"], "sourcesContent": ["export function resolveContainer(getContainer) {\n  const container = typeof getContainer === 'function' ? getContainer() : getContainer;\n  return container || document.body;\n}"], "mappings": "AAAA,OAAO,SAASA,gBAAgBA,CAACC,YAAY,EAAE;EAC7C,MAAMC,SAAS,GAAG,OAAOD,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY;EACpF,OAAOC,SAAS,IAAIC,QAAQ,CAACC,IAAI;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}