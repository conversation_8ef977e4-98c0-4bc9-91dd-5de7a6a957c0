/**
 * 統一的名片表單組件
 */

import React, { useState, useEffect } from 'react';
import { Form, Card, Button, Space, Divider } from 'antd-mobile';
import { CheckOutline, UserContactOutline } from 'antd-mobile-icons';
import FormField from './FormField';
import { validateForm, cleanFormData, hasFormChanged } from '../../utils/validation';
import { formatFormData } from '../../utils/formatters';
import { CARD_FIELDS, FIELD_VALIDATION } from '../../utils/constants';

const CardForm = ({
  initialData = {},
  onSubmit,
  onCancel,
  loading = false,
  submitText = '保存名片',
  title = '名片資訊',
  showCancel = false,
  disabled = false,
  style = {},
  className = ''
}) => {
  const [formData, setFormData] = useState({
    name: '',
    company_name: '',
    position: '',
    mobile_phone: '',
    office_phone: '',
    email: '',
    line_id: '',
    notes: '',
    company_address_1: '',
    company_address_2: '',
    ...initialData
  });

  const [errors, setErrors] = useState({});
  const [hasChanges, setHasChanges] = useState(false);

  // 處理欄位變更
  const handleFieldChange = (fieldName, value) => {
    setFormData(prev => ({
      ...prev,
      [fieldName]: value
    }));

    // 清除該欄位的錯誤
    if (errors[fieldName]) {
      setErrors(prev => ({
        ...prev,
        [fieldName]: undefined
      }));
    }
  };

  // 檢查表單變更
  useEffect(() => {
    const changed = hasFormChanged(initialData, formData);
    setHasChanges(changed);
  }, [initialData, formData]);

  // 處理表單提交
  const handleSubmit = async () => {
    // 驗證表單
    const validation = validateForm(formData);
    
    if (!validation.isValid) {
      setErrors(validation.errors);
      return;
    }

    // 清理和格式化數據
    const cleanedData = cleanFormData(formData);
    const formattedData = formatFormData(cleanedData);

    if (onSubmit) {
      try {
        await onSubmit(formattedData);
        // 提交成功後重置變更狀態
        setHasChanges(false);
      } catch (error) {
        console.error('表單提交失敗:', error);
      }
    }
  };

  // 處理取消
  const handleCancel = () => {
    if (onCancel) {
      onCancel();
    }
  };

  // 重置表單
  const resetForm = () => {
    setFormData({
      name: '',
      company_name: '',
      position: '',
      mobile_phone: '',
      office_phone: '',
      email: '',
      line_id: '',
      notes: '',
      company_address_1: '',
      company_address_2: '',
      ...initialData
    });
    setErrors({});
    setHasChanges(false);
  };

  // 當初始數據變更時重置表單
  useEffect(() => {
    resetForm();
  }, [initialData]);

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <UserContactOutline />
          {title}
        </div>
      }
      className={`card-form ${className}`}
      style={style}
    >
      <Form layout="vertical">
        {/* 基本資訊 */}
        <div className="form-section">
          <FormField
            name="name"
            value={formData.name}
            onChange={handleFieldChange}
            required={true}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.name.maxLength}
          />
          
          <FormField
            name="company_name"
            value={formData.company_name}
            onChange={handleFieldChange}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.company_name.maxLength}
          />
          
          <FormField
            name="position"
            value={formData.position}
            onChange={handleFieldChange}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.position.maxLength}
          />
        </div>

        <Divider>聯絡方式</Divider>

        {/* 聯絡資訊 */}
        <div className="form-section">
          <FormField
            name="mobile_phone"
            value={formData.mobile_phone}
            onChange={handleFieldChange}
            type="tel"
            disabled={disabled}
            maxLength={FIELD_VALIDATION.mobile_phone.maxLength}
          />
          
          <FormField
            name="office_phone"
            value={formData.office_phone}
            onChange={handleFieldChange}
            type="tel"
            disabled={disabled}
            maxLength={FIELD_VALIDATION.office_phone.maxLength}
          />
          
          <FormField
            name="email"
            value={formData.email}
            onChange={handleFieldChange}
            type="email"
            disabled={disabled}
            maxLength={FIELD_VALIDATION.email.maxLength}
          />
          
          <FormField
            name="line_id"
            value={formData.line_id}
            onChange={handleFieldChange}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.line_id.maxLength}
          />
        </div>

        <Divider>地址資訊</Divider>

        {/* 地址資訊 */}
        <div className="form-section">
          <FormField
            name="company_address_1"
            value={formData.company_address_1}
            onChange={handleFieldChange}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.company_address_1.maxLength}
          />
          
          <FormField
            name="company_address_2"
            value={formData.company_address_2}
            onChange={handleFieldChange}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.company_address_2.maxLength}
          />
        </div>

        <Divider>備註</Divider>

        {/* 備註 */}
        <div className="form-section">
          <FormField
            name="notes"
            value={formData.notes}
            onChange={handleFieldChange}
            multiline={true}
            disabled={disabled}
            maxLength={FIELD_VALIDATION.notes.maxLength}
          />
        </div>

        {/* 操作按鈕 */}
        {!disabled && (
          <div style={{ marginTop: '24px' }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button
                color="primary"
                size="large"
                block
                onClick={handleSubmit}
                loading={loading}
                disabled={!hasChanges && !!initialData.name}
              >
                <CheckOutline /> {submitText}
              </Button>
              
              {showCancel && (
                <Button
                  color="default"
                  size="large"
                  block
                  onClick={handleCancel}
                  disabled={loading}
                >
                  取消
                </Button>
              )}
            </Space>
          </div>
        )}
      </Form>
    </Card>
  );
};

export default CardForm;
