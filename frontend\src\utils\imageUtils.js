/**
 * 圖片處理工具函數
 */

import { IMAGE_CONFIG, ERROR_MESSAGES } from './constants';

/**
 * 驗證圖片文件
 * @param {File} file - 圖片文件
 * @returns {Object} 驗證結果 { isValid: boolean, error: string }
 */
export const validateImageFile = (file) => {
  if (!file) {
    return { isValid: false, error: '請選擇圖片文件' };
  }

  // 檢查文件大小
  if (file.size > IMAGE_CONFIG.MAX_FILE_SIZE) {
    return { 
      isValid: false, 
      error: `${ERROR_MESSAGES.FILE_TOO_LARGE}（最大${Math.round(IMAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024)}MB）` 
    };
  }

  // 檢查文件格式
  if (!IMAGE_CONFIG.ACCEPTED_FORMATS.includes(file.type)) {
    return { 
      isValid: false, 
      error: `${ERROR_MESSAGES.INVALID_FILE_FORMAT}，支持格式：${IMAGE_CONFIG.ACCEPTED_FORMATS.join(', ')}` 
    };
  }

  return { isValid: true, error: null };
};

/**
 * 壓縮圖片
 * @param {File} file - 原始圖片文件
 * @param {Object} options - 壓縮選項
 * @returns {Promise<File>} 壓縮後的圖片文件
 */
export const compressImage = (file, options = {}) => {
  return new Promise((resolve, reject) => {
    const {
      maxWidth = IMAGE_CONFIG.MAX_WIDTH,
      maxHeight = IMAGE_CONFIG.MAX_HEIGHT,
      quality = IMAGE_CONFIG.COMPRESSION_QUALITY
    } = options;

    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      // 計算新的尺寸
      let { width, height } = img;
      
      if (width > maxWidth || height > maxHeight) {
        const ratio = Math.min(maxWidth / width, maxHeight / height);
        width *= ratio;
        height *= ratio;
      }

      // 設置畫布尺寸
      canvas.width = width;
      canvas.height = height;

      // 繪製圖片
      ctx.drawImage(img, 0, 0, width, height);

      // 轉換為Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            // 創建新的File對象
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          } else {
            reject(new Error('圖片壓縮失敗'));
          }
        },
        file.type,
        quality
      );
    };

    img.onerror = () => {
      reject(new Error('圖片加載失敗'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * 獲取圖片預覽URL
 * @param {File} file - 圖片文件
 * @returns {string} 預覽URL
 */
export const getImagePreviewUrl = (file) => {
  if (!file) return null;
  return URL.createObjectURL(file);
};

/**
 * 釋放圖片預覽URL
 * @param {string} url - 預覽URL
 */
export const revokeImagePreviewUrl = (url) => {
  if (url) {
    URL.revokeObjectURL(url);
  }
};

/**
 * 將圖片轉換為Base64
 * @param {File} file - 圖片文件
 * @returns {Promise<string>} Base64字符串
 */
export const imageToBase64 = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();
    
    reader.onload = () => {
      resolve(reader.result);
    };
    
    reader.onerror = () => {
      reject(new Error('圖片轉換失敗'));
    };
    
    reader.readAsDataURL(file);
  });
};

/**
 * 從Base64創建圖片文件
 * @param {string} base64 - Base64字符串
 * @param {string} filename - 文件名
 * @returns {File} 圖片文件
 */
export const base64ToImageFile = (base64, filename = 'image.jpg') => {
  const arr = base64.split(',');
  const mime = arr[0].match(/:(.*?);/)[1];
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }
  
  return new File([u8arr], filename, { type: mime });
};

/**
 * 獲取圖片尺寸信息
 * @param {File} file - 圖片文件
 * @returns {Promise<Object>} 尺寸信息 { width, height }
 */
export const getImageDimensions = (file) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    
    img.onload = () => {
      resolve({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    };
    
    img.onerror = () => {
      reject(new Error('無法獲取圖片尺寸'));
    };
    
    img.src = URL.createObjectURL(file);
  });
};

/**
 * 旋轉圖片
 * @param {File} file - 圖片文件
 * @param {number} degrees - 旋轉角度（90, 180, 270）
 * @returns {Promise<File>} 旋轉後的圖片文件
 */
export const rotateImage = (file, degrees) => {
  return new Promise((resolve, reject) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    const img = new Image();

    img.onload = () => {
      const { width, height } = img;
      
      // 根據旋轉角度設置畫布尺寸
      if (degrees === 90 || degrees === 270) {
        canvas.width = height;
        canvas.height = width;
      } else {
        canvas.width = width;
        canvas.height = height;
      }

      // 移動到畫布中心
      ctx.translate(canvas.width / 2, canvas.height / 2);
      
      // 旋轉
      ctx.rotate((degrees * Math.PI) / 180);
      
      // 繪製圖片
      ctx.drawImage(img, -width / 2, -height / 2);

      // 轉換為Blob
      canvas.toBlob(
        (blob) => {
          if (blob) {
            const rotatedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(rotatedFile);
          } else {
            reject(new Error('圖片旋轉失敗'));
          }
        },
        file.type,
        IMAGE_CONFIG.COMPRESSION_QUALITY
      );
    };

    img.onerror = () => {
      reject(new Error('圖片加載失敗'));
    };

    img.src = URL.createObjectURL(file);
  });
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字節數
 * @returns {string} 格式化後的大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

export default {
  validateImageFile,
  compressImage,
  getImagePreviewUrl,
  revokeImagePreviewUrl,
  imageToBase64,
  base64ToImageFile,
  getImageDimensions,
  rotateImage,
  formatFileSize
};
