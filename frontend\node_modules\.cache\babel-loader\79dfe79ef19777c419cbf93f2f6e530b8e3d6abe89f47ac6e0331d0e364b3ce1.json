{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nfunction useEventTarget(options) {\n  var _a = options || {},\n    initialValue = _a.initialValue,\n    transformer = _a.transformer;\n  var _b = __read(useState(initialValue), 2),\n    value = _b[0],\n    setValue = _b[1];\n  var transformerRef = useLatest(transformer);\n  var reset = useCallback(function () {\n    return setValue(initialValue);\n  }, []);\n  var onChange = useCallback(function (e) {\n    var _value = e.target.value;\n    if (isFunction(transformerRef.current)) {\n      return setValue(transformerRef.current(_value));\n    }\n    // no transformer => U and T should be the same\n    return setValue(_value);\n  }, []);\n  return [value, {\n    onChange: onChange,\n    reset: reset\n  }];\n}\nexport default useEventTarget;", "map": {"version": 3, "names": ["__read", "useCallback", "useState", "useLatest", "isFunction", "useEventTarget", "options", "_a", "initialValue", "transformer", "_b", "value", "setValue", "transformerRef", "reset", "onChange", "e", "_value", "target", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useEventTarget/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nimport useLatest from '../useLatest';\nimport { isFunction } from '../utils';\nfunction useEventTarget(options) {\n  var _a = options || {},\n    initialValue = _a.initialValue,\n    transformer = _a.transformer;\n  var _b = __read(useState(initialValue), 2),\n    value = _b[0],\n    setValue = _b[1];\n  var transformerRef = useLatest(transformer);\n  var reset = useCallback(function () {\n    return setValue(initialValue);\n  }, []);\n  var onChange = useCallback(function (e) {\n    var _value = e.target.value;\n    if (isFunction(transformerRef.current)) {\n      return setValue(transformerRef.current(_value));\n    }\n    // no transformer => U and T should be the same\n    return setValue(_value);\n  }, []);\n  return [value, {\n    onChange: onChange,\n    reset: reset\n  }];\n}\nexport default useEventTarget;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAC7C,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,cAAcA,CAACC,OAAO,EAAE;EAC/B,IAAIC,EAAE,GAAGD,OAAO,IAAI,CAAC,CAAC;IACpBE,YAAY,GAAGD,EAAE,CAACC,YAAY;IAC9BC,WAAW,GAAGF,EAAE,CAACE,WAAW;EAC9B,IAAIC,EAAE,GAAGV,MAAM,CAACE,QAAQ,CAACM,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCG,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,cAAc,GAAGV,SAAS,CAACM,WAAW,CAAC;EAC3C,IAAIK,KAAK,GAAGb,WAAW,CAAC,YAAY;IAClC,OAAOW,QAAQ,CAACJ,YAAY,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACN,IAAIO,QAAQ,GAAGd,WAAW,CAAC,UAAUe,CAAC,EAAE;IACtC,IAAIC,MAAM,GAAGD,CAAC,CAACE,MAAM,CAACP,KAAK;IAC3B,IAAIP,UAAU,CAACS,cAAc,CAACM,OAAO,CAAC,EAAE;MACtC,OAAOP,QAAQ,CAACC,cAAc,CAACM,OAAO,CAACF,MAAM,CAAC,CAAC;IACjD;IACA;IACA,OAAOL,QAAQ,CAACK,MAAM,CAAC;EACzB,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACN,KAAK,EAAE;IACbI,QAAQ,EAAEA,QAAQ;IAClBD,KAAK,EAAEA;EACT,CAAC,CAAC;AACJ;AACA,eAAeT,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}