/**
 * 統一的相機拍攝組件
 * 集成現有的相機管理器和移動端相機組件
 */

import React, { useRef } from 'react';
import { Card, Button, Space, Modal } from 'antd-mobile';
import { CameraOutline, ScanningOutline } from 'antd-mobile-icons';
import CameraControls from './CameraControls';
import ImagePreview from './ImagePreview';
import MobileCameraModal from '../MobileCameraModal';
import { useCameraState } from '../../hooks';

const CameraCapture = ({
  onImageCaptured,
  onOCRCompleted,
  title = '拍攝名片',
  style = {},
  className = ''
}) => {
  const {
    images,
    currentTarget,
    cameraModalVisible,
    deviceType,
    cameraManager,
    startCamera,
    stopCamera,
    capturePhoto,
    selectFromGallery,
    removeImage,
    switchTarget,
    isMobile
  } = useCameraState();

  const videoRef = useRef(null);
  const canvasRef = useRef(null);

  // 處理拍照
  const handleTakePhoto = async (target) => {
    try {
      await startCamera(target);
    } catch (error) {
      console.error('啟動相機失敗:', error);
    }
  };

  // 處理相冊選擇
  const handleSelectFromGallery = (target) => {
    selectFromGallery(target);
  };

  // 處理移動端拍照完成
  const handleMobilePhotoTaken = async (photoData) => {
    if (photoData && photoData.file) {
      stopCamera();
      
      // 通知父組件圖片已捕獲
      if (onImageCaptured) {
        onImageCaptured(photoData.file, currentTarget);
      }
    }
  };

  // 處理圖片移除
  const handleRemoveImage = (target) => {
    removeImage(target);
  };

  // 處理重新拍攝
  const handleRetakeImage = (target) => {
    removeImage(target);
    handleTakePhoto(target);
  };

  // 處理目標切換
  const handleTargetSwitch = (target) => {
    switchTarget(target);
  };

  return (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          <ScanningOutline />
          {title}
        </div>
      }
      className={`camera-capture ${className}`}
      style={style}
    >
      {/* 拍攝模式切換 */}
      <div style={{ marginBottom: '16px' }}>
        <Space style={{ width: '100%', justifyContent: 'center' }}>
          <Button
            color={currentTarget === 'front' ? 'primary' : 'default'}
            fill={currentTarget === 'front' ? 'solid' : 'outline'}
            onClick={() => handleTargetSwitch('front')}
            style={{ flex: 1 }}
          >
            正面
          </Button>
          <Button
            color={currentTarget === 'back' ? 'primary' : 'default'}
            fill={currentTarget === 'back' ? 'solid' : 'outline'}
            onClick={() => handleTargetSwitch('back')}
            style={{ flex: 1 }}
          >
            反面
          </Button>
        </Space>
      </div>

      {/* 圖片預覽區域 */}
      <div style={{ marginBottom: '16px' }}>
        {/* 正面圖片預覽 */}
        {images.front.file && (
          <ImagePreview
            image={images.front}
            side="front"
            onRemove={handleRemoveImage}
            onRetake={handleRetakeImage}
          />
        )}

        {/* 反面圖片預覽 */}
        {images.back.file && (
          <ImagePreview
            image={images.back}
            side="back"
            onRemove={handleRemoveImage}
            onRetake={handleRetakeImage}
          />
        )}

        {/* 當前目標的拍攝提示 */}
        {!images[currentTarget].file && (
          <div
            style={{
              textAlign: 'center',
              padding: '40px 20px',
              border: '2px dashed #d9d9d9',
              borderRadius: '8px',
              backgroundColor: '#fafafa'
            }}
          >
            <CameraOutline style={{ fontSize: '64px', marginBottom: '12px', color: '#ccc' }} />
            <div style={{ fontSize: '16px', marginBottom: '8px' }}>
              請拍攝名片{currentTarget === 'front' ? '正面' : '反面'}
            </div>
            <div style={{ fontSize: '14px', color: '#8c8c8c' }}>
              點擊下方按鈕開始拍照或選擇圖片
            </div>
          </div>
        )}
      </div>

      {/* 拍攝控制按鈕 */}
      <CameraControls
        onTakePhoto={handleTakePhoto}
        onSelectFromGallery={handleSelectFromGallery}
        currentTarget={currentTarget}
      />

      {/* 相機模態框 */}
      {isMobile ? (
        // 移動端：使用全屏相機組件
        <MobileCameraModal
          visible={cameraModalVisible}
          onClose={stopCamera}
          onPhotoTaken={handleMobilePhotoTaken}
          cameraManager={cameraManager}
          target={currentTarget}
        />
      ) : (
        // Web端：使用傳統Modal
        <Modal
          visible={cameraModalVisible}
          onClose={stopCamera}
          content={
            <div style={{ padding: '20px', textAlign: 'center' }}>
              <video
                ref={videoRef}
                autoPlay
                playsInline
                style={{
                  width: '100%',
                  maxHeight: '400px',
                  borderRadius: '8px',
                  backgroundColor: '#000'
                }}
              />
              <canvas ref={canvasRef} style={{ display: 'none' }} />
              <div style={{ marginTop: '16px' }}>
                <Button
                  color="primary"
                  size="large"
                  onClick={() => capturePhoto(currentTarget)}
                >
                  <CameraOutline /> 拍照
                </Button>
              </div>
            </div>
          }
          closeOnAction
        />
      )}
    </Card>
  );
};

export default CameraCapture;
