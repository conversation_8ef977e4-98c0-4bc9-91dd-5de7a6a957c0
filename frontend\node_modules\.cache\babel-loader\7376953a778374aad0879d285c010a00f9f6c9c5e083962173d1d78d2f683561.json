{"ast": null, "code": "import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useEventListener from '../useEventListener';\nimport { getTargetElement } from '../utils/domTarget';\nvar initState = {\n  screenX: NaN,\n  screenY: NaN,\n  clientX: NaN,\n  clientY: NaN,\n  pageX: NaN,\n  pageY: NaN,\n  elementX: NaN,\n  elementY: NaN,\n  elementH: NaN,\n  elementW: NaN,\n  elementPosX: NaN,\n  elementPosY: NaN\n};\nexport default (function (target) {\n  var _a = __read(useRafState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEventListener('mousemove', function (event) {\n    var screenX = event.screenX,\n      screenY = event.screenY,\n      clientX = event.clientX,\n      clientY = event.clientY,\n      pageX = event.pageX,\n      pageY = event.pageY;\n    var newState = {\n      screenX: screenX,\n      screenY: screenY,\n      clientX: clientX,\n      clientY: clientY,\n      pageX: pageX,\n      pageY: pageY,\n      elementX: NaN,\n      elementY: NaN,\n      elementH: NaN,\n      elementW: NaN,\n      elementPosX: NaN,\n      elementPosY: NaN\n    };\n    var targetElement = getTargetElement(target);\n    if (targetElement) {\n      var _a = targetElement.getBoundingClientRect(),\n        left = _a.left,\n        top_1 = _a.top,\n        width = _a.width,\n        height = _a.height;\n      newState.elementPosX = left + window.pageXOffset;\n      newState.elementPosY = top_1 + window.pageYOffset;\n      newState.elementX = pageX - newState.elementPosX;\n      newState.elementY = pageY - newState.elementPosY;\n      newState.elementW = width;\n      newState.elementH = height;\n    }\n    setState(newState);\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return state;\n});", "map": {"version": 3, "names": ["__read", "useRafState", "useEventListener", "getTargetElement", "initState", "screenX", "NaN", "screenY", "clientX", "clientY", "pageX", "pageY", "elementX", "elementY", "elementH", "elementW", "elementPosX", "elementPosY", "target", "_a", "state", "setState", "event", "newState", "targetElement", "getBoundingClientRect", "left", "top_1", "top", "width", "height", "window", "pageXOffset", "pageYOffset", "document"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useMouse/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport useRafState from '../useRafState';\nimport useEventListener from '../useEventListener';\nimport { getTargetElement } from '../utils/domTarget';\nvar initState = {\n  screenX: NaN,\n  screenY: NaN,\n  clientX: NaN,\n  clientY: NaN,\n  pageX: NaN,\n  pageY: NaN,\n  elementX: NaN,\n  elementY: NaN,\n  elementH: NaN,\n  elementW: NaN,\n  elementPosX: NaN,\n  elementPosY: NaN\n};\nexport default (function (target) {\n  var _a = __read(useRafState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEventListener('mousemove', function (event) {\n    var screenX = event.screenX,\n      screenY = event.screenY,\n      clientX = event.clientX,\n      clientY = event.clientY,\n      pageX = event.pageX,\n      pageY = event.pageY;\n    var newState = {\n      screenX: screenX,\n      screenY: screenY,\n      clientX: clientX,\n      clientY: clientY,\n      pageX: pageX,\n      pageY: pageY,\n      elementX: NaN,\n      elementY: NaN,\n      elementH: NaN,\n      elementW: NaN,\n      elementPosX: NaN,\n      elementPosY: NaN\n    };\n    var targetElement = getTargetElement(target);\n    if (targetElement) {\n      var _a = targetElement.getBoundingClientRect(),\n        left = _a.left,\n        top_1 = _a.top,\n        width = _a.width,\n        height = _a.height;\n      newState.elementPosX = left + window.pageXOffset;\n      newState.elementPosY = top_1 + window.pageYOffset;\n      newState.elementX = pageX - newState.elementPosX;\n      newState.elementY = pageY - newState.elementPosY;\n      newState.elementW = width;\n      newState.elementH = height;\n    }\n    setState(newState);\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return state;\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,IAAIC,SAAS,GAAG;EACdC,OAAO,EAAEC,GAAG;EACZC,OAAO,EAAED,GAAG;EACZE,OAAO,EAAEF,GAAG;EACZG,OAAO,EAAEH,GAAG;EACZI,KAAK,EAAEJ,GAAG;EACVK,KAAK,EAAEL,GAAG;EACVM,QAAQ,EAAEN,GAAG;EACbO,QAAQ,EAAEP,GAAG;EACbQ,QAAQ,EAAER,GAAG;EACbS,QAAQ,EAAET,GAAG;EACbU,WAAW,EAAEV,GAAG;EAChBW,WAAW,EAAEX;AACf,CAAC;AACD,gBAAgB,UAAUY,MAAM,EAAE;EAChC,IAAIC,EAAE,GAAGnB,MAAM,CAACC,WAAW,CAACG,SAAS,CAAC,EAAE,CAAC,CAAC;IACxCgB,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClBjB,gBAAgB,CAAC,WAAW,EAAE,UAAUoB,KAAK,EAAE;IAC7C,IAAIjB,OAAO,GAAGiB,KAAK,CAACjB,OAAO;MACzBE,OAAO,GAAGe,KAAK,CAACf,OAAO;MACvBC,OAAO,GAAGc,KAAK,CAACd,OAAO;MACvBC,OAAO,GAAGa,KAAK,CAACb,OAAO;MACvBC,KAAK,GAAGY,KAAK,CAACZ,KAAK;MACnBC,KAAK,GAAGW,KAAK,CAACX,KAAK;IACrB,IAAIY,QAAQ,GAAG;MACblB,OAAO,EAAEA,OAAO;MAChBE,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBC,OAAO,EAAEA,OAAO;MAChBC,KAAK,EAAEA,KAAK;MACZC,KAAK,EAAEA,KAAK;MACZC,QAAQ,EAAEN,GAAG;MACbO,QAAQ,EAAEP,GAAG;MACbQ,QAAQ,EAAER,GAAG;MACbS,QAAQ,EAAET,GAAG;MACbU,WAAW,EAAEV,GAAG;MAChBW,WAAW,EAAEX;IACf,CAAC;IACD,IAAIkB,aAAa,GAAGrB,gBAAgB,CAACe,MAAM,CAAC;IAC5C,IAAIM,aAAa,EAAE;MACjB,IAAIL,EAAE,GAAGK,aAAa,CAACC,qBAAqB,CAAC,CAAC;QAC5CC,IAAI,GAAGP,EAAE,CAACO,IAAI;QACdC,KAAK,GAAGR,EAAE,CAACS,GAAG;QACdC,KAAK,GAAGV,EAAE,CAACU,KAAK;QAChBC,MAAM,GAAGX,EAAE,CAACW,MAAM;MACpBP,QAAQ,CAACP,WAAW,GAAGU,IAAI,GAAGK,MAAM,CAACC,WAAW;MAChDT,QAAQ,CAACN,WAAW,GAAGU,KAAK,GAAGI,MAAM,CAACE,WAAW;MACjDV,QAAQ,CAACX,QAAQ,GAAGF,KAAK,GAAGa,QAAQ,CAACP,WAAW;MAChDO,QAAQ,CAACV,QAAQ,GAAGF,KAAK,GAAGY,QAAQ,CAACN,WAAW;MAChDM,QAAQ,CAACR,QAAQ,GAAGc,KAAK;MACzBN,QAAQ,CAACT,QAAQ,GAAGgB,MAAM;IAC5B;IACAT,QAAQ,CAACE,QAAQ,CAAC;EACpB,CAAC,EAAE;IACDL,MAAM,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAOgB,QAAQ;IACjB;EACF,CAAC,CAAC;EACF,OAAOd,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}