{"ast": null, "code": "import { useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport depsAreSame from './depsAreSame';\nimport { getTargetElement } from './domTarget';\nvar createEffectWithTarget = function (useEffectType) {\n  /**\n   *\n   * @param effect\n   * @param deps\n   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom\n   */\n  var useEffectWithTarget = function (effect, deps, target) {\n    var hasInitRef = useRef(false);\n    var lastElementRef = useRef([]);\n    var lastDepsRef = useRef([]);\n    var unLoadRef = useRef();\n    useEffectType(function () {\n      var _a;\n      var targets = Array.isArray(target) ? target : [target];\n      var els = targets.map(function (item) {\n        return getTargetElement(item);\n      });\n      // init run\n      if (!hasInitRef.current) {\n        hasInitRef.current = true;\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n        return;\n      }\n      if (els.length !== lastElementRef.current.length || !depsAreSame(lastElementRef.current, els) || !depsAreSame(lastDepsRef.current, deps)) {\n        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n      }\n    });\n    useUnmount(function () {\n      var _a;\n      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n      // for react-refresh\n      hasInitRef.current = false;\n    });\n  };\n  return useEffectWithTarget;\n};\nexport default createEffectWithTarget;", "map": {"version": 3, "names": ["useRef", "useUnmount", "depsAreSame", "getTargetElement", "createEffectWithTarget", "useEffectType", "useEffectWithTarget", "effect", "deps", "target", "hasInitRef", "lastElementRef", "lastDepsRef", "unLoadRef", "_a", "targets", "Array", "isArray", "els", "map", "item", "current", "length", "call"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/createEffectWithTarget.js"], "sourcesContent": ["import { useRef } from 'react';\nimport useUnmount from '../useUnmount';\nimport depsAreSame from './depsAreSame';\nimport { getTargetElement } from './domTarget';\nvar createEffectWithTarget = function (useEffectType) {\n  /**\n   *\n   * @param effect\n   * @param deps\n   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom\n   */\n  var useEffectWithTarget = function (effect, deps, target) {\n    var hasInitRef = useRef(false);\n    var lastElementRef = useRef([]);\n    var lastDepsRef = useRef([]);\n    var unLoadRef = useRef();\n    useEffectType(function () {\n      var _a;\n      var targets = Array.isArray(target) ? target : [target];\n      var els = targets.map(function (item) {\n        return getTargetElement(item);\n      });\n      // init run\n      if (!hasInitRef.current) {\n        hasInitRef.current = true;\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n        return;\n      }\n      if (els.length !== lastElementRef.current.length || !depsAreSame(lastElementRef.current, els) || !depsAreSame(lastDepsRef.current, deps)) {\n        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n      }\n    });\n    useUnmount(function () {\n      var _a;\n      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n      // for react-refresh\n      hasInitRef.current = false;\n    });\n  };\n  return useEffectWithTarget;\n};\nexport default createEffectWithTarget;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,gBAAgB,QAAQ,aAAa;AAC9C,IAAIC,sBAAsB,GAAG,SAAAA,CAAUC,aAAa,EAAE;EACpD;AACF;AACA;AACA;AACA;AACA;EACE,IAAIC,mBAAmB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAEC,MAAM,EAAE;IACxD,IAAIC,UAAU,GAAGV,MAAM,CAAC,KAAK,CAAC;IAC9B,IAAIW,cAAc,GAAGX,MAAM,CAAC,EAAE,CAAC;IAC/B,IAAIY,WAAW,GAAGZ,MAAM,CAAC,EAAE,CAAC;IAC5B,IAAIa,SAAS,GAAGb,MAAM,CAAC,CAAC;IACxBK,aAAa,CAAC,YAAY;MACxB,IAAIS,EAAE;MACN,IAAIC,OAAO,GAAGC,KAAK,CAACC,OAAO,CAACR,MAAM,CAAC,GAAGA,MAAM,GAAG,CAACA,MAAM,CAAC;MACvD,IAAIS,GAAG,GAAGH,OAAO,CAACI,GAAG,CAAC,UAAUC,IAAI,EAAE;QACpC,OAAOjB,gBAAgB,CAACiB,IAAI,CAAC;MAC/B,CAAC,CAAC;MACF;MACA,IAAI,CAACV,UAAU,CAACW,OAAO,EAAE;QACvBX,UAAU,CAACW,OAAO,GAAG,IAAI;QACzBV,cAAc,CAACU,OAAO,GAAGH,GAAG;QAC5BN,WAAW,CAACS,OAAO,GAAGb,IAAI;QAC1BK,SAAS,CAACQ,OAAO,GAAGd,MAAM,CAAC,CAAC;QAC5B;MACF;MACA,IAAIW,GAAG,CAACI,MAAM,KAAKX,cAAc,CAACU,OAAO,CAACC,MAAM,IAAI,CAACpB,WAAW,CAACS,cAAc,CAACU,OAAO,EAAEH,GAAG,CAAC,IAAI,CAAChB,WAAW,CAACU,WAAW,CAACS,OAAO,EAAEb,IAAI,CAAC,EAAE;QACxI,CAACM,EAAE,GAAGD,SAAS,CAACQ,OAAO,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAACV,SAAS,CAAC;QAChFF,cAAc,CAACU,OAAO,GAAGH,GAAG;QAC5BN,WAAW,CAACS,OAAO,GAAGb,IAAI;QAC1BK,SAAS,CAACQ,OAAO,GAAGd,MAAM,CAAC,CAAC;MAC9B;IACF,CAAC,CAAC;IACFN,UAAU,CAAC,YAAY;MACrB,IAAIa,EAAE;MACN,CAACA,EAAE,GAAGD,SAAS,CAACQ,OAAO,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,IAAI,CAACV,SAAS,CAAC;MAChF;MACAH,UAAU,CAACW,OAAO,GAAG,KAAK;IAC5B,CAAC,CAAC;EACJ,CAAC;EACD,OAAOf,mBAAmB;AAC5B,CAAC;AACD,eAAeF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}