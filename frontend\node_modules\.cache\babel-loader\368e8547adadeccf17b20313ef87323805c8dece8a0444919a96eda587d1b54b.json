{"ast": null, "code": "var isAppleDevice = /(mac|iphone|ipod|ipad)/i.test(typeof navigator !== 'undefined' ? navigator === null || navigator === void 0 ? void 0 : navigator.platform : '');\nexport default isAppleDevice;", "map": {"version": 3, "names": ["isAppleDevice", "test", "navigator", "platform"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/isAppleDevice.js"], "sourcesContent": ["var isAppleDevice = /(mac|iphone|ipod|ipad)/i.test(typeof navigator !== 'undefined' ? navigator === null || navigator === void 0 ? void 0 : navigator.platform : '');\nexport default isAppleDevice;"], "mappings": "AAAA,IAAIA,aAAa,GAAG,yBAAyB,CAACC,IAAI,CAAC,OAAOC,SAAS,KAAK,WAAW,GAAGA,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAACC,QAAQ,GAAG,EAAE,CAAC;AACpK,eAAeH,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}