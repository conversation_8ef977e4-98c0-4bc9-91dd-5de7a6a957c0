{"ast": null, "code": "import { useLayoutEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useLayoutEffect);", "map": {"version": 3, "names": ["useLayoutEffect", "createUpdateEffect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useUpdateLayoutEffect/index.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useLayoutEffect);"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,eAAeA,kBAAkB,CAACD,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}