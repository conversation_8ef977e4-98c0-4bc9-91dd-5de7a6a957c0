{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\OCR\\\\ParsedFieldsDisplay.js\",\n  _s = $RefreshSig$();\n/**\n * 解析結果展示組件\n */\n\nimport React, { useState } from 'react';\nimport { Card, Button, Space, Collapse, Tag } from 'antd-mobile';\nimport { EditSOutline, EyeOutline, CheckCircleOutline, DownOutline } from 'antd-mobile-icons';\nimport { getFieldDisplayName } from '../../utils/validation';\nimport { formatFormData } from '../../utils/formatters';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ParsedFieldsDisplay = ({\n  frontFields = {},\n  backFields = {},\n  mergedFields = {},\n  onApplyToForm,\n  onEditField,\n  showMerged = true,\n  style = {},\n  className = ''\n}) => {\n  _s();\n  const [activeKey, setActiveKey] = useState(showMerged ? ['merged'] : []);\n\n  // 渲染欄位列表\n  const renderFields = (fields, title, color = '#1677ff') => {\n    const fieldEntries = Object.entries(fields).filter(([key, value]) => value && value.trim());\n    if (fieldEntries.length === 0) {\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '20px',\n          color: '#8c8c8c'\n        },\n        children: \"\\u66AB\\u7121\\u89E3\\u6790\\u7D50\\u679C\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '12px',\n          display: 'flex',\n          alignItems: 'center',\n          gap: '8px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CheckCircleOutline, {\n          style: {\n            color\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          style: {\n            fontWeight: 'bold',\n            color\n          },\n          children: title\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tag, {\n          color: color,\n          fill: \"outline\",\n          style: {\n            marginLeft: 'auto'\n          },\n          children: [fieldEntries.length, \" \\u500B\\u6B04\\u4F4D\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          flexDirection: 'column',\n          gap: '8px'\n        },\n        children: fieldEntries.map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center',\n            padding: '8px 12px',\n            backgroundColor: '#f8f9fa',\n            borderRadius: '6px',\n            border: '1px solid #e9ecef'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              flex: 1\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#6c757d',\n                marginBottom: '2px'\n              },\n              children: getFieldDisplayName(key)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '14px',\n                color: '#212529',\n                wordBreak: 'break-all'\n              },\n              children: value\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 68,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 64,\n            columnNumber: 15\n          }, this), onEditField && /*#__PURE__*/_jsxDEV(Button, {\n            size: \"mini\",\n            fill: \"none\",\n            onClick: () => onEditField(key, value),\n            style: {\n              marginLeft: '8px'\n            },\n            children: /*#__PURE__*/_jsxDEV(EditSOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 17\n          }, this)]\n        }, key, true, {\n          fileName: _jsxFileName,\n          lineNumber: 52,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this);\n  };\n\n  // 處理應用到表單\n  const handleApplyToForm = fields => {\n    if (onApplyToForm) {\n      const formattedFields = formatFormData(fields);\n      onApplyToForm(formattedFields);\n    }\n  };\n  const hasFrontFields = Object.keys(frontFields).some(key => frontFields[key] && frontFields[key].trim());\n  const hasBackFields = Object.keys(backFields).some(key => backFields[key] && backFields[key].trim());\n  const hasMergedFields = Object.keys(mergedFields).some(key => mergedFields[key] && mergedFields[key].trim());\n  if (!hasFrontFields && !hasBackFields && !hasMergedFields) {\n    return null;\n  }\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: \"OCR\\u89E3\\u6790\\u7D50\\u679C\",\n    className: `parsed-fields-display ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(Collapse, {\n      activeKey: activeKey,\n      onChange: setActiveKey,\n      accordion: false,\n      children: [showMerged && hasMergedFields && /*#__PURE__*/_jsxDEV(Collapse.Panel, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(CheckCircleOutline, {\n            style: {\n              color: '#52c41a'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 17\n          }, this), \"\\u667A\\u80FD\\u5408\\u4F75\\u7D50\\u679C\", /*#__PURE__*/_jsxDEV(Tag, {\n            color: \"#52c41a\",\n            fill: \"outline\",\n            children: \"\\u63A8\\u85A6\\u4F7F\\u7528\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 125,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 122,\n          columnNumber: 15\n        }, this),\n        children: [renderFields(mergedFields, '合併後的欄位', '#52c41a'), onApplyToForm && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"primary\",\n            onClick: () => handleApplyToForm(mergedFields),\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 19\n            }, this), \" \\u61C9\\u7528\\u5230\\u8868\\u55AE\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 134,\n          columnNumber: 15\n        }, this)]\n      }, \"merged\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 119,\n        columnNumber: 11\n      }, this), hasFrontFields && /*#__PURE__*/_jsxDEV(Collapse.Panel, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(EyeOutline, {\n            style: {\n              color: '#1677ff'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 152,\n            columnNumber: 17\n          }, this), \"\\u6B63\\u9762\\u89E3\\u6790\\u7D50\\u679C\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 15\n        }, this),\n        children: [renderFields(frontFields, '正面欄位', '#1677ff'), onApplyToForm && !showMerged && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"primary\",\n            fill: \"outline\",\n            onClick: () => handleApplyToForm(frontFields),\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 166,\n              columnNumber: 19\n            }, this), \" \\u61C9\\u7528\\u5230\\u8868\\u55AE\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 161,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 15\n        }, this)]\n      }, \"front\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 148,\n        columnNumber: 11\n      }, this), hasBackFields && /*#__PURE__*/_jsxDEV(Collapse.Panel, {\n        title: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            alignItems: 'center',\n            gap: '8px'\n          },\n          children: [/*#__PURE__*/_jsxDEV(EyeOutline, {\n            style: {\n              color: '#722ed1'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 17\n          }, this), \"\\u53CD\\u9762\\u89E3\\u6790\\u7D50\\u679C\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 15\n        }, this),\n        children: [renderFields(backFields, '反面欄位', '#722ed1'), onApplyToForm && !showMerged && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"primary\",\n            fill: \"outline\",\n            onClick: () => handleApplyToForm(backFields),\n            children: [/*#__PURE__*/_jsxDEV(CheckCircleOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 19\n            }, this), \" \\u61C9\\u7528\\u5230\\u8868\\u55AE\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 188,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 187,\n          columnNumber: 15\n        }, this)]\n      }, \"back\", true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginTop: '16px',\n        padding: '12px',\n        backgroundColor: '#f0f2f5',\n        borderRadius: '6px',\n        fontSize: '12px',\n        color: '#8c8c8c'\n      },\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: 'flex',\n          justifyContent: 'space-between'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u6B63\\u9762: \", Object.keys(frontFields).filter(key => frontFields[key]).length, \" \\u500B\\u6B04\\u4F4D\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 213,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u53CD\\u9762: \", Object.keys(backFields).filter(key => backFields[key]).length, \" \\u500B\\u6B04\\u4F4D\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 214,\n          columnNumber: 11\n        }, this), showMerged && /*#__PURE__*/_jsxDEV(\"span\", {\n          children: [\"\\u5408\\u4F75: \", Object.keys(mergedFields).filter(key => mergedFields[key]).length, \" \\u500B\\u6B04\\u4F4D\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 216,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 212,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 202,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 107,\n    columnNumber: 5\n  }, this);\n};\n_s(ParsedFieldsDisplay, \"H5Eh7st+nGt0JeBBkgojndnAjdM=\");\n_c = ParsedFieldsDisplay;\nexport default ParsedFieldsDisplay;\nvar _c;\n$RefreshReg$(_c, \"ParsedFieldsDisplay\");", "map": {"version": 3, "names": ["React", "useState", "Card", "<PERSON><PERSON>", "Space", "Collapse", "Tag", "EditSOutline", "EyeOutline", "CheckCircleOutline", "DownOutline", "getFieldDisplayName", "formatFormData", "jsxDEV", "_jsxDEV", "ParsedFieldsDisplay", "frontFields", "backFields", "mergedFields", "onApplyToForm", "onEditField", "showMerged", "style", "className", "_s", "active<PERSON><PERSON>", "setActiveKey", "renderFields", "fields", "title", "color", "fieldEntries", "Object", "entries", "filter", "key", "value", "trim", "length", "textAlign", "padding", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "display", "alignItems", "gap", "fontWeight", "fill", "marginLeft", "flexDirection", "map", "justifyContent", "backgroundColor", "borderRadius", "border", "flex", "fontSize", "wordBreak", "size", "onClick", "handleApplyToForm", "formattedFields", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keys", "some", "<PERSON><PERSON><PERSON><PERSON>ields", "hasMerged<PERSON><PERSON>s", "onChange", "accordion", "Panel", "marginTop", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/OCR/ParsedFieldsDisplay.js"], "sourcesContent": ["/**\n * 解析結果展示組件\n */\n\nimport React, { useState } from 'react';\nimport { Card, Button, Space, Collapse, Tag } from 'antd-mobile';\nimport { \n  EditSOutline, \n  EyeOutline, \n  CheckCircleOutline,\n  DownOutline \n} from 'antd-mobile-icons';\nimport { getFieldDisplayName } from '../../utils/validation';\nimport { formatFormData } from '../../utils/formatters';\n\nconst ParsedFieldsDisplay = ({\n  frontFields = {},\n  backFields = {},\n  mergedFields = {},\n  onApplyToForm,\n  onEditField,\n  showMerged = true,\n  style = {},\n  className = ''\n}) => {\n  const [activeKey, setActiveKey] = useState(showMerged ? ['merged'] : []);\n\n  // 渲染欄位列表\n  const renderFields = (fields, title, color = '#1677ff') => {\n    const fieldEntries = Object.entries(fields).filter(([key, value]) => value && value.trim());\n    \n    if (fieldEntries.length === 0) {\n      return (\n        <div style={{ textAlign: 'center', padding: '20px', color: '#8c8c8c' }}>\n          暫無解析結果\n        </div>\n      );\n    }\n\n    return (\n      <div>\n        <div style={{ marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>\n          <CheckCircleOutline style={{ color }} />\n          <span style={{ fontWeight: 'bold', color }}>{title}</span>\n          <Tag color={color} fill=\"outline\" style={{ marginLeft: 'auto' }}>\n            {fieldEntries.length} 個欄位\n          </Tag>\n        </div>\n        \n        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>\n          {fieldEntries.map(([key, value]) => (\n            <div \n              key={key}\n              style={{\n                display: 'flex',\n                justifyContent: 'space-between',\n                alignItems: 'center',\n                padding: '8px 12px',\n                backgroundColor: '#f8f9fa',\n                borderRadius: '6px',\n                border: '1px solid #e9ecef'\n              }}\n            >\n              <div style={{ flex: 1 }}>\n                <div style={{ fontSize: '12px', color: '#6c757d', marginBottom: '2px' }}>\n                  {getFieldDisplayName(key)}\n                </div>\n                <div style={{ fontSize: '14px', color: '#212529', wordBreak: 'break-all' }}>\n                  {value}\n                </div>\n              </div>\n              \n              {onEditField && (\n                <Button\n                  size=\"mini\"\n                  fill=\"none\"\n                  onClick={() => onEditField(key, value)}\n                  style={{ marginLeft: '8px' }}\n                >\n                  <EditSOutline />\n                </Button>\n              )}\n            </div>\n          ))}\n        </div>\n      </div>\n    );\n  };\n\n  // 處理應用到表單\n  const handleApplyToForm = (fields) => {\n    if (onApplyToForm) {\n      const formattedFields = formatFormData(fields);\n      onApplyToForm(formattedFields);\n    }\n  };\n\n  const hasFrontFields = Object.keys(frontFields).some(key => frontFields[key] && frontFields[key].trim());\n  const hasBackFields = Object.keys(backFields).some(key => backFields[key] && backFields[key].trim());\n  const hasMergedFields = Object.keys(mergedFields).some(key => mergedFields[key] && mergedFields[key].trim());\n\n  if (!hasFrontFields && !hasBackFields && !hasMergedFields) {\n    return null;\n  }\n\n  return (\n    <Card \n      title=\"OCR解析結果\"\n      className={`parsed-fields-display ${className}`}\n      style={style}\n    >\n      <Collapse \n        activeKey={activeKey}\n        onChange={setActiveKey}\n        accordion={false}\n      >\n        {/* 合併結果 */}\n        {showMerged && hasMergedFields && (\n          <Collapse.Panel \n            key=\"merged\" \n            title={\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <CheckCircleOutline style={{ color: '#52c41a' }} />\n                智能合併結果\n                <Tag color=\"#52c41a\" fill=\"outline\">\n                  推薦使用\n                </Tag>\n              </div>\n            }\n          >\n            {renderFields(mergedFields, '合併後的欄位', '#52c41a')}\n            \n            {onApplyToForm && (\n              <div style={{ marginTop: '16px', textAlign: 'center' }}>\n                <Button\n                  color=\"primary\"\n                  onClick={() => handleApplyToForm(mergedFields)}\n                >\n                  <CheckCircleOutline /> 應用到表單\n                </Button>\n              </div>\n            )}\n          </Collapse.Panel>\n        )}\n\n        {/* 正面結果 */}\n        {hasFrontFields && (\n          <Collapse.Panel \n            key=\"front\" \n            title={\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <EyeOutline style={{ color: '#1677ff' }} />\n                正面解析結果\n              </div>\n            }\n          >\n            {renderFields(frontFields, '正面欄位', '#1677ff')}\n            \n            {onApplyToForm && !showMerged && (\n              <div style={{ marginTop: '16px', textAlign: 'center' }}>\n                <Button\n                  color=\"primary\"\n                  fill=\"outline\"\n                  onClick={() => handleApplyToForm(frontFields)}\n                >\n                  <CheckCircleOutline /> 應用到表單\n                </Button>\n              </div>\n            )}\n          </Collapse.Panel>\n        )}\n\n        {/* 反面結果 */}\n        {hasBackFields && (\n          <Collapse.Panel \n            key=\"back\" \n            title={\n              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n                <EyeOutline style={{ color: '#722ed1' }} />\n                反面解析結果\n              </div>\n            }\n          >\n            {renderFields(backFields, '反面欄位', '#722ed1')}\n            \n            {onApplyToForm && !showMerged && (\n              <div style={{ marginTop: '16px', textAlign: 'center' }}>\n                <Button\n                  color=\"primary\"\n                  fill=\"outline\"\n                  onClick={() => handleApplyToForm(backFields)}\n                >\n                  <CheckCircleOutline /> 應用到表單\n                </Button>\n              </div>\n            )}\n          </Collapse.Panel>\n        )}\n      </Collapse>\n\n      {/* 統計信息 */}\n      <div \n        style={{ \n          marginTop: '16px', \n          padding: '12px',\n          backgroundColor: '#f0f2f5',\n          borderRadius: '6px',\n          fontSize: '12px',\n          color: '#8c8c8c'\n        }}\n      >\n        <div style={{ display: 'flex', justifyContent: 'space-between' }}>\n          <span>正面: {Object.keys(frontFields).filter(key => frontFields[key]).length} 個欄位</span>\n          <span>反面: {Object.keys(backFields).filter(key => backFields[key]).length} 個欄位</span>\n          {showMerged && (\n            <span>合併: {Object.keys(mergedFields).filter(key => mergedFields[key]).length} 個欄位</span>\n          )}\n        </div>\n      </div>\n    </Card>\n  );\n};\n\nexport default ParsedFieldsDisplay;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,EAAEC,GAAG,QAAQ,aAAa;AAChE,SACEC,YAAY,EACZC,UAAU,EACVC,kBAAkB,EAClBC,WAAW,QACN,mBAAmB;AAC1B,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,cAAc,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAMC,mBAAmB,GAAGA,CAAC;EAC3BC,WAAW,GAAG,CAAC,CAAC;EAChBC,UAAU,GAAG,CAAC,CAAC;EACfC,YAAY,GAAG,CAAC,CAAC;EACjBC,aAAa;EACbC,WAAW;EACXC,UAAU,GAAG,IAAI;EACjBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGzB,QAAQ,CAACoB,UAAU,GAAG,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;;EAExE;EACA,MAAMM,YAAY,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,KAAK,GAAG,SAAS,KAAK;IACzD,MAAMC,YAAY,GAAGC,MAAM,CAACC,OAAO,CAACL,MAAM,CAAC,CAACM,MAAM,CAAC,CAAC,CAACC,GAAG,EAAEC,KAAK,CAAC,KAAKA,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;IAE3F,IAAIN,YAAY,CAACO,MAAM,KAAK,CAAC,EAAE;MAC7B,oBACExB,OAAA;QAAKQ,KAAK,EAAE;UAAEiB,SAAS,EAAE,QAAQ;UAAEC,OAAO,EAAE,MAAM;UAAEV,KAAK,EAAE;QAAU,CAAE;QAAAW,QAAA,EAAC;MAExE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAK,CAAC;IAEV;IAEA,oBACE/B,OAAA;MAAA2B,QAAA,gBACE3B,OAAA;QAAKQ,KAAK,EAAE;UAAEwB,YAAY,EAAE,MAAM;UAAEC,OAAO,EAAE,MAAM;UAAEC,UAAU,EAAE,QAAQ;UAAEC,GAAG,EAAE;QAAM,CAAE;QAAAR,QAAA,gBACtF3B,OAAA,CAACL,kBAAkB;UAACa,KAAK,EAAE;YAAEQ;UAAM;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACxC/B,OAAA;UAAMQ,KAAK,EAAE;YAAE4B,UAAU,EAAE,MAAM;YAAEpB;UAAM,CAAE;UAAAW,QAAA,EAAEZ;QAAK;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC,eAC1D/B,OAAA,CAACR,GAAG;UAACwB,KAAK,EAAEA,KAAM;UAACqB,IAAI,EAAC,SAAS;UAAC7B,KAAK,EAAE;YAAE8B,UAAU,EAAE;UAAO,CAAE;UAAAX,QAAA,GAC7DV,YAAY,CAACO,MAAM,EAAC,qBACvB;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEN/B,OAAA;QAAKQ,KAAK,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEM,aAAa,EAAE,QAAQ;UAAEJ,GAAG,EAAE;QAAM,CAAE;QAAAR,QAAA,EAClEV,YAAY,CAACuB,GAAG,CAAC,CAAC,CAACnB,GAAG,EAAEC,KAAK,CAAC,kBAC7BtB,OAAA;UAEEQ,KAAK,EAAE;YACLyB,OAAO,EAAE,MAAM;YACfQ,cAAc,EAAE,eAAe;YAC/BP,UAAU,EAAE,QAAQ;YACpBR,OAAO,EAAE,UAAU;YACnBgB,eAAe,EAAE,SAAS;YAC1BC,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE;UACV,CAAE;UAAAjB,QAAA,gBAEF3B,OAAA;YAAKQ,KAAK,EAAE;cAAEqC,IAAI,EAAE;YAAE,CAAE;YAAAlB,QAAA,gBACtB3B,OAAA;cAAKQ,KAAK,EAAE;gBAAEsC,QAAQ,EAAE,MAAM;gBAAE9B,KAAK,EAAE,SAAS;gBAAEgB,YAAY,EAAE;cAAM,CAAE;cAAAL,QAAA,EACrE9B,mBAAmB,CAACwB,GAAG;YAAC;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtB,CAAC,eACN/B,OAAA;cAAKQ,KAAK,EAAE;gBAAEsC,QAAQ,EAAE,MAAM;gBAAE9B,KAAK,EAAE,SAAS;gBAAE+B,SAAS,EAAE;cAAY,CAAE;cAAApB,QAAA,EACxEL;YAAK;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,EAELzB,WAAW,iBACVN,OAAA,CAACX,MAAM;YACL2D,IAAI,EAAC,MAAM;YACXX,IAAI,EAAC,MAAM;YACXY,OAAO,EAAEA,CAAA,KAAM3C,WAAW,CAACe,GAAG,EAAEC,KAAK,CAAE;YACvCd,KAAK,EAAE;cAAE8B,UAAU,EAAE;YAAM,CAAE;YAAAX,QAAA,eAE7B3B,OAAA,CAACP,YAAY;cAAAmC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CACT;QAAA,GA7BIV,GAAG;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OA8BL,CACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAEV,CAAC;;EAED;EACA,MAAMmB,iBAAiB,GAAIpC,MAAM,IAAK;IACpC,IAAIT,aAAa,EAAE;MACjB,MAAM8C,eAAe,GAAGrD,cAAc,CAACgB,MAAM,CAAC;MAC9CT,aAAa,CAAC8C,eAAe,CAAC;IAChC;EACF,CAAC;EAED,MAAMC,cAAc,GAAGlC,MAAM,CAACmC,IAAI,CAACnD,WAAW,CAAC,CAACoD,IAAI,CAACjC,GAAG,IAAInB,WAAW,CAACmB,GAAG,CAAC,IAAInB,WAAW,CAACmB,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACxG,MAAMgC,aAAa,GAAGrC,MAAM,CAACmC,IAAI,CAAClD,UAAU,CAAC,CAACmD,IAAI,CAACjC,GAAG,IAAIlB,UAAU,CAACkB,GAAG,CAAC,IAAIlB,UAAU,CAACkB,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACpG,MAAMiC,eAAe,GAAGtC,MAAM,CAACmC,IAAI,CAACjD,YAAY,CAAC,CAACkD,IAAI,CAACjC,GAAG,IAAIjB,YAAY,CAACiB,GAAG,CAAC,IAAIjB,YAAY,CAACiB,GAAG,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EAE5G,IAAI,CAAC6B,cAAc,IAAI,CAACG,aAAa,IAAI,CAACC,eAAe,EAAE;IACzD,OAAO,IAAI;EACb;EAEA,oBACExD,OAAA,CAACZ,IAAI;IACH2B,KAAK,EAAC,6BAAS;IACfN,SAAS,EAAE,yBAAyBA,SAAS,EAAG;IAChDD,KAAK,EAAEA,KAAM;IAAAmB,QAAA,gBAEb3B,OAAA,CAACT,QAAQ;MACPoB,SAAS,EAAEA,SAAU;MACrB8C,QAAQ,EAAE7C,YAAa;MACvB8C,SAAS,EAAE,KAAM;MAAA/B,QAAA,GAGhBpB,UAAU,IAAIiD,eAAe,iBAC5BxD,OAAA,CAACT,QAAQ,CAACoE,KAAK;QAEb5C,KAAK,eACHf,OAAA;UAAKQ,KAAK,EAAE;YAAEyB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAR,QAAA,gBAChE3B,OAAA,CAACL,kBAAkB;YAACa,KAAK,EAAE;cAAEQ,KAAK,EAAE;YAAU;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wCAEnD,eAAA/B,OAAA,CAACR,GAAG;YAACwB,KAAK,EAAC,SAAS;YAACqB,IAAI,EAAC,SAAS;YAAAV,QAAA,EAAC;UAEpC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;QAAAJ,QAAA,GAEAd,YAAY,CAACT,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,EAE/CC,aAAa,iBACZL,OAAA;UAAKQ,KAAK,EAAE;YAAEoD,SAAS,EAAE,MAAM;YAAEnC,SAAS,EAAE;UAAS,CAAE;UAAAE,QAAA,eACrD3B,OAAA,CAACX,MAAM;YACL2B,KAAK,EAAC,SAAS;YACfiC,OAAO,EAAEA,CAAA,KAAMC,iBAAiB,CAAC9C,YAAY,CAAE;YAAAuB,QAAA,gBAE/C3B,OAAA,CAACL,kBAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mCACxB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA,GAtBG,QAAQ;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAuBE,CACjB,EAGAqB,cAAc,iBACbpD,OAAA,CAACT,QAAQ,CAACoE,KAAK;QAEb5C,KAAK,eACHf,OAAA;UAAKQ,KAAK,EAAE;YAAEyB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAR,QAAA,gBAChE3B,OAAA,CAACN,UAAU;YAACc,KAAK,EAAE;cAAEQ,KAAK,EAAE;YAAU;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;QAAAJ,QAAA,GAEAd,YAAY,CAACX,WAAW,EAAE,MAAM,EAAE,SAAS,CAAC,EAE5CG,aAAa,IAAI,CAACE,UAAU,iBAC3BP,OAAA;UAAKQ,KAAK,EAAE;YAAEoD,SAAS,EAAE,MAAM;YAAEnC,SAAS,EAAE;UAAS,CAAE;UAAAE,QAAA,eACrD3B,OAAA,CAACX,MAAM;YACL2B,KAAK,EAAC,SAAS;YACfqB,IAAI,EAAC,SAAS;YACdY,OAAO,EAAEA,CAAA,KAAMC,iBAAiB,CAAChD,WAAW,CAAE;YAAAyB,QAAA,gBAE9C3B,OAAA,CAACL,kBAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mCACxB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA,GApBG,OAAO;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBG,CACjB,EAGAwB,aAAa,iBACZvD,OAAA,CAACT,QAAQ,CAACoE,KAAK;QAEb5C,KAAK,eACHf,OAAA;UAAKQ,KAAK,EAAE;YAAEyB,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,GAAG,EAAE;UAAM,CAAE;UAAAR,QAAA,gBAChE3B,OAAA,CAACN,UAAU;YAACc,KAAK,EAAE;cAAEQ,KAAK,EAAE;YAAU;UAAE;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,wCAE7C;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CACN;QAAAJ,QAAA,GAEAd,YAAY,CAACV,UAAU,EAAE,MAAM,EAAE,SAAS,CAAC,EAE3CE,aAAa,IAAI,CAACE,UAAU,iBAC3BP,OAAA;UAAKQ,KAAK,EAAE;YAAEoD,SAAS,EAAE,MAAM;YAAEnC,SAAS,EAAE;UAAS,CAAE;UAAAE,QAAA,eACrD3B,OAAA,CAACX,MAAM;YACL2B,KAAK,EAAC,SAAS;YACfqB,IAAI,EAAC,SAAS;YACdY,OAAO,EAAEA,CAAA,KAAMC,iBAAiB,CAAC/C,UAAU,CAAE;YAAAwB,QAAA,gBAE7C3B,OAAA,CAACL,kBAAkB;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,mCACxB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CACN;MAAA,GApBG,MAAM;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAqBI,CACjB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACO,CAAC,eAGX/B,OAAA;MACEQ,KAAK,EAAE;QACLoD,SAAS,EAAE,MAAM;QACjBlC,OAAO,EAAE,MAAM;QACfgB,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE,KAAK;QACnBG,QAAQ,EAAE,MAAM;QAChB9B,KAAK,EAAE;MACT,CAAE;MAAAW,QAAA,eAEF3B,OAAA;QAAKQ,KAAK,EAAE;UAAEyB,OAAO,EAAE,MAAM;UAAEQ,cAAc,EAAE;QAAgB,CAAE;QAAAd,QAAA,gBAC/D3B,OAAA;UAAA2B,QAAA,GAAM,gBAAI,EAACT,MAAM,CAACmC,IAAI,CAACnD,WAAW,CAAC,CAACkB,MAAM,CAACC,GAAG,IAAInB,WAAW,CAACmB,GAAG,CAAC,CAAC,CAACG,MAAM,EAAC,qBAAI;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtF/B,OAAA;UAAA2B,QAAA,GAAM,gBAAI,EAACT,MAAM,CAACmC,IAAI,CAAClD,UAAU,CAAC,CAACiB,MAAM,CAACC,GAAG,IAAIlB,UAAU,CAACkB,GAAG,CAAC,CAAC,CAACG,MAAM,EAAC,qBAAI;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,EACnFxB,UAAU,iBACTP,OAAA;UAAA2B,QAAA,GAAM,gBAAI,EAACT,MAAM,CAACmC,IAAI,CAACjD,YAAY,CAAC,CAACgB,MAAM,CAACC,GAAG,IAAIjB,YAAY,CAACiB,GAAG,CAAC,CAAC,CAACG,MAAM,EAAC,qBAAI;QAAA;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CACxF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACrB,EAAA,CA9MIT,mBAAmB;AAAA4D,EAAA,GAAnB5D,mBAAmB;AAgNzB,eAAeA,mBAAmB;AAAC,IAAA4D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}