{"ast": null, "code": "import { useLayoutEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useLayoutEffect);", "map": {"version": 3, "names": ["useLayoutEffect", "createDeepCompareEffect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDeepCompareLayoutEffect/index.js"], "sourcesContent": ["import { useLayoutEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useLayoutEffect);"], "mappings": "AAAA,SAASA,eAAe,QAAQ,OAAO;AACvC,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,eAAeA,uBAAuB,CAACD,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}