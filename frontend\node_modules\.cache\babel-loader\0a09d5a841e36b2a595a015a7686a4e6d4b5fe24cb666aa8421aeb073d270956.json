{"ast": null, "code": "/**\n * 設備和環境檢測工具\n * 用於識別運行環境並提供相應的功能支持檢測\n */\n\n/**\n * 檢測是否可以使用DOM\n */\nexport const canUseDom = () => {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n};\n\n/**\n * 檢測是否為移動設備\n */\nexport const isMobileDevice = () => {\n  if (!canUseDom()) return false;\n  const userAgent = navigator.userAgent || navigator.vendor || window.opera;\n\n  // 檢測移動設備的用戶代理字符串\n  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;\n\n  // 檢測觸摸支持\n  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n\n  // 檢測屏幕尺寸（移動設備通常寬度較小）\n  const isSmallScreen = window.innerWidth <= 768;\n  return mobileRegex.test(userAgent) || hasTouchSupport && isSmallScreen;\n};\n\n/**\n * 檢測是否為Android設備\n */\nexport const isAndroid = () => {\n  if (!canUseDom()) return false;\n  return /android/i.test(navigator.userAgent);\n};\n\n/**\n * 檢測是否為iOS設備\n */\nexport const isIOS = () => {\n  if (!canUseDom()) return false;\n  return /ios|iphone|ipad|ipod/i.test(navigator.userAgent);\n};\n\n/**\n * 檢測是否為Web瀏覽器環境\n */\nexport const isWebBrowser = () => {\n  return canUseDom() && !isMobileDevice();\n};\n\n/**\n * 檢測是否為平板設備\n */\nexport const isTablet = () => {\n  if (!canUseDom()) return false;\n  const userAgent = navigator.userAgent;\n  const isTabletUA = /ipad|android(?!.*mobile)|tablet/i.test(userAgent);\n  const isLargeScreen = window.innerWidth >= 768 && window.innerWidth <= 1024;\n  return isTabletUA || isMobileDevice() && isLargeScreen;\n};\n\n/**\n * 獲取設備類型\n */\nexport const getDeviceType = () => {\n  if (isTablet()) return 'tablet';\n  if (isMobileDevice()) return 'mobile';\n  return 'desktop';\n};\n\n/**\n * 檢測相機功能支持\n */\nexport const getCameraCapabilities = async () => {\n  const capabilities = {\n    hasCamera: false,\n    hasUserMedia: false,\n    supportsFacingMode: false,\n    supportsConstraints: false,\n    availableCameras: []\n  };\n  if (!canUseDom() || !navigator.mediaDevices) {\n    return capabilities;\n  }\n  try {\n    // 檢測 getUserMedia 支持\n    capabilities.hasUserMedia = !!navigator.mediaDevices.getUserMedia;\n    if (capabilities.hasUserMedia) {\n      // 嘗試獲取設備列表\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const videoDevices = devices.filter(device => device.kind === 'videoinput');\n      capabilities.hasCamera = videoDevices.length > 0;\n      capabilities.availableCameras = videoDevices.map(device => ({\n        deviceId: device.deviceId,\n        label: device.label || `Camera ${videoDevices.indexOf(device) + 1}`,\n        groupId: device.groupId\n      }));\n\n      // 檢測是否支持 facingMode 約束\n      if (capabilities.hasCamera) {\n        try {\n          const stream = await navigator.mediaDevices.getUserMedia({\n            video: {\n              facingMode: 'environment'\n            }\n          });\n          capabilities.supportsFacingMode = true;\n          capabilities.supportsConstraints = true;\n\n          // 立即停止測試流\n          stream.getTracks().forEach(track => track.stop());\n        } catch (error) {\n          // 如果 facingMode 不支持，嘗試基本的視頻約束\n          try {\n            const stream = await navigator.mediaDevices.getUserMedia({\n              video: true\n            });\n            capabilities.supportsConstraints = true;\n            stream.getTracks().forEach(track => track.stop());\n          } catch (basicError) {\n            console.warn('基本相機功能不可用:', basicError);\n          }\n        }\n      }\n    }\n  } catch (error) {\n    console.warn('檢測相機功能時出錯:', error);\n  }\n  return capabilities;\n};\n\n/**\n * 檢測是否支持全屏API\n */\nexport const supportsFullscreen = () => {\n  if (!canUseDom()) return false;\n  return !!(document.fullscreenEnabled || document.webkitFullscreenEnabled || document.mozFullScreenEnabled || document.msFullscreenEnabled);\n};\n\n/**\n * 檢測設備方向支持\n */\nexport const supportsOrientation = () => {\n  if (!canUseDom()) return false;\n  return 'orientation' in window || 'onorientationchange' in window;\n};\n\n/**\n * 獲取當前設備方向\n */\nexport const getDeviceOrientation = () => {\n  if (!supportsOrientation()) return 'unknown';\n  if (window.orientation !== undefined) {\n    const orientation = Math.abs(window.orientation);\n    return orientation === 90 ? 'landscape' : 'portrait';\n  }\n\n  // 備用方案：基於屏幕尺寸判斷\n  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';\n};\n\n/**\n * 綜合環境信息\n */\nexport const getEnvironmentInfo = async () => {\n  const cameraCapabilities = await getCameraCapabilities();\n  return {\n    deviceType: getDeviceType(),\n    isMobile: isMobileDevice(),\n    isTablet: isTablet(),\n    isDesktop: isWebBrowser(),\n    isAndroid: isAndroid(),\n    isIOS: isIOS(),\n    orientation: getDeviceOrientation(),\n    supportsFullscreen: supportsFullscreen(),\n    supportsOrientation: supportsOrientation(),\n    camera: cameraCapabilities,\n    userAgent: canUseDom() ? navigator.userAgent : '',\n    screenSize: canUseDom() ? {\n      width: window.innerWidth,\n      height: window.innerHeight,\n      pixelRatio: window.devicePixelRatio || 1\n    } : null\n  };\n};\n\n/**\n * 推薦的拍照模式\n */\nexport const getRecommendedCameraMode = async () => {\n  const envInfo = await getEnvironmentInfo();\n  if (!envInfo.camera.hasCamera) {\n    return 'none'; // 無相機支持\n  }\n  if (envInfo.isMobile || envInfo.isTablet) {\n    return 'mobile'; // 移動端全屏模式\n  }\n  return 'web'; // Web端模態框模式\n};\n\n// 導出對象\nconst deviceDetectorExports = {\n  canUseDom,\n  isMobileDevice,\n  isAndroid,\n  isIOS,\n  isWebBrowser,\n  isTablet,\n  getDeviceType,\n  getCameraCapabilities,\n  supportsFullscreen,\n  supportsOrientation,\n  getDeviceOrientation,\n  getEnvironmentInfo,\n  getRecommendedCameraMode\n};\nexport default deviceDetectorExports;", "map": {"version": 3, "names": ["canUseDom", "window", "document", "createElement", "isMobileDevice", "userAgent", "navigator", "vendor", "opera", "mobileRegex", "hasTouchSupport", "maxTouchPoints", "isSmallScreen", "innerWidth", "test", "isAndroid", "isIOS", "is<PERSON><PERSON><PERSON><PERSON><PERSON>", "isTablet", "isTabletUA", "isLargeScreen", "getDeviceType", "getCameraCapabilities", "capabilities", "hasCamera", "hasUserMedia", "supportsFacingMode", "supportsConstraints", "availableCameras", "mediaDevices", "getUserMedia", "devices", "enumerateDevices", "videoDevices", "filter", "device", "kind", "length", "map", "deviceId", "label", "indexOf", "groupId", "stream", "video", "facingMode", "getTracks", "for<PERSON>ach", "track", "stop", "error", "basicError", "console", "warn", "supportsFullscreen", "fullscreenEnabled", "webkitFullscreenEnabled", "mozFullScreenEnabled", "msFullscreenEnabled", "supportsOrientation", "getDeviceOrientation", "orientation", "undefined", "Math", "abs", "innerHeight", "getEnvironmentInfo", "cameraCapabilities", "deviceType", "isMobile", "isDesktop", "camera", "screenSize", "width", "height", "pixelRatio", "devicePixelRatio", "getRecommendedCameraMode", "envInfo", "deviceDetectorExports"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/utils/deviceDetector.js"], "sourcesContent": ["/**\n * 設備和環境檢測工具\n * 用於識別運行環境並提供相應的功能支持檢測\n */\n\n/**\n * 檢測是否可以使用DOM\n */\nexport const canUseDom = () => {\n  return !!(typeof window !== 'undefined' && window.document && window.document.createElement);\n};\n\n/**\n * 檢測是否為移動設備\n */\nexport const isMobileDevice = () => {\n  if (!canUseDom()) return false;\n  \n  const userAgent = navigator.userAgent || navigator.vendor || window.opera;\n  \n  // 檢測移動設備的用戶代理字符串\n  const mobileRegex = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i;\n  \n  // 檢測觸摸支持\n  const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;\n  \n  // 檢測屏幕尺寸（移動設備通常寬度較小）\n  const isSmallScreen = window.innerWidth <= 768;\n  \n  return mobileRegex.test(userAgent) || (hasTouchSupport && isSmallScreen);\n};\n\n/**\n * 檢測是否為Android設備\n */\nexport const isAndroid = () => {\n  if (!canUseDom()) return false;\n  return /android/i.test(navigator.userAgent);\n};\n\n/**\n * 檢測是否為iOS設備\n */\nexport const isIOS = () => {\n  if (!canUseDom()) return false;\n  return /ios|iphone|ipad|ipod/i.test(navigator.userAgent);\n};\n\n/**\n * 檢測是否為Web瀏覽器環境\n */\nexport const isWebBrowser = () => {\n  return canUseDom() && !isMobileDevice();\n};\n\n/**\n * 檢測是否為平板設備\n */\nexport const isTablet = () => {\n  if (!canUseDom()) return false;\n  \n  const userAgent = navigator.userAgent;\n  const isTabletUA = /ipad|android(?!.*mobile)|tablet/i.test(userAgent);\n  const isLargeScreen = window.innerWidth >= 768 && window.innerWidth <= 1024;\n  \n  return isTabletUA || (isMobileDevice() && isLargeScreen);\n};\n\n/**\n * 獲取設備類型\n */\nexport const getDeviceType = () => {\n  if (isTablet()) return 'tablet';\n  if (isMobileDevice()) return 'mobile';\n  return 'desktop';\n};\n\n/**\n * 檢測相機功能支持\n */\nexport const getCameraCapabilities = async () => {\n  const capabilities = {\n    hasCamera: false,\n    hasUserMedia: false,\n    supportsFacingMode: false,\n    supportsConstraints: false,\n    availableCameras: []\n  };\n\n  if (!canUseDom() || !navigator.mediaDevices) {\n    return capabilities;\n  }\n\n  try {\n    // 檢測 getUserMedia 支持\n    capabilities.hasUserMedia = !!navigator.mediaDevices.getUserMedia;\n\n    if (capabilities.hasUserMedia) {\n      // 嘗試獲取設備列表\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      const videoDevices = devices.filter(device => device.kind === 'videoinput');\n      \n      capabilities.hasCamera = videoDevices.length > 0;\n      capabilities.availableCameras = videoDevices.map(device => ({\n        deviceId: device.deviceId,\n        label: device.label || `Camera ${videoDevices.indexOf(device) + 1}`,\n        groupId: device.groupId\n      }));\n\n      // 檢測是否支持 facingMode 約束\n      if (capabilities.hasCamera) {\n        try {\n          const stream = await navigator.mediaDevices.getUserMedia({\n            video: { facingMode: 'environment' }\n          });\n          capabilities.supportsFacingMode = true;\n          capabilities.supportsConstraints = true;\n          \n          // 立即停止測試流\n          stream.getTracks().forEach(track => track.stop());\n        } catch (error) {\n          // 如果 facingMode 不支持，嘗試基本的視頻約束\n          try {\n            const stream = await navigator.mediaDevices.getUserMedia({ video: true });\n            capabilities.supportsConstraints = true;\n            stream.getTracks().forEach(track => track.stop());\n          } catch (basicError) {\n            console.warn('基本相機功能不可用:', basicError);\n          }\n        }\n      }\n    }\n  } catch (error) {\n    console.warn('檢測相機功能時出錯:', error);\n  }\n\n  return capabilities;\n};\n\n/**\n * 檢測是否支持全屏API\n */\nexport const supportsFullscreen = () => {\n  if (!canUseDom()) return false;\n  \n  return !!(\n    document.fullscreenEnabled ||\n    document.webkitFullscreenEnabled ||\n    document.mozFullScreenEnabled ||\n    document.msFullscreenEnabled\n  );\n};\n\n/**\n * 檢測設備方向支持\n */\nexport const supportsOrientation = () => {\n  if (!canUseDom()) return false;\n  return 'orientation' in window || 'onorientationchange' in window;\n};\n\n/**\n * 獲取當前設備方向\n */\nexport const getDeviceOrientation = () => {\n  if (!supportsOrientation()) return 'unknown';\n  \n  if (window.orientation !== undefined) {\n    const orientation = Math.abs(window.orientation);\n    return orientation === 90 ? 'landscape' : 'portrait';\n  }\n  \n  // 備用方案：基於屏幕尺寸判斷\n  return window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';\n};\n\n/**\n * 綜合環境信息\n */\nexport const getEnvironmentInfo = async () => {\n  const cameraCapabilities = await getCameraCapabilities();\n  \n  return {\n    deviceType: getDeviceType(),\n    isMobile: isMobileDevice(),\n    isTablet: isTablet(),\n    isDesktop: isWebBrowser(),\n    isAndroid: isAndroid(),\n    isIOS: isIOS(),\n    orientation: getDeviceOrientation(),\n    supportsFullscreen: supportsFullscreen(),\n    supportsOrientation: supportsOrientation(),\n    camera: cameraCapabilities,\n    userAgent: canUseDom() ? navigator.userAgent : '',\n    screenSize: canUseDom() ? {\n      width: window.innerWidth,\n      height: window.innerHeight,\n      pixelRatio: window.devicePixelRatio || 1\n    } : null\n  };\n};\n\n/**\n * 推薦的拍照模式\n */\nexport const getRecommendedCameraMode = async () => {\n  const envInfo = await getEnvironmentInfo();\n  \n  if (!envInfo.camera.hasCamera) {\n    return 'none'; // 無相機支持\n  }\n  \n  if (envInfo.isMobile || envInfo.isTablet) {\n    return 'mobile'; // 移動端全屏模式\n  }\n  \n  return 'web'; // Web端模態框模式\n};\n\n// 導出對象\nconst deviceDetectorExports = {\n  canUseDom,\n  isMobileDevice,\n  isAndroid,\n  isIOS,\n  isWebBrowser,\n  isTablet,\n  getDeviceType,\n  getCameraCapabilities,\n  supportsFullscreen,\n  supportsOrientation,\n  getDeviceOrientation,\n  getEnvironmentInfo,\n  getRecommendedCameraMode\n};\n\nexport default deviceDetectorExports;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA,OAAO,MAAMA,SAAS,GAAGA,CAAA,KAAM;EAC7B,OAAO,CAAC,EAAE,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,IAAID,MAAM,CAACC,QAAQ,CAACC,aAAa,CAAC;AAC9F,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAClC,IAAI,CAACJ,SAAS,CAAC,CAAC,EAAE,OAAO,KAAK;EAE9B,MAAMK,SAAS,GAAGC,SAAS,CAACD,SAAS,IAAIC,SAAS,CAACC,MAAM,IAAIN,MAAM,CAACO,KAAK;;EAEzE;EACA,MAAMC,WAAW,GAAG,gEAAgE;;EAEpF;EACA,MAAMC,eAAe,GAAG,cAAc,IAAIT,MAAM,IAAIK,SAAS,CAACK,cAAc,GAAG,CAAC;;EAEhF;EACA,MAAMC,aAAa,GAAGX,MAAM,CAACY,UAAU,IAAI,GAAG;EAE9C,OAAOJ,WAAW,CAACK,IAAI,CAACT,SAAS,CAAC,IAAKK,eAAe,IAAIE,aAAc;AAC1E,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMG,SAAS,GAAGA,CAAA,KAAM;EAC7B,IAAI,CAACf,SAAS,CAAC,CAAC,EAAE,OAAO,KAAK;EAC9B,OAAO,UAAU,CAACc,IAAI,CAACR,SAAS,CAACD,SAAS,CAAC;AAC7C,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMW,KAAK,GAAGA,CAAA,KAAM;EACzB,IAAI,CAAChB,SAAS,CAAC,CAAC,EAAE,OAAO,KAAK;EAC9B,OAAO,uBAAuB,CAACc,IAAI,CAACR,SAAS,CAACD,SAAS,CAAC;AAC1D,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMY,YAAY,GAAGA,CAAA,KAAM;EAChC,OAAOjB,SAAS,CAAC,CAAC,IAAI,CAACI,cAAc,CAAC,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMc,QAAQ,GAAGA,CAAA,KAAM;EAC5B,IAAI,CAAClB,SAAS,CAAC,CAAC,EAAE,OAAO,KAAK;EAE9B,MAAMK,SAAS,GAAGC,SAAS,CAACD,SAAS;EACrC,MAAMc,UAAU,GAAG,kCAAkC,CAACL,IAAI,CAACT,SAAS,CAAC;EACrE,MAAMe,aAAa,GAAGnB,MAAM,CAACY,UAAU,IAAI,GAAG,IAAIZ,MAAM,CAACY,UAAU,IAAI,IAAI;EAE3E,OAAOM,UAAU,IAAKf,cAAc,CAAC,CAAC,IAAIgB,aAAc;AAC1D,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAAA,KAAM;EACjC,IAAIH,QAAQ,CAAC,CAAC,EAAE,OAAO,QAAQ;EAC/B,IAAId,cAAc,CAAC,CAAC,EAAE,OAAO,QAAQ;EACrC,OAAO,SAAS;AAClB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMkB,qBAAqB,GAAG,MAAAA,CAAA,KAAY;EAC/C,MAAMC,YAAY,GAAG;IACnBC,SAAS,EAAE,KAAK;IAChBC,YAAY,EAAE,KAAK;IACnBC,kBAAkB,EAAE,KAAK;IACzBC,mBAAmB,EAAE,KAAK;IAC1BC,gBAAgB,EAAE;EACpB,CAAC;EAED,IAAI,CAAC5B,SAAS,CAAC,CAAC,IAAI,CAACM,SAAS,CAACuB,YAAY,EAAE;IAC3C,OAAON,YAAY;EACrB;EAEA,IAAI;IACF;IACAA,YAAY,CAACE,YAAY,GAAG,CAAC,CAACnB,SAAS,CAACuB,YAAY,CAACC,YAAY;IAEjE,IAAIP,YAAY,CAACE,YAAY,EAAE;MAC7B;MACA,MAAMM,OAAO,GAAG,MAAMzB,SAAS,CAACuB,YAAY,CAACG,gBAAgB,CAAC,CAAC;MAC/D,MAAMC,YAAY,GAAGF,OAAO,CAACG,MAAM,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;MAE3Eb,YAAY,CAACC,SAAS,GAAGS,YAAY,CAACI,MAAM,GAAG,CAAC;MAChDd,YAAY,CAACK,gBAAgB,GAAGK,YAAY,CAACK,GAAG,CAACH,MAAM,KAAK;QAC1DI,QAAQ,EAAEJ,MAAM,CAACI,QAAQ;QACzBC,KAAK,EAAEL,MAAM,CAACK,KAAK,IAAI,UAAUP,YAAY,CAACQ,OAAO,CAACN,MAAM,CAAC,GAAG,CAAC,EAAE;QACnEO,OAAO,EAAEP,MAAM,CAACO;MAClB,CAAC,CAAC,CAAC;;MAEH;MACA,IAAInB,YAAY,CAACC,SAAS,EAAE;QAC1B,IAAI;UACF,MAAMmB,MAAM,GAAG,MAAMrC,SAAS,CAACuB,YAAY,CAACC,YAAY,CAAC;YACvDc,KAAK,EAAE;cAAEC,UAAU,EAAE;YAAc;UACrC,CAAC,CAAC;UACFtB,YAAY,CAACG,kBAAkB,GAAG,IAAI;UACtCH,YAAY,CAACI,mBAAmB,GAAG,IAAI;;UAEvC;UACAgB,MAAM,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;QACnD,CAAC,CAAC,OAAOC,KAAK,EAAE;UACd;UACA,IAAI;YACF,MAAMP,MAAM,GAAG,MAAMrC,SAAS,CAACuB,YAAY,CAACC,YAAY,CAAC;cAAEc,KAAK,EAAE;YAAK,CAAC,CAAC;YACzErB,YAAY,CAACI,mBAAmB,GAAG,IAAI;YACvCgB,MAAM,CAACG,SAAS,CAAC,CAAC,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,CAAC,CAAC,CAAC;UACnD,CAAC,CAAC,OAAOE,UAAU,EAAE;YACnBC,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEF,UAAU,CAAC;UACxC;QACF;MACF;IACF;EACF,CAAC,CAAC,OAAOD,KAAK,EAAE;IACdE,OAAO,CAACC,IAAI,CAAC,YAAY,EAAEH,KAAK,CAAC;EACnC;EAEA,OAAO3B,YAAY;AACrB,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM+B,kBAAkB,GAAGA,CAAA,KAAM;EACtC,IAAI,CAACtD,SAAS,CAAC,CAAC,EAAE,OAAO,KAAK;EAE9B,OAAO,CAAC,EACNE,QAAQ,CAACqD,iBAAiB,IAC1BrD,QAAQ,CAACsD,uBAAuB,IAChCtD,QAAQ,CAACuD,oBAAoB,IAC7BvD,QAAQ,CAACwD,mBAAmB,CAC7B;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;EACvC,IAAI,CAAC3D,SAAS,CAAC,CAAC,EAAE,OAAO,KAAK;EAC9B,OAAO,aAAa,IAAIC,MAAM,IAAI,qBAAqB,IAAIA,MAAM;AACnE,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAM2D,oBAAoB,GAAGA,CAAA,KAAM;EACxC,IAAI,CAACD,mBAAmB,CAAC,CAAC,EAAE,OAAO,SAAS;EAE5C,IAAI1D,MAAM,CAAC4D,WAAW,KAAKC,SAAS,EAAE;IACpC,MAAMD,WAAW,GAAGE,IAAI,CAACC,GAAG,CAAC/D,MAAM,CAAC4D,WAAW,CAAC;IAChD,OAAOA,WAAW,KAAK,EAAE,GAAG,WAAW,GAAG,UAAU;EACtD;;EAEA;EACA,OAAO5D,MAAM,CAACY,UAAU,GAAGZ,MAAM,CAACgE,WAAW,GAAG,WAAW,GAAG,UAAU;AAC1E,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,kBAAkB,GAAG,MAAAA,CAAA,KAAY;EAC5C,MAAMC,kBAAkB,GAAG,MAAM7C,qBAAqB,CAAC,CAAC;EAExD,OAAO;IACL8C,UAAU,EAAE/C,aAAa,CAAC,CAAC;IAC3BgD,QAAQ,EAAEjE,cAAc,CAAC,CAAC;IAC1Bc,QAAQ,EAAEA,QAAQ,CAAC,CAAC;IACpBoD,SAAS,EAAErD,YAAY,CAAC,CAAC;IACzBF,SAAS,EAAEA,SAAS,CAAC,CAAC;IACtBC,KAAK,EAAEA,KAAK,CAAC,CAAC;IACd6C,WAAW,EAAED,oBAAoB,CAAC,CAAC;IACnCN,kBAAkB,EAAEA,kBAAkB,CAAC,CAAC;IACxCK,mBAAmB,EAAEA,mBAAmB,CAAC,CAAC;IAC1CY,MAAM,EAAEJ,kBAAkB;IAC1B9D,SAAS,EAAEL,SAAS,CAAC,CAAC,GAAGM,SAAS,CAACD,SAAS,GAAG,EAAE;IACjDmE,UAAU,EAAExE,SAAS,CAAC,CAAC,GAAG;MACxByE,KAAK,EAAExE,MAAM,CAACY,UAAU;MACxB6D,MAAM,EAAEzE,MAAM,CAACgE,WAAW;MAC1BU,UAAU,EAAE1E,MAAM,CAAC2E,gBAAgB,IAAI;IACzC,CAAC,GAAG;EACN,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMC,wBAAwB,GAAG,MAAAA,CAAA,KAAY;EAClD,MAAMC,OAAO,GAAG,MAAMZ,kBAAkB,CAAC,CAAC;EAE1C,IAAI,CAACY,OAAO,CAACP,MAAM,CAAC/C,SAAS,EAAE;IAC7B,OAAO,MAAM,CAAC,CAAC;EACjB;EAEA,IAAIsD,OAAO,CAACT,QAAQ,IAAIS,OAAO,CAAC5D,QAAQ,EAAE;IACxC,OAAO,QAAQ,CAAC,CAAC;EACnB;EAEA,OAAO,KAAK,CAAC,CAAC;AAChB,CAAC;;AAED;AACA,MAAM6D,qBAAqB,GAAG;EAC5B/E,SAAS;EACTI,cAAc;EACdW,SAAS;EACTC,KAAK;EACLC,YAAY;EACZC,QAAQ;EACRG,aAAa;EACbC,qBAAqB;EACrBgC,kBAAkB;EAClBK,mBAAmB;EACnBC,oBAAoB;EACpBM,kBAAkB;EAClBW;AACF,CAAC;AAED,eAAeE,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}