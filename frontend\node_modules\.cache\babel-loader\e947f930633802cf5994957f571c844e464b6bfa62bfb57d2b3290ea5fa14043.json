{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\Layout\\\\PageContainer.js\";\n/**\n * 頁面容器組件\n */\n\nimport React from 'react';\nimport PageHeader from './PageHeader';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PageContainer = ({\n  title,\n  onBack,\n  backText,\n  headerRight,\n  showBack = true,\n  children,\n  style = {},\n  className = '',\n  contentStyle = {},\n  contentClassName = ''\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `page-container ${className}`,\n    style: {\n      minHeight: '100vh',\n      backgroundColor: '#f5f5f5',\n      display: 'flex',\n      flexDirection: 'column',\n      ...style\n    },\n    children: [title && /*#__PURE__*/_jsxDEV(PageHeader, {\n      title: title,\n      onBack: onBack,\n      backText: backText,\n      right: headerRight,\n      showBack: showBack\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 9\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `page-content ${contentClassName}`,\n      style: {\n        flex: 1,\n        padding: '16px',\n        ...contentStyle\n      },\n      children: children\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 41,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 21,\n    columnNumber: 5\n  }, this);\n};\n_c = PageContainer;\nexport default PageContainer;\nvar _c;\n$RefreshReg$(_c, \"PageContainer\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON>", "jsxDEV", "_jsxDEV", "<PERSON><PERSON><PERSON><PERSON>", "title", "onBack", "backText", "headerRight", "showBack", "children", "style", "className", "contentStyle", "contentClassName", "minHeight", "backgroundColor", "display", "flexDirection", "right", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "flex", "padding", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Layout/PageContainer.js"], "sourcesContent": ["/**\n * 頁面容器組件\n */\n\nimport React from 'react';\nimport PageHeader from './PageHeader';\n\nconst PageContainer = ({ \n  title,\n  onBack,\n  backText,\n  headerRight,\n  showBack = true,\n  children,\n  style = {},\n  className = '',\n  contentStyle = {},\n  contentClassName = ''\n}) => {\n  return (\n    <div \n      className={`page-container ${className}`}\n      style={{\n        minHeight: '100vh',\n        backgroundColor: '#f5f5f5',\n        display: 'flex',\n        flexDirection: 'column',\n        ...style\n      }}\n    >\n      {title && (\n        <PageHeader\n          title={title}\n          onBack={onBack}\n          backText={backText}\n          right={headerRight}\n          showBack={showBack}\n        />\n      )}\n      \n      <div \n        className={`page-content ${contentClassName}`}\n        style={{\n          flex: 1,\n          padding: '16px',\n          ...contentStyle\n        }}\n      >\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default PageContainer;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,UAAU,MAAM,cAAc;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEtC,MAAMC,aAAa,GAAGA,CAAC;EACrBC,KAAK;EACLC,MAAM;EACNC,QAAQ;EACRC,WAAW;EACXC,QAAQ,GAAG,IAAI;EACfC,QAAQ;EACRC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG,EAAE;EACdC,YAAY,GAAG,CAAC,CAAC;EACjBC,gBAAgB,GAAG;AACrB,CAAC,KAAK;EACJ,oBACEX,OAAA;IACES,SAAS,EAAE,kBAAkBA,SAAS,EAAG;IACzCD,KAAK,EAAE;MACLI,SAAS,EAAE,OAAO;MAClBC,eAAe,EAAE,SAAS;MAC1BC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvB,GAAGP;IACL,CAAE;IAAAD,QAAA,GAEDL,KAAK,iBACJF,OAAA,CAACF,UAAU;MACTI,KAAK,EAAEA,KAAM;MACbC,MAAM,EAAEA,MAAO;MACfC,QAAQ,EAAEA,QAAS;MACnBY,KAAK,EAAEX,WAAY;MACnBC,QAAQ,EAAEA;IAAS;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpB,CACF,eAEDpB,OAAA;MACES,SAAS,EAAE,gBAAgBE,gBAAgB,EAAG;MAC9CH,KAAK,EAAE;QACLa,IAAI,EAAE,CAAC;QACPC,OAAO,EAAE,MAAM;QACf,GAAGZ;MACL,CAAE;MAAAH,QAAA,EAEDA;IAAQ;MAAAU,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACG,EAAA,GA7CItB,aAAa;AA+CnB,eAAeA,aAAa;AAAC,IAAAsB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}