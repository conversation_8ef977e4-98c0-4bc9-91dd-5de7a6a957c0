{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nfunction useToggle(defaultValue, reverseValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useState(defaultValue), 2),\n    state = _a[0],\n    setState = _a[1];\n  var actions = useMemo(function () {\n    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;\n    var toggle = function () {\n      return setState(function (s) {\n        return s === defaultValue ? reverseValueOrigin : defaultValue;\n      });\n    };\n    var set = function (value) {\n      return setState(value);\n    };\n    var setLeft = function () {\n      return setState(defaultValue);\n    };\n    var setRight = function () {\n      return setState(reverseValueOrigin);\n    };\n    return {\n      toggle: toggle,\n      set: set,\n      setLeft: setLeft,\n      setRight: setRight\n    };\n    // useToggle ignore value change\n    // }, [defaultValue, reverseValue]);\n  }, []);\n  return [state, actions];\n}\nexport default useToggle;", "map": {"version": 3, "names": ["__read", "useMemo", "useState", "useToggle", "defaultValue", "reverseValue", "_a", "state", "setState", "actions", "reverseValueOrigin", "undefined", "toggle", "s", "set", "value", "setLeft", "setRight"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useToggle/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useMemo, useState } from 'react';\nfunction useToggle(defaultValue, reverseValue) {\n  if (defaultValue === void 0) {\n    defaultValue = false;\n  }\n  var _a = __read(useState(defaultValue), 2),\n    state = _a[0],\n    setState = _a[1];\n  var actions = useMemo(function () {\n    var reverseValueOrigin = reverseValue === undefined ? !defaultValue : reverseValue;\n    var toggle = function () {\n      return setState(function (s) {\n        return s === defaultValue ? reverseValueOrigin : defaultValue;\n      });\n    };\n    var set = function (value) {\n      return setState(value);\n    };\n    var setLeft = function () {\n      return setState(defaultValue);\n    };\n    var setRight = function () {\n      return setState(reverseValueOrigin);\n    };\n    return {\n      toggle: toggle,\n      set: set,\n      setLeft: setLeft,\n      setRight: setRight\n    };\n    // useToggle ignore value change\n    // }, [defaultValue, reverseValue]);\n  }, []);\n  return [state, actions];\n}\nexport default useToggle;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,OAAO,EAAEC,QAAQ,QAAQ,OAAO;AACzC,SAASC,SAASA,CAACC,YAAY,EAAEC,YAAY,EAAE;EAC7C,IAAID,YAAY,KAAK,KAAK,CAAC,EAAE;IAC3BA,YAAY,GAAG,KAAK;EACtB;EACA,IAAIE,EAAE,GAAGN,MAAM,CAACE,QAAQ,CAACE,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCG,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,OAAO,GAAGR,OAAO,CAAC,YAAY;IAChC,IAAIS,kBAAkB,GAAGL,YAAY,KAAKM,SAAS,GAAG,CAACP,YAAY,GAAGC,YAAY;IAClF,IAAIO,MAAM,GAAG,SAAAA,CAAA,EAAY;MACvB,OAAOJ,QAAQ,CAAC,UAAUK,CAAC,EAAE;QAC3B,OAAOA,CAAC,KAAKT,YAAY,GAAGM,kBAAkB,GAAGN,YAAY;MAC/D,CAAC,CAAC;IACJ,CAAC;IACD,IAAIU,GAAG,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACzB,OAAOP,QAAQ,CAACO,KAAK,CAAC;IACxB,CAAC;IACD,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAY;MACxB,OAAOR,QAAQ,CAACJ,YAAY,CAAC;IAC/B,CAAC;IACD,IAAIa,QAAQ,GAAG,SAAAA,CAAA,EAAY;MACzB,OAAOT,QAAQ,CAACE,kBAAkB,CAAC;IACrC,CAAC;IACD,OAAO;MACLE,MAAM,EAAEA,MAAM;MACdE,GAAG,EAAEA,GAAG;MACRE,OAAO,EAAEA,OAAO;MAChBC,QAAQ,EAAEA;IACZ,CAAC;IACD;IACA;EACF,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACV,KAAK,EAAEE,OAAO,CAAC;AACzB;AACA,eAAeN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}