{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nexport default function useFocusWithin(target, options) {\n  var _a = __read(useState(false), 2),\n    isFocusWithin = _a[0],\n    setIsFocusWithin = _a[1];\n  var _b = options || {},\n    onFocus = _b.onFocus,\n    onBlur = _b.onBlur,\n    onChange = _b.onChange;\n  useEventListener('focusin', function (e) {\n    if (!isFocusWithin) {\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(true);\n      setIsFocusWithin(true);\n    }\n  }, {\n    target: target\n  });\n  useEventListener('focusout', function (e) {\n    var _a, _b;\n    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(false);\n      setIsFocusWithin(false);\n    }\n  }, {\n    target: target\n  });\n  return isFocusWithin;\n}", "map": {"version": 3, "names": ["__read", "useState", "useEventListener", "useFocusWithin", "target", "options", "_a", "isFocusWithin", "setIsFocusWithin", "_b", "onFocus", "onBlur", "onChange", "e", "currentTarget", "contains", "call", "relatedTarget"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useFocusWithin/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nexport default function useFocusWithin(target, options) {\n  var _a = __read(useState(false), 2),\n    isFocusWithin = _a[0],\n    setIsFocusWithin = _a[1];\n  var _b = options || {},\n    onFocus = _b.onFocus,\n    onBlur = _b.onBlur,\n    onChange = _b.onChange;\n  useEventListener('focusin', function (e) {\n    if (!isFocusWithin) {\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(true);\n      setIsFocusWithin(true);\n    }\n  }, {\n    target: target\n  });\n  useEventListener('focusout', function (e) {\n    var _a, _b;\n    if (isFocusWithin && !((_b = (_a = e.currentTarget) === null || _a === void 0 ? void 0 : _a.contains) === null || _b === void 0 ? void 0 : _b.call(_a, e.relatedTarget))) {\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur(e);\n      onChange === null || onChange === void 0 ? void 0 : onChange(false);\n      setIsFocusWithin(false);\n    }\n  }, {\n    target: target\n  });\n  return isFocusWithin;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,eAAe,SAASC,cAAcA,CAACC,MAAM,EAAEC,OAAO,EAAE;EACtD,IAAIC,EAAE,GAAGN,MAAM,CAACC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACjCM,aAAa,GAAGD,EAAE,CAAC,CAAC,CAAC;IACrBE,gBAAgB,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC1B,IAAIG,EAAE,GAAGJ,OAAO,IAAI,CAAC,CAAC;IACpBK,OAAO,GAAGD,EAAE,CAACC,OAAO;IACpBC,MAAM,GAAGF,EAAE,CAACE,MAAM;IAClBC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;EACxBV,gBAAgB,CAAC,SAAS,EAAE,UAAUW,CAAC,EAAE;IACvC,IAAI,CAACN,aAAa,EAAE;MAClBG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACG,CAAC,CAAC;MAC5DD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,IAAI,CAAC;MAClEJ,gBAAgB,CAAC,IAAI,CAAC;IACxB;EACF,CAAC,EAAE;IACDJ,MAAM,EAAEA;EACV,CAAC,CAAC;EACFF,gBAAgB,CAAC,UAAU,EAAE,UAAUW,CAAC,EAAE;IACxC,IAAIP,EAAE,EAAEG,EAAE;IACV,IAAIF,aAAa,IAAI,EAAE,CAACE,EAAE,GAAG,CAACH,EAAE,GAAGO,CAAC,CAACC,aAAa,MAAM,IAAI,IAAIR,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACS,QAAQ,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAACV,EAAE,EAAEO,CAAC,CAACI,aAAa,CAAC,CAAC,EAAE;MACxKN,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACE,CAAC,CAAC;MACzDD,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,KAAK,CAAC;MACnEJ,gBAAgB,CAAC,KAAK,CAAC;IACzB;EACF,CAAC,EAAE;IACDJ,MAAM,EAAEA;EACV,CAAC,CAAC;EACF,OAAOG,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}