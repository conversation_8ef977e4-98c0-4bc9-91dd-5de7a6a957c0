{"ast": null, "code": "import useRequest from './src/useRequest';\nimport { clearCache } from './src/utils/cache';\nexport { clearCache };\nexport default useRequest;", "map": {"version": 3, "names": ["useRequest", "clearCache"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/index.js"], "sourcesContent": ["import useRequest from './src/useRequest';\nimport { clearCache } from './src/utils/cache';\nexport { clearCache };\nexport default useRequest;"], "mappings": "AAAA,OAAOA,UAAU,MAAM,kBAAkB;AACzC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,SAASA,UAAU;AACnB,eAAeD,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}