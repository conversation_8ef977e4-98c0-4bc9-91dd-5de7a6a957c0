{"ast": null, "code": "/**\n * 表單驗證工具函數\n */\n\nimport { FIELD_VALIDATION, REQUIRED_FIELDS, ERROR_MESSAGES } from './constants';\n\n/**\n * 驗證單個欄位\n * @param {string} fieldName - 欄位名稱\n * @param {string} value - 欄位值\n * @returns {Object} 驗證結果 { isValid: boolean, error: string }\n */\nexport const validateField = (fieldName, value) => {\n  const validation = FIELD_VALIDATION[fieldName];\n  if (!validation) {\n    return {\n      isValid: true,\n      error: null\n    };\n  }\n\n  // 檢查必填欄位\n  if (validation.required && (!value || value.trim() === '')) {\n    return {\n      isValid: false,\n      error: `${fieldName === 'name' ? '姓名' : fieldName}為必填欄位`\n    };\n  }\n\n  // 如果值為空且非必填，則通過驗證\n  if (!value || value.trim() === '') {\n    return {\n      isValid: true,\n      error: null\n    };\n  }\n\n  // 檢查最大長度\n  if (validation.maxLength && value.length > validation.maxLength) {\n    return {\n      isValid: false,\n      error: `長度不能超過${validation.maxLength}個字符`\n    };\n  }\n\n  // 檢查格式\n  if (validation.pattern && !validation.pattern.test(value)) {\n    let errorMessage = '格式不正確';\n    switch (fieldName) {\n      case 'mobile_phone':\n      case 'office_phone':\n        errorMessage = '請輸入有效的電話號碼';\n        break;\n      case 'email':\n        errorMessage = '請輸入有效的電子郵件地址';\n        break;\n      default:\n        errorMessage = '格式不正確';\n    }\n    return {\n      isValid: false,\n      error: errorMessage\n    };\n  }\n  return {\n    isValid: true,\n    error: null\n  };\n};\n\n/**\n * 驗證整個表單\n * @param {Object} formData - 表單數據\n * @returns {Object} 驗證結果 { isValid: boolean, errors: Object }\n */\nexport const validateForm = formData => {\n  const errors = {};\n  let isValid = true;\n\n  // 驗證所有欄位\n  Object.keys(FIELD_VALIDATION).forEach(fieldName => {\n    const value = formData[fieldName];\n    const result = validateField(fieldName, value);\n    if (!result.isValid) {\n      errors[fieldName] = result.error;\n      isValid = false;\n    }\n  });\n  return {\n    isValid,\n    errors\n  };\n};\n\n/**\n * 驗證必填欄位\n * @param {Object} formData - 表單數據\n * @returns {Object} 驗證結果\n */\nexport const validateRequiredFields = formData => {\n  const errors = {};\n  let isValid = true;\n  REQUIRED_FIELDS.forEach(fieldName => {\n    const value = formData[fieldName];\n    if (!value || value.trim() === '') {\n      errors[fieldName] = `${fieldName === 'name' ? '姓名' : fieldName}為必填欄位`;\n      isValid = false;\n    }\n  });\n  return {\n    isValid,\n    errors\n  };\n};\n\n/**\n * 驗證電話號碼格式\n * @param {string} phone - 電話號碼\n * @returns {boolean} 是否有效\n */\nexport const isValidPhone = phone => {\n  if (!phone) return true; // 非必填欄位\n  const phonePattern = /^[\\d\\s\\-\\+\\(\\)]+$/;\n  return phonePattern.test(phone);\n};\n\n/**\n * 驗證電子郵件格式\n * @param {string} email - 電子郵件\n * @returns {boolean} 是否有效\n */\nexport const isValidEmail = email => {\n  if (!email) return true; // 非必填欄位\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\n\n/**\n * 清理表單數據（移除空值和多餘空格）\n * @param {Object} formData - 原始表單數據\n * @returns {Object} 清理後的表單數據\n */\nexport const cleanFormData = formData => {\n  const cleanedData = {};\n  Object.keys(formData).forEach(key => {\n    const value = formData[key];\n    if (value !== null && value !== undefined) {\n      const trimmedValue = typeof value === 'string' ? value.trim() : value;\n      if (trimmedValue !== '') {\n        cleanedData[key] = trimmedValue;\n      }\n    }\n  });\n  return cleanedData;\n};\n\n/**\n * 檢查表單是否有變更\n * @param {Object} originalData - 原始數據\n * @param {Object} currentData - 當前數據\n * @returns {boolean} 是否有變更\n */\nexport const hasFormChanged = (originalData, currentData) => {\n  const cleanedOriginal = cleanFormData(originalData);\n  const cleanedCurrent = cleanFormData(currentData);\n  const originalKeys = Object.keys(cleanedOriginal);\n  const currentKeys = Object.keys(cleanedCurrent);\n\n  // 檢查鍵的數量是否相同\n  if (originalKeys.length !== currentKeys.length) {\n    return true;\n  }\n\n  // 檢查每個鍵的值是否相同\n  for (const key of originalKeys) {\n    if (cleanedOriginal[key] !== cleanedCurrent[key]) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * 獲取欄位的顯示名稱\n * @param {string} fieldName - 欄位名稱\n * @returns {string} 顯示名稱\n */\nexport const getFieldDisplayName = fieldName => {\n  const displayNames = {\n    name: '姓名',\n    company_name: '公司名稱',\n    position: '職位',\n    mobile_phone: '手機',\n    office_phone: '公司電話',\n    email: 'Email',\n    line_id: 'Line ID',\n    notes: '備註',\n    company_address_1: '公司地址一',\n    company_address_2: '公司地址二'\n  };\n  return displayNames[fieldName] || fieldName;\n};\nexport default {\n  validateField,\n  validateForm,\n  validateRequiredFields,\n  isValidPhone,\n  isValidEmail,\n  cleanFormData,\n  hasFormChanged,\n  getFieldDisplayName\n};", "map": {"version": 3, "names": ["FIELD_VALIDATION", "REQUIRED_FIELDS", "ERROR_MESSAGES", "validateField", "fieldName", "value", "validation", "<PERSON><PERSON><PERSON><PERSON>", "error", "required", "trim", "max<PERSON><PERSON><PERSON>", "length", "pattern", "test", "errorMessage", "validateForm", "formData", "errors", "Object", "keys", "for<PERSON>ach", "result", "validateRequired<PERSON><PERSON>s", "isValidPhone", "phone", "phonePattern", "isValidEmail", "email", "emailPattern", "cleanFormData", "cleanedData", "key", "undefined", "trimmedValue", "hasFormChanged", "originalData", "currentData", "cleanedOriginal", "cleanedCurrent", "originalKeys", "currentKeys", "getFieldDisplayName", "displayNames", "name", "company_name", "position", "mobile_phone", "office_phone", "line_id", "notes", "company_address_1", "company_address_2"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/utils/validation.js"], "sourcesContent": ["/**\n * 表單驗證工具函數\n */\n\nimport { FIELD_VALIDATION, REQUIRED_FIELDS, ERROR_MESSAGES } from './constants';\n\n/**\n * 驗證單個欄位\n * @param {string} fieldName - 欄位名稱\n * @param {string} value - 欄位值\n * @returns {Object} 驗證結果 { isValid: boolean, error: string }\n */\nexport const validateField = (fieldName, value) => {\n  const validation = FIELD_VALIDATION[fieldName];\n  \n  if (!validation) {\n    return { isValid: true, error: null };\n  }\n\n  // 檢查必填欄位\n  if (validation.required && (!value || value.trim() === '')) {\n    return {\n      isValid: false,\n      error: `${fieldName === 'name' ? '姓名' : fieldName}為必填欄位`\n    };\n  }\n\n  // 如果值為空且非必填，則通過驗證\n  if (!value || value.trim() === '') {\n    return { isValid: true, error: null };\n  }\n\n  // 檢查最大長度\n  if (validation.maxLength && value.length > validation.maxLength) {\n    return {\n      isValid: false,\n      error: `長度不能超過${validation.maxLength}個字符`\n    };\n  }\n\n  // 檢查格式\n  if (validation.pattern && !validation.pattern.test(value)) {\n    let errorMessage = '格式不正確';\n    \n    switch (fieldName) {\n      case 'mobile_phone':\n      case 'office_phone':\n        errorMessage = '請輸入有效的電話號碼';\n        break;\n      case 'email':\n        errorMessage = '請輸入有效的電子郵件地址';\n        break;\n      default:\n        errorMessage = '格式不正確';\n    }\n    \n    return {\n      isValid: false,\n      error: errorMessage\n    };\n  }\n\n  return { isValid: true, error: null };\n};\n\n/**\n * 驗證整個表單\n * @param {Object} formData - 表單數據\n * @returns {Object} 驗證結果 { isValid: boolean, errors: Object }\n */\nexport const validateForm = (formData) => {\n  const errors = {};\n  let isValid = true;\n\n  // 驗證所有欄位\n  Object.keys(FIELD_VALIDATION).forEach(fieldName => {\n    const value = formData[fieldName];\n    const result = validateField(fieldName, value);\n    \n    if (!result.isValid) {\n      errors[fieldName] = result.error;\n      isValid = false;\n    }\n  });\n\n  return { isValid, errors };\n};\n\n/**\n * 驗證必填欄位\n * @param {Object} formData - 表單數據\n * @returns {Object} 驗證結果\n */\nexport const validateRequiredFields = (formData) => {\n  const errors = {};\n  let isValid = true;\n\n  REQUIRED_FIELDS.forEach(fieldName => {\n    const value = formData[fieldName];\n    if (!value || value.trim() === '') {\n      errors[fieldName] = `${fieldName === 'name' ? '姓名' : fieldName}為必填欄位`;\n      isValid = false;\n    }\n  });\n\n  return { isValid, errors };\n};\n\n/**\n * 驗證電話號碼格式\n * @param {string} phone - 電話號碼\n * @returns {boolean} 是否有效\n */\nexport const isValidPhone = (phone) => {\n  if (!phone) return true; // 非必填欄位\n  const phonePattern = /^[\\d\\s\\-\\+\\(\\)]+$/;\n  return phonePattern.test(phone);\n};\n\n/**\n * 驗證電子郵件格式\n * @param {string} email - 電子郵件\n * @returns {boolean} 是否有效\n */\nexport const isValidEmail = (email) => {\n  if (!email) return true; // 非必填欄位\n  const emailPattern = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailPattern.test(email);\n};\n\n/**\n * 清理表單數據（移除空值和多餘空格）\n * @param {Object} formData - 原始表單數據\n * @returns {Object} 清理後的表單數據\n */\nexport const cleanFormData = (formData) => {\n  const cleanedData = {};\n  \n  Object.keys(formData).forEach(key => {\n    const value = formData[key];\n    if (value !== null && value !== undefined) {\n      const trimmedValue = typeof value === 'string' ? value.trim() : value;\n      if (trimmedValue !== '') {\n        cleanedData[key] = trimmedValue;\n      }\n    }\n  });\n  \n  return cleanedData;\n};\n\n/**\n * 檢查表單是否有變更\n * @param {Object} originalData - 原始數據\n * @param {Object} currentData - 當前數據\n * @returns {boolean} 是否有變更\n */\nexport const hasFormChanged = (originalData, currentData) => {\n  const cleanedOriginal = cleanFormData(originalData);\n  const cleanedCurrent = cleanFormData(currentData);\n  \n  const originalKeys = Object.keys(cleanedOriginal);\n  const currentKeys = Object.keys(cleanedCurrent);\n  \n  // 檢查鍵的數量是否相同\n  if (originalKeys.length !== currentKeys.length) {\n    return true;\n  }\n  \n  // 檢查每個鍵的值是否相同\n  for (const key of originalKeys) {\n    if (cleanedOriginal[key] !== cleanedCurrent[key]) {\n      return true;\n    }\n  }\n  \n  return false;\n};\n\n/**\n * 獲取欄位的顯示名稱\n * @param {string} fieldName - 欄位名稱\n * @returns {string} 顯示名稱\n */\nexport const getFieldDisplayName = (fieldName) => {\n  const displayNames = {\n    name: '姓名',\n    company_name: '公司名稱',\n    position: '職位',\n    mobile_phone: '手機',\n    office_phone: '公司電話',\n    email: 'Email',\n    line_id: 'Line ID',\n    notes: '備註',\n    company_address_1: '公司地址一',\n    company_address_2: '公司地址二'\n  };\n  \n  return displayNames[fieldName] || fieldName;\n};\n\nexport default {\n  validateField,\n  validateForm,\n  validateRequiredFields,\n  isValidPhone,\n  isValidEmail,\n  cleanFormData,\n  hasFormChanged,\n  getFieldDisplayName\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,gBAAgB,EAAEC,eAAe,EAAEC,cAAc,QAAQ,aAAa;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,aAAa,GAAGA,CAACC,SAAS,EAAEC,KAAK,KAAK;EACjD,MAAMC,UAAU,GAAGN,gBAAgB,CAACI,SAAS,CAAC;EAE9C,IAAI,CAACE,UAAU,EAAE;IACf,OAAO;MAAEC,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;EACvC;;EAEA;EACA,IAAIF,UAAU,CAACG,QAAQ,KAAK,CAACJ,KAAK,IAAIA,KAAK,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;IAC1D,OAAO;MACLH,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,GAAGJ,SAAS,KAAK,MAAM,GAAG,IAAI,GAAGA,SAAS;IACnD,CAAC;EACH;;EAEA;EACA,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;IACjC,OAAO;MAAEH,OAAO,EAAE,IAAI;MAAEC,KAAK,EAAE;IAAK,CAAC;EACvC;;EAEA;EACA,IAAIF,UAAU,CAACK,SAAS,IAAIN,KAAK,CAACO,MAAM,GAAGN,UAAU,CAACK,SAAS,EAAE;IAC/D,OAAO;MACLJ,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,SAASF,UAAU,CAACK,SAAS;IACtC,CAAC;EACH;;EAEA;EACA,IAAIL,UAAU,CAACO,OAAO,IAAI,CAACP,UAAU,CAACO,OAAO,CAACC,IAAI,CAACT,KAAK,CAAC,EAAE;IACzD,IAAIU,YAAY,GAAG,OAAO;IAE1B,QAAQX,SAAS;MACf,KAAK,cAAc;MACnB,KAAK,cAAc;QACjBW,YAAY,GAAG,YAAY;QAC3B;MACF,KAAK,OAAO;QACVA,YAAY,GAAG,cAAc;QAC7B;MACF;QACEA,YAAY,GAAG,OAAO;IAC1B;IAEA,OAAO;MACLR,OAAO,EAAE,KAAK;MACdC,KAAK,EAAEO;IACT,CAAC;EACH;EAEA,OAAO;IAAER,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,YAAY,GAAIC,QAAQ,IAAK;EACxC,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAIX,OAAO,GAAG,IAAI;;EAElB;EACAY,MAAM,CAACC,IAAI,CAACpB,gBAAgB,CAAC,CAACqB,OAAO,CAACjB,SAAS,IAAI;IACjD,MAAMC,KAAK,GAAGY,QAAQ,CAACb,SAAS,CAAC;IACjC,MAAMkB,MAAM,GAAGnB,aAAa,CAACC,SAAS,EAAEC,KAAK,CAAC;IAE9C,IAAI,CAACiB,MAAM,CAACf,OAAO,EAAE;MACnBW,MAAM,CAACd,SAAS,CAAC,GAAGkB,MAAM,CAACd,KAAK;MAChCD,OAAO,GAAG,KAAK;IACjB;EACF,CAAC,CAAC;EAEF,OAAO;IAAEA,OAAO;IAAEW;EAAO,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,sBAAsB,GAAIN,QAAQ,IAAK;EAClD,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjB,IAAIX,OAAO,GAAG,IAAI;EAElBN,eAAe,CAACoB,OAAO,CAACjB,SAAS,IAAI;IACnC,MAAMC,KAAK,GAAGY,QAAQ,CAACb,SAAS,CAAC;IACjC,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACK,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MACjCQ,MAAM,CAACd,SAAS,CAAC,GAAG,GAAGA,SAAS,KAAK,MAAM,GAAG,IAAI,GAAGA,SAAS,OAAO;MACrEG,OAAO,GAAG,KAAK;IACjB;EACF,CAAC,CAAC;EAEF,OAAO;IAAEA,OAAO;IAAEW;EAAO,CAAC;AAC5B,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,YAAY,GAAIC,KAAK,IAAK;EACrC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI,CAAC,CAAC;EACzB,MAAMC,YAAY,GAAG,mBAAmB;EACxC,OAAOA,YAAY,CAACZ,IAAI,CAACW,KAAK,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,YAAY,GAAIC,KAAK,IAAK;EACrC,IAAI,CAACA,KAAK,EAAE,OAAO,IAAI,CAAC,CAAC;EACzB,MAAMC,YAAY,GAAG,4BAA4B;EACjD,OAAOA,YAAY,CAACf,IAAI,CAACc,KAAK,CAAC;AACjC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAIb,QAAQ,IAAK;EACzC,MAAMc,WAAW,GAAG,CAAC,CAAC;EAEtBZ,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAACW,GAAG,IAAI;IACnC,MAAM3B,KAAK,GAAGY,QAAQ,CAACe,GAAG,CAAC;IAC3B,IAAI3B,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK4B,SAAS,EAAE;MACzC,MAAMC,YAAY,GAAG,OAAO7B,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACK,IAAI,CAAC,CAAC,GAAGL,KAAK;MACrE,IAAI6B,YAAY,KAAK,EAAE,EAAE;QACvBH,WAAW,CAACC,GAAG,CAAC,GAAGE,YAAY;MACjC;IACF;EACF,CAAC,CAAC;EAEF,OAAOH,WAAW;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,cAAc,GAAGA,CAACC,YAAY,EAAEC,WAAW,KAAK;EAC3D,MAAMC,eAAe,GAAGR,aAAa,CAACM,YAAY,CAAC;EACnD,MAAMG,cAAc,GAAGT,aAAa,CAACO,WAAW,CAAC;EAEjD,MAAMG,YAAY,GAAGrB,MAAM,CAACC,IAAI,CAACkB,eAAe,CAAC;EACjD,MAAMG,WAAW,GAAGtB,MAAM,CAACC,IAAI,CAACmB,cAAc,CAAC;;EAE/C;EACA,IAAIC,YAAY,CAAC5B,MAAM,KAAK6B,WAAW,CAAC7B,MAAM,EAAE;IAC9C,OAAO,IAAI;EACb;;EAEA;EACA,KAAK,MAAMoB,GAAG,IAAIQ,YAAY,EAAE;IAC9B,IAAIF,eAAe,CAACN,GAAG,CAAC,KAAKO,cAAc,CAACP,GAAG,CAAC,EAAE;MAChD,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,mBAAmB,GAAItC,SAAS,IAAK;EAChD,MAAMuC,YAAY,GAAG;IACnBC,IAAI,EAAE,IAAI;IACVC,YAAY,EAAE,MAAM;IACpBC,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,MAAM;IACpBpB,KAAK,EAAE,OAAO;IACdqB,OAAO,EAAE,SAAS;IAClBC,KAAK,EAAE,IAAI;IACXC,iBAAiB,EAAE,OAAO;IAC1BC,iBAAiB,EAAE;EACrB,CAAC;EAED,OAAOT,YAAY,CAACvC,SAAS,CAAC,IAAIA,SAAS;AAC7C,CAAC;AAED,eAAe;EACbD,aAAa;EACba,YAAY;EACZO,sBAAsB;EACtBC,YAAY;EACZG,YAAY;EACZG,aAAa;EACbK,cAAc;EACdO;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}