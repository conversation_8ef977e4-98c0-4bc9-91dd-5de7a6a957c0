{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\App.js\";\n/**\r\n * 主應用組件 - 重構版本\r\n * 使用新的頁面組件和路由配置\r\n */\n\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport 'antd-mobile/es/global';\nimport './App.css';\n\n// 導入新的頁面組件\nimport { HomePage, ScanPage, CardsPage, CardEditPage, CardViewPage } from './pages';\n\n// 保留相機測試頁面（開發用）\nimport CameraTestPage from './components/CameraTestPage';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction App() {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/\",\n        element: /*#__PURE__*/_jsxDEV(HomePage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 28,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/scan\",\n        element: /*#__PURE__*/_jsxDEV(ScanPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 38\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cards\",\n        element: /*#__PURE__*/_jsxDEV(CardsPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 39\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 34,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cards/new\",\n        element: /*#__PURE__*/_jsxDEV(CardEditPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cards/:id\",\n        element: /*#__PURE__*/_jsxDEV(CardViewPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 36,\n          columnNumber: 43\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/cards/:id/edit\",\n        element: /*#__PURE__*/_jsxDEV(CardEditPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 48\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/camera-test\",\n        element: /*#__PURE__*/_jsxDEV(CameraTestPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 45\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 40,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n}\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "HomePage", "ScanPage", "CardsPage", "CardEditPage", "CardViewPage", "CameraTestPage", "jsxDEV", "_jsxDEV", "App", "children", "path", "element", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/App.js"], "sourcesContent": ["/**\r\n * 主應用組件 - 重構版本\r\n * 使用新的頁面組件和路由配置\r\n */\r\n\r\nimport React from 'react';\r\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\r\nimport 'antd-mobile/es/global';\r\nimport './App.css';\r\n\r\n// 導入新的頁面組件\r\nimport {\r\n  HomePage,\r\n  ScanPage,\r\n  CardsPage,\r\n  CardEditPage,\r\n  CardViewPage\r\n} from './pages';\r\n\r\n// 保留相機測試頁面（開發用）\r\nimport CameraTestPage from './components/CameraTestPage';\r\n\r\nfunction App() {\r\n  return (\r\n    <Router>\r\n      <Routes>\r\n        {/* 主頁 */}\r\n        <Route path=\"/\" element={<HomePage />} />\r\n\r\n        {/* 掃描頁面 */}\r\n        <Route path=\"/scan\" element={<ScanPage />} />\r\n\r\n        {/* 名片管理 */}\r\n        <Route path=\"/cards\" element={<CardsPage />} />\r\n        <Route path=\"/cards/new\" element={<CardEditPage />} />\r\n        <Route path=\"/cards/:id\" element={<CardViewPage />} />\r\n        <Route path=\"/cards/:id/edit\" element={<CardEditPage />} />\r\n\r\n        {/* 開發測試頁面 */}\r\n        <Route path=\"/camera-test\" element={<CameraTestPage />} />\r\n      </Routes>\r\n    </Router>\r\n  );\r\n}\r\n\r\nexport default App;"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,OAAO,uBAAuB;AAC9B,OAAO,WAAW;;AAElB;AACA,SACEC,QAAQ,EACRC,QAAQ,EACRC,SAAS,EACTC,YAAY,EACZC,YAAY,QACP,SAAS;;AAEhB;AACA,OAAOC,cAAc,MAAM,6BAA6B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,SAASC,GAAGA,CAAA,EAAG;EACb,oBACED,OAAA,CAACV,MAAM;IAAAY,QAAA,eACLF,OAAA,CAACT,MAAM;MAAAW,QAAA,gBAELF,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEJ,OAAA,CAACP,QAAQ;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGzCR,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,OAAO;QAACC,OAAO,eAAEJ,OAAA,CAACN,QAAQ;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG7CR,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,QAAQ;QAACC,OAAO,eAAEJ,OAAA,CAACL,SAAS;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC/CR,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEJ,OAAA,CAACJ,YAAY;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDR,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,YAAY;QAACC,OAAO,eAAEJ,OAAA,CAACH,YAAY;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACtDR,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,iBAAiB;QAACC,OAAO,eAAEJ,OAAA,CAACJ,YAAY;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAG3DR,OAAA,CAACR,KAAK;QAACW,IAAI,EAAC,cAAc;QAACC,OAAO,eAAEJ,OAAA,CAACF,cAAc;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACpD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb;AAACC,EAAA,GArBQR,GAAG;AAuBZ,eAAeA,GAAG;AAAC,IAAAQ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}