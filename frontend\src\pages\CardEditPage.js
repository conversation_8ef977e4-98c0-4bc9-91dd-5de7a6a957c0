/**
 * 名片編輯頁面 - 統一的新增/編輯頁面
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { PageContainer } from '../components/Layout';
import { CardForm } from '../components/Form';
import { LoadingSpinner, ErrorMessage } from '../components/UI';
import { useCardData } from '../hooks';

const CardEditPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const isEditing = !!id;
  
  const { 
    getCard, 
    createCard, 
    updateCard, 
    loading 
  } = useCardData();
  
  const [cardData, setCardData] = useState({});
  const [loadError, setLoadError] = useState(null);
  const [initialLoading, setInitialLoading] = useState(isEditing);

  // 載入名片數據（編輯模式）
  useEffect(() => {
    if (isEditing) {
      loadCardData();
    }
  }, [id, isEditing]);

  const loadCardData = async () => {
    try {
      setInitialLoading(true);
      setLoadError(null);
      const data = await getCard(id);
      setCardData(data);
    } catch (error) {
      console.error('載入名片失敗:', error);
      setLoadError(error.message || '載入名片失敗');
    } finally {
      setInitialLoading(false);
    }
  };

  // 處理表單提交
  const handleSubmit = async (formData) => {
    try {
      if (isEditing) {
        await updateCard(id, formData);
        navigate(`/cards/${id}`);
      } else {
        const newCard = await createCard(formData);
        navigate(`/cards/${newCard.id}`);
      }
    } catch (error) {
      console.error('保存失敗:', error);
      throw error;
    }
  };

  // 處理取消
  const handleCancel = () => {
    if (isEditing) {
      navigate(`/cards/${id}`);
    } else {
      navigate('/cards');
    }
  };

  const pageTitle = isEditing ? '編輯名片' : '新增名片';

  if (initialLoading) {
    return (
      <PageContainer title={pageTitle}>
        <LoadingSpinner text="載入中..." />
      </PageContainer>
    );
  }

  if (loadError) {
    return (
      <PageContainer title={pageTitle}>
        <ErrorMessage 
          error={loadError}
          onRetry={loadCardData}
        />
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title={pageTitle}
      onBack={handleCancel}
    >
      <CardForm
        initialData={cardData}
        onSubmit={handleSubmit}
        onCancel={handleCancel}
        loading={loading}
        submitText={isEditing ? '更新名片' : '創建名片'}
        title={pageTitle}
        showCancel={true}
      />
    </PageContainer>
  );
};

export default CardEditPage;
