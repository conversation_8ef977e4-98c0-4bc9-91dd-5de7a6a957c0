/**
 * 應用常量定義
 */

// 名片欄位定義
export const CARD_FIELDS = {
  name: '姓名',
  company_name: '公司名稱',
  position: '職位',
  mobile_phone: '手機',
  office_phone: '公司電話',
  email: 'Email',
  line_id: 'Line ID',
  notes: '備註',
  company_address_1: '公司地址一',
  company_address_2: '公司地址二'
};

// 必填欄位
export const REQUIRED_FIELDS = ['name'];

// 欄位驗證規則
export const FIELD_VALIDATION = {
  name: {
    required: true,
    maxLength: 50,
    pattern: null
  },
  company_name: {
    required: false,
    maxLength: 100,
    pattern: null
  },
  position: {
    required: false,
    maxLength: 50,
    pattern: null
  },
  mobile_phone: {
    required: false,
    maxLength: 20,
    pattern: /^[\d\s\-\+\(\)]+$/
  },
  office_phone: {
    required: false,
    maxLength: 20,
    pattern: /^[\d\s\-\+\(\)]+$/
  },
  email: {
    required: false,
    maxLength: 100,
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  },
  line_id: {
    required: false,
    maxLength: 50,
    pattern: null
  },
  notes: {
    required: false,
    maxLength: 500,
    pattern: null
  },
  company_address_1: {
    required: false,
    maxLength: 200,
    pattern: null
  },
  company_address_2: {
    required: false,
    maxLength: 200,
    pattern: null
  }
};

// OCR處理狀態
export const OCR_STATUS = {
  IDLE: 'idle',
  PROCESSING: 'processing',
  SUCCESS: 'success',
  ERROR: 'error'
};

// 相機目標
export const CAMERA_TARGET = {
  FRONT: 'front',
  BACK: 'back'
};

// 相機狀態
export const CAMERA_STATUS = {
  INACTIVE: 'inactive',
  STARTING: 'starting',
  ACTIVE: 'active',
  ERROR: 'error'
};

// 圖片處理相關常量
export const IMAGE_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ACCEPTED_FORMATS: ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'],
  COMPRESSION_QUALITY: 0.8,
  MAX_WIDTH: 1920,
  MAX_HEIGHT: 1080
};

// 路由路徑
export const ROUTES = {
  HOME: '/',
  SCAN: '/scan',
  CARDS: '/cards',
  CARD_NEW: '/cards/new',
  CARD_VIEW: '/cards/:id',
  CARD_EDIT: '/cards/:id/edit'
};

// 本地存儲鍵名
export const STORAGE_KEYS = {
  CAMERA_SETTINGS: 'ocr_camera_settings',
  USER_PREFERENCES: 'ocr_user_preferences',
  LAST_SCAN_DATA: 'ocr_last_scan_data'
};

// 錯誤消息
export const ERROR_MESSAGES = {
  NETWORK_ERROR: '網絡連接失敗，請檢查網絡設置',
  OCR_FAILED: 'OCR識別失敗，請重試',
  CAMERA_PERMISSION_DENIED: '相機權限被拒絕，請在瀏覽器設置中允許相機訪問',
  CAMERA_NOT_FOUND: '未找到可用的相機設備',
  CAMERA_OCCUPIED: '相機被其他應用程序占用',
  FILE_TOO_LARGE: '文件大小超過限制',
  INVALID_FILE_FORMAT: '不支持的文件格式',
  VALIDATION_FAILED: '數據驗證失敗',
  SAVE_FAILED: '保存失敗，請重試',
  DELETE_FAILED: '刪除失敗，請重試',
  LOAD_FAILED: '加載失敗，請重試'
};

// 成功消息
export const SUCCESS_MESSAGES = {
  OCR_SUCCESS: 'OCR識別完成',
  CARD_SAVED: '名片保存成功',
  CARD_UPDATED: '名片更新成功',
  CARD_DELETED: '名片刪除成功',
  EXPORT_SUCCESS: '導出成功'
};

// 主題色彩
export const THEME_COLORS = {
  PRIMARY: '#1677ff',
  SUCCESS: '#52c41a',
  WARNING: '#faad14',
  ERROR: '#ff4d4f',
  INFO: '#13c2c2',
  TEXT_PRIMARY: '#262626',
  TEXT_SECONDARY: '#8c8c8c',
  BORDER: '#d9d9d9',
  BACKGROUND: '#f5f5f5'
};

export default {
  CARD_FIELDS,
  REQUIRED_FIELDS,
  FIELD_VALIDATION,
  OCR_STATUS,
  CAMERA_TARGET,
  CAMERA_STATUS,
  IMAGE_CONFIG,
  ROUTES,
  STORAGE_KEYS,
  ERROR_MESSAGES,
  SUCCESS_MESSAGES,
  THEME_COLORS
};
