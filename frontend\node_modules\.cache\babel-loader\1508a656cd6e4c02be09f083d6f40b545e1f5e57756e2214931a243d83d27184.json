{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\pages\\\\ScanPage.js\",\n  _s = $RefreshSig$();\n/**\n * 掃描頁面 - 重構版本\n * 集成相機拍攝、OCR處理和表單填寫功能\n */\n\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Space, Divider } from 'antd-mobile';\nimport { PageContainer } from '../components/Layout';\nimport { CameraCapture } from '../components/Camera';\nimport { OCRProcessor } from '../components/OCR';\nimport { CardForm } from '../components/Form';\nimport { LoadingSpinner } from '../components/UI';\nimport { useCardData, useOCRState, useCameraState } from '../hooks';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ScanPage = () => {\n  _s();\n  const navigate = useNavigate();\n  const {\n    createCard,\n    loading: cardLoading\n  } = useCardData();\n  const {\n    processImage,\n    frontOCR,\n    backOCR,\n    getMergedFields,\n    isProcessing: ocrProcessing\n  } = useOCRState();\n  const {\n    images,\n    getImagesForSubmit,\n    updateOCRResult\n  } = useCameraState();\n  const [formData, setFormData] = useState({\n    name: '',\n    company_name: '',\n    position: '',\n    mobile_phone: '',\n    office_phone: '',\n    email: '',\n    line_id: '',\n    notes: '',\n    company_address_1: '',\n    company_address_2: ''\n  });\n\n  // 處理圖片拍攝完成\n  const handleImageCaptured = async (imageFile, side) => {\n    console.log(`📸 圖片拍攝完成 - ${side}面:`, imageFile.name);\n    try {\n      // 更新圖片解析狀態為處理中\n      updateOCRResult(side, '', 'processing');\n\n      // 執行OCR處理\n      const ocrText = await processImage(imageFile, side);\n\n      // 更新OCR結果\n      updateOCRResult(side, ocrText, 'success');\n    } catch (error) {\n      console.error(`❌ OCR處理失敗 - ${side}面:`, error);\n      updateOCRResult(side, '', 'error');\n    }\n  };\n\n  // 處理OCR解析完成\n  const handleFieldsParsed = (mergedFields, frontFields, backFields) => {\n    console.log('🧠 OCR解析完成:', {\n      mergedFields,\n      frontFields,\n      backFields\n    });\n\n    // 自動填充表單（只填充空欄位）\n    setFormData(prev => {\n      const updated = {\n        ...prev\n      };\n      Object.keys(mergedFields).forEach(key => {\n        if (mergedFields[key] && (!prev[key] || prev[key].trim() === '')) {\n          updated[key] = mergedFields[key];\n        }\n      });\n      return updated;\n    });\n  };\n\n  // 處理表單提交\n  const handleFormSubmit = async formDataToSubmit => {\n    try {\n      const images = getImagesForSubmit();\n      const newCard = await createCard(formDataToSubmit, images);\n      console.log('✅ 名片創建成功:', newCard);\n\n      // 導航到名片管理頁面\n      navigate('/cards');\n    } catch (error) {\n      console.error('❌ 名片創建失敗:', error);\n    }\n  };\n\n  // 處理表單數據變更\n  const handleFormDataChange = newFormData => {\n    setFormData(newFormData);\n  };\n  const isLoading = cardLoading || ocrProcessing;\n  return /*#__PURE__*/_jsxDEV(PageContainer, {\n    title: \"\\u6383\\u63CF\\u540D\\u7247\",\n    onBack: () => navigate('/'),\n    children: /*#__PURE__*/_jsxDEV(Space, {\n      direction: \"vertical\",\n      style: {\n        width: '100%'\n      },\n      children: [/*#__PURE__*/_jsxDEV(CameraCapture, {\n        onImageCaptured: handleImageCaptured,\n        title: \"\\u62CD\\u651D\\u540D\\u7247\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 112,\n        columnNumber: 9\n      }, this), (images.front.file || images.back.file) && /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [/*#__PURE__*/_jsxDEV(Divider, {\n          children: \"OCR\\u8B58\\u5225\\u7D50\\u679C\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 120,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(OCRProcessor, {\n          frontImage: images.front,\n          backImage: images.back,\n          onFieldsParsed: handleFieldsParsed,\n          autoProcess: true,\n          showStatus: true,\n          showResults: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true), /*#__PURE__*/_jsxDEV(Divider, {\n        children: \"\\u540D\\u7247\\u8CC7\\u8A0A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 133,\n        columnNumber: 9\n      }, this), isLoading ? /*#__PURE__*/_jsxDEV(LoadingSpinner, {\n        text: \"\\u8655\\u7406\\u4E2D...\",\n        style: {\n          minHeight: '200px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 136,\n        columnNumber: 11\n      }, this) : /*#__PURE__*/_jsxDEV(CardForm, {\n        initialData: formData,\n        onSubmit: handleFormSubmit,\n        loading: cardLoading,\n        submitText: \"\\u4FDD\\u5B58\\u540D\\u7247\",\n        title: \"\\u7DE8\\u8F2F\\u540D\\u7247\\u8CC7\\u8A0A\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 141,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 106,\n    columnNumber: 5\n  }, this);\n};\n_s(ScanPage, \"tYujjkzZidbPnHulzPYnBKitRTM=\", false, function () {\n  return [useNavigate, useCardData, useOCRState, useCameraState];\n});\n_c = ScanPage;\nexport default ScanPage;\nvar _c;\n$RefreshReg$(_c, \"ScanPage\");", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Space", "Divider", "<PERSON><PERSON><PERSON><PERSON>", "CameraCapture", "OCRProcessor", "CardForm", "LoadingSpinner", "useCardData", "useOCRState", "useCameraState", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ScanPage", "_s", "navigate", "createCard", "loading", "cardLoading", "processImage", "frontOCR", "backOCR", "getMergedFields", "isProcessing", "ocrProcessing", "images", "getImagesForSubmit", "updateOCRResult", "formData", "setFormData", "name", "company_name", "position", "mobile_phone", "office_phone", "email", "line_id", "notes", "company_address_1", "company_address_2", "handleImageCaptured", "imageFile", "side", "console", "log", "ocrText", "error", "handleFieldsParsed", "mergedFields", "frontFields", "backFields", "prev", "updated", "Object", "keys", "for<PERSON>ach", "key", "trim", "handleFormSubmit", "formDataToSubmit", "newCard", "handleFormDataChange", "newFormData", "isLoading", "title", "onBack", "children", "direction", "style", "width", "onImageCaptured", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "front", "file", "back", "frontImage", "backImage", "onFieldsParsed", "autoProcess", "showStatus", "showResults", "text", "minHeight", "initialData", "onSubmit", "submitText", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/pages/ScanPage.js"], "sourcesContent": ["/**\n * 掃描頁面 - 重構版本\n * 集成相機拍攝、OCR處理和表單填寫功能\n */\n\nimport React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Space, Divider } from 'antd-mobile';\nimport { PageContainer } from '../components/Layout';\nimport { CameraCapture } from '../components/Camera';\nimport { OCRProcessor } from '../components/OCR';\nimport { CardForm } from '../components/Form';\nimport { LoadingSpinner } from '../components/UI';\nimport { useCardData, useOCRState, useCameraState } from '../hooks';\n\nconst ScanPage = () => {\n  const navigate = useNavigate();\n  const { createCard, loading: cardLoading } = useCardData();\n  const { \n    processImage, \n    frontOCR, \n    backOCR, \n    getMergedFields,\n    isProcessing: ocrProcessing \n  } = useOCRState();\n  const { \n    images, \n    getImagesForSubmit,\n    updateOCRResult \n  } = useCameraState();\n\n  const [formData, setFormData] = useState({\n    name: '',\n    company_name: '',\n    position: '',\n    mobile_phone: '',\n    office_phone: '',\n    email: '',\n    line_id: '',\n    notes: '',\n    company_address_1: '',\n    company_address_2: ''\n  });\n\n  // 處理圖片拍攝完成\n  const handleImageCaptured = async (imageFile, side) => {\n    console.log(`📸 圖片拍攝完成 - ${side}面:`, imageFile.name);\n    \n    try {\n      // 更新圖片解析狀態為處理中\n      updateOCRResult(side, '', 'processing');\n      \n      // 執行OCR處理\n      const ocrText = await processImage(imageFile, side);\n      \n      // 更新OCR結果\n      updateOCRResult(side, ocrText, 'success');\n      \n    } catch (error) {\n      console.error(`❌ OCR處理失敗 - ${side}面:`, error);\n      updateOCRResult(side, '', 'error');\n    }\n  };\n\n  // 處理OCR解析完成\n  const handleFieldsParsed = (mergedFields, frontFields, backFields) => {\n    console.log('🧠 OCR解析完成:', { mergedFields, frontFields, backFields });\n    \n    // 自動填充表單（只填充空欄位）\n    setFormData(prev => {\n      const updated = { ...prev };\n      \n      Object.keys(mergedFields).forEach(key => {\n        if (mergedFields[key] && (!prev[key] || prev[key].trim() === '')) {\n          updated[key] = mergedFields[key];\n        }\n      });\n      \n      return updated;\n    });\n  };\n\n  // 處理表單提交\n  const handleFormSubmit = async (formDataToSubmit) => {\n    try {\n      const images = getImagesForSubmit();\n      const newCard = await createCard(formDataToSubmit, images);\n      \n      console.log('✅ 名片創建成功:', newCard);\n      \n      // 導航到名片管理頁面\n      navigate('/cards');\n    } catch (error) {\n      console.error('❌ 名片創建失敗:', error);\n    }\n  };\n\n  // 處理表單數據變更\n  const handleFormDataChange = (newFormData) => {\n    setFormData(newFormData);\n  };\n\n  const isLoading = cardLoading || ocrProcessing;\n\n  return (\n    <PageContainer\n      title=\"掃描名片\"\n      onBack={() => navigate('/')}\n    >\n      <Space direction=\"vertical\" style={{ width: '100%' }}>\n        {/* 相機拍攝區域 */}\n        <CameraCapture\n          onImageCaptured={handleImageCaptured}\n          title=\"拍攝名片\"\n        />\n\n        {/* OCR處理區域 */}\n        {(images.front.file || images.back.file) && (\n          <>\n            <Divider>OCR識別結果</Divider>\n            <OCRProcessor\n              frontImage={images.front}\n              backImage={images.back}\n              onFieldsParsed={handleFieldsParsed}\n              autoProcess={true}\n              showStatus={true}\n              showResults={true}\n            />\n          </>\n        )}\n\n        {/* 表單填寫區域 */}\n        <Divider>名片資訊</Divider>\n        \n        {isLoading ? (\n          <LoadingSpinner \n            text=\"處理中...\" \n            style={{ minHeight: '200px' }}\n          />\n        ) : (\n          <CardForm\n            initialData={formData}\n            onSubmit={handleFormSubmit}\n            loading={cardLoading}\n            submitText=\"保存名片\"\n            title=\"編輯名片資訊\"\n          />\n        )}\n      </Space>\n    </PageContainer>\n  );\n};\n\nexport default ScanPage;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,KAAK,EAAEC,OAAO,QAAQ,aAAa;AAC5C,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,aAAa,QAAQ,sBAAsB;AACpD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,cAAc,QAAQ,kBAAkB;AACjD,SAASC,WAAW,EAAEC,WAAW,EAAEC,cAAc,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpE,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGjB,WAAW,CAAC,CAAC;EAC9B,MAAM;IAAEkB,UAAU;IAAEC,OAAO,EAAEC;EAAY,CAAC,GAAGZ,WAAW,CAAC,CAAC;EAC1D,MAAM;IACJa,YAAY;IACZC,QAAQ;IACRC,OAAO;IACPC,eAAe;IACfC,YAAY,EAAEC;EAChB,CAAC,GAAGjB,WAAW,CAAC,CAAC;EACjB,MAAM;IACJkB,MAAM;IACNC,kBAAkB;IAClBC;EACF,CAAC,GAAGnB,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACoB,QAAQ,EAAEC,WAAW,CAAC,GAAGhC,QAAQ,CAAC;IACvCiC,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,EAAE;IAChBC,QAAQ,EAAE,EAAE;IACZC,YAAY,EAAE,EAAE;IAChBC,YAAY,EAAE,EAAE;IAChBC,KAAK,EAAE,EAAE;IACTC,OAAO,EAAE,EAAE;IACXC,KAAK,EAAE,EAAE;IACTC,iBAAiB,EAAE,EAAE;IACrBC,iBAAiB,EAAE;EACrB,CAAC,CAAC;;EAEF;EACA,MAAMC,mBAAmB,GAAG,MAAAA,CAAOC,SAAS,EAAEC,IAAI,KAAK;IACrDC,OAAO,CAACC,GAAG,CAAC,eAAeF,IAAI,IAAI,EAAED,SAAS,CAACX,IAAI,CAAC;IAEpD,IAAI;MACF;MACAH,eAAe,CAACe,IAAI,EAAE,EAAE,EAAE,YAAY,CAAC;;MAEvC;MACA,MAAMG,OAAO,GAAG,MAAM1B,YAAY,CAACsB,SAAS,EAAEC,IAAI,CAAC;;MAEnD;MACAf,eAAe,CAACe,IAAI,EAAEG,OAAO,EAAE,SAAS,CAAC;IAE3C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,eAAeJ,IAAI,IAAI,EAAEI,KAAK,CAAC;MAC7CnB,eAAe,CAACe,IAAI,EAAE,EAAE,EAAE,OAAO,CAAC;IACpC;EACF,CAAC;;EAED;EACA,MAAMK,kBAAkB,GAAGA,CAACC,YAAY,EAAEC,WAAW,EAAEC,UAAU,KAAK;IACpEP,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE;MAAEI,YAAY;MAAEC,WAAW;MAAEC;IAAW,CAAC,CAAC;;IAErE;IACArB,WAAW,CAACsB,IAAI,IAAI;MAClB,MAAMC,OAAO,GAAG;QAAE,GAAGD;MAAK,CAAC;MAE3BE,MAAM,CAACC,IAAI,CAACN,YAAY,CAAC,CAACO,OAAO,CAACC,GAAG,IAAI;QACvC,IAAIR,YAAY,CAACQ,GAAG,CAAC,KAAK,CAACL,IAAI,CAACK,GAAG,CAAC,IAAIL,IAAI,CAACK,GAAG,CAAC,CAACC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;UAChEL,OAAO,CAACI,GAAG,CAAC,GAAGR,YAAY,CAACQ,GAAG,CAAC;QAClC;MACF,CAAC,CAAC;MAEF,OAAOJ,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC;;EAED;EACA,MAAMM,gBAAgB,GAAG,MAAOC,gBAAgB,IAAK;IACnD,IAAI;MACF,MAAMlC,MAAM,GAAGC,kBAAkB,CAAC,CAAC;MACnC,MAAMkC,OAAO,GAAG,MAAM5C,UAAU,CAAC2C,gBAAgB,EAAElC,MAAM,CAAC;MAE1DkB,OAAO,CAACC,GAAG,CAAC,WAAW,EAAEgB,OAAO,CAAC;;MAEjC;MACA7C,QAAQ,CAAC,QAAQ,CAAC;IACpB,CAAC,CAAC,OAAO+B,KAAK,EAAE;MACdH,OAAO,CAACG,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;IACnC;EACF,CAAC;;EAED;EACA,MAAMe,oBAAoB,GAAIC,WAAW,IAAK;IAC5CjC,WAAW,CAACiC,WAAW,CAAC;EAC1B,CAAC;EAED,MAAMC,SAAS,GAAG7C,WAAW,IAAIM,aAAa;EAE9C,oBACEd,OAAA,CAACT,aAAa;IACZ+D,KAAK,EAAC,0BAAM;IACZC,MAAM,EAAEA,CAAA,KAAMlD,QAAQ,CAAC,GAAG,CAAE;IAAAmD,QAAA,eAE5BxD,OAAA,CAACX,KAAK;MAACoE,SAAS,EAAC,UAAU;MAACC,KAAK,EAAE;QAAEC,KAAK,EAAE;MAAO,CAAE;MAAAH,QAAA,gBAEnDxD,OAAA,CAACR,aAAa;QACZoE,eAAe,EAAE9B,mBAAoB;QACrCwB,KAAK,EAAC;MAAM;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC,EAGD,CAACjD,MAAM,CAACkD,KAAK,CAACC,IAAI,IAAInD,MAAM,CAACoD,IAAI,CAACD,IAAI,kBACrClE,OAAA,CAAAE,SAAA;QAAAsD,QAAA,gBACExD,OAAA,CAACV,OAAO;UAAAkE,QAAA,EAAC;QAAO;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAS,CAAC,eAC1BhE,OAAA,CAACP,YAAY;UACX2E,UAAU,EAAErD,MAAM,CAACkD,KAAM;UACzBI,SAAS,EAAEtD,MAAM,CAACoD,IAAK;UACvBG,cAAc,EAAEjC,kBAAmB;UACnCkC,WAAW,EAAE,IAAK;UAClBC,UAAU,EAAE,IAAK;UACjBC,WAAW,EAAE;QAAK;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CAAC;MAAA,eACF,CACH,eAGDhE,OAAA,CAACV,OAAO;QAAAkE,QAAA,EAAC;MAAI;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAS,CAAC,EAEtBX,SAAS,gBACRrD,OAAA,CAACL,cAAc;QACb+E,IAAI,EAAC,uBAAQ;QACbhB,KAAK,EAAE;UAAEiB,SAAS,EAAE;QAAQ;MAAE;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/B,CAAC,gBAEFhE,OAAA,CAACN,QAAQ;QACPkF,WAAW,EAAE1D,QAAS;QACtB2D,QAAQ,EAAE7B,gBAAiB;QAC3BzC,OAAO,EAAEC,WAAY;QACrBsE,UAAU,EAAC,0BAAM;QACjBxB,KAAK,EAAC;MAAQ;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACf,CACF;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACK,CAAC;AAEpB,CAAC;AAAC5D,EAAA,CAxIID,QAAQ;EAAA,QACKf,WAAW,EACiBQ,WAAW,EAOpDC,WAAW,EAKXC,cAAc;AAAA;AAAAiF,EAAA,GAdd5E,QAAQ;AA0Id,eAAeA,QAAQ;AAAC,IAAA4E,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}