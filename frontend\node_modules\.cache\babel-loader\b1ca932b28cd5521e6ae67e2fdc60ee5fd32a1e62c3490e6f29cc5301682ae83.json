{"ast": null, "code": "/**\n * 名片相關API服務\n * 處理名片的CRUD操作和相關功能\n */\n\nimport apiClient from './apiClient';\nexport const cardService = {\n  /**\n   * 獲取所有名片\n   * @param {Object} params - 查詢參數\n   * @returns {Promise<Object[]>} 名片列表\n   */\n  getCards: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/cards/', {\n        params\n      });\n      return response.data;\n    } catch (error) {\n      console.error('獲取名片列表失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 獲取單張名片\n   * @param {number} id - 名片ID\n   * @returns {Promise<Object>} 名片詳情\n   */\n  getCard: async id => {\n    try {\n      const response = await apiClient.get(`/cards/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('獲取名片詳情失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 創建名片\n   * @param {Object} cardData - 名片數據\n   * @param {Object} images - 圖片數據 { front: {file, ocrText}, back: {file, ocrText} }\n   * @returns {Promise<Object>} 創建的名片\n   */\n  createCard: async (cardData, images = {}) => {\n    try {\n      const formData = new FormData();\n\n      // 添加名片數據\n      Object.keys(cardData).forEach(key => {\n        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {\n          formData.append(key, cardData[key]);\n        }\n      });\n\n      // 添加圖片文件和OCR數據\n      if (images.front) {\n        if (images.front.file) {\n          formData.append('front_image', images.front.file);\n        }\n        if (images.front.ocrText) {\n          formData.append('front_ocr_text', images.front.ocrText);\n        }\n      }\n      if (images.back) {\n        if (images.back.file) {\n          formData.append('back_image', images.back.file);\n        }\n        if (images.back.ocrText) {\n          formData.append('back_ocr_text', images.back.ocrText);\n        }\n      }\n      const response = await apiClient.post('/cards/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('創建名片失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 更新名片\n   * @param {number} id - 名片ID\n   * @param {Object} cardData - 更新的名片數據\n   * @param {Object} images - 圖片數據\n   * @returns {Promise<Object>} 更新後的名片\n   */\n  updateCard: async (id, cardData, images = {}) => {\n    try {\n      const formData = new FormData();\n\n      // 添加名片數據\n      Object.keys(cardData).forEach(key => {\n        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {\n          formData.append(key, cardData[key]);\n        }\n      });\n\n      // 添加圖片文件和OCR數據\n      if (images.front) {\n        if (images.front.file) {\n          formData.append('front_image', images.front.file);\n        }\n        if (images.front.ocrText) {\n          formData.append('front_ocr_text', images.front.ocrText);\n        }\n      }\n      if (images.back) {\n        if (images.back.file) {\n          formData.append('back_image', images.back.file);\n        }\n        if (images.back.ocrText) {\n          formData.append('back_ocr_text', images.back.ocrText);\n        }\n      }\n      const response = await apiClient.put(`/cards/${id}`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('更新名片失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 刪除名片\n   * @param {number} id - 名片ID\n   * @returns {Promise<Object>} 刪除結果\n   */\n  deleteCard: async id => {\n    try {\n      const response = await apiClient.delete(`/cards/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('刪除名片失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 導出名片\n   * @param {string} format - 導出格式 ('csv' 或 'excel')\n   * @returns {Promise<Blob>} 導出文件\n   */\n  exportCards: async (format = 'csv') => {\n    try {\n      const response = await apiClient.get(`/cards/export?format=${format}`, {\n        responseType: 'blob'\n      });\n      return response.data;\n    } catch (error) {\n      console.error('導出名片失敗:', error);\n      throw error;\n    }\n  }\n};\nexport default cardService;", "map": {"version": 3, "names": ["apiClient", "cardService", "getCards", "params", "response", "get", "data", "error", "console", "getCard", "id", "createCard", "cardData", "images", "formData", "FormData", "Object", "keys", "for<PERSON>ach", "key", "undefined", "append", "front", "file", "ocrText", "back", "post", "headers", "updateCard", "put", "deleteCard", "delete", "exportCards", "format", "responseType"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/services/api/cardService.js"], "sourcesContent": ["/**\n * 名片相關API服務\n * 處理名片的CRUD操作和相關功能\n */\n\nimport apiClient from './apiClient';\n\nexport const cardService = {\n  /**\n   * 獲取所有名片\n   * @param {Object} params - 查詢參數\n   * @returns {Promise<Object[]>} 名片列表\n   */\n  getCards: async (params = {}) => {\n    try {\n      const response = await apiClient.get('/cards/', { params });\n      return response.data;\n    } catch (error) {\n      console.error('獲取名片列表失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 獲取單張名片\n   * @param {number} id - 名片ID\n   * @returns {Promise<Object>} 名片詳情\n   */\n  getCard: async (id) => {\n    try {\n      const response = await apiClient.get(`/cards/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('獲取名片詳情失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 創建名片\n   * @param {Object} cardData - 名片數據\n   * @param {Object} images - 圖片數據 { front: {file, ocrText}, back: {file, ocrText} }\n   * @returns {Promise<Object>} 創建的名片\n   */\n  createCard: async (cardData, images = {}) => {\n    try {\n      const formData = new FormData();\n      \n      // 添加名片數據\n      Object.keys(cardData).forEach(key => {\n        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {\n          formData.append(key, cardData[key]);\n        }\n      });\n      \n      // 添加圖片文件和OCR數據\n      if (images.front) {\n        if (images.front.file) {\n          formData.append('front_image', images.front.file);\n        }\n        if (images.front.ocrText) {\n          formData.append('front_ocr_text', images.front.ocrText);\n        }\n      }\n      \n      if (images.back) {\n        if (images.back.file) {\n          formData.append('back_image', images.back.file);\n        }\n        if (images.back.ocrText) {\n          formData.append('back_ocr_text', images.back.ocrText);\n        }\n      }\n      \n      const response = await apiClient.post('/cards/', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      \n      return response.data;\n    } catch (error) {\n      console.error('創建名片失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 更新名片\n   * @param {number} id - 名片ID\n   * @param {Object} cardData - 更新的名片數據\n   * @param {Object} images - 圖片數據\n   * @returns {Promise<Object>} 更新後的名片\n   */\n  updateCard: async (id, cardData, images = {}) => {\n    try {\n      const formData = new FormData();\n      \n      // 添加名片數據\n      Object.keys(cardData).forEach(key => {\n        if (cardData[key] !== null && cardData[key] !== undefined && cardData[key] !== '') {\n          formData.append(key, cardData[key]);\n        }\n      });\n      \n      // 添加圖片文件和OCR數據\n      if (images.front) {\n        if (images.front.file) {\n          formData.append('front_image', images.front.file);\n        }\n        if (images.front.ocrText) {\n          formData.append('front_ocr_text', images.front.ocrText);\n        }\n      }\n      \n      if (images.back) {\n        if (images.back.file) {\n          formData.append('back_image', images.back.file);\n        }\n        if (images.back.ocrText) {\n          formData.append('back_ocr_text', images.back.ocrText);\n        }\n      }\n      \n      const response = await apiClient.put(`/cards/${id}`, formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      \n      return response.data;\n    } catch (error) {\n      console.error('更新名片失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 刪除名片\n   * @param {number} id - 名片ID\n   * @returns {Promise<Object>} 刪除結果\n   */\n  deleteCard: async (id) => {\n    try {\n      const response = await apiClient.delete(`/cards/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('刪除名片失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 導出名片\n   * @param {string} format - 導出格式 ('csv' 或 'excel')\n   * @returns {Promise<Blob>} 導出文件\n   */\n  exportCards: async (format = 'csv') => {\n    try {\n      const response = await apiClient.get(`/cards/export?format=${format}`, {\n        responseType: 'blob',\n      });\n      return response.data;\n    } catch (error) {\n      console.error('導出名片失敗:', error);\n      throw error;\n    }\n  }\n};\n\nexport default cardService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,SAAS,MAAM,aAAa;AAEnC,OAAO,MAAMC,WAAW,GAAG;EACzB;AACF;AACA;AACA;AACA;EACEC,QAAQ,EAAE,MAAAA,CAAOC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC/B,IAAI;MACF,MAAMC,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,SAAS,EAAE;QAAEF;MAAO,CAAC,CAAC;MAC3D,OAAOC,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACEE,OAAO,EAAE,MAAOC,EAAE,IAAK;IACrB,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,UAAUK,EAAE,EAAE,CAAC;MACpD,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEI,UAAU,EAAE,MAAAA,CAAOC,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC3C,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAAC,CAACM,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIP,QAAQ,CAACO,GAAG,CAAC,KAAK,IAAI,IAAIP,QAAQ,CAACO,GAAG,CAAC,KAAKC,SAAS,IAAIR,QAAQ,CAACO,GAAG,CAAC,KAAK,EAAE,EAAE;UACjFL,QAAQ,CAACO,MAAM,CAACF,GAAG,EAAEP,QAAQ,CAACO,GAAG,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;;MAEF;MACA,IAAIN,MAAM,CAACS,KAAK,EAAE;QAChB,IAAIT,MAAM,CAACS,KAAK,CAACC,IAAI,EAAE;UACrBT,QAAQ,CAACO,MAAM,CAAC,aAAa,EAAER,MAAM,CAACS,KAAK,CAACC,IAAI,CAAC;QACnD;QACA,IAAIV,MAAM,CAACS,KAAK,CAACE,OAAO,EAAE;UACxBV,QAAQ,CAACO,MAAM,CAAC,gBAAgB,EAAER,MAAM,CAACS,KAAK,CAACE,OAAO,CAAC;QACzD;MACF;MAEA,IAAIX,MAAM,CAACY,IAAI,EAAE;QACf,IAAIZ,MAAM,CAACY,IAAI,CAACF,IAAI,EAAE;UACpBT,QAAQ,CAACO,MAAM,CAAC,YAAY,EAAER,MAAM,CAACY,IAAI,CAACF,IAAI,CAAC;QACjD;QACA,IAAIV,MAAM,CAACY,IAAI,CAACD,OAAO,EAAE;UACvBV,QAAQ,CAACO,MAAM,CAAC,eAAe,EAAER,MAAM,CAACY,IAAI,CAACD,OAAO,CAAC;QACvD;MACF;MAEA,MAAMpB,QAAQ,GAAG,MAAMJ,SAAS,CAAC0B,IAAI,CAAC,SAAS,EAAEZ,QAAQ,EAAE;QACzDa,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,OAAOvB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;AACA;EACEqB,UAAU,EAAE,MAAAA,CAAOlB,EAAE,EAAEE,QAAQ,EAAEC,MAAM,GAAG,CAAC,CAAC,KAAK;IAC/C,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;;MAE/B;MACAC,MAAM,CAACC,IAAI,CAACL,QAAQ,CAAC,CAACM,OAAO,CAACC,GAAG,IAAI;QACnC,IAAIP,QAAQ,CAACO,GAAG,CAAC,KAAK,IAAI,IAAIP,QAAQ,CAACO,GAAG,CAAC,KAAKC,SAAS,IAAIR,QAAQ,CAACO,GAAG,CAAC,KAAK,EAAE,EAAE;UACjFL,QAAQ,CAACO,MAAM,CAACF,GAAG,EAAEP,QAAQ,CAACO,GAAG,CAAC,CAAC;QACrC;MACF,CAAC,CAAC;;MAEF;MACA,IAAIN,MAAM,CAACS,KAAK,EAAE;QAChB,IAAIT,MAAM,CAACS,KAAK,CAACC,IAAI,EAAE;UACrBT,QAAQ,CAACO,MAAM,CAAC,aAAa,EAAER,MAAM,CAACS,KAAK,CAACC,IAAI,CAAC;QACnD;QACA,IAAIV,MAAM,CAACS,KAAK,CAACE,OAAO,EAAE;UACxBV,QAAQ,CAACO,MAAM,CAAC,gBAAgB,EAAER,MAAM,CAACS,KAAK,CAACE,OAAO,CAAC;QACzD;MACF;MAEA,IAAIX,MAAM,CAACY,IAAI,EAAE;QACf,IAAIZ,MAAM,CAACY,IAAI,CAACF,IAAI,EAAE;UACpBT,QAAQ,CAACO,MAAM,CAAC,YAAY,EAAER,MAAM,CAACY,IAAI,CAACF,IAAI,CAAC;QACjD;QACA,IAAIV,MAAM,CAACY,IAAI,CAACD,OAAO,EAAE;UACvBV,QAAQ,CAACO,MAAM,CAAC,eAAe,EAAER,MAAM,CAACY,IAAI,CAACD,OAAO,CAAC;QACvD;MACF;MAEA,MAAMpB,QAAQ,GAAG,MAAMJ,SAAS,CAAC6B,GAAG,CAAC,UAAUnB,EAAE,EAAE,EAAEI,QAAQ,EAAE;QAC7Da,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,OAAOvB,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACEuB,UAAU,EAAE,MAAOpB,EAAE,IAAK;IACxB,IAAI;MACF,MAAMN,QAAQ,GAAG,MAAMJ,SAAS,CAAC+B,MAAM,CAAC,UAAUrB,EAAE,EAAE,CAAC;MACvD,OAAON,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACEyB,WAAW,EAAE,MAAAA,CAAOC,MAAM,GAAG,KAAK,KAAK;IACrC,IAAI;MACF,MAAM7B,QAAQ,GAAG,MAAMJ,SAAS,CAACK,GAAG,CAAC,wBAAwB4B,MAAM,EAAE,EAAE;QACrEC,YAAY,EAAE;MAChB,CAAC,CAAC;MACF,OAAO9B,QAAQ,CAACE,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}