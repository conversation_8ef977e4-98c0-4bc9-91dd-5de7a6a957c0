/**
 * 主應用組件 - 重構版本
 * 使用新的頁面組件和路由配置
 */

import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import 'antd-mobile/es/global';
import './App.css';

// 導入新的頁面組件
import {
  HomePage,
  ScanPage,
  CardsPage,
  CardEditPage,
  CardViewPage
} from './pages';

// 保留相機測試頁面（開發用）
import CameraTestPage from './components/CameraTestPage';

function App() {
  return (
    <Router>
      <Routes>
        {/* 主頁 */}
        <Route path="/" element={<HomePage />} />

        {/* 掃描頁面 */}
        <Route path="/scan" element={<ScanPage />} />

        {/* 名片管理 */}
        <Route path="/cards" element={<CardsPage />} />
        <Route path="/cards/new" element={<CardEditPage />} />
        <Route path="/cards/:id" element={<CardViewPage />} />
        <Route path="/cards/:id/edit" element={<CardEditPage />} />

        {/* 開發測試頁面 */}
        <Route path="/camera-test" element={<CameraTestPage />} />
      </Routes>
    </Router>
  );
}

export default App;