{"ast": null, "code": "import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport usePagination from '../usePagination';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useAntdTable = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var form = options.form,\n    _b = options.defaultType,\n    defaultType = _b === void 0 ? 'simple' : _b,\n    defaultParams = options.defaultParams,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    _d = options.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    _e = options.ready,\n    ready = _e === void 0 ? true : _e,\n    rest = __rest(options, [\"form\", \"defaultType\", \"defaultParams\", \"manual\", \"refreshDeps\", \"ready\"]);\n  var result = usePagination(service, __assign(__assign({\n    ready: ready,\n    manual: true\n  }, rest), {\n    onSuccess: function () {\n      var _a;\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runSuccessRef.current = true;\n      (_a = rest.onSuccess) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([rest], __read(args), false));\n    }\n  }));\n  var _f = result.params,\n    params = _f === void 0 ? [] : _f,\n    run = result.run;\n  var cacheFormTableData = params[2] || {};\n  var _g = __read(useState((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),\n    type = _g[0],\n    setType = _g[1];\n  var allFormDataRef = useRef({});\n  var defaultDataSourceRef = useRef([]);\n  var runSuccessRef = useRef(false);\n  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks);\n  // get current active field values\n  var getActiveFieldValues = function () {\n    if (!form) {\n      return {};\n    }\n    // antd 4\n    if (isAntdV4) {\n      return form.getFieldsValue(null, function () {\n        return true;\n      });\n    }\n    // antd 3\n    var allFieldsValue = form.getFieldsValue();\n    var activeFieldsValue = {};\n    Object.keys(allFieldsValue).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFieldsValue[key];\n      }\n    });\n    return activeFieldsValue;\n  };\n  var validateFields = function () {\n    if (!form) {\n      return Promise.resolve({});\n    }\n    var activeFieldsValue = getActiveFieldValues();\n    var fields = Object.keys(activeFieldsValue);\n    // antd 4\n    if (isAntdV4) {\n      return form.validateFields(fields);\n    }\n    // antd 3\n    return new Promise(function (resolve, reject) {\n      form.validateFields(fields, function (errors, values) {\n        if (errors) {\n          reject(errors);\n        } else {\n          resolve(values);\n        }\n      });\n    });\n  };\n  var restoreForm = function () {\n    if (!form) {\n      return;\n    }\n    // antd v4\n    if (isAntdV4) {\n      return form.setFieldsValue(allFormDataRef.current);\n    }\n    // antd v3\n    var activeFieldsValue = {};\n    Object.keys(allFormDataRef.current).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFormDataRef.current[key];\n      }\n    });\n    form.setFieldsValue(activeFieldsValue);\n  };\n  var changeType = function () {\n    var activeFieldsValue = getActiveFieldValues();\n    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);\n    setType(function (t) {\n      return t === 'simple' ? 'advance' : 'simple';\n    });\n  };\n  var _submit = function (initPagination) {\n    if (!ready) {\n      return;\n    }\n    setTimeout(function () {\n      validateFields().then(function (values) {\n        if (values === void 0) {\n          values = {};\n        }\n        var pagination = initPagination || __assign(__assign({\n          pageSize: options.defaultPageSize || 10\n        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {\n          current: 1\n        });\n        if (!form) {\n          // @ts-ignore\n          run(pagination);\n          return;\n        }\n        // record all form data\n        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values);\n        // @ts-ignore\n        run(pagination, values, {\n          allFormData: allFormDataRef.current,\n          type: type\n        });\n      }).catch(function (err) {\n        return err;\n      });\n    });\n  };\n  var reset = function () {\n    var _a, _b;\n    if (form) {\n      form.resetFields();\n    }\n    _submit(__assign(__assign({}, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}), {\n      pageSize: options.defaultPageSize || ((_b = (_a = options.defaultParams) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.pageSize) || 10,\n      current: 1\n    }));\n  };\n  var submit = function (e) {\n    var _a, _b, _c;\n    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);\n    _submit(runSuccessRef.current ? undefined : __assign({\n      pageSize: options.defaultPageSize || ((_c = (_b = options.defaultParams) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.pageSize) || 10,\n      current: 1\n    }, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}));\n  };\n  var onTableChange = function (pagination, filters, sorter, extra) {\n    var _a = __read(params || []),\n      oldPaginationParams = _a[0],\n      restParams = _a.slice(1);\n    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: pagination.current,\n      pageSize: pagination.pageSize,\n      filters: filters,\n      sorter: sorter,\n      extra: extra\n    })], __read(restParams), false));\n  };\n  // init\n  useEffect(function () {\n    // if has cache, use cached params. ignore manual and ready.\n    if (params.length > 0) {\n      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};\n      restoreForm();\n      // @ts-ignore\n      run.apply(void 0, __spreadArray([], __read(params), false));\n      return;\n    }\n    if (ready) {\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      if (!manual) {\n        _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n      }\n    }\n  }, []);\n  // change search type, restore form data\n  useUpdateEffect(function () {\n    if (!ready) {\n      return;\n    }\n    restoreForm();\n  }, [type]);\n  // refresh & ready change on the same time\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      if (form) {\n        form.resetFields();\n      }\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!ready) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      result.pagination.changeCurrent(1);\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return __assign(__assign({}, result), {\n    tableProps: {\n      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,\n      loading: result.loading,\n      onChange: useMemoizedFn(onTableChange),\n      pagination: {\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize,\n        total: result.pagination.total\n      }\n    },\n    search: {\n      submit: useMemoizedFn(submit),\n      type: type,\n      changeType: useMemoizedFn(changeType),\n      reset: useMemoizedFn(reset)\n    }\n  });\n};\nexport default useAntdTable;", "map": {"version": 3, "names": ["__assign", "__read", "__rest", "__spread<PERSON><PERSON>y", "useEffect", "useRef", "useState", "useMemoizedFn", "usePagination", "useUpdateEffect", "useAntdTable", "service", "options", "_a", "form", "_b", "defaultType", "defaultParams", "_c", "manual", "_d", "refreshDeps", "_e", "ready", "rest", "result", "onSuccess", "args", "_i", "arguments", "length", "runSuccessRef", "current", "call", "apply", "_f", "params", "run", "cacheFormTableData", "_g", "type", "setType", "allFormDataRef", "defaultDataSourceRef", "isAntdV4", "getInternalHooks", "getActiveFieldValues", "getFieldsValue", "allFieldsValue", "activeFieldsValue", "Object", "keys", "for<PERSON>ach", "key", "getFieldInstance", "validateFields", "Promise", "resolve", "fields", "reject", "errors", "values", "restoreForm", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "changeType", "t", "_submit", "initPagination", "setTimeout", "then", "pagination", "pageSize", "defaultPageSize", "allFormData", "catch", "err", "reset", "resetFields", "submit", "e", "preventDefault", "undefined", "onTableChange", "filters", "sorter", "extra", "oldPaginationParams", "restParams", "slice", "hasAutoRun", "changeCurrent", "tableProps", "dataSource", "data", "list", "loading", "onChange", "total", "search"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useAntdTable/index.js"], "sourcesContent": ["import { __assign, __read, __rest, __spreadArray } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nimport usePagination from '../usePagination';\nimport useUpdateEffect from '../useUpdateEffect';\nvar useAntdTable = function (service, options) {\n  var _a;\n  if (options === void 0) {\n    options = {};\n  }\n  var form = options.form,\n    _b = options.defaultType,\n    defaultType = _b === void 0 ? 'simple' : _b,\n    defaultParams = options.defaultParams,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    _d = options.refreshDeps,\n    refreshDeps = _d === void 0 ? [] : _d,\n    _e = options.ready,\n    ready = _e === void 0 ? true : _e,\n    rest = __rest(options, [\"form\", \"defaultType\", \"defaultParams\", \"manual\", \"refreshDeps\", \"ready\"]);\n  var result = usePagination(service, __assign(__assign({\n    ready: ready,\n    manual: true\n  }, rest), {\n    onSuccess: function () {\n      var _a;\n      var args = [];\n      for (var _i = 0; _i < arguments.length; _i++) {\n        args[_i] = arguments[_i];\n      }\n      // eslint-disable-next-line @typescript-eslint/no-use-before-define\n      runSuccessRef.current = true;\n      (_a = rest.onSuccess) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([rest], __read(args), false));\n    }\n  }));\n  var _f = result.params,\n    params = _f === void 0 ? [] : _f,\n    run = result.run;\n  var cacheFormTableData = params[2] || {};\n  var _g = __read(useState((cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.type) || defaultType), 2),\n    type = _g[0],\n    setType = _g[1];\n  var allFormDataRef = useRef({});\n  var defaultDataSourceRef = useRef([]);\n  var runSuccessRef = useRef(false);\n  var isAntdV4 = !!(form === null || form === void 0 ? void 0 : form.getInternalHooks);\n  // get current active field values\n  var getActiveFieldValues = function () {\n    if (!form) {\n      return {};\n    }\n    // antd 4\n    if (isAntdV4) {\n      return form.getFieldsValue(null, function () {\n        return true;\n      });\n    }\n    // antd 3\n    var allFieldsValue = form.getFieldsValue();\n    var activeFieldsValue = {};\n    Object.keys(allFieldsValue).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFieldsValue[key];\n      }\n    });\n    return activeFieldsValue;\n  };\n  var validateFields = function () {\n    if (!form) {\n      return Promise.resolve({});\n    }\n    var activeFieldsValue = getActiveFieldValues();\n    var fields = Object.keys(activeFieldsValue);\n    // antd 4\n    if (isAntdV4) {\n      return form.validateFields(fields);\n    }\n    // antd 3\n    return new Promise(function (resolve, reject) {\n      form.validateFields(fields, function (errors, values) {\n        if (errors) {\n          reject(errors);\n        } else {\n          resolve(values);\n        }\n      });\n    });\n  };\n  var restoreForm = function () {\n    if (!form) {\n      return;\n    }\n    // antd v4\n    if (isAntdV4) {\n      return form.setFieldsValue(allFormDataRef.current);\n    }\n    // antd v3\n    var activeFieldsValue = {};\n    Object.keys(allFormDataRef.current).forEach(function (key) {\n      if (form.getFieldInstance ? form.getFieldInstance(key) : true) {\n        activeFieldsValue[key] = allFormDataRef.current[key];\n      }\n    });\n    form.setFieldsValue(activeFieldsValue);\n  };\n  var changeType = function () {\n    var activeFieldsValue = getActiveFieldValues();\n    allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), activeFieldsValue);\n    setType(function (t) {\n      return t === 'simple' ? 'advance' : 'simple';\n    });\n  };\n  var _submit = function (initPagination) {\n    if (!ready) {\n      return;\n    }\n    setTimeout(function () {\n      validateFields().then(function (values) {\n        if (values === void 0) {\n          values = {};\n        }\n        var pagination = initPagination || __assign(__assign({\n          pageSize: options.defaultPageSize || 10\n        }, (params === null || params === void 0 ? void 0 : params[0]) || {}), {\n          current: 1\n        });\n        if (!form) {\n          // @ts-ignore\n          run(pagination);\n          return;\n        }\n        // record all form data\n        allFormDataRef.current = __assign(__assign({}, allFormDataRef.current), values);\n        // @ts-ignore\n        run(pagination, values, {\n          allFormData: allFormDataRef.current,\n          type: type\n        });\n      }).catch(function (err) {\n        return err;\n      });\n    });\n  };\n  var reset = function () {\n    var _a, _b;\n    if (form) {\n      form.resetFields();\n    }\n    _submit(__assign(__assign({}, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}), {\n      pageSize: options.defaultPageSize || ((_b = (_a = options.defaultParams) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.pageSize) || 10,\n      current: 1\n    }));\n  };\n  var submit = function (e) {\n    var _a, _b, _c;\n    (_a = e === null || e === void 0 ? void 0 : e.preventDefault) === null || _a === void 0 ? void 0 : _a.call(e);\n    _submit(runSuccessRef.current ? undefined : __assign({\n      pageSize: options.defaultPageSize || ((_c = (_b = options.defaultParams) === null || _b === void 0 ? void 0 : _b[0]) === null || _c === void 0 ? void 0 : _c.pageSize) || 10,\n      current: 1\n    }, (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]) || {}));\n  };\n  var onTableChange = function (pagination, filters, sorter, extra) {\n    var _a = __read(params || []),\n      oldPaginationParams = _a[0],\n      restParams = _a.slice(1);\n    run.apply(void 0, __spreadArray([__assign(__assign({}, oldPaginationParams), {\n      current: pagination.current,\n      pageSize: pagination.pageSize,\n      filters: filters,\n      sorter: sorter,\n      extra: extra\n    })], __read(restParams), false));\n  };\n  // init\n  useEffect(function () {\n    // if has cache, use cached params. ignore manual and ready.\n    if (params.length > 0) {\n      allFormDataRef.current = (cacheFormTableData === null || cacheFormTableData === void 0 ? void 0 : cacheFormTableData.allFormData) || {};\n      restoreForm();\n      // @ts-ignore\n      run.apply(void 0, __spreadArray([], __read(params), false));\n      return;\n    }\n    if (ready) {\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      if (!manual) {\n        _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n      }\n    }\n  }, []);\n  // change search type, restore form data\n  useUpdateEffect(function () {\n    if (!ready) {\n      return;\n    }\n    restoreForm();\n  }, [type]);\n  // refresh & ready change on the same time\n  var hasAutoRun = useRef(false);\n  hasAutoRun.current = false;\n  useUpdateEffect(function () {\n    if (!manual && ready) {\n      hasAutoRun.current = true;\n      if (form) {\n        form.resetFields();\n      }\n      allFormDataRef.current = (defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[1]) || {};\n      restoreForm();\n      _submit(defaultParams === null || defaultParams === void 0 ? void 0 : defaultParams[0]);\n    }\n  }, [ready]);\n  useUpdateEffect(function () {\n    if (hasAutoRun.current) {\n      return;\n    }\n    if (!ready) {\n      return;\n    }\n    if (!manual) {\n      hasAutoRun.current = true;\n      result.pagination.changeCurrent(1);\n    }\n  }, __spreadArray([], __read(refreshDeps), false));\n  return __assign(__assign({}, result), {\n    tableProps: {\n      dataSource: ((_a = result.data) === null || _a === void 0 ? void 0 : _a.list) || defaultDataSourceRef.current,\n      loading: result.loading,\n      onChange: useMemoizedFn(onTableChange),\n      pagination: {\n        current: result.pagination.current,\n        pageSize: result.pagination.pageSize,\n        total: result.pagination.total\n      }\n    },\n    search: {\n      submit: useMemoizedFn(submit),\n      type: type,\n      changeType: useMemoizedFn(changeType),\n      reset: useMemoizedFn(reset)\n    }\n  });\n};\nexport default useAntdTable;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC/D,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,IAAIC,YAAY,GAAG,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;EAC7C,IAAIC,EAAE;EACN,IAAID,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIE,IAAI,GAAGF,OAAO,CAACE,IAAI;IACrBC,EAAE,GAAGH,OAAO,CAACI,WAAW;IACxBA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,QAAQ,GAAGA,EAAE;IAC3CE,aAAa,GAAGL,OAAO,CAACK,aAAa;IACrCC,EAAE,GAAGN,OAAO,CAACO,MAAM;IACnBA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IACnCE,EAAE,GAAGR,OAAO,CAACS,WAAW;IACxBA,WAAW,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IACrCE,EAAE,GAAGV,OAAO,CAACW,KAAK;IAClBA,KAAK,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,EAAE;IACjCE,IAAI,GAAGtB,MAAM,CAACU,OAAO,EAAE,CAAC,MAAM,EAAE,aAAa,EAAE,eAAe,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,CAAC,CAAC;EACpG,IAAIa,MAAM,GAAGjB,aAAa,CAACG,OAAO,EAAEX,QAAQ,CAACA,QAAQ,CAAC;IACpDuB,KAAK,EAAEA,KAAK;IACZJ,MAAM,EAAE;EACV,CAAC,EAAEK,IAAI,CAAC,EAAE;IACRE,SAAS,EAAE,SAAAA,CAAA,EAAY;MACrB,IAAIb,EAAE;MACN,IAAIc,IAAI,GAAG,EAAE;MACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;QAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;MAC1B;MACA;MACAG,aAAa,CAACC,OAAO,GAAG,IAAI;MAC5B,CAACnB,EAAE,GAAGW,IAAI,CAACE,SAAS,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoB,IAAI,CAACC,KAAK,CAACrB,EAAE,EAAEV,aAAa,CAAC,CAACqB,IAAI,CAAC,EAAEvB,MAAM,CAAC0B,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IAC1H;EACF,CAAC,CAAC,CAAC;EACH,IAAIQ,EAAE,GAAGV,MAAM,CAACW,MAAM;IACpBA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,EAAE,GAAGA,EAAE;IAChCE,GAAG,GAAGZ,MAAM,CAACY,GAAG;EAClB,IAAIC,kBAAkB,GAAGF,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;EACxC,IAAIG,EAAE,GAAGtC,MAAM,CAACK,QAAQ,CAAC,CAACgC,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACE,IAAI,KAAKxB,WAAW,CAAC,EAAE,CAAC,CAAC;IAC5IwB,IAAI,GAAGD,EAAE,CAAC,CAAC,CAAC;IACZE,OAAO,GAAGF,EAAE,CAAC,CAAC,CAAC;EACjB,IAAIG,cAAc,GAAGrC,MAAM,CAAC,CAAC,CAAC,CAAC;EAC/B,IAAIsC,oBAAoB,GAAGtC,MAAM,CAAC,EAAE,CAAC;EACrC,IAAI0B,aAAa,GAAG1B,MAAM,CAAC,KAAK,CAAC;EACjC,IAAIuC,QAAQ,GAAG,CAAC,EAAE9B,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAAC+B,gBAAgB,CAAC;EACpF;EACA,IAAIC,oBAAoB,GAAG,SAAAA,CAAA,EAAY;IACrC,IAAI,CAAChC,IAAI,EAAE;MACT,OAAO,CAAC,CAAC;IACX;IACA;IACA,IAAI8B,QAAQ,EAAE;MACZ,OAAO9B,IAAI,CAACiC,cAAc,CAAC,IAAI,EAAE,YAAY;QAC3C,OAAO,IAAI;MACb,CAAC,CAAC;IACJ;IACA;IACA,IAAIC,cAAc,GAAGlC,IAAI,CAACiC,cAAc,CAAC,CAAC;IAC1C,IAAIE,iBAAiB,GAAG,CAAC,CAAC;IAC1BC,MAAM,CAACC,IAAI,CAACH,cAAc,CAAC,CAACI,OAAO,CAAC,UAAUC,GAAG,EAAE;MACjD,IAAIvC,IAAI,CAACwC,gBAAgB,GAAGxC,IAAI,CAACwC,gBAAgB,CAACD,GAAG,CAAC,GAAG,IAAI,EAAE;QAC7DJ,iBAAiB,CAACI,GAAG,CAAC,GAAGL,cAAc,CAACK,GAAG,CAAC;MAC9C;IACF,CAAC,CAAC;IACF,OAAOJ,iBAAiB;EAC1B,CAAC;EACD,IAAIM,cAAc,GAAG,SAAAA,CAAA,EAAY;IAC/B,IAAI,CAACzC,IAAI,EAAE;MACT,OAAO0C,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC,CAAC;IAC5B;IACA,IAAIR,iBAAiB,GAAGH,oBAAoB,CAAC,CAAC;IAC9C,IAAIY,MAAM,GAAGR,MAAM,CAACC,IAAI,CAACF,iBAAiB,CAAC;IAC3C;IACA,IAAIL,QAAQ,EAAE;MACZ,OAAO9B,IAAI,CAACyC,cAAc,CAACG,MAAM,CAAC;IACpC;IACA;IACA,OAAO,IAAIF,OAAO,CAAC,UAAUC,OAAO,EAAEE,MAAM,EAAE;MAC5C7C,IAAI,CAACyC,cAAc,CAACG,MAAM,EAAE,UAAUE,MAAM,EAAEC,MAAM,EAAE;QACpD,IAAID,MAAM,EAAE;UACVD,MAAM,CAACC,MAAM,CAAC;QAChB,CAAC,MAAM;UACLH,OAAO,CAACI,MAAM,CAAC;QACjB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,WAAW,GAAG,SAAAA,CAAA,EAAY;IAC5B,IAAI,CAAChD,IAAI,EAAE;MACT;IACF;IACA;IACA,IAAI8B,QAAQ,EAAE;MACZ,OAAO9B,IAAI,CAACiD,cAAc,CAACrB,cAAc,CAACV,OAAO,CAAC;IACpD;IACA;IACA,IAAIiB,iBAAiB,GAAG,CAAC,CAAC;IAC1BC,MAAM,CAACC,IAAI,CAACT,cAAc,CAACV,OAAO,CAAC,CAACoB,OAAO,CAAC,UAAUC,GAAG,EAAE;MACzD,IAAIvC,IAAI,CAACwC,gBAAgB,GAAGxC,IAAI,CAACwC,gBAAgB,CAACD,GAAG,CAAC,GAAG,IAAI,EAAE;QAC7DJ,iBAAiB,CAACI,GAAG,CAAC,GAAGX,cAAc,CAACV,OAAO,CAACqB,GAAG,CAAC;MACtD;IACF,CAAC,CAAC;IACFvC,IAAI,CAACiD,cAAc,CAACd,iBAAiB,CAAC;EACxC,CAAC;EACD,IAAIe,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,IAAIf,iBAAiB,GAAGH,oBAAoB,CAAC,CAAC;IAC9CJ,cAAc,CAACV,OAAO,GAAGhC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0C,cAAc,CAACV,OAAO,CAAC,EAAEiB,iBAAiB,CAAC;IAC1FR,OAAO,CAAC,UAAUwB,CAAC,EAAE;MACnB,OAAOA,CAAC,KAAK,QAAQ,GAAG,SAAS,GAAG,QAAQ;IAC9C,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,OAAO,GAAG,SAAAA,CAAUC,cAAc,EAAE;IACtC,IAAI,CAAC5C,KAAK,EAAE;MACV;IACF;IACA6C,UAAU,CAAC,YAAY;MACrBb,cAAc,CAAC,CAAC,CAACc,IAAI,CAAC,UAAUR,MAAM,EAAE;QACtC,IAAIA,MAAM,KAAK,KAAK,CAAC,EAAE;UACrBA,MAAM,GAAG,CAAC,CAAC;QACb;QACA,IAAIS,UAAU,GAAGH,cAAc,IAAInE,QAAQ,CAACA,QAAQ,CAAC;UACnDuE,QAAQ,EAAE3D,OAAO,CAAC4D,eAAe,IAAI;QACvC,CAAC,EAAE,CAACpC,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;UACrEJ,OAAO,EAAE;QACX,CAAC,CAAC;QACF,IAAI,CAAClB,IAAI,EAAE;UACT;UACAuB,GAAG,CAACiC,UAAU,CAAC;UACf;QACF;QACA;QACA5B,cAAc,CAACV,OAAO,GAAGhC,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE0C,cAAc,CAACV,OAAO,CAAC,EAAE6B,MAAM,CAAC;QAC/E;QACAxB,GAAG,CAACiC,UAAU,EAAET,MAAM,EAAE;UACtBY,WAAW,EAAE/B,cAAc,CAACV,OAAO;UACnCQ,IAAI,EAAEA;QACR,CAAC,CAAC;MACJ,CAAC,CAAC,CAACkC,KAAK,CAAC,UAAUC,GAAG,EAAE;QACtB,OAAOA,GAAG;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,KAAK,GAAG,SAAAA,CAAA,EAAY;IACtB,IAAI/D,EAAE,EAAEE,EAAE;IACV,IAAID,IAAI,EAAE;MACRA,IAAI,CAAC+D,WAAW,CAAC,CAAC;IACpB;IACAX,OAAO,CAAClE,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,CAACiB,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE;MACrHsD,QAAQ,EAAE3D,OAAO,CAAC4D,eAAe,KAAK,CAACzD,EAAE,GAAG,CAACF,EAAE,GAAGD,OAAO,CAACK,aAAa,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwD,QAAQ,CAAC,IAAI,EAAE;MAC5KvC,OAAO,EAAE;IACX,CAAC,CAAC,CAAC;EACL,CAAC;EACD,IAAI8C,MAAM,GAAG,SAAAA,CAAUC,CAAC,EAAE;IACxB,IAAIlE,EAAE,EAAEE,EAAE,EAAEG,EAAE;IACd,CAACL,EAAE,GAAGkE,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACC,cAAc,MAAM,IAAI,IAAInE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACoB,IAAI,CAAC8C,CAAC,CAAC;IAC7Gb,OAAO,CAACnC,aAAa,CAACC,OAAO,GAAGiD,SAAS,GAAGjF,QAAQ,CAAC;MACnDuE,QAAQ,EAAE3D,OAAO,CAAC4D,eAAe,KAAK,CAACtD,EAAE,GAAG,CAACH,EAAE,GAAGH,OAAO,CAACK,aAAa,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC,MAAM,IAAI,IAAIG,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqD,QAAQ,CAAC,IAAI,EAAE;MAC5KvC,OAAO,EAAE;IACX,CAAC,EAAE,CAACf,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7F,CAAC;EACD,IAAIiE,aAAa,GAAG,SAAAA,CAAUZ,UAAU,EAAEa,OAAO,EAAEC,MAAM,EAAEC,KAAK,EAAE;IAChE,IAAIxE,EAAE,GAAGZ,MAAM,CAACmC,MAAM,IAAI,EAAE,CAAC;MAC3BkD,mBAAmB,GAAGzE,EAAE,CAAC,CAAC,CAAC;MAC3B0E,UAAU,GAAG1E,EAAE,CAAC2E,KAAK,CAAC,CAAC,CAAC;IAC1BnD,GAAG,CAACH,KAAK,CAAC,KAAK,CAAC,EAAE/B,aAAa,CAAC,CAACH,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEsF,mBAAmB,CAAC,EAAE;MAC3EtD,OAAO,EAAEsC,UAAU,CAACtC,OAAO;MAC3BuC,QAAQ,EAAED,UAAU,CAACC,QAAQ;MAC7BY,OAAO,EAAEA,OAAO;MAChBC,MAAM,EAAEA,MAAM;MACdC,KAAK,EAAEA;IACT,CAAC,CAAC,CAAC,EAAEpF,MAAM,CAACsF,UAAU,CAAC,EAAE,KAAK,CAAC,CAAC;EAClC,CAAC;EACD;EACAnF,SAAS,CAAC,YAAY;IACpB;IACA,IAAIgC,MAAM,CAACN,MAAM,GAAG,CAAC,EAAE;MACrBY,cAAc,CAACV,OAAO,GAAG,CAACM,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACmC,WAAW,KAAK,CAAC,CAAC;MACvIX,WAAW,CAAC,CAAC;MACb;MACAzB,GAAG,CAACH,KAAK,CAAC,KAAK,CAAC,EAAE/B,aAAa,CAAC,EAAE,EAAEF,MAAM,CAACmC,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;MAC3D;IACF;IACA,IAAIb,KAAK,EAAE;MACTmB,cAAc,CAACV,OAAO,GAAG,CAACf,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;MAC/G6C,WAAW,CAAC,CAAC;MACb,IAAI,CAAC3C,MAAM,EAAE;QACX+C,OAAO,CAACjD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,CAAC;MACzF;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EACN;EACAR,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACc,KAAK,EAAE;MACV;IACF;IACAuC,WAAW,CAAC,CAAC;EACf,CAAC,EAAE,CAACtB,IAAI,CAAC,CAAC;EACV;EACA,IAAIiD,UAAU,GAAGpF,MAAM,CAAC,KAAK,CAAC;EAC9BoF,UAAU,CAACzD,OAAO,GAAG,KAAK;EAC1BvB,eAAe,CAAC,YAAY;IAC1B,IAAI,CAACU,MAAM,IAAII,KAAK,EAAE;MACpBkE,UAAU,CAACzD,OAAO,GAAG,IAAI;MACzB,IAAIlB,IAAI,EAAE;QACRA,IAAI,CAAC+D,WAAW,CAAC,CAAC;MACpB;MACAnC,cAAc,CAACV,OAAO,GAAG,CAACf,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC;MAC/G6C,WAAW,CAAC,CAAC;MACbI,OAAO,CAACjD,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAAC,CAAC,CAAC,CAAC;IACzF;EACF,CAAC,EAAE,CAACM,KAAK,CAAC,CAAC;EACXd,eAAe,CAAC,YAAY;IAC1B,IAAIgF,UAAU,CAACzD,OAAO,EAAE;MACtB;IACF;IACA,IAAI,CAACT,KAAK,EAAE;MACV;IACF;IACA,IAAI,CAACJ,MAAM,EAAE;MACXsE,UAAU,CAACzD,OAAO,GAAG,IAAI;MACzBP,MAAM,CAAC6C,UAAU,CAACoB,aAAa,CAAC,CAAC,CAAC;IACpC;EACF,CAAC,EAAEvF,aAAa,CAAC,EAAE,EAAEF,MAAM,CAACoB,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC;EACjD,OAAOrB,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAAC,EAAE;IACpCkE,UAAU,EAAE;MACVC,UAAU,EAAE,CAAC,CAAC/E,EAAE,GAAGY,MAAM,CAACoE,IAAI,MAAM,IAAI,IAAIhF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACiF,IAAI,KAAKnD,oBAAoB,CAACX,OAAO;MAC7G+D,OAAO,EAAEtE,MAAM,CAACsE,OAAO;MACvBC,QAAQ,EAAEzF,aAAa,CAAC2E,aAAa,CAAC;MACtCZ,UAAU,EAAE;QACVtC,OAAO,EAAEP,MAAM,CAAC6C,UAAU,CAACtC,OAAO;QAClCuC,QAAQ,EAAE9C,MAAM,CAAC6C,UAAU,CAACC,QAAQ;QACpC0B,KAAK,EAAExE,MAAM,CAAC6C,UAAU,CAAC2B;MAC3B;IACF,CAAC;IACDC,MAAM,EAAE;MACNpB,MAAM,EAAEvE,aAAa,CAACuE,MAAM,CAAC;MAC7BtC,IAAI,EAAEA,IAAI;MACVwB,UAAU,EAAEzD,aAAa,CAACyD,UAAU,CAAC;MACrCY,KAAK,EAAErE,aAAa,CAACqE,KAAK;IAC5B;EACF,CAAC,CAAC;AACJ,CAAC;AACD,eAAelE,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}