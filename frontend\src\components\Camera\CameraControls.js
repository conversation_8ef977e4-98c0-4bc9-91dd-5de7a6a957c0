/**
 * 相機控制按鈕組件
 */

import React from 'react';
import { Button, Space } from 'antd-mobile';
import { CameraOutline, PictureOutline } from 'antd-mobile-icons';

const CameraControls = ({
  onTakePhoto,
  onSelectFromGallery,
  currentTarget = 'front',
  disabled = false,
  style = {},
  className = ''
}) => {
  const sideText = currentTarget === 'front' ? '正面' : '反面';

  return (
    <div 
      className={`camera-controls ${className}`}
      style={{
        display: 'flex',
        gap: '8px',
        justifyContent: 'center',
        ...style
      }}
    >
      <Button
        color="primary"
        size="large"
        onClick={() => onTakePhoto(currentTarget)}
        disabled={disabled}
        style={{ flex: 1 }}
      >
        <CameraOutline /> 拍攝{sideText}
      </Button>
      
      <Button
        color="primary"
        fill="outline"
        size="large"
        onClick={() => onSelectFromGallery(currentTarget)}
        disabled={disabled}
        style={{ flex: 1 }}
      >
        <PictureOutline /> 相冊
      </Button>
    </div>
  );
};

export default CameraControls;
