/**
 * 頁面容器組件
 */

import React from 'react';
import PageHeader from './PageHeader';

const PageContainer = ({ 
  title,
  onBack,
  backText,
  headerRight,
  showBack = true,
  children,
  style = {},
  className = '',
  contentStyle = {},
  contentClassName = ''
}) => {
  return (
    <div 
      className={`page-container ${className}`}
      style={{
        minHeight: '100vh',
        backgroundColor: '#f5f5f5',
        display: 'flex',
        flexDirection: 'column',
        ...style
      }}
    >
      {title && (
        <PageHeader
          title={title}
          onBack={onBack}
          backText={backText}
          right={headerRight}
          showBack={showBack}
        />
      )}
      
      <div 
        className={`page-content ${contentClassName}`}
        style={{
          flex: 1,
          padding: '16px',
          ...contentStyle
        }}
      >
        {children}
      </div>
    </div>
  );
};

export default PageContainer;
