{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\n// {[path]: count}\n// remove external when no used\nvar EXTERNAL_USED_COUNT = {};\nvar loadScript = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var script = document.querySelector(\"script[src=\\\"\".concat(path, \"\\\"]\"));\n  if (!script) {\n    var newScript_1 = document.createElement('script');\n    newScript_1.src = path;\n    Object.keys(props).forEach(function (key) {\n      newScript_1[key] = props[key];\n    });\n    newScript_1.setAttribute('data-status', 'loading');\n    document.body.appendChild(newScript_1);\n    return {\n      ref: newScript_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: script,\n    status: script.getAttribute('data-status') || 'ready'\n  };\n};\nvar loadCss = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var css = document.querySelector(\"link[href=\\\"\".concat(path, \"\\\"]\"));\n  if (!css) {\n    var newCss_1 = document.createElement('link');\n    newCss_1.rel = 'stylesheet';\n    newCss_1.href = path;\n    Object.keys(props).forEach(function (key) {\n      newCss_1[key] = props[key];\n    });\n    // IE9+\n    var isLegacyIECss = 'hideFocus' in newCss_1;\n    // use preload in IE Edge (to detect load errors)\n    if (isLegacyIECss && newCss_1.relList) {\n      newCss_1.rel = 'preload';\n      newCss_1.as = 'style';\n    }\n    newCss_1.setAttribute('data-status', 'loading');\n    document.head.appendChild(newCss_1);\n    return {\n      ref: newCss_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: css,\n    status: css.getAttribute('data-status') || 'ready'\n  };\n};\nvar useExternal = function (path, options) {\n  var _a = __read(useState(path ? 'loading' : 'unset'), 2),\n    status = _a[0],\n    setStatus = _a[1];\n  var ref = useRef();\n  useEffect(function () {\n    if (!path) {\n      setStatus('unset');\n      return;\n    }\n    var pathname = path.replace(/[|#].*$/, '');\n    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\\.css$)/.test(pathname)) {\n      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\\.js$)/.test(pathname)) {\n      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else {\n      // do nothing\n      console.error(\"Cannot infer the type of external resource, and please provide a type ('js' | 'css'). \" + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');\n    }\n    if (!ref.current) {\n      return;\n    }\n    if (EXTERNAL_USED_COUNT[path] === undefined) {\n      EXTERNAL_USED_COUNT[path] = 1;\n    } else {\n      EXTERNAL_USED_COUNT[path] += 1;\n    }\n    var handler = function (event) {\n      var _a;\n      var targetStatus = event.type === 'load' ? 'ready' : 'error';\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);\n      setStatus(targetStatus);\n    };\n    ref.current.addEventListener('load', handler);\n    ref.current.addEventListener('error', handler);\n    return function () {\n      var _a, _b, _c;\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);\n      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);\n      EXTERNAL_USED_COUNT[path] -= 1;\n      if (EXTERNAL_USED_COUNT[path] === 0 && !(options === null || options === void 0 ? void 0 : options.keepWhenUnused)) {\n        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();\n      }\n      ref.current = undefined;\n    };\n  }, [path]);\n  return status;\n};\nexport default useExternal;", "map": {"version": 3, "names": ["__read", "useEffect", "useRef", "useState", "EXTERNAL_USED_COUNT", "loadScript", "path", "props", "script", "document", "querySelector", "concat", "newScript_1", "createElement", "src", "Object", "keys", "for<PERSON>ach", "key", "setAttribute", "body", "append<PERSON><PERSON><PERSON>", "ref", "status", "getAttribute", "loadCss", "css", "newCss_1", "rel", "href", "isLegacyIECss", "relList", "as", "head", "useExternal", "options", "_a", "setStatus", "pathname", "replace", "type", "test", "result", "current", "js", "console", "error", "undefined", "handler", "event", "targetStatus", "addEventListener", "_b", "_c", "removeEventListener", "keep<PERSON>henUnused", "remove"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useExternal/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\n// {[path]: count}\n// remove external when no used\nvar EXTERNAL_USED_COUNT = {};\nvar loadScript = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var script = document.querySelector(\"script[src=\\\"\".concat(path, \"\\\"]\"));\n  if (!script) {\n    var newScript_1 = document.createElement('script');\n    newScript_1.src = path;\n    Object.keys(props).forEach(function (key) {\n      newScript_1[key] = props[key];\n    });\n    newScript_1.setAttribute('data-status', 'loading');\n    document.body.appendChild(newScript_1);\n    return {\n      ref: newScript_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: script,\n    status: script.getAttribute('data-status') || 'ready'\n  };\n};\nvar loadCss = function (path, props) {\n  if (props === void 0) {\n    props = {};\n  }\n  var css = document.querySelector(\"link[href=\\\"\".concat(path, \"\\\"]\"));\n  if (!css) {\n    var newCss_1 = document.createElement('link');\n    newCss_1.rel = 'stylesheet';\n    newCss_1.href = path;\n    Object.keys(props).forEach(function (key) {\n      newCss_1[key] = props[key];\n    });\n    // IE9+\n    var isLegacyIECss = 'hideFocus' in newCss_1;\n    // use preload in IE Edge (to detect load errors)\n    if (isLegacyIECss && newCss_1.relList) {\n      newCss_1.rel = 'preload';\n      newCss_1.as = 'style';\n    }\n    newCss_1.setAttribute('data-status', 'loading');\n    document.head.appendChild(newCss_1);\n    return {\n      ref: newCss_1,\n      status: 'loading'\n    };\n  }\n  return {\n    ref: css,\n    status: css.getAttribute('data-status') || 'ready'\n  };\n};\nvar useExternal = function (path, options) {\n  var _a = __read(useState(path ? 'loading' : 'unset'), 2),\n    status = _a[0],\n    setStatus = _a[1];\n  var ref = useRef();\n  useEffect(function () {\n    if (!path) {\n      setStatus('unset');\n      return;\n    }\n    var pathname = path.replace(/[|#].*$/, '');\n    if ((options === null || options === void 0 ? void 0 : options.type) === 'css' || !(options === null || options === void 0 ? void 0 : options.type) && /(^css!|\\.css$)/.test(pathname)) {\n      var result = loadCss(path, options === null || options === void 0 ? void 0 : options.css);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else if ((options === null || options === void 0 ? void 0 : options.type) === 'js' || !(options === null || options === void 0 ? void 0 : options.type) && /(^js!|\\.js$)/.test(pathname)) {\n      var result = loadScript(path, options === null || options === void 0 ? void 0 : options.js);\n      ref.current = result.ref;\n      setStatus(result.status);\n    } else {\n      // do nothing\n      console.error(\"Cannot infer the type of external resource, and please provide a type ('js' | 'css'). \" + 'Refer to the https://ahooks.js.org/hooks/dom/use-external/#options');\n    }\n    if (!ref.current) {\n      return;\n    }\n    if (EXTERNAL_USED_COUNT[path] === undefined) {\n      EXTERNAL_USED_COUNT[path] = 1;\n    } else {\n      EXTERNAL_USED_COUNT[path] += 1;\n    }\n    var handler = function (event) {\n      var _a;\n      var targetStatus = event.type === 'load' ? 'ready' : 'error';\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.setAttribute('data-status', targetStatus);\n      setStatus(targetStatus);\n    };\n    ref.current.addEventListener('load', handler);\n    ref.current.addEventListener('error', handler);\n    return function () {\n      var _a, _b, _c;\n      (_a = ref.current) === null || _a === void 0 ? void 0 : _a.removeEventListener('load', handler);\n      (_b = ref.current) === null || _b === void 0 ? void 0 : _b.removeEventListener('error', handler);\n      EXTERNAL_USED_COUNT[path] -= 1;\n      if (EXTERNAL_USED_COUNT[path] === 0 && !(options === null || options === void 0 ? void 0 : options.keepWhenUnused)) {\n        (_c = ref.current) === null || _c === void 0 ? void 0 : _c.remove();\n      }\n      ref.current = undefined;\n    };\n  }, [path]);\n  return status;\n};\nexport default useExternal;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD;AACA;AACA,IAAIC,mBAAmB,GAAG,CAAC,CAAC;AAC5B,IAAIC,UAAU,GAAG,SAAAA,CAAUC,IAAI,EAAEC,KAAK,EAAE;EACtC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC,CAAC;EACZ;EACA,IAAIC,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,eAAe,CAACC,MAAM,CAACL,IAAI,EAAE,KAAK,CAAC,CAAC;EACxE,IAAI,CAACE,MAAM,EAAE;IACX,IAAII,WAAW,GAAGH,QAAQ,CAACI,aAAa,CAAC,QAAQ,CAAC;IAClDD,WAAW,CAACE,GAAG,GAAGR,IAAI;IACtBS,MAAM,CAACC,IAAI,CAACT,KAAK,CAAC,CAACU,OAAO,CAAC,UAAUC,GAAG,EAAE;MACxCN,WAAW,CAACM,GAAG,CAAC,GAAGX,KAAK,CAACW,GAAG,CAAC;IAC/B,CAAC,CAAC;IACFN,WAAW,CAACO,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC;IAClDV,QAAQ,CAACW,IAAI,CAACC,WAAW,CAACT,WAAW,CAAC;IACtC,OAAO;MACLU,GAAG,EAAEV,WAAW;MAChBW,MAAM,EAAE;IACV,CAAC;EACH;EACA,OAAO;IACLD,GAAG,EAAEd,MAAM;IACXe,MAAM,EAAEf,MAAM,CAACgB,YAAY,CAAC,aAAa,CAAC,IAAI;EAChD,CAAC;AACH,CAAC;AACD,IAAIC,OAAO,GAAG,SAAAA,CAAUnB,IAAI,EAAEC,KAAK,EAAE;EACnC,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC,CAAC;EACZ;EACA,IAAImB,GAAG,GAAGjB,QAAQ,CAACC,aAAa,CAAC,cAAc,CAACC,MAAM,CAACL,IAAI,EAAE,KAAK,CAAC,CAAC;EACpE,IAAI,CAACoB,GAAG,EAAE;IACR,IAAIC,QAAQ,GAAGlB,QAAQ,CAACI,aAAa,CAAC,MAAM,CAAC;IAC7Cc,QAAQ,CAACC,GAAG,GAAG,YAAY;IAC3BD,QAAQ,CAACE,IAAI,GAAGvB,IAAI;IACpBS,MAAM,CAACC,IAAI,CAACT,KAAK,CAAC,CAACU,OAAO,CAAC,UAAUC,GAAG,EAAE;MACxCS,QAAQ,CAACT,GAAG,CAAC,GAAGX,KAAK,CAACW,GAAG,CAAC;IAC5B,CAAC,CAAC;IACF;IACA,IAAIY,aAAa,GAAG,WAAW,IAAIH,QAAQ;IAC3C;IACA,IAAIG,aAAa,IAAIH,QAAQ,CAACI,OAAO,EAAE;MACrCJ,QAAQ,CAACC,GAAG,GAAG,SAAS;MACxBD,QAAQ,CAACK,EAAE,GAAG,OAAO;IACvB;IACAL,QAAQ,CAACR,YAAY,CAAC,aAAa,EAAE,SAAS,CAAC;IAC/CV,QAAQ,CAACwB,IAAI,CAACZ,WAAW,CAACM,QAAQ,CAAC;IACnC,OAAO;MACLL,GAAG,EAAEK,QAAQ;MACbJ,MAAM,EAAE;IACV,CAAC;EACH;EACA,OAAO;IACLD,GAAG,EAAEI,GAAG;IACRH,MAAM,EAAEG,GAAG,CAACF,YAAY,CAAC,aAAa,CAAC,IAAI;EAC7C,CAAC;AACH,CAAC;AACD,IAAIU,WAAW,GAAG,SAAAA,CAAU5B,IAAI,EAAE6B,OAAO,EAAE;EACzC,IAAIC,EAAE,GAAGpC,MAAM,CAACG,QAAQ,CAACG,IAAI,GAAG,SAAS,GAAG,OAAO,CAAC,EAAE,CAAC,CAAC;IACtDiB,MAAM,GAAGa,EAAE,CAAC,CAAC,CAAC;IACdC,SAAS,GAAGD,EAAE,CAAC,CAAC,CAAC;EACnB,IAAId,GAAG,GAAGpB,MAAM,CAAC,CAAC;EAClBD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACK,IAAI,EAAE;MACT+B,SAAS,CAAC,OAAO,CAAC;MAClB;IACF;IACA,IAAIC,QAAQ,GAAGhC,IAAI,CAACiC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC;IAC1C,IAAI,CAACJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,IAAI,MAAM,KAAK,IAAI,EAAEL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,IAAI,CAAC,IAAI,gBAAgB,CAACC,IAAI,CAACH,QAAQ,CAAC,EAAE;MACtL,IAAII,MAAM,GAAGjB,OAAO,CAACnB,IAAI,EAAE6B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACT,GAAG,CAAC;MACzFJ,GAAG,CAACqB,OAAO,GAAGD,MAAM,CAACpB,GAAG;MACxBe,SAAS,CAACK,MAAM,CAACnB,MAAM,CAAC;IAC1B,CAAC,MAAM,IAAI,CAACY,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,IAAI,MAAM,IAAI,IAAI,EAAEL,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACK,IAAI,CAAC,IAAI,cAAc,CAACC,IAAI,CAACH,QAAQ,CAAC,EAAE;MAC1L,IAAII,MAAM,GAAGrC,UAAU,CAACC,IAAI,EAAE6B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACS,EAAE,CAAC;MAC3FtB,GAAG,CAACqB,OAAO,GAAGD,MAAM,CAACpB,GAAG;MACxBe,SAAS,CAACK,MAAM,CAACnB,MAAM,CAAC;IAC1B,CAAC,MAAM;MACL;MACAsB,OAAO,CAACC,KAAK,CAAC,wFAAwF,GAAG,oEAAoE,CAAC;IAChL;IACA,IAAI,CAACxB,GAAG,CAACqB,OAAO,EAAE;MAChB;IACF;IACA,IAAIvC,mBAAmB,CAACE,IAAI,CAAC,KAAKyC,SAAS,EAAE;MAC3C3C,mBAAmB,CAACE,IAAI,CAAC,GAAG,CAAC;IAC/B,CAAC,MAAM;MACLF,mBAAmB,CAACE,IAAI,CAAC,IAAI,CAAC;IAChC;IACA,IAAI0C,OAAO,GAAG,SAAAA,CAAUC,KAAK,EAAE;MAC7B,IAAIb,EAAE;MACN,IAAIc,YAAY,GAAGD,KAAK,CAACT,IAAI,KAAK,MAAM,GAAG,OAAO,GAAG,OAAO;MAC5D,CAACJ,EAAE,GAAGd,GAAG,CAACqB,OAAO,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjB,YAAY,CAAC,aAAa,EAAE+B,YAAY,CAAC;MACpGb,SAAS,CAACa,YAAY,CAAC;IACzB,CAAC;IACD5B,GAAG,CAACqB,OAAO,CAACQ,gBAAgB,CAAC,MAAM,EAAEH,OAAO,CAAC;IAC7C1B,GAAG,CAACqB,OAAO,CAACQ,gBAAgB,CAAC,OAAO,EAAEH,OAAO,CAAC;IAC9C,OAAO,YAAY;MACjB,IAAIZ,EAAE,EAAEgB,EAAE,EAAEC,EAAE;MACd,CAACjB,EAAE,GAAGd,GAAG,CAACqB,OAAO,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACkB,mBAAmB,CAAC,MAAM,EAAEN,OAAO,CAAC;MAC/F,CAACI,EAAE,GAAG9B,GAAG,CAACqB,OAAO,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACE,mBAAmB,CAAC,OAAO,EAAEN,OAAO,CAAC;MAChG5C,mBAAmB,CAACE,IAAI,CAAC,IAAI,CAAC;MAC9B,IAAIF,mBAAmB,CAACE,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE6B,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACoB,cAAc,CAAC,EAAE;QAClH,CAACF,EAAE,GAAG/B,GAAG,CAACqB,OAAO,MAAM,IAAI,IAAIU,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,MAAM,CAAC,CAAC;MACrE;MACAlC,GAAG,CAACqB,OAAO,GAAGI,SAAS;IACzB,CAAC;EACH,CAAC,EAAE,CAACzC,IAAI,CAAC,CAAC;EACV,OAAOiB,MAAM;AACf,CAAC;AACD,eAAeW,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}