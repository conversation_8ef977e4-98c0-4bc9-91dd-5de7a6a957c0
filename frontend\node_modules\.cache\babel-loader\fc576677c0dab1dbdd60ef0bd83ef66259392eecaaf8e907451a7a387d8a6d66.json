{"ast": null, "code": "var i;\n!function (i) {\n  i[i.HIGH_SURROGATE_START = 55296] = \"HIGH_SURROGATE_START\", i[i.HIGH_SURROGATE_END = 56319] = \"HIGH_SURROGATE_END\", i[i.LOW_SURROGATE_START = 56320] = \"LOW_SURROGATE_START\", i[i.REGIONAL_INDICATOR_START = 127462] = \"REGIONAL_INDICATOR_START\", i[i.REGIONAL_INDICATOR_END = 127487] = \"REGIONAL_INDICATOR_END\", i[i.FITZPATRICK_MODIFIER_START = 127995] = \"FITZPATRICK_MODIFIER_START\", i[i.FITZPATRICK_MODIFIER_END = 127999] = \"FITZPATRICK_MODIFIER_END\", i[i.VARIATION_MODIFIER_START = 65024] = \"VARIATION_MODIFIER_START\", i[i.VARIATION_MODIFIER_END = 65039] = \"VARIATION_MODIFIER_END\", i[i.DIACRITICAL_MARKS_START = 8400] = \"DIACRITICAL_MARKS_START\", i[i.DIACRITICAL_MARKS_END = 8447] = \"DIACRITICAL_MARKS_END\", i[i.SUBDIVISION_INDICATOR_START = 127988] = \"SUBDIVISION_INDICATOR_START\", i[i.TAGS_START = 917504] = \"TAGS_START\", i[i.TAGS_END = 917631] = \"TAGS_END\", i[i.ZWJ = 8205] = \"ZWJ\";\n}(i || (i = {}));\nconst e = Object.freeze([0x0308, 0x0937, 0x093F, 0x0BA8, 0x0BBF, 0x0BCD, 0x0E31, 0x0E33, 0x0E40, 0x0E49, 0x1100, 0x1161, 0x11A8]);\nvar n;\nfunction runes(i) {\n  if (\"string\" != typeof i) throw new TypeError(\"string cannot be undefined or null\");\n  const e = [];\n  let n = 0,\n    t = 0;\n  for (; n < i.length;) t += nextUnits(n + t, i), isGrapheme(i[n + t]) && t++, isVariationSelector(i[n + t]) && t++, isDiacriticalMark(i[n + t]) && t++, isZeroWidthJoiner(i[n + t]) ? t++ : (e.push(i.substring(n, n + t)), n += t, t = 0);\n  return e;\n}\nfunction nextUnits(i, e) {\n  const n = e[i];\n  if (!isFirstOfSurrogatePair(n) || i === e.length - 1) return 1;\n  const t = n + e[i + 1];\n  let r = e.substring(i + 2, i + 5);\n  return isRegionalIndicator(t) && isRegionalIndicator(r) ? 4 : isSubdivisionFlag(t) && isSupplementarySpecialpurposePlane(r) ? e.slice(i).indexOf(String.fromCodePoint(917631)) + 2 : isFitzpatrickModifier(r) ? 4 : 2;\n}\nfunction isFirstOfSurrogatePair(i) {\n  return i && betweenInclusive(i[0].charCodeAt(0), 55296, 56319);\n}\nfunction isRegionalIndicator(i) {\n  return betweenInclusive(codePointFromSurrogatePair(i), 127462, 127487);\n}\nfunction isSubdivisionFlag(i) {\n  return betweenInclusive(codePointFromSurrogatePair(i), 127988, 127988);\n}\nfunction isFitzpatrickModifier(i) {\n  return betweenInclusive(codePointFromSurrogatePair(i), 127995, 127999);\n}\nfunction isVariationSelector(i) {\n  return \"string\" == typeof i && betweenInclusive(i.charCodeAt(0), 65024, 65039);\n}\nfunction isDiacriticalMark(i) {\n  return \"string\" == typeof i && betweenInclusive(i.charCodeAt(0), 8400, 8447);\n}\nfunction isSupplementarySpecialpurposePlane(i) {\n  const e = i.codePointAt(0);\n  return \"string\" == typeof i && \"number\" == typeof e && betweenInclusive(e, 917504, 917631);\n}\nfunction isGrapheme(i) {\n  return \"string\" == typeof i && e.includes(i.charCodeAt(0));\n}\nfunction isZeroWidthJoiner(i) {\n  return \"string\" == typeof i && 8205 === i.charCodeAt(0);\n}\nfunction codePointFromSurrogatePair(i) {\n  return (i.charCodeAt(0) - 55296 << 10) + (i.charCodeAt(1) - 56320) + 0x10000;\n}\nfunction betweenInclusive(i, e, n) {\n  return i >= e && i <= n;\n}\nfunction substring(i, e, n) {\n  const t = runes(i);\n  if (void 0 === e) return i;\n  if (e >= t.length) return \"\";\n  const r = t.length - e;\n  let o = e + (void 0 === n ? r : n);\n  return o > e + r && (o = void 0), t.slice(e, o).join(\"\");\n}\n!function (i) {\n  i[i.unit_1 = 1] = \"unit_1\", i[i.unit_2 = 2] = \"unit_2\", i[i.unit_4 = 4] = \"unit_4\";\n}(n || (n = {}));\nexport { n as EnumCodeUnits, i as EnumRunesCode, e as GRAPHEMES, betweenInclusive, codePointFromSurrogatePair, runes as default, isDiacriticalMark, isFirstOfSurrogatePair, isFitzpatrickModifier, isGrapheme, isRegionalIndicator, isSubdivisionFlag, isSupplementarySpecialpurposePlane, isVariationSelector, isZeroWidthJoiner, nextUnits, runes, substring as substr, substring };", "map": {"version": 3, "names": ["i", "HIGH_SURROGATE_START", "HIGH_SURROGATE_END", "LOW_SURROGATE_START", "REGIONAL_INDICATOR_START", "REGIONAL_INDICATOR_END", "FITZPATRICK_MODIFIER_START", "FITZPATRICK_MODIFIER_END", "VARIATION_MODIFIER_START", "VARIATION_MODIFIER_END", "DIACRITICAL_MARKS_START", "DIACRITICAL_MARKS_END", "SUBDIVISION_INDICATOR_START", "TAGS_START", "TAGS_END", "ZWJ", "e", "Object", "freeze", "n", "runes", "TypeError", "t", "length", "nextUnits", "isGrapheme", "isVariationSelector", "isDiacriticalMark", "isZeroWidthJ<PERSON>ner", "push", "substring", "isFirstOfSurrogatePair", "r", "isRegionalIndicator", "isSubdivisionFlag", "isSupplementarySpecialpurposePlane", "slice", "indexOf", "String", "fromCodePoint", "isFitzpatrickModifier", "betweenInclusive", "charCodeAt", "codePointFromSurrogatePair", "codePointAt", "includes", "o", "join", "unit_1", "unit_2", "unit_4"], "sources": ["C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\node_modules\\runes2\\src\\index.ts"], "sourcesContent": ["export const enum EnumRunesCode\r\n{\r\n\tHIGH_SURROGATE_START = 0xd800,\r\n\tHIGH_SURROGATE_END = 0xdbff,\r\n\r\n\tLOW_SURROGATE_START = 0xdc00,\r\n\r\n\tREGIONAL_INDICATOR_START = 0x1f1e6,\r\n\tR<PERSON><PERSON>AL_INDICATOR_END = 0x1f1ff,\r\n\r\n\tFITZPATRICK_MODIFIER_START = 0x1f3fb,\r\n\tFITZPATRICK_MODIFIER_END = 0x1f3ff,\r\n\r\n\tVARIATION_MODIFIER_START = 0xfe00,\r\n\tVARIATION_MODIFIER_END = 0xfe0f,\r\n\r\n\tDIACRITICAL_MARKS_START = 0x20d0,\r\n\tDIACRITICAL_MARKS_END = 0x20ff,\r\n\r\n\tSUBDIVISION_INDICATOR_START = 0x1f3f4,\r\n\tTAGS_START = 0xe0000,\r\n\tTAGS_END = 0xe007f,\r\n\r\n\tZWJ = 0x200d,\r\n}\r\n\r\nexport const GRAPHEMES = Object.freeze([\r\n\t0x0308, // ( ◌̈ ) COMBINING DIAERESIS\r\n\t0x0937, // ( ष ) DEVANAGARI LETTER SSA\r\n\t0x093F, // ( ि ) DEVANAGARI VOWEL SIGN I\r\n\t0x0BA8, // ( ந ) TAMIL LETTER NA\r\n\t0x0BBF, // ( ி ) TAMIL VOWEL SIGN I\r\n\t0x0BCD, // ( ◌்) TAMIL SIGN VIRAMA\r\n\t0x0E31, // ( ◌ั ) THAI CHARACTER MAI HAN-AKAT\r\n\t0x0E33, // ( ำ ) THAI CHARACTER SARA AM\r\n\t0x0E40, // ( เ ) THAI CHARACTER SARA E\r\n\t0x0E49, // ( เ ) THAI CHARACTER MAI THO\r\n\t0x1100, // ( ᄀ ) HANGUL CHOSEONG KIYEOK\r\n\t0x1161, // ( ᅡ ) HANGUL JUNGSEONG A\r\n\t0x11A8, // ( ᆨ ) HANGUL JONGSEONG KIYEOK\r\n]);\r\n\r\nexport const enum EnumCodeUnits\r\n{\r\n\tunit_1 = 1,\r\n\tunit_2 = 2,\r\n\tunit_4 = 4,\r\n}\r\n\r\nexport function runes(string: string): string[]\r\n{\r\n\tif (typeof string !== 'string')\r\n\t{\r\n\t\tthrow new TypeError('string cannot be undefined or null')\r\n\t}\r\n\tconst result: string[] = []\r\n\tlet i = 0\r\n\tlet increment = 0\r\n\twhile (i < string.length)\r\n\t{\r\n\t\tincrement += nextUnits(i + increment, string)\r\n\t\tif (isGrapheme(string[i + increment]))\r\n\t\t{\r\n\t\t\tincrement++\r\n\t\t}\r\n\t\tif (isVariationSelector(string[i + increment]))\r\n\t\t{\r\n\t\t\tincrement++\r\n\t\t}\r\n\t\tif (isDiacriticalMark(string[i + increment]))\r\n\t\t{\r\n\t\t\tincrement++\r\n\t\t}\r\n\t\tif (isZeroWidthJoiner(string[i + increment]))\r\n\t\t{\r\n\t\t\tincrement++\r\n\t\t\tcontinue\r\n\t\t}\r\n\t\tresult.push(string.substring(i, i + increment))\r\n\t\ti += increment\r\n\t\tincrement = 0\r\n\t}\r\n\treturn result\r\n}\r\n\r\n// Decide how many code units make up the current character.\r\n// BMP characters: 1 code unit\r\n// Non-BMP characters (represented by surrogate pairs): 2 code units\r\n// Emoji with skin-tone modifiers: 4 code units (2 code points)\r\n// Country flags: 4 code units (2 code points)\r\n// Variations: 2 code units\r\n// Subdivision flags: 14 code units (7 code points)\r\nexport function nextUnits(i: number, string: string)\r\n{\r\n\tconst current = string[i]\r\n\t// If we don't have a value that is part of a surrogate pair, or we're at\r\n\t// the end, only take the value at i\r\n\tif (!isFirstOfSurrogatePair(current) || i === string.length - 1)\r\n\t{\r\n\t\treturn EnumCodeUnits.unit_1\r\n\t}\r\n\r\n\tconst currentPair = current + string[i + 1]\r\n\tlet nextPair = string.substring(i + 2, i + 5)\r\n\r\n\t// Country flags are comprised of two regional indicator symbols,\r\n\t// each represented by a surrogate pair.\r\n\t// See http://emojipedia.org/flags/\r\n\t// If both pairs are regional indicator symbols, take 4\r\n\tif (isRegionalIndicator(currentPair) && isRegionalIndicator(nextPair))\r\n\t{\r\n\t\treturn EnumCodeUnits.unit_4\r\n\t}\r\n\r\n\t// https://unicode.org/emoji/charts/full-emoji-list.html#subdivision-flag\r\n\t// See https://emojipedia.org/emoji-tag-sequence/\r\n\t// If nextPair is in Tags(https://en.wikipedia.org/wiki/Tags_(Unicode_block)),\r\n\t// then find next closest U+E007F(CANCEL TAG)\r\n\tif (isSubdivisionFlag(currentPair) &&\tisSupplementarySpecialpurposePlane(nextPair))\r\n\t{\r\n\t\treturn string.slice(i).indexOf(String.fromCodePoint(EnumRunesCode.TAGS_END)) + 2\r\n\t}\r\n\r\n\t// If the next pair make a Fitzpatrick skin tone\r\n\t// modifier, take 4\r\n\t// See http://emojipedia.org/modifiers/\r\n\t// Technically, only some code points are meant to be\r\n\t// combined with the skin tone modifiers. This function\r\n\t// does not check the current pair to see if it is\r\n\t// one of them.\r\n\tif (isFitzpatrickModifier(nextPair))\r\n\t{\r\n\t\treturn EnumCodeUnits.unit_4\r\n\t}\r\n\treturn EnumCodeUnits.unit_2\r\n}\r\n\r\nexport function isFirstOfSurrogatePair(string: string)\r\n{\r\n\treturn string && betweenInclusive(string[0].charCodeAt(0), EnumRunesCode.HIGH_SURROGATE_START, EnumRunesCode.HIGH_SURROGATE_END)\r\n}\r\n\r\nexport function isRegionalIndicator(string: string)\r\n{\r\n\treturn betweenInclusive(codePointFromSurrogatePair(string), EnumRunesCode.REGIONAL_INDICATOR_START, EnumRunesCode.REGIONAL_INDICATOR_END)\r\n}\r\n\r\nexport function isSubdivisionFlag(string: string)\r\n{\r\n\treturn betweenInclusive(codePointFromSurrogatePair(string),\tEnumRunesCode.SUBDIVISION_INDICATOR_START, EnumRunesCode.SUBDIVISION_INDICATOR_START)\r\n}\r\n\r\nexport function isFitzpatrickModifier(string: string)\r\n{\r\n\treturn betweenInclusive(codePointFromSurrogatePair(string), EnumRunesCode.FITZPATRICK_MODIFIER_START, EnumRunesCode.FITZPATRICK_MODIFIER_END)\r\n}\r\n\r\nexport function isVariationSelector(string: string)\r\n{\r\n\treturn typeof string === 'string' && betweenInclusive(string.charCodeAt(0), EnumRunesCode.VARIATION_MODIFIER_START, EnumRunesCode.VARIATION_MODIFIER_END)\r\n}\r\n\r\nexport function isDiacriticalMark(string: string)\r\n{\r\n\treturn typeof string === 'string' && betweenInclusive(string.charCodeAt(0), EnumRunesCode.DIACRITICAL_MARKS_START, EnumRunesCode.DIACRITICAL_MARKS_END)\r\n}\r\n\r\nexport function isSupplementarySpecialpurposePlane(string: string)\r\n{\r\n\tconst codePoint = string.codePointAt(0)\r\n\treturn (typeof string === 'string' &&\ttypeof codePoint === 'number' && betweenInclusive(codePoint, EnumRunesCode.TAGS_START, EnumRunesCode.TAGS_END))\r\n}\r\n\r\nexport function isGrapheme(string: string)\r\n{\r\n\treturn typeof string === 'string' && GRAPHEMES.includes(string.charCodeAt(0))\r\n}\r\n\r\nexport function isZeroWidthJoiner(string: string)\r\n{\r\n\treturn typeof string === 'string' && string.charCodeAt(0) === EnumRunesCode.ZWJ\r\n}\r\n\r\nexport function codePointFromSurrogatePair(pair: string)\r\n{\r\n\tconst highOffset = pair.charCodeAt(0) - EnumRunesCode.HIGH_SURROGATE_START\r\n\tconst lowOffset = pair.charCodeAt(1) - EnumRunesCode.LOW_SURROGATE_START\r\n\treturn (highOffset << 10) + lowOffset + 0x10000\r\n}\r\n\r\nexport function betweenInclusive(value: number, lower: number, upper: number)\r\n{\r\n\treturn value >= lower && value <= upper\r\n}\r\n\r\nexport function substring(string: string, start?: number, width?: number)\r\n{\r\n\tconst chars = runes(string)\r\n\tif (start === undefined)\r\n\t{\r\n\t\treturn string\r\n\t}\r\n\tif (start >= chars.length)\r\n\t{\r\n\t\treturn ''\r\n\t}\r\n\tconst rest = chars.length - start\r\n\tconst stringWidth = width === undefined ? rest : width\r\n\tlet endIndex = start + stringWidth\r\n\tif (endIndex > (start + rest))\r\n\t{\r\n\t\tendIndex = undefined\r\n\t}\r\n\treturn chars.slice(start, endIndex).join('')\r\n}\r\n\r\nexport { substring as substr }\r\n\r\n// @ts-ignore\r\nif (process.env.TSDX_FORMAT !== 'esm')\r\n{\r\n\tObject.defineProperty(runes, 'runes', { value: runes });\r\n\tObject.defineProperty(runes, 'default', { value: runes });\r\n\tObject.defineProperty(runes, \"__esModule\", { value: true });\r\n\r\n\tObject.defineProperty(runes, 'substr', { value: substring });\r\n\tObject.defineProperty(runes, 'substring', { value: substring });\r\n\r\n\t// @ts-ignore\r\n\tObject.defineProperty(runes, 'EnumRunesCode', { value: EnumRunesCode });\r\n\t// @ts-ignore\r\n\tObject.defineProperty(runes, 'EnumCodeUnits', { value: EnumCodeUnits });\r\n\tObject.defineProperty(runes, 'GRAPHEMES', { value: GRAPHEMES });\r\n}\r\n\r\nexport default runes\r\n"], "mappings": "IAAkBA,CAAA;CAAlB,UAAkBA,CAAA;EAEjBA,CAAA,CAAAA,CAAA,CAAAC,oBAAA,oCACAD,CAAA,CAAAA,CAAA,CAAAE,kBAAA,kCAEAF,CAAA,CAAAA,CAAA,CAAAG,mBAAA,mCAEAH,CAAA,CAAAA,CAAA,CAAAI,wBAAA,yCACAJ,CAAA,CAAAA,CAAA,CAAAK,sBAAA,uCAEAL,CAAA,CAAAA,CAAA,CAAAM,0BAAA,2CACAN,CAAA,CAAAA,CAAA,CAAAO,wBAAA,yCAEAP,CAAA,CAAAA,CAAA,CAAAQ,wBAAA,wCACAR,CAAA,CAAAA,CAAA,CAAAS,sBAAA,sCAEAT,CAAA,CAAAA,CAAA,CAAAU,uBAAA,sCACAV,CAAA,CAAAA,CAAA,CAAAW,qBAAA,oCAEAX,CAAA,CAAAA,CAAA,CAAAY,2BAAA,4CACAZ,CAAA,CAAAA,CAAA,CAAAa,UAAA,2BACAb,CAAA,CAAAA,CAAA,CAAAc,QAAA,yBAEAd,CAAA,CAAAA,CAAA,CAAAe,GAAA;AACA,CAxBD,CAAkBf,CAAA,KAAAA,CAAA,GAwBjB;AAEY,MAAAgB,CAAA,GAAYC,MAAA,CAAOC,MAAA,CAAO,CACtC,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA,QACA;IAGiBC,CAAA;AAOZ,SAAUC,MAAMpB,CAAA;EAErB,IAAsB,mBAAXA,CAAA,EAEV,MAAM,IAAIqB,SAAA,CAAU;EAErB,MAAML,CAAA,GAAmB;EACzB,IAAIG,CAAA,GAAI;IACJG,CAAA,GAAY;EAChB,OAAOH,CAAA,GAAInB,CAAA,CAAOuB,MAAA,GAEjBD,CAAA,IAAaE,SAAA,CAAUL,CAAA,GAAIG,CAAA,EAAWtB,CAAA,GAClCyB,UAAA,CAAWzB,CAAA,CAAOmB,CAAA,GAAIG,CAAA,MAEzBA,CAAA,IAEGI,mBAAA,CAAoB1B,CAAA,CAAOmB,CAAA,GAAIG,CAAA,MAElCA,CAAA,IAEGK,iBAAA,CAAkB3B,CAAA,CAAOmB,CAAA,GAAIG,CAAA,MAEhCA,CAAA,IAEGM,iBAAA,CAAkB5B,CAAA,CAAOmB,CAAA,GAAIG,CAAA,KAEhCA,CAAA,MAGDN,CAAA,CAAOa,IAAA,CAAK7B,CAAA,CAAO8B,SAAA,CAAUX,CAAA,EAAGA,CAAA,GAAIG,CAAA,IACpCH,CAAA,IAAKG,CAAA,EACLA,CAAA,GAAY;EAEb,OAAON,CAAA;AACR;AASgB,SAAAQ,UAAUxB,CAAA,EAAWgB,CAAA;EAEpC,MAAMG,CAAA,GAAUH,CAAA,CAAOhB,CAAA;EAGvB,KAAK+B,sBAAA,CAAuBZ,CAAA,KAAYnB,CAAA,KAAMgB,CAAA,CAAOO,MAAA,GAAS,GAE7D,OAA2B;EAG5B,MAAMD,CAAA,GAAcH,CAAA,GAAUH,CAAA,CAAOhB,CAAA,GAAI;EACzC,IAAIgC,CAAA,GAAWhB,CAAA,CAAOc,SAAA,CAAU9B,CAAA,GAAI,GAAGA,CAAA,GAAI;EAM3C,OAAIiC,mBAAA,CAAoBX,CAAA,KAAgBW,mBAAA,CAAoBD,CAAA,IAEhC,IAOxBE,iBAAA,CAAkBZ,CAAA,KAAgBa,kCAAA,CAAmCH,CAAA,IAEjEhB,CAAA,CAAOoB,KAAA,CAAMpC,CAAA,EAAGqC,OAAA,CAAQC,MAAA,CAAOC,aAAA,CAAa,WAA4B,IAU5EC,qBAAA,CAAsBR,CAAA,IAEE,IAED;AAC5B;AAEM,SAAUD,uBAAuB/B,CAAA;EAEtC,OAAOA,CAAA,IAAUyC,gBAAA,CAAiBzC,CAAA,CAAO,GAAG0C,UAAA,CAAW;AACxD;AAEM,SAAUT,oBAAoBjC,CAAA;EAEnC,OAAOyC,gBAAA,CAAiBE,0BAAA,CAA2B3C,CAAA;AACpD;AAEM,SAAUkC,kBAAkBlC,CAAA;EAEjC,OAAOyC,gBAAA,CAAiBE,0BAAA,CAA2B3C,CAAA;AACpD;AAEM,SAAUwC,sBAAsBxC,CAAA;EAErC,OAAOyC,gBAAA,CAAiBE,0BAAA,CAA2B3C,CAAA;AACpD;AAEM,SAAU0B,oBAAoB1B,CAAA;EAEnC,OAAyB,mBAAXA,CAAA,IAAuByC,gBAAA,CAAiBzC,CAAA,CAAO0C,UAAA,CAAW;AACzE;AAEM,SAAUf,kBAAkB3B,CAAA;EAEjC,OAAyB,mBAAXA,CAAA,IAAuByC,gBAAA,CAAiBzC,CAAA,CAAO0C,UAAA,CAAW;AACzE;AAEM,SAAUP,mCAAmCnC,CAAA;EAElD,MAAMgB,CAAA,GAAYhB,CAAA,CAAO4C,WAAA,CAAY;EACrC,OAA0B,mBAAX5C,CAAA,IAA4C,mBAAdgB,CAAA,IAA0ByB,gBAAA,CAAiBzB,CAAA,EAAS;AAClG;AAEM,SAAUS,WAAWzB,CAAA;EAE1B,OAAyB,mBAAXA,CAAA,IAAuBgB,CAAA,CAAU6B,QAAA,CAAS7C,CAAA,CAAO0C,UAAA,CAAW;AAC3E;AAEM,SAAUd,kBAAkB5B,CAAA;EAEjC,OAAyB,mBAAXA,CAAA,IAA2C,SAApBA,CAAA,CAAO0C,UAAA,CAAW;AACxD;AAEM,SAAUC,2BAA2B3C,CAAA;EAI1C,QAFmBA,CAAA,CAAK0C,UAAA,CAAW,KAAE,SAEf,OADJ1C,CAAA,CAAK0C,UAAA,CAAW,KAAE,SACI;AACzC;SAEgBD,iBAAiBzC,CAAA,EAAegB,CAAA,EAAeG,CAAA;EAE9D,OAAOnB,CAAA,IAASgB,CAAA,IAAShB,CAAA,IAASmB,CAAA;AACnC;SAEgBW,UAAU9B,CAAA,EAAgBgB,CAAA,EAAgBG,CAAA;EAEzD,MAAMG,CAAA,GAAQF,KAAA,CAAMpB,CAAA;EACpB,SAAc,MAAVgB,CAAA,EAEH,OAAOhB,CAAA;EAER,IAAIgB,CAAA,IAASM,CAAA,CAAMC,MAAA,EAElB,OAAO;EAER,MAAMS,CAAA,GAAOV,CAAA,CAAMC,MAAA,GAASP,CAAA;EAE5B,IAAI8B,CAAA,GAAW9B,CAAA,SADe,MAAVG,CAAA,GAAsBa,CAAA,GAAOb,CAAA;EAMjD,OAJI2B,CAAA,GAAY9B,CAAA,GAAQgB,CAAA,KAEvBc,CAAA,QAAW,IAELxB,CAAA,CAAMc,KAAA,CAAMpB,CAAA,EAAO8B,CAAA,EAAUC,IAAA,CAAK;AAC1C;CA5KA,UAAkB/C,CAAA;EAEjBA,CAAA,CAAAA,CAAA,CAAAgD,MAAA,kBACAhD,CAAA,CAAAA,CAAA,CAAAiD,MAAA,kBACAjD,CAAA,CAAAA,CAAA,CAAAkD,MAAA;AACA,CALD,CAAkB/B,CAAA,KAAAA,CAAA,GAKjB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}