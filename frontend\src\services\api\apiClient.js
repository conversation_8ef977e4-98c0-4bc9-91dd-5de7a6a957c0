/**
 * 統一的API客戶端
 * 提供統一的請求配置、錯誤處理和日誌記錄
 */

import axios from 'axios';

// 創建API客戶端實例
const apiClient = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 請求攔截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加請求日誌
    console.log(`🚀 API Request: ${config.method?.toUpperCase()} ${config.url}`);
    
    // 如果是FormData，移除Content-Type讓瀏覽器自動設置
    if (config.data instanceof FormData) {
      delete config.headers['Content-Type'];
    }
    
    return config;
  },
  (error) => {
    console.error('❌ API Request Error:', error);
    return Promise.reject(error);
  }
);

// 響應攔截器
apiClient.interceptors.response.use(
  (response) => {
    console.log(`✅ API Response: ${response.config.method?.toUpperCase()} ${response.config.url}`, response.data);
    return response;
  },
  (error) => {
    console.error('❌ API Response Error:', error);
    
    // 統一錯誤處理
    const errorMessage = error.response?.data?.detail || 
                        error.response?.data?.message || 
                        error.message || 
                        '網絡請求失敗';
    
    // 創建統一的錯誤對象
    const apiError = {
      message: errorMessage,
      status: error.response?.status,
      statusText: error.response?.statusText,
      data: error.response?.data,
      originalError: error
    };
    
    return Promise.reject(apiError);
  }
);

export default apiClient;
