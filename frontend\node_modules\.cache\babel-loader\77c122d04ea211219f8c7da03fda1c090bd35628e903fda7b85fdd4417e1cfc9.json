{"ast": null, "code": "var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\nmodule.exports = Symbol;", "map": {"version": 3, "names": ["root", "require", "Symbol", "module", "exports"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/lodash/_Symbol.js"], "sourcesContent": ["var root = require('./_root');\n\n/** Built-in value references. */\nvar Symbol = root.Symbol;\n\nmodule.exports = Symbol;\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO,CAAC,SAAS,CAAC;;AAE7B;AACA,IAAIC,MAAM,GAAGF,IAAI,CAACE,MAAM;AAExBC,MAAM,CAACC,OAAO,GAAGF,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}