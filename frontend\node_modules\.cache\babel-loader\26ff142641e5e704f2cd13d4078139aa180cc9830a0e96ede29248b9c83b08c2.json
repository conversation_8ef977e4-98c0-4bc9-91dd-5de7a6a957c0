{"ast": null, "code": "import { useEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useEffect);", "map": {"version": 3, "names": ["useEffect", "createUpdateEffect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useUpdateEffect/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { createUpdateEffect } from '../createUpdateEffect';\nexport default createUpdateEffect(useEffect);"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,eAAeA,kBAAkB,CAACD,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}