{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useSet(initialValue) {\n  var getInitValue = function () {\n    return new Set(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    set = _a[0],\n    setSet = _a[1];\n  var add = function (key) {\n    if (set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.add(key);\n      return temp;\n    });\n  };\n  var remove = function (key) {\n    if (!set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setSet(getInitValue());\n  };\n  return [set, {\n    add: useMemoizedFn(add),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useSet;", "map": {"version": 3, "names": ["__read", "useState", "useMemoizedFn", "useSet", "initialValue", "getInitValue", "Set", "_a", "set", "setSet", "add", "key", "has", "prevSet", "temp", "remove", "delete", "reset"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useSet/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useSet(initialValue) {\n  var getInitValue = function () {\n    return new Set(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    set = _a[0],\n    setSet = _a[1];\n  var add = function (key) {\n    if (set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.add(key);\n      return temp;\n    });\n  };\n  var remove = function (key) {\n    if (!set.has(key)) {\n      return;\n    }\n    setSet(function (prevSet) {\n      var temp = new Set(prevSet);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setSet(getInitValue());\n  };\n  return [set, {\n    add: useMemoizedFn(add),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset)\n  }];\n}\nexport default useSet;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,MAAMA,CAACC,YAAY,EAAE;EAC5B,IAAIC,YAAY,GAAG,SAAAA,CAAA,EAAY;IAC7B,OAAO,IAAIC,GAAG,CAACF,YAAY,CAAC;EAC9B,CAAC;EACD,IAAIG,EAAE,GAAGP,MAAM,CAACC,QAAQ,CAACI,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCG,GAAG,GAAGD,EAAE,CAAC,CAAC,CAAC;IACXE,MAAM,GAAGF,EAAE,CAAC,CAAC,CAAC;EAChB,IAAIG,GAAG,GAAG,SAAAA,CAAUC,GAAG,EAAE;IACvB,IAAIH,GAAG,CAACI,GAAG,CAACD,GAAG,CAAC,EAAE;MAChB;IACF;IACAF,MAAM,CAAC,UAAUI,OAAO,EAAE;MACxB,IAAIC,IAAI,GAAG,IAAIR,GAAG,CAACO,OAAO,CAAC;MAC3BC,IAAI,CAACJ,GAAG,CAACC,GAAG,CAAC;MACb,OAAOG,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,MAAM,GAAG,SAAAA,CAAUJ,GAAG,EAAE;IAC1B,IAAI,CAACH,GAAG,CAACI,GAAG,CAACD,GAAG,CAAC,EAAE;MACjB;IACF;IACAF,MAAM,CAAC,UAAUI,OAAO,EAAE;MACxB,IAAIC,IAAI,GAAG,IAAIR,GAAG,CAACO,OAAO,CAAC;MAC3BC,IAAI,CAACE,MAAM,CAACL,GAAG,CAAC;MAChB,OAAOG,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EACD,IAAIG,KAAK,GAAG,SAAAA,CAAA,EAAY;IACtB,OAAOR,MAAM,CAACJ,YAAY,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,OAAO,CAACG,GAAG,EAAE;IACXE,GAAG,EAAER,aAAa,CAACQ,GAAG,CAAC;IACvBK,MAAM,EAAEb,aAAa,CAACa,MAAM,CAAC;IAC7BE,KAAK,EAAEf,aAAa,CAACe,KAAK;EAC5B,CAAC,CAAC;AACJ;AACA,eAAed,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}