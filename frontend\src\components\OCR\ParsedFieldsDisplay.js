/**
 * 解析結果展示組件
 */

import React, { useState } from 'react';
import { Card, Button, Space, Collapse, Tag } from 'antd-mobile';
import { 
  EditSOutline, 
  EyeOutline, 
  CheckCircleOutline,
  DownOutline 
} from 'antd-mobile-icons';
import { getFieldDisplayName } from '../../utils/validation';
import { formatFormData } from '../../utils/formatters';

const ParsedFieldsDisplay = ({
  frontFields = {},
  backFields = {},
  mergedFields = {},
  onApplyToForm,
  onEditField,
  showMerged = true,
  style = {},
  className = ''
}) => {
  const [activeKey, setActiveKey] = useState(showMerged ? ['merged'] : []);

  // 渲染欄位列表
  const renderFields = (fields, title, color = '#1677ff') => {
    const fieldEntries = Object.entries(fields).filter(([key, value]) => value && value.trim());
    
    if (fieldEntries.length === 0) {
      return (
        <div style={{ textAlign: 'center', padding: '20px', color: '#8c8c8c' }}>
          暫無解析結果
        </div>
      );
    }

    return (
      <div>
        <div style={{ marginBottom: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
          <CheckCircleOutline style={{ color }} />
          <span style={{ fontWeight: 'bold', color }}>{title}</span>
          <Tag color={color} fill="outline" style={{ marginLeft: 'auto' }}>
            {fieldEntries.length} 個欄位
          </Tag>
        </div>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          {fieldEntries.map(([key, value]) => (
            <div 
              key={key}
              style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                padding: '8px 12px',
                backgroundColor: '#f8f9fa',
                borderRadius: '6px',
                border: '1px solid #e9ecef'
              }}
            >
              <div style={{ flex: 1 }}>
                <div style={{ fontSize: '12px', color: '#6c757d', marginBottom: '2px' }}>
                  {getFieldDisplayName(key)}
                </div>
                <div style={{ fontSize: '14px', color: '#212529', wordBreak: 'break-all' }}>
                  {value}
                </div>
              </div>
              
              {onEditField && (
                <Button
                  size="mini"
                  fill="none"
                  onClick={() => onEditField(key, value)}
                  style={{ marginLeft: '8px' }}
                >
                  <EditSOutline />
                </Button>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 處理應用到表單
  const handleApplyToForm = (fields) => {
    if (onApplyToForm) {
      const formattedFields = formatFormData(fields);
      onApplyToForm(formattedFields);
    }
  };

  const hasFrontFields = Object.keys(frontFields).some(key => frontFields[key] && frontFields[key].trim());
  const hasBackFields = Object.keys(backFields).some(key => backFields[key] && backFields[key].trim());
  const hasMergedFields = Object.keys(mergedFields).some(key => mergedFields[key] && mergedFields[key].trim());

  if (!hasFrontFields && !hasBackFields && !hasMergedFields) {
    return null;
  }

  return (
    <Card 
      title="OCR解析結果"
      className={`parsed-fields-display ${className}`}
      style={style}
    >
      <Collapse 
        activeKey={activeKey}
        onChange={setActiveKey}
        accordion={false}
      >
        {/* 合併結果 */}
        {showMerged && hasMergedFields && (
          <Collapse.Panel 
            key="merged" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <CheckCircleOutline style={{ color: '#52c41a' }} />
                智能合併結果
                <Tag color="#52c41a" fill="outline">
                  推薦使用
                </Tag>
              </div>
            }
          >
            {renderFields(mergedFields, '合併後的欄位', '#52c41a')}
            
            {onApplyToForm && (
              <div style={{ marginTop: '16px', textAlign: 'center' }}>
                <Button
                  color="primary"
                  onClick={() => handleApplyToForm(mergedFields)}
                >
                  <CheckCircleOutline /> 應用到表單
                </Button>
              </div>
            )}
          </Collapse.Panel>
        )}

        {/* 正面結果 */}
        {hasFrontFields && (
          <Collapse.Panel 
            key="front" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <EyeOutline style={{ color: '#1677ff' }} />
                正面解析結果
              </div>
            }
          >
            {renderFields(frontFields, '正面欄位', '#1677ff')}
            
            {onApplyToForm && !showMerged && (
              <div style={{ marginTop: '16px', textAlign: 'center' }}>
                <Button
                  color="primary"
                  fill="outline"
                  onClick={() => handleApplyToForm(frontFields)}
                >
                  <CheckCircleOutline /> 應用到表單
                </Button>
              </div>
            )}
          </Collapse.Panel>
        )}

        {/* 反面結果 */}
        {hasBackFields && (
          <Collapse.Panel 
            key="back" 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <EyeOutline style={{ color: '#722ed1' }} />
                反面解析結果
              </div>
            }
          >
            {renderFields(backFields, '反面欄位', '#722ed1')}
            
            {onApplyToForm && !showMerged && (
              <div style={{ marginTop: '16px', textAlign: 'center' }}>
                <Button
                  color="primary"
                  fill="outline"
                  onClick={() => handleApplyToForm(backFields)}
                >
                  <CheckCircleOutline /> 應用到表單
                </Button>
              </div>
            )}
          </Collapse.Panel>
        )}
      </Collapse>

      {/* 統計信息 */}
      <div 
        style={{ 
          marginTop: '16px', 
          padding: '12px',
          backgroundColor: '#f0f2f5',
          borderRadius: '6px',
          fontSize: '12px',
          color: '#8c8c8c'
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          <span>正面: {Object.keys(frontFields).filter(key => frontFields[key]).length} 個欄位</span>
          <span>反面: {Object.keys(backFields).filter(key => backFields[key]).length} 個欄位</span>
          {showMerged && (
            <span>合併: {Object.keys(mergedFields).filter(key => mergedFields[key]).length} 個欄位</span>
          )}
        </div>
      </div>
    </Card>
  );
};

export default ParsedFieldsDisplay;
