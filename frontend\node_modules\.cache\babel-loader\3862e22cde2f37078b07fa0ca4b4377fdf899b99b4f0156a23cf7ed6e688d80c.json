{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\Camera\\\\CameraCapture.js\",\n  _s = $RefreshSig$();\n/**\n * 統一的相機拍攝組件\n * 集成現有的相機管理器和移動端相機組件\n */\n\nimport React, { useRef } from 'react';\nimport { Card, Button, Space, Modal } from 'antd-mobile';\nimport { CameraOutline, ScanningOutline } from 'antd-mobile-icons';\nimport CameraControls from './CameraControls';\nimport ImagePreview from './ImagePreview';\nimport MobileCameraModal from '../MobileCameraModal';\nimport { useCameraState } from '../../hooks';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CameraCapture = ({\n  onImageCaptured,\n  onOCRCompleted,\n  title = '拍攝名片',\n  style = {},\n  className = ''\n}) => {\n  _s();\n  const {\n    images,\n    currentTarget,\n    cameraModalVisible,\n    deviceType,\n    cameraManager,\n    startCamera,\n    stopCamera,\n    capturePhoto,\n    selectFromGallery,\n    removeImage,\n    switchTarget,\n    isMobile\n  } = useCameraState();\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // 處理拍照\n  const handleTakePhoto = async target => {\n    try {\n      await startCamera(target);\n    } catch (error) {\n      console.error('啟動相機失敗:', error);\n    }\n  };\n\n  // 處理相冊選擇\n  const handleSelectFromGallery = target => {\n    selectFromGallery(target);\n  };\n\n  // 處理移動端拍照完成\n  const handleMobilePhotoTaken = async photoData => {\n    if (photoData && photoData.file) {\n      stopCamera();\n\n      // 通知父組件圖片已捕獲\n      if (onImageCaptured) {\n        onImageCaptured(photoData.file, currentTarget);\n      }\n    }\n  };\n\n  // 處理圖片移除\n  const handleRemoveImage = target => {\n    removeImage(target);\n  };\n\n  // 處理重新拍攝\n  const handleRetakeImage = target => {\n    removeImage(target);\n    handleTakePhoto(target);\n  };\n\n  // 處理目標切換\n  const handleTargetSwitch = target => {\n    switchTarget(target);\n  };\n  return /*#__PURE__*/_jsxDEV(Card, {\n    title: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: 'flex',\n        alignItems: 'center',\n        gap: '8px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ScanningOutline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 85,\n        columnNumber: 11\n      }, this), title]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 84,\n      columnNumber: 9\n    }, this),\n    className: `camera-capture ${className}`,\n    style: style,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '16px'\n      },\n      children: /*#__PURE__*/_jsxDEV(Space, {\n        style: {\n          width: '100%',\n          justifyContent: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: currentTarget === 'front' ? 'primary' : 'default',\n          fill: currentTarget === 'front' ? 'solid' : 'outline',\n          onClick: () => handleTargetSwitch('front'),\n          style: {\n            flex: 1\n          },\n          children: \"\\u6B63\\u9762\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: currentTarget === 'back' ? 'primary' : 'default',\n          fill: currentTarget === 'back' ? 'solid' : 'outline',\n          onClick: () => handleTargetSwitch('back'),\n          style: {\n            flex: 1\n          },\n          children: \"\\u53CD\\u9762\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 103,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 93,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        marginBottom: '16px'\n      },\n      children: [images.front.file && /*#__PURE__*/_jsxDEV(ImagePreview, {\n        image: images.front,\n        side: \"front\",\n        onRemove: handleRemoveImage,\n        onRetake: handleRetakeImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 118,\n        columnNumber: 11\n      }, this), images.back.file && /*#__PURE__*/_jsxDEV(ImagePreview, {\n        image: images.back,\n        side: \"back\",\n        onRemove: handleRemoveImage,\n        onRetake: handleRetakeImage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 128,\n        columnNumber: 11\n      }, this), !images[currentTarget].file && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          padding: '40px 20px',\n          border: '2px dashed #d9d9d9',\n          borderRadius: '8px',\n          backgroundColor: '#fafafa'\n        },\n        children: [/*#__PURE__*/_jsxDEV(CameraOutline, {\n          style: {\n            fontSize: '64px',\n            marginBottom: '12px',\n            color: '#ccc'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 147,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            marginBottom: '8px'\n          },\n          children: [\"\\u8ACB\\u62CD\\u651D\\u540D\\u7247\", currentTarget === 'front' ? '正面' : '反面']\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 148,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#8c8c8c'\n          },\n          children: \"\\u9EDE\\u64CA\\u4E0B\\u65B9\\u6309\\u9215\\u958B\\u59CB\\u62CD\\u7167\\u6216\\u9078\\u64C7\\u5716\\u7247\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 151,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 138,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(CameraControls, {\n      onTakePhoto: handleTakePhoto,\n      onSelectFromGallery: handleSelectFromGallery,\n      currentTarget: currentTarget\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), isMobile ?\n    /*#__PURE__*/\n    // 移動端：使用全屏相機組件\n    _jsxDEV(MobileCameraModal, {\n      visible: cameraModalVisible,\n      onClose: stopCamera,\n      onPhotoTaken: handleMobilePhotoTaken,\n      cameraManager: cameraManager,\n      target: currentTarget\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 9\n    }, this) :\n    /*#__PURE__*/\n    // Web端：使用傳統Modal\n    _jsxDEV(Modal, {\n      visible: cameraModalVisible,\n      onClose: stopCamera,\n      content: /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          padding: '20px',\n          textAlign: 'center'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"video\", {\n          ref: videoRef,\n          autoPlay: true,\n          playsInline: true,\n          style: {\n            width: '100%',\n            maxHeight: '400px',\n            borderRadius: '8px',\n            backgroundColor: '#000'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 182,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"canvas\", {\n          ref: canvasRef,\n          style: {\n            display: 'none'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 193,\n          columnNumber: 15\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Button, {\n            color: \"primary\",\n            size: \"large\",\n            onClick: () => capturePhoto(currentTarget),\n            children: [/*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 19\n            }, this), \" \\u62CD\\u7167\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 195,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 194,\n          columnNumber: 15\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 181,\n        columnNumber: 13\n      }, this),\n      closeOnAction: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 177,\n      columnNumber: 9\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 5\n  }, this);\n};\n_s(CameraCapture, \"gsxUHnwjQb1MYDVvgtWZXDpIWPE=\", false, function () {\n  return [useCameraState];\n});\n_c = CameraCapture;\nexport default CameraCapture;\nvar _c;\n$RefreshReg$(_c, \"CameraCapture\");", "map": {"version": 3, "names": ["React", "useRef", "Card", "<PERSON><PERSON>", "Space", "Modal", "CameraOutline", "ScanningOutline", "CameraControls", "ImagePreview", "MobileCameraModal", "useCameraState", "jsxDEV", "_jsxDEV", "CameraCapture", "onImageCaptured", "onOCRCompleted", "title", "style", "className", "_s", "images", "currentTarget", "cameraModalVisible", "deviceType", "cameraManager", "startCamera", "stopCamera", "capturePhoto", "selectFromGallery", "removeImage", "switchTarget", "isMobile", "videoRef", "canvasRef", "handleTakePhoto", "target", "error", "console", "handleSelectFromGallery", "handleMobilePhotoTaken", "photoData", "file", "handleRemoveImage", "handleRetakeImage", "handleTargetSwitch", "display", "alignItems", "gap", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "marginBottom", "width", "justifyContent", "color", "fill", "onClick", "flex", "front", "image", "side", "onRemove", "onRetake", "back", "textAlign", "padding", "border", "borderRadius", "backgroundColor", "fontSize", "onTakePhoto", "onSelectFromGallery", "visible", "onClose", "onPhotoTaken", "content", "ref", "autoPlay", "playsInline", "maxHeight", "marginTop", "size", "closeOnAction", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Camera/CameraCapture.js"], "sourcesContent": ["/**\n * 統一的相機拍攝組件\n * 集成現有的相機管理器和移動端相機組件\n */\n\nimport React, { useRef } from 'react';\nimport { Card, Button, Space, Modal } from 'antd-mobile';\nimport { CameraOutline, ScanningOutline } from 'antd-mobile-icons';\nimport CameraControls from './CameraControls';\nimport ImagePreview from './ImagePreview';\nimport MobileCameraModal from '../MobileCameraModal';\nimport { useCameraState } from '../../hooks';\n\nconst CameraCapture = ({\n  onImageCaptured,\n  onOCRCompleted,\n  title = '拍攝名片',\n  style = {},\n  className = ''\n}) => {\n  const {\n    images,\n    currentTarget,\n    cameraModalVisible,\n    deviceType,\n    cameraManager,\n    startCamera,\n    stopCamera,\n    capturePhoto,\n    selectFromGallery,\n    removeImage,\n    switchTarget,\n    isMobile\n  } = useCameraState();\n\n  const videoRef = useRef(null);\n  const canvasRef = useRef(null);\n\n  // 處理拍照\n  const handleTakePhoto = async (target) => {\n    try {\n      await startCamera(target);\n    } catch (error) {\n      console.error('啟動相機失敗:', error);\n    }\n  };\n\n  // 處理相冊選擇\n  const handleSelectFromGallery = (target) => {\n    selectFromGallery(target);\n  };\n\n  // 處理移動端拍照完成\n  const handleMobilePhotoTaken = async (photoData) => {\n    if (photoData && photoData.file) {\n      stopCamera();\n      \n      // 通知父組件圖片已捕獲\n      if (onImageCaptured) {\n        onImageCaptured(photoData.file, currentTarget);\n      }\n    }\n  };\n\n  // 處理圖片移除\n  const handleRemoveImage = (target) => {\n    removeImage(target);\n  };\n\n  // 處理重新拍攝\n  const handleRetakeImage = (target) => {\n    removeImage(target);\n    handleTakePhoto(target);\n  };\n\n  // 處理目標切換\n  const handleTargetSwitch = (target) => {\n    switchTarget(target);\n  };\n\n  return (\n    <Card \n      title={\n        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>\n          <ScanningOutline />\n          {title}\n        </div>\n      }\n      className={`camera-capture ${className}`}\n      style={style}\n    >\n      {/* 拍攝模式切換 */}\n      <div style={{ marginBottom: '16px' }}>\n        <Space style={{ width: '100%', justifyContent: 'center' }}>\n          <Button\n            color={currentTarget === 'front' ? 'primary' : 'default'}\n            fill={currentTarget === 'front' ? 'solid' : 'outline'}\n            onClick={() => handleTargetSwitch('front')}\n            style={{ flex: 1 }}\n          >\n            正面\n          </Button>\n          <Button\n            color={currentTarget === 'back' ? 'primary' : 'default'}\n            fill={currentTarget === 'back' ? 'solid' : 'outline'}\n            onClick={() => handleTargetSwitch('back')}\n            style={{ flex: 1 }}\n          >\n            反面\n          </Button>\n        </Space>\n      </div>\n\n      {/* 圖片預覽區域 */}\n      <div style={{ marginBottom: '16px' }}>\n        {/* 正面圖片預覽 */}\n        {images.front.file && (\n          <ImagePreview\n            image={images.front}\n            side=\"front\"\n            onRemove={handleRemoveImage}\n            onRetake={handleRetakeImage}\n          />\n        )}\n\n        {/* 反面圖片預覽 */}\n        {images.back.file && (\n          <ImagePreview\n            image={images.back}\n            side=\"back\"\n            onRemove={handleRemoveImage}\n            onRetake={handleRetakeImage}\n          />\n        )}\n\n        {/* 當前目標的拍攝提示 */}\n        {!images[currentTarget].file && (\n          <div\n            style={{\n              textAlign: 'center',\n              padding: '40px 20px',\n              border: '2px dashed #d9d9d9',\n              borderRadius: '8px',\n              backgroundColor: '#fafafa'\n            }}\n          >\n            <CameraOutline style={{ fontSize: '64px', marginBottom: '12px', color: '#ccc' }} />\n            <div style={{ fontSize: '16px', marginBottom: '8px' }}>\n              請拍攝名片{currentTarget === 'front' ? '正面' : '反面'}\n            </div>\n            <div style={{ fontSize: '14px', color: '#8c8c8c' }}>\n              點擊下方按鈕開始拍照或選擇圖片\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* 拍攝控制按鈕 */}\n      <CameraControls\n        onTakePhoto={handleTakePhoto}\n        onSelectFromGallery={handleSelectFromGallery}\n        currentTarget={currentTarget}\n      />\n\n      {/* 相機模態框 */}\n      {isMobile ? (\n        // 移動端：使用全屏相機組件\n        <MobileCameraModal\n          visible={cameraModalVisible}\n          onClose={stopCamera}\n          onPhotoTaken={handleMobilePhotoTaken}\n          cameraManager={cameraManager}\n          target={currentTarget}\n        />\n      ) : (\n        // Web端：使用傳統Modal\n        <Modal\n          visible={cameraModalVisible}\n          onClose={stopCamera}\n          content={\n            <div style={{ padding: '20px', textAlign: 'center' }}>\n              <video\n                ref={videoRef}\n                autoPlay\n                playsInline\n                style={{\n                  width: '100%',\n                  maxHeight: '400px',\n                  borderRadius: '8px',\n                  backgroundColor: '#000'\n                }}\n              />\n              <canvas ref={canvasRef} style={{ display: 'none' }} />\n              <div style={{ marginTop: '16px' }}>\n                <Button\n                  color=\"primary\"\n                  size=\"large\"\n                  onClick={() => capturePhoto(currentTarget)}\n                >\n                  <CameraOutline /> 拍照\n                </Button>\n              </div>\n            </div>\n          }\n          closeOnAction\n        />\n      )}\n    </Card>\n  );\n};\n\nexport default CameraCapture;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,MAAM,QAAQ,OAAO;AACrC,SAASC,IAAI,EAAEC,MAAM,EAAEC,KAAK,EAAEC,KAAK,QAAQ,aAAa;AACxD,SAASC,aAAa,EAAEC,eAAe,QAAQ,mBAAmB;AAClE,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,iBAAiB,MAAM,sBAAsB;AACpD,SAASC,cAAc,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE7C,MAAMC,aAAa,GAAGA,CAAC;EACrBC,eAAe;EACfC,cAAc;EACdC,KAAK,GAAG,MAAM;EACdC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EAAAC,EAAA;EACJ,MAAM;IACJC,MAAM;IACNC,aAAa;IACbC,kBAAkB;IAClBC,UAAU;IACVC,aAAa;IACbC,WAAW;IACXC,UAAU;IACVC,YAAY;IACZC,iBAAiB;IACjBC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGrB,cAAc,CAAC,CAAC;EAEpB,MAAMsB,QAAQ,GAAGhC,MAAM,CAAC,IAAI,CAAC;EAC7B,MAAMiC,SAAS,GAAGjC,MAAM,CAAC,IAAI,CAAC;;EAE9B;EACA,MAAMkC,eAAe,GAAG,MAAOC,MAAM,IAAK;IACxC,IAAI;MACF,MAAMV,WAAW,CAACU,MAAM,CAAC;IAC3B,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;IACjC;EACF,CAAC;;EAED;EACA,MAAME,uBAAuB,GAAIH,MAAM,IAAK;IAC1CP,iBAAiB,CAACO,MAAM,CAAC;EAC3B,CAAC;;EAED;EACA,MAAMI,sBAAsB,GAAG,MAAOC,SAAS,IAAK;IAClD,IAAIA,SAAS,IAAIA,SAAS,CAACC,IAAI,EAAE;MAC/Bf,UAAU,CAAC,CAAC;;MAEZ;MACA,IAAIZ,eAAe,EAAE;QACnBA,eAAe,CAAC0B,SAAS,CAACC,IAAI,EAAEpB,aAAa,CAAC;MAChD;IACF;EACF,CAAC;;EAED;EACA,MAAMqB,iBAAiB,GAAIP,MAAM,IAAK;IACpCN,WAAW,CAACM,MAAM,CAAC;EACrB,CAAC;;EAED;EACA,MAAMQ,iBAAiB,GAAIR,MAAM,IAAK;IACpCN,WAAW,CAACM,MAAM,CAAC;IACnBD,eAAe,CAACC,MAAM,CAAC;EACzB,CAAC;;EAED;EACA,MAAMS,kBAAkB,GAAIT,MAAM,IAAK;IACrCL,YAAY,CAACK,MAAM,CAAC;EACtB,CAAC;EAED,oBACEvB,OAAA,CAACX,IAAI;IACHe,KAAK,eACHJ,OAAA;MAAKK,KAAK,EAAE;QAAE4B,OAAO,EAAE,MAAM;QAAEC,UAAU,EAAE,QAAQ;QAAEC,GAAG,EAAE;MAAM,CAAE;MAAAC,QAAA,gBAChEpC,OAAA,CAACN,eAAe;QAAA2C,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAClBpC,KAAK;IAAA;MAAAiC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CACN;IACDlC,SAAS,EAAE,kBAAkBA,SAAS,EAAG;IACzCD,KAAK,EAAEA,KAAM;IAAA+B,QAAA,gBAGbpC,OAAA;MAAKK,KAAK,EAAE;QAAEoC,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,eACnCpC,OAAA,CAACT,KAAK;QAACc,KAAK,EAAE;UAAEqC,KAAK,EAAE,MAAM;UAAEC,cAAc,EAAE;QAAS,CAAE;QAAAP,QAAA,gBACxDpC,OAAA,CAACV,MAAM;UACLsD,KAAK,EAAEnC,aAAa,KAAK,OAAO,GAAG,SAAS,GAAG,SAAU;UACzDoC,IAAI,EAAEpC,aAAa,KAAK,OAAO,GAAG,OAAO,GAAG,SAAU;UACtDqC,OAAO,EAAEA,CAAA,KAAMd,kBAAkB,CAAC,OAAO,CAAE;UAC3C3B,KAAK,EAAE;YAAE0C,IAAI,EAAE;UAAE,CAAE;UAAAX,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eACTxC,OAAA,CAACV,MAAM;UACLsD,KAAK,EAAEnC,aAAa,KAAK,MAAM,GAAG,SAAS,GAAG,SAAU;UACxDoC,IAAI,EAAEpC,aAAa,KAAK,MAAM,GAAG,OAAO,GAAG,SAAU;UACrDqC,OAAO,EAAEA,CAAA,KAAMd,kBAAkB,CAAC,MAAM,CAAE;UAC1C3B,KAAK,EAAE;YAAE0C,IAAI,EAAE;UAAE,CAAE;UAAAX,QAAA,EACpB;QAED;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC,eAGNxC,OAAA;MAAKK,KAAK,EAAE;QAAEoC,YAAY,EAAE;MAAO,CAAE;MAAAL,QAAA,GAElC5B,MAAM,CAACwC,KAAK,CAACnB,IAAI,iBAChB7B,OAAA,CAACJ,YAAY;QACXqD,KAAK,EAAEzC,MAAM,CAACwC,KAAM;QACpBE,IAAI,EAAC,OAAO;QACZC,QAAQ,EAAErB,iBAAkB;QAC5BsB,QAAQ,EAAErB;MAAkB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,EAGAhC,MAAM,CAAC6C,IAAI,CAACxB,IAAI,iBACf7B,OAAA,CAACJ,YAAY;QACXqD,KAAK,EAAEzC,MAAM,CAAC6C,IAAK;QACnBH,IAAI,EAAC,MAAM;QACXC,QAAQ,EAAErB,iBAAkB;QAC5BsB,QAAQ,EAAErB;MAAkB;QAAAM,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC7B,CACF,EAGA,CAAChC,MAAM,CAACC,aAAa,CAAC,CAACoB,IAAI,iBAC1B7B,OAAA;QACEK,KAAK,EAAE;UACLiD,SAAS,EAAE,QAAQ;UACnBC,OAAO,EAAE,WAAW;UACpBC,MAAM,EAAE,oBAAoB;UAC5BC,YAAY,EAAE,KAAK;UACnBC,eAAe,EAAE;QACnB,CAAE;QAAAtB,QAAA,gBAEFpC,OAAA,CAACP,aAAa;UAACY,KAAK,EAAE;YAAEsD,QAAQ,EAAE,MAAM;YAAElB,YAAY,EAAE,MAAM;YAAEG,KAAK,EAAE;UAAO;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACnFxC,OAAA;UAAKK,KAAK,EAAE;YAAEsD,QAAQ,EAAE,MAAM;YAAElB,YAAY,EAAE;UAAM,CAAE;UAAAL,QAAA,GAAC,gCAChD,EAAC3B,aAAa,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;QAAA;UAAA4B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1C,CAAC,eACNxC,OAAA;UAAKK,KAAK,EAAE;YAAEsD,QAAQ,EAAE,MAAM;YAAEf,KAAK,EAAE;UAAU,CAAE;UAAAR,QAAA,EAAC;QAEpD;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC,eAGNxC,OAAA,CAACL,cAAc;MACbiE,WAAW,EAAEtC,eAAgB;MAC7BuC,mBAAmB,EAAEnC,uBAAwB;MAC7CjB,aAAa,EAAEA;IAAc;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9B,CAAC,EAGDrB,QAAQ;IAAA;IACP;IACAnB,OAAA,CAACH,iBAAiB;MAChBiE,OAAO,EAAEpD,kBAAmB;MAC5BqD,OAAO,EAAEjD,UAAW;MACpBkD,YAAY,EAAErC,sBAAuB;MACrCf,aAAa,EAAEA,aAAc;MAC7BW,MAAM,EAAEd;IAAc;MAAA4B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC;IAAA;IAEF;IACAxC,OAAA,CAACR,KAAK;MACJsE,OAAO,EAAEpD,kBAAmB;MAC5BqD,OAAO,EAAEjD,UAAW;MACpBmD,OAAO,eACLjE,OAAA;QAAKK,KAAK,EAAE;UAAEkD,OAAO,EAAE,MAAM;UAAED,SAAS,EAAE;QAAS,CAAE;QAAAlB,QAAA,gBACnDpC,OAAA;UACEkE,GAAG,EAAE9C,QAAS;UACd+C,QAAQ;UACRC,WAAW;UACX/D,KAAK,EAAE;YACLqC,KAAK,EAAE,MAAM;YACb2B,SAAS,EAAE,OAAO;YAClBZ,YAAY,EAAE,KAAK;YACnBC,eAAe,EAAE;UACnB;QAAE;UAAArB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFxC,OAAA;UAAQkE,GAAG,EAAE7C,SAAU;UAAChB,KAAK,EAAE;YAAE4B,OAAO,EAAE;UAAO;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACtDxC,OAAA;UAAKK,KAAK,EAAE;YAAEiE,SAAS,EAAE;UAAO,CAAE;UAAAlC,QAAA,eAChCpC,OAAA,CAACV,MAAM;YACLsD,KAAK,EAAC,SAAS;YACf2B,IAAI,EAAC,OAAO;YACZzB,OAAO,EAAEA,CAAA,KAAM/B,YAAY,CAACN,aAAa,CAAE;YAAA2B,QAAA,gBAE3CpC,OAAA,CAACP,aAAa;cAAA4C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,iBACnB;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;MACDgC,aAAa;IAAA;MAAAnC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACd,CACF;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACG,CAAC;AAEX,CAAC;AAACjC,EAAA,CApMIN,aAAa;EAAA,QAoBbH,cAAc;AAAA;AAAA2E,EAAA,GApBdxE,aAAa;AAsMnB,eAAeA,aAAa;AAAC,IAAAwE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}