{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useMap(initialValue) {\n  var getInitValue = function () {\n    return new Map(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    map = _a[0],\n    setMap = _a[1];\n  var set = function (key, entry) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.set(key, entry);\n      return temp;\n    });\n  };\n  var setAll = function (newMap) {\n    setMap(new Map(newMap));\n  };\n  var remove = function (key) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setMap(getInitValue());\n  };\n  var get = function (key) {\n    return map.get(key);\n  };\n  return [map, {\n    set: useMemoizedFn(set),\n    setAll: useMemoizedFn(setAll),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset),\n    get: useMemoizedFn(get)\n  }];\n}\nexport default useMap;", "map": {"version": 3, "names": ["__read", "useState", "useMemoizedFn", "useMap", "initialValue", "getInitValue", "Map", "_a", "map", "setMap", "set", "key", "entry", "prev", "temp", "setAll", "newMap", "remove", "delete", "reset", "get"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useMap/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useMemoizedFn from '../useMemoizedFn';\nfunction useMap(initialValue) {\n  var getInitValue = function () {\n    return new Map(initialValue);\n  };\n  var _a = __read(useState(getInitValue), 2),\n    map = _a[0],\n    setMap = _a[1];\n  var set = function (key, entry) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.set(key, entry);\n      return temp;\n    });\n  };\n  var setAll = function (newMap) {\n    setMap(new Map(newMap));\n  };\n  var remove = function (key) {\n    setMap(function (prev) {\n      var temp = new Map(prev);\n      temp.delete(key);\n      return temp;\n    });\n  };\n  var reset = function () {\n    return setMap(getInitValue());\n  };\n  var get = function (key) {\n    return map.get(key);\n  };\n  return [map, {\n    set: useMemoizedFn(set),\n    setAll: useMemoizedFn(setAll),\n    remove: useMemoizedFn(remove),\n    reset: useMemoizedFn(reset),\n    get: useMemoizedFn(get)\n  }];\n}\nexport default useMap;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,MAAMA,CAACC,YAAY,EAAE;EAC5B,IAAIC,YAAY,GAAG,SAAAA,CAAA,EAAY;IAC7B,OAAO,IAAIC,GAAG,CAACF,YAAY,CAAC;EAC9B,CAAC;EACD,IAAIG,EAAE,GAAGP,MAAM,CAACC,QAAQ,CAACI,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCG,GAAG,GAAGD,EAAE,CAAC,CAAC,CAAC;IACXE,MAAM,GAAGF,EAAE,CAAC,CAAC,CAAC;EAChB,IAAIG,GAAG,GAAG,SAAAA,CAAUC,GAAG,EAAEC,KAAK,EAAE;IAC9BH,MAAM,CAAC,UAAUI,IAAI,EAAE;MACrB,IAAIC,IAAI,GAAG,IAAIR,GAAG,CAACO,IAAI,CAAC;MACxBC,IAAI,CAACJ,GAAG,CAACC,GAAG,EAAEC,KAAK,CAAC;MACpB,OAAOE,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EACD,IAAIC,MAAM,GAAG,SAAAA,CAAUC,MAAM,EAAE;IAC7BP,MAAM,CAAC,IAAIH,GAAG,CAACU,MAAM,CAAC,CAAC;EACzB,CAAC;EACD,IAAIC,MAAM,GAAG,SAAAA,CAAUN,GAAG,EAAE;IAC1BF,MAAM,CAAC,UAAUI,IAAI,EAAE;MACrB,IAAIC,IAAI,GAAG,IAAIR,GAAG,CAACO,IAAI,CAAC;MACxBC,IAAI,CAACI,MAAM,CAACP,GAAG,CAAC;MAChB,OAAOG,IAAI;IACb,CAAC,CAAC;EACJ,CAAC;EACD,IAAIK,KAAK,GAAG,SAAAA,CAAA,EAAY;IACtB,OAAOV,MAAM,CAACJ,YAAY,CAAC,CAAC,CAAC;EAC/B,CAAC;EACD,IAAIe,GAAG,GAAG,SAAAA,CAAUT,GAAG,EAAE;IACvB,OAAOH,GAAG,CAACY,GAAG,CAACT,GAAG,CAAC;EACrB,CAAC;EACD,OAAO,CAACH,GAAG,EAAE;IACXE,GAAG,EAAER,aAAa,CAACQ,GAAG,CAAC;IACvBK,MAAM,EAAEb,aAAa,CAACa,MAAM,CAAC;IAC7BE,MAAM,EAAEf,aAAa,CAACe,MAAM,CAAC;IAC7BE,KAAK,EAAEjB,aAAa,CAACiB,KAAK,CAAC;IAC3BC,GAAG,EAAElB,aAAa,CAACkB,GAAG;EACxB,CAAC,CAAC;AACJ;AACA,eAAejB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}