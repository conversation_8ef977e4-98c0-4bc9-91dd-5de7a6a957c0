/**
 * API連接測試
 * 測試前端與後端的連接是否正常
 */

// 測試API連接
async function testAPIConnection() {
  console.log('🔗 開始測試API連接...');
  
  try {
    // 測試後端健康檢查
    const response = await fetch('/api/v1/cards/');
    console.log('📡 API響應狀態:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API連接成功，獲取到', data.length, '張名片');
      return true;
    } else {
      console.error('❌ API響應錯誤:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ API連接失敗:', error);
    return false;
  }
}

// 測試OCR API
async function testOCRAPI() {
  console.log('🧠 開始測試OCR API...');
  
  try {
    // 創建一個測試圖片（1x1像素的PNG）
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, 1, 1);
    
    // 轉換為Blob
    const blob = await new Promise(resolve => {
      canvas.toBlob(resolve, 'image/png');
    });
    
    // 創建FormData
    const formData = new FormData();
    formData.append('file', blob, 'test.png');
    
    // 發送OCR請求
    const response = await fetch('/api/v1/ocr/image', {
      method: 'POST',
      body: formData
    });
    
    console.log('🔍 OCR API響應狀態:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ OCR API連接成功:', data);
      return true;
    } else {
      console.error('❌ OCR API響應錯誤:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ OCR API測試失敗:', error);
    return false;
  }
}

// 執行所有測試
async function runAllTests() {
  console.log('🚀 開始執行API測試...');
  
  const results = {
    apiConnection: await testAPIConnection(),
    ocrAPI: await testOCRAPI()
  };
  
  console.log('📊 測試結果:', results);
  
  const allPassed = Object.values(results).every(result => result === true);
  
  if (allPassed) {
    console.log('🎉 所有API測試通過！');
  } else {
    console.log('⚠️ 部分API測試失敗，請檢查後端服務');
  }
  
  return results;
}

// 如果在瀏覽器環境中，自動執行測試
if (typeof window !== 'undefined') {
  // 等待頁面加載完成後執行測試
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllTests);
  } else {
    runAllTests();
  }
}

export { testAPIConnection, testOCRAPI, runAllTests };
