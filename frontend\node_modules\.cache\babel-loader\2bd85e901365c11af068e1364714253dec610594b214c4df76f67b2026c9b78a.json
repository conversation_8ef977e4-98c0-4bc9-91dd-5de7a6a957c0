{"ast": null, "code": "/*\r\nMIT License\r\n\r\nCopyright (c) 2018-2023 Simon <PERSON>\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\n\tThe above copyright notice and this permission notice shall be included in all\r\ncopies or substantial portions of the Software.\r\n\r\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\nSOFTWARE.\r\n*/function $cf838c15c8b009ba$var$vrgs(f) {\n  var s = f + \"\",\n    i = s.indexOf(\"...\");\n  return i >= 0 && (i < s.indexOf(\")\") || s.indexOf(\"arguments\") >= 0);\n}\nfunction $cf838c15c8b009ba$export$22f15dd4e5be7e52(fn, o) {\n  /*o = {\r\n  serializer, // used to serialize arguments of single argument functions, multis are not serialized\r\n  equals, // equality tester, will force use of slower multiarg approach even for single arg functions\r\n  maxAge, // max cache age is ms, set > 0 && < Infinity if you want automatic clearing\r\n  maxArgs, // max args to use for signature\r\n  vargs = vrgs(fn) // set to true if function may have variable or beyond-signature arguments, default is best attempt at infering\r\n  } = {}\r\n  */\n  o || (o = {});\n  var vargs = o.vargs || $cf838c15c8b009ba$var$vrgs(fn),\n    k = [],\n    cache = new Map(),\n    u,\n    to,\n    d = function (key) {\n      return to = setTimeout(function () {\n        if (u) {\n          cache.delete(key);\n          return;\n        }\n        // dealing with multi-arg function, c and k are Arrays\n        k.splice(key, 1);\n        //v.splice(key,1);\n      }, o.maxAge);\n    },\n    c = o.maxAge > 0 && o.maxAge < Infinity ? d : 0,\n    eq = o.equals ? o.equals : 0,\n    maxargs = o.maxArgs,\n    srlz = o.serializer,\n    f; // memoized function to return\n  if (fn.length === 1 && !o.equals && !vargs) {\n    // for single argument functions, just use a Map lookup\n    f = function (a) {\n      if (srlz) a = srlz(a);\n      var r;\n      return cache.get(a) || (!c || c(a), cache.set(a, r = fn.call(this, a)), r);\n    };\n    u = 1;\n  } else if (eq)\n    // for multiple arg functions, loop through a cache of all the args\n    // looking at each arg separately so a test can abort as soon as possible\n    f = function () {\n      var l = maxargs || arguments.length,\n        kl = k.length,\n        i = -1;\n      while (++i < kl) if (k[i].length === l) {\n        var j = -1;\n        while (++j < l && eq(arguments[j], k[i][j])); // compare each arg\n        if (j === l) return k[i].val //the args matched;\n        ;\n      }\n      // set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\n      k[i] = arguments;\n      return !c || c(i), arguments.val = fn.apply(this, k[i]);\n    };else f = function () {\n    var l = maxargs || arguments.length,\n      kl = k.length,\n      i = -1;\n    while (++i < kl) if (k[i].length === l) {\n      var j = -1;\n      while (++j < l && arguments[j] === k[i][j]); // compare each arg\n      if (j === l) return k[i].val //the args matched;\n      ;\n    }\n    // set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\n    k[i] = arguments;\n    return !c || c(i), arguments.val = fn.apply(this, k[i]);\n  };\n  // reset all the caches\n  f.clear = function () {\n    if (to) clearTimeout(to);\n    cache.clear();\n    k = [];\n  };\n  f.keys = function () {\n    return u ? [...cache.keys()] : k.slice();\n  };\n  f.values = function () {\n    return u ? [...cache.values()] : k.map(args => args.val);\n  };\n  return f;\n}\nexport { $cf838c15c8b009ba$export$22f15dd4e5be7e52 as nanomemoize, $cf838c15c8b009ba$export$22f15dd4e5be7e52 as default };", "map": {"version": 3, "names": ["$cf838c15c8b009ba$var$vrgs", "f", "s", "i", "indexOf", "$cf838c15c8b009ba$export$22f15dd4e5be7e52", "fn", "o", "vargs", "k", "cache", "Map", "u", "to", "d", "key", "setTimeout", "delete", "splice", "maxAge", "c", "Infinity", "eq", "equals", "maxargs", "maxArgs", "srlz", "serializer", "length", "a", "r", "get", "set", "call", "l", "arguments", "kl", "j", "val", "apply", "clear", "clearTimeout", "keys", "slice", "values", "map", "args"], "sources": ["C:\\Users\\<USER>\\Desktop\\app應用\\ocr_app_v2\\frontend\\node_modules\\nano-memoize\\src\\index.js"], "sourcesContent": ["/*\r\nMIT License\r\n\r\nCopyright (c) 2018-2023 Simon <PERSON>\r\n\r\nPermission is hereby granted, free of charge, to any person obtaining a copy\r\nof this software and associated documentation files (the \"Software\"), to deal\r\nin the Software without restriction, including without limitation the rights\r\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\r\ncopies of the Software, and to permit persons to whom the Software is\r\nfurnished to do so, subject to the following conditions:\r\n\r\n\tThe above copyright notice and this permission notice shall be included in all\r\ncopies or substantial portions of the Software.\r\n\r\n\tTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\r\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\r\n\tFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\r\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\r\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\r\n\tOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\r\nSOFTWARE.\r\n*/\r\n\r\nfunction vrgs(f) {\r\n\t\tvar s = f+\"\",\r\n\t\t\ti = s.indexOf(\"...\");\r\n\t\treturn i>=0 && (i<s.indexOf(\")\") || s.indexOf(\"arguments\")>=0);\r\n}\r\nfunction nanomemoize(fn,o) {\r\n\t/*o = {\r\n\t\tserializer, // used to serialize arguments of single argument functions, multis are not serialized\r\n\t\tequals, // equality tester, will force use of slower multiarg approach even for single arg functions\r\n\t\tmaxAge, // max cache age is ms, set > 0 && < Infinity if you want automatic clearing\r\n\t\tmaxArgs, // max args to use for signature\r\n\t\tvargs = vrgs(fn) // set to true if function may have variable or beyond-signature arguments, default is best attempt at infering\r\n\t  } = {}\r\n\t*/\r\n\to || (o={});\r\n\tvar vargs = o.vargs || vrgs(fn),\r\n\t\tk = [], // multiple arg function arg key cache\r\n\t\tcache = new Map(), // single arg function key/value cache\r\n\t\tu, // flag indicating a unary arg function is in use for clear operation\r\n\t\tto, // timeout for clearing cache\r\n\t\td = function(key) { return to = setTimeout(function() {\r\n\t\t\tif(u) {\r\n\t\t\t\tcache.delete(key);\r\n\t\t\t\treturn;\r\n\t\t\t}\r\n\t\t\t// dealing with multi-arg function, c and k are Arrays\r\n\t\t\tk.splice (key,1);\r\n\t\t\t//v.splice(key,1);\r\n\t\t\t},o.maxAge);\r\n\t\t},\r\n\t\tc = o.maxAge>0 && o.maxAge<Infinity ? d : 0, // cache change timeout,\r\n\t\teq = o.equals ? o.equals : 0,\r\n\t\tmaxargs = o.maxArgs,\r\n\t\tsrlz = o.serializer,\r\n\t\tf; // memoized function to return\r\n\tif(fn.length===1 && !o.equals && !vargs) {\r\n\t\t// for single argument functions, just use a Map lookup\r\n\t\tf =  function(a) {\r\n\t\t\t\tif(srlz) a = srlz(a);\r\n\t\t\t\tvar r;\r\n\t\t\t\treturn cache.get(a) || ((!c||c(a)),cache.set(a,r = fn.call(this, a)),r);\r\n\t\t};\r\n\t\tu = 1;\r\n\t}  else if(eq) {\r\n\t// for multiple arg functions, loop through a cache of all the args\r\n\t// looking at each arg separately so a test can abort as soon as possible\r\n\t\tf = function() {\r\n\t\t\tvar l = maxargs||arguments.length, kl = k.length,i = -1;\r\n\t\t\twhile(++i<kl) { // k is an array of arrays of args, each array represents a call signature\r\n\t\t\t\tif (k[i].length === l) { // maxargs!=null ||\r\n\t\t\t\t\tvar j = -1;\r\n\t\t\t\t\twhile ( ++j < l && eq(arguments[j], k[i][j])) {\t}// compare each arg\r\n\t\t\t\t\tif (j === l) return k[i].val //the args matched;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\r\n\t\t\tk[i] = arguments;\r\n\t\t\treturn (!c||c(i)),arguments.val = fn.apply(this,k[i]);\r\n\t\t}\r\n\t} else {\r\n\t\tf = function() {\r\n\t\t\tvar l = maxargs||arguments.length, kl = k.length, i = -1;\r\n\t\t\twhile(++i<kl) { // k is an array of arrays of args, each array represents a call signature\r\n\t\t\t\tif (k[i].length === l) { // maxargs!=null ||\r\n\t\t\t\t\tvar j = -1;\r\n\t\t\t\t\twhile (++j < l && arguments[j]===k[i][j]) {\t}// compare each arg\r\n\t\t\t\t\tif (j === l) return k[i].val //the args matched;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// set change timeout only when new value computed, hits will not push out the tte, but it is arguable they should not\r\n\t\t\tk[i] = arguments;\r\n\t\t\treturn (!c||c(i)),arguments.val = fn.apply(this,k[i]);\r\n\t\t}\r\n\t}\r\n\t// reset all the caches\r\n\tf.clear = function() {\r\n\t\tif(to) clearTimeout(to);\r\n\t\tcache.clear();\r\n\t\tk = [];\r\n\t};\r\n\tf.keys = function() { return u ? [...cache.keys()] : k.slice(); };\r\n\tf.values = function() { return u ? [...cache.values()] : k.map((args) => args.val); };\r\n\treturn f;\r\n}\r\nexport {nanomemoize,nanomemoize as default}\r\n\r\n"], "mappings": "AAAA;;;;;;;;;;;;;;;;;;;;;;AAsBA,EAEA,SAASA,2BAAKC,CAAC;EACb,IAAIC,CAAA,GAAID,CAAA,GAAE;IACTE,CAAA,GAAID,CAAA,CAAEE,OAAA,CAAQ;EACf,OAAOD,CAAA,IAAG,MAAMA,CAAA,GAAED,CAAA,CAAEE,OAAA,CAAQ,QAAQF,CAAA,CAAEE,OAAA,CAAQ,gBAAc;AAC9D;AACA,SAASC,0CAAYC,EAAE,EAACC,CAAC;EACxB;;;;;;;;EAQAA,CAAA,KAAMA,CAAA,GAAE,CAAC;EACT,IAAIC,KAAA,GAAQD,CAAA,CAAEC,KAAA,IAASR,0BAAA,CAAKM,EAAA;IAC3BG,CAAA,GAAI,EAAE;IACNC,KAAA,GAAQ,IAAIC,GAAA;IACZC,CAAA;IACAC,EAAA;IACAC,CAAA,GAAI,SAAAA,CAASC,GAAG;MAAI,OAAOF,EAAA,GAAKG,UAAA,CAAW;QAC1C,IAAGJ,CAAA,EAAG;UACLF,KAAA,CAAMO,MAAA,CAAOF,GAAA;UACb;QACD;QACA;QACAN,CAAA,CAAES,MAAA,CAAQH,GAAA,EAAI;QACd;MACA,GAAER,CAAA,CAAEY,MAAA;IACL;IACAC,CAAA,GAAIb,CAAA,CAAEY,MAAA,GAAO,KAAKZ,CAAA,CAAEY,MAAA,GAAOE,QAAA,GAAWP,CAAA,GAAI;IAC1CQ,EAAA,GAAKf,CAAA,CAAEgB,MAAA,GAAShB,CAAA,CAAEgB,MAAA,GAAS;IAC3BC,OAAA,GAAUjB,CAAA,CAAEkB,OAAA;IACZC,IAAA,GAAOnB,CAAA,CAAEoB,UAAA;IACT1B,CAAA,EAAG;EACJ,IAAGK,EAAA,CAAGsB,MAAA,KAAS,KAAK,CAACrB,CAAA,CAAEgB,MAAA,IAAU,CAACf,KAAA,EAAO;IACxC;IACAP,CAAA,GAAK,SAAAA,CAAS4B,CAAC;MACb,IAAGH,IAAA,EAAMG,CAAA,GAAIH,IAAA,CAAKG,CAAA;MAClB,IAAIC,CAAA;MACJ,OAAOpB,KAAA,CAAMqB,GAAA,CAAIF,CAAA,MAAO,CAAET,CAAA,IAAGA,CAAA,CAAES,CAAA,GAAInB,KAAA,CAAMsB,GAAA,CAAIH,CAAA,EAAEC,CAAA,GAAIxB,EAAA,CAAG2B,IAAA,CAAK,IAAI,EAAEJ,CAAA,IAAIC,CAAA;IACvE;IACAlB,CAAA,GAAI;EACL,OAAQ,IAAGU,EAAA;IACX;IACA;IACCrB,CAAA,GAAI,SAAAA,CAAA;MACH,IAAIiC,CAAA,GAAIV,OAAA,IAASW,SAAA,CAAUP,MAAA;QAAQQ,EAAA,GAAK3B,CAAA,CAAEmB,MAAA;QAAOzB,CAAA,GAAI;MACrD,OAAM,EAAEA,CAAA,GAAEiC,EAAA,EACT,IAAI3B,CAAC,CAACN,CAAA,CAAE,CAACyB,MAAA,KAAWM,CAAA,EAAG;QACtB,IAAIG,CAAA,GAAI;QACR,OAAQ,EAAEA,CAAA,GAAIH,CAAA,IAAKZ,EAAA,CAAGa,SAAS,CAACE,CAAA,CAAE,EAAE5B,CAAC,CAACN,CAAA,CAAE,CAACkC,CAAA,CAAE,IAAM;QACjD,IAAIA,CAAA,KAAMH,CAAA,EAAG,OAAOzB,CAAC,CAACN,CAAA,CAAE,CAACmC,GAAA,CAAI;QAAA;MAC9B;MAED;MACA7B,CAAC,CAACN,CAAA,CAAE,GAAGgC,SAAA;MACP,OAAO,CAAEf,CAAA,IAAGA,CAAA,CAAEjB,CAAA,GAAIgC,SAAA,CAAUG,GAAA,GAAMhC,EAAA,CAAGiC,KAAA,CAAM,IAAI,EAAC9B,CAAC,CAACN,CAAA,CAAE;IACrD,OAEAF,CAAA,GAAI,SAAAA,CAAA;IACH,IAAIiC,CAAA,GAAIV,OAAA,IAASW,SAAA,CAAUP,MAAA;MAAQQ,EAAA,GAAK3B,CAAA,CAAEmB,MAAA;MAAQzB,CAAA,GAAI;IACtD,OAAM,EAAEA,CAAA,GAAEiC,EAAA,EACT,IAAI3B,CAAC,CAACN,CAAA,CAAE,CAACyB,MAAA,KAAWM,CAAA,EAAG;MACtB,IAAIG,CAAA,GAAI;MACR,OAAO,EAAEA,CAAA,GAAIH,CAAA,IAAKC,SAAS,CAACE,CAAA,CAAE,KAAG5B,CAAC,CAACN,CAAA,CAAE,CAACkC,CAAA,CAAE,GAAK;MAC7C,IAAIA,CAAA,KAAMH,CAAA,EAAG,OAAOzB,CAAC,CAACN,CAAA,CAAE,CAACmC,GAAA,CAAI;MAAA;IAC9B;IAED;IACA7B,CAAC,CAACN,CAAA,CAAE,GAAGgC,SAAA;IACP,OAAO,CAAEf,CAAA,IAAGA,CAAA,CAAEjB,CAAA,GAAIgC,SAAA,CAAUG,GAAA,GAAMhC,EAAA,CAAGiC,KAAA,CAAM,IAAI,EAAC9B,CAAC,CAACN,CAAA,CAAE;EACrD;EAED;EACAF,CAAA,CAAEuC,KAAA,GAAQ;IACT,IAAG3B,EAAA,EAAI4B,YAAA,CAAa5B,EAAA;IACpBH,KAAA,CAAM8B,KAAA;IACN/B,CAAA,GAAI,EAAE;EACP;EACAR,CAAA,CAAEyC,IAAA,GAAO;IAAa,OAAO9B,CAAA,GAAI,C,GAAIF,KAAA,CAAMgC,IAAA,GAAO,GAAGjC,CAAA,CAAEkC,KAAA;EAAS;EAChE1C,CAAA,CAAE2C,MAAA,GAAS;IAAa,OAAOhC,CAAA,GAAI,C,GAAIF,KAAA,CAAMkC,MAAA,GAAS,GAAGnC,CAAA,CAAEoC,GAAA,CAAKC,IAAA,IAASA,IAAA,CAAKR,GAAA;EAAM;EACpF,OAAOrC,CAAA;AACR", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}