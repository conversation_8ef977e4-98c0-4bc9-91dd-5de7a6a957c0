{"ast": null, "code": "import { useEffect, useRef } from 'react';\nvar diffTwoDeps = function (deps1, deps2) {\n  //Let's do a reference equality check on 2 dependency list.\n  //If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2\n  //As this func is used only in this hook, we assume 2 deps always have same length.\n  return deps1 ? deps1.map(function (_ele, idx) {\n    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;\n  }).filter(function (ele) {\n    return ele >= 0;\n  }) : deps2 ? deps2.map(function (_ele, idx) {\n    return idx;\n  }) : [];\n};\nvar useTrackedEffect = function (effect, deps) {\n  var previousDepsRef = useRef();\n  useEffect(function () {\n    var changes = diffTwoDeps(previousDepsRef.current, deps);\n    var previousDeps = previousDepsRef.current;\n    previousDepsRef.current = deps;\n    return effect(changes, previousDeps, deps);\n  }, deps);\n};\nexport default useTrackedEffect;", "map": {"version": 3, "names": ["useEffect", "useRef", "diffTwoDeps", "deps1", "deps2", "map", "_ele", "idx", "Object", "is", "filter", "ele", "useTrackedEffect", "effect", "deps", "previousDepsRef", "changes", "current", "previousDeps"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useTrackedEffect/index.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nvar diffTwoDeps = function (deps1, deps2) {\n  //Let's do a reference equality check on 2 dependency list.\n  //If deps1 is defined, we iterate over deps1 and do comparison on each element with equivalent element from deps2\n  //As this func is used only in this hook, we assume 2 deps always have same length.\n  return deps1 ? deps1.map(function (_ele, idx) {\n    return !Object.is(deps1[idx], deps2 === null || deps2 === void 0 ? void 0 : deps2[idx]) ? idx : -1;\n  }).filter(function (ele) {\n    return ele >= 0;\n  }) : deps2 ? deps2.map(function (_ele, idx) {\n    return idx;\n  }) : [];\n};\nvar useTrackedEffect = function (effect, deps) {\n  var previousDepsRef = useRef();\n  useEffect(function () {\n    var changes = diffTwoDeps(previousDepsRef.current, deps);\n    var previousDeps = previousDepsRef.current;\n    previousDepsRef.current = deps;\n    return effect(changes, previousDeps, deps);\n  }, deps);\n};\nexport default useTrackedEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,IAAIC,WAAW,GAAG,SAAAA,CAAUC,KAAK,EAAEC,KAAK,EAAE;EACxC;EACA;EACA;EACA,OAAOD,KAAK,GAAGA,KAAK,CAACE,GAAG,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;IAC5C,OAAO,CAACC,MAAM,CAACC,EAAE,CAACN,KAAK,CAACI,GAAG,CAAC,EAAEH,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACG,GAAG,CAAC,CAAC,GAAGA,GAAG,GAAG,CAAC,CAAC;EACpG,CAAC,CAAC,CAACG,MAAM,CAAC,UAAUC,GAAG,EAAE;IACvB,OAAOA,GAAG,IAAI,CAAC;EACjB,CAAC,CAAC,GAAGP,KAAK,GAAGA,KAAK,CAACC,GAAG,CAAC,UAAUC,IAAI,EAAEC,GAAG,EAAE;IAC1C,OAAOA,GAAG;EACZ,CAAC,CAAC,GAAG,EAAE;AACT,CAAC;AACD,IAAIK,gBAAgB,GAAG,SAAAA,CAAUC,MAAM,EAAEC,IAAI,EAAE;EAC7C,IAAIC,eAAe,GAAGd,MAAM,CAAC,CAAC;EAC9BD,SAAS,CAAC,YAAY;IACpB,IAAIgB,OAAO,GAAGd,WAAW,CAACa,eAAe,CAACE,OAAO,EAAEH,IAAI,CAAC;IACxD,IAAII,YAAY,GAAGH,eAAe,CAACE,OAAO;IAC1CF,eAAe,CAACE,OAAO,GAAGH,IAAI;IAC9B,OAAOD,MAAM,CAACG,OAAO,EAAEE,YAAY,EAAEJ,IAAI,CAAC;EAC5C,CAAC,EAAEA,IAAI,CAAC;AACV,CAAC;AACD,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}