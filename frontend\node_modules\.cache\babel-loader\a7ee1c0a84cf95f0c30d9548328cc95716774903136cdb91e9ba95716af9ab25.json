{"ast": null, "code": "/* eslint-disable no-console */\nvar warned = {};\nvar preWarningFns = [];\n\n/**\n * Pre warning enable you to parse content before console.error.\n * Modify to null will prevent warning.\n */\nexport var preMessage = function preMessage(fn) {\n  preWarningFns.push(fn);\n};\n\n/**\n * Warning if condition not match.\n * @param valid Condition\n * @param message Warning message\n * @example\n * ```js\n * warning(false, 'some error'); // print some error\n * warning(true, 'some error'); // print nothing\n * warning(1 === 2, 'some error'); // print some error\n * ```\n */\nexport function warning(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'warning');\n    }, message);\n    if (finalMessage) {\n      console.error(\"Warning: \".concat(finalMessage));\n    }\n  }\n}\n\n/** @see Similar to {@link warning} */\nexport function note(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'note');\n    }, message);\n    if (finalMessage) {\n      console.warn(\"Note: \".concat(finalMessage));\n    }\n  }\n}\nexport function resetWarned() {\n  warned = {};\n}\nexport function call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nexport function warningOnce(valid, message) {\n  call(warning, valid, message);\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nexport function noteOnce(valid, message) {\n  call(note, valid, message);\n}\nwarningOnce.preMessage = preMessage;\nwarningOnce.resetWarned = resetWarned;\nwarningOnce.noteOnce = noteOnce;\nexport default warningOnce;", "map": {"version": 3, "names": ["warned", "preWarningFns", "preMessage", "fn", "push", "warning", "valid", "message", "process", "env", "NODE_ENV", "console", "undefined", "finalMessage", "reduce", "msg", "preMessageFn", "error", "concat", "note", "warn", "resetWarned", "call", "method", "warningOnce", "noteOnce"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-util/es/warning.js"], "sourcesContent": ["/* eslint-disable no-console */\nvar warned = {};\nvar preWarningFns = [];\n\n/**\n * Pre warning enable you to parse content before console.error.\n * Modify to null will prevent warning.\n */\nexport var preMessage = function preMessage(fn) {\n  preWarningFns.push(fn);\n};\n\n/**\n * Warning if condition not match.\n * @param valid Condition\n * @param message Warning message\n * @example\n * ```js\n * warning(false, 'some error'); // print some error\n * warning(true, 'some error'); // print nothing\n * warning(1 === 2, 'some error'); // print some error\n * ```\n */\nexport function warning(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'warning');\n    }, message);\n    if (finalMessage) {\n      console.error(\"Warning: \".concat(finalMessage));\n    }\n  }\n}\n\n/** @see Similar to {@link warning} */\nexport function note(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'note');\n    }, message);\n    if (finalMessage) {\n      console.warn(\"Note: \".concat(finalMessage));\n    }\n  }\n}\nexport function resetWarned() {\n  warned = {};\n}\nexport function call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nexport function warningOnce(valid, message) {\n  call(warning, valid, message);\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nexport function noteOnce(valid, message) {\n  call(note, valid, message);\n}\nwarningOnce.preMessage = preMessage;\nwarningOnce.resetWarned = resetWarned;\nwarningOnce.noteOnce = noteOnce;\nexport default warningOnce;"], "mappings": "AAAA;AACA,IAAIA,MAAM,GAAG,CAAC,CAAC;AACf,IAAIC,aAAa,GAAG,EAAE;;AAEtB;AACA;AACA;AACA;AACA,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,EAAE,EAAE;EAC9CF,aAAa,CAACG,IAAI,CAACD,EAAE,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASE,OAAOA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACtC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5E,IAAIC,YAAY,GAAGZ,aAAa,CAACa,MAAM,CAAC,UAAUC,GAAG,EAAEC,YAAY,EAAE;MACnE,OAAOA,YAAY,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,EAAE,EAAE,SAAS,CAAC;IAC3E,CAAC,EAAER,OAAO,CAAC;IACX,IAAIM,YAAY,EAAE;MAChBF,OAAO,CAACM,KAAK,CAAC,WAAW,CAACC,MAAM,CAACL,YAAY,CAAC,CAAC;IACjD;EACF;AACF;;AAEA;AACA,OAAO,SAASM,IAAIA,CAACb,KAAK,EAAEC,OAAO,EAAE;EACnC,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5E,IAAIC,YAAY,GAAGZ,aAAa,CAACa,MAAM,CAAC,UAAUC,GAAG,EAAEC,YAAY,EAAE;MACnE,OAAOA,YAAY,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,EAAE,EAAE,MAAM,CAAC;IACxE,CAAC,EAAER,OAAO,CAAC;IACX,IAAIM,YAAY,EAAE;MAChBF,OAAO,CAACS,IAAI,CAAC,QAAQ,CAACF,MAAM,CAACL,YAAY,CAAC,CAAC;IAC7C;EACF;AACF;AACA,OAAO,SAASQ,WAAWA,CAAA,EAAG;EAC5BrB,MAAM,GAAG,CAAC,CAAC;AACb;AACA,OAAO,SAASsB,IAAIA,CAACC,MAAM,EAAEjB,KAAK,EAAEC,OAAO,EAAE;EAC3C,IAAI,CAACD,KAAK,IAAI,CAACN,MAAM,CAACO,OAAO,CAAC,EAAE;IAC9BgB,MAAM,CAAC,KAAK,EAAEhB,OAAO,CAAC;IACtBP,MAAM,CAACO,OAAO,CAAC,GAAG,IAAI;EACxB;AACF;;AAEA;AACA,OAAO,SAASiB,WAAWA,CAAClB,KAAK,EAAEC,OAAO,EAAE;EAC1Ce,IAAI,CAACjB,OAAO,EAAEC,KAAK,EAAEC,OAAO,CAAC;AAC/B;;AAEA;AACA,OAAO,SAASkB,QAAQA,CAACnB,KAAK,EAAEC,OAAO,EAAE;EACvCe,IAAI,CAACH,IAAI,EAAEb,KAAK,EAAEC,OAAO,CAAC;AAC5B;AACAiB,WAAW,CAACtB,UAAU,GAAGA,UAAU;AACnCsB,WAAW,CAACH,WAAW,GAAGA,WAAW;AACrCG,WAAW,CAACC,QAAQ,GAAGA,QAAQ;AAC/B,eAAeD,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}