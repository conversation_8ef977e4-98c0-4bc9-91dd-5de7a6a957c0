{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\Camera\\\\CameraControls.js\";\n/**\n * 相機控制按鈕組件\n */\n\nimport React from 'react';\nimport { Button, Space } from 'antd-mobile';\nimport { CameraOutline, PictureOutline } from 'antd-mobile-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst CameraControls = ({\n  onTakePhoto,\n  onSelectFromGallery,\n  currentTarget = 'front',\n  disabled = false,\n  style = {},\n  className = ''\n}) => {\n  const sideText = currentTarget === 'front' ? '正面' : '反面';\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `camera-controls ${className}`,\n    style: {\n      display: 'flex',\n      gap: '8px',\n      justifyContent: 'center',\n      ...style\n    },\n    children: [/*#__PURE__*/_jsxDEV(Button, {\n      color: \"primary\",\n      size: \"large\",\n      onClick: () => onTakePhoto(currentTarget),\n      disabled: disabled,\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), \" \\u62CD\\u651D\", sideText]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Button, {\n      color: \"primary\",\n      fill: \"outline\",\n      size: \"large\",\n      onClick: () => onSelectFromGallery(currentTarget),\n      disabled: disabled,\n      style: {\n        flex: 1\n      },\n      children: [/*#__PURE__*/_jsxDEV(PictureOutline, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 47,\n        columnNumber: 9\n      }, this), \" \\u76F8\\u518A\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 39,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = CameraControls;\nexport default CameraControls;\nvar _c;\n$RefreshReg$(_c, \"CameraControls\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON>", "Space", "CameraOutline", "PictureOutline", "jsxDEV", "_jsxDEV", "CameraControls", "onTakePhoto", "onSelectFromGallery", "currentTarget", "disabled", "style", "className", "sideText", "display", "gap", "justifyContent", "children", "color", "size", "onClick", "flex", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Camera/CameraControls.js"], "sourcesContent": ["/**\n * 相機控制按鈕組件\n */\n\nimport React from 'react';\nimport { Button, Space } from 'antd-mobile';\nimport { CameraOutline, PictureOutline } from 'antd-mobile-icons';\n\nconst CameraControls = ({\n  onTakePhoto,\n  onSelectFromGallery,\n  currentTarget = 'front',\n  disabled = false,\n  style = {},\n  className = ''\n}) => {\n  const sideText = currentTarget === 'front' ? '正面' : '反面';\n\n  return (\n    <div \n      className={`camera-controls ${className}`}\n      style={{\n        display: 'flex',\n        gap: '8px',\n        justifyContent: 'center',\n        ...style\n      }}\n    >\n      <Button\n        color=\"primary\"\n        size=\"large\"\n        onClick={() => onTakePhoto(currentTarget)}\n        disabled={disabled}\n        style={{ flex: 1 }}\n      >\n        <CameraOutline /> 拍攝{sideText}\n      </Button>\n      \n      <Button\n        color=\"primary\"\n        fill=\"outline\"\n        size=\"large\"\n        onClick={() => onSelectFromGallery(currentTarget)}\n        disabled={disabled}\n        style={{ flex: 1 }}\n      >\n        <PictureOutline /> 相冊\n      </Button>\n    </div>\n  );\n};\n\nexport default CameraControls;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,QAAQ,aAAa;AAC3C,SAASC,aAAa,EAAEC,cAAc,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAElE,MAAMC,cAAc,GAAGA,CAAC;EACtBC,WAAW;EACXC,mBAAmB;EACnBC,aAAa,GAAG,OAAO;EACvBC,QAAQ,GAAG,KAAK;EAChBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,QAAQ,GAAGJ,aAAa,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI;EAExD,oBACEJ,OAAA;IACEO,SAAS,EAAE,mBAAmBA,SAAS,EAAG;IAC1CD,KAAK,EAAE;MACLG,OAAO,EAAE,MAAM;MACfC,GAAG,EAAE,KAAK;MACVC,cAAc,EAAE,QAAQ;MACxB,GAAGL;IACL,CAAE;IAAAM,QAAA,gBAEFZ,OAAA,CAACL,MAAM;MACLkB,KAAK,EAAC,SAAS;MACfC,IAAI,EAAC,OAAO;MACZC,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAACE,aAAa,CAAE;MAC1CC,QAAQ,EAAEA,QAAS;MACnBC,KAAK,EAAE;QAAEU,IAAI,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAEnBZ,OAAA,CAACH,aAAa;QAAAoB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,iBAAG,EAACZ,QAAQ;IAAA;MAAAS,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvB,CAAC,eAETpB,OAAA,CAACL,MAAM;MACLkB,KAAK,EAAC,SAAS;MACfQ,IAAI,EAAC,SAAS;MACdP,IAAI,EAAC,OAAO;MACZC,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAACC,aAAa,CAAE;MAClDC,QAAQ,EAAEA,QAAS;MACnBC,KAAK,EAAE;QAAEU,IAAI,EAAE;MAAE,CAAE;MAAAJ,QAAA,gBAEnBZ,OAAA,CAACF,cAAc;QAAAmB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,iBACpB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV,CAAC;AAACE,EAAA,GA1CIrB,cAAc;AA4CpB,eAAeA,cAAc;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}