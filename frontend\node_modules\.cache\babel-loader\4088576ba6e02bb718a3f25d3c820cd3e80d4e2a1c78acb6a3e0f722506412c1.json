{"ast": null, "code": "import { __assign, __awaiter, __generator, __read, __rest, __spreadArray } from \"tslib\";\n/* eslint-disable @typescript-eslint/no-parameter-properties */\nimport { isFunction } from '../../utils';\nvar Fetch = /** @class */function () {\n  function Fetch(serviceRef, options, subscribe, initState) {\n    if (initState === void 0) {\n      initState = {};\n    }\n    this.serviceRef = serviceRef;\n    this.options = options;\n    this.subscribe = subscribe;\n    this.initState = initState;\n    this.count = 0;\n    this.state = {\n      loading: false,\n      params: undefined,\n      data: undefined,\n      error: undefined\n    };\n    this.state = __assign(__assign(__assign({}, this.state), {\n      loading: !options.manual\n    }), initState);\n  }\n  Fetch.prototype.setState = function (s) {\n    if (s === void 0) {\n      s = {};\n    }\n    this.state = __assign(__assign({}, this.state), s);\n    this.subscribe();\n  };\n  Fetch.prototype.runPluginHandler = function (event) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    }\n    // @ts-ignore\n    var r = this.pluginImpls.map(function (i) {\n      var _a;\n      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));\n    }).filter(Boolean);\n    return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));\n  };\n  Fetch.prototype.runAsync = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var currentCount, _a, _b, stopNow, _c, returnNow, state, servicePromise, res, error_1;\n      var _d;\n      var _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;\n      return __generator(this, function (_q) {\n        switch (_q.label) {\n          case 0:\n            this.count += 1;\n            currentCount = this.count;\n            _a = this.runPluginHandler('onBefore', params), _b = _a.stopNow, stopNow = _b === void 0 ? false : _b, _c = _a.returnNow, returnNow = _c === void 0 ? false : _c, state = __rest(_a, [\"stopNow\", \"returnNow\"]);\n            // stop request\n            if (stopNow) {\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState(__assign({\n              loading: true,\n              params: params\n            }, state));\n            // return now\n            if (returnNow) {\n              return [2 /*return*/, Promise.resolve(state.data)];\n            }\n            (_f = (_e = this.options).onBefore) === null || _f === void 0 ? void 0 : _f.call(_e, params);\n            _q.label = 1;\n          case 1:\n            _q.trys.push([1, 3,, 4]);\n            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;\n            if (!servicePromise) {\n              servicePromise = (_d = this.serviceRef).current.apply(_d, __spreadArray([], __read(params), false));\n            }\n            return [4 /*yield*/, servicePromise];\n          case 2:\n            res = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;\n            this.setState({\n              data: res,\n              error: undefined,\n              loading: false\n            });\n            (_h = (_g = this.options).onSuccess) === null || _h === void 0 ? void 0 : _h.call(_g, res, params);\n            this.runPluginHandler('onSuccess', res, params);\n            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, res, undefined);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, res, undefined);\n            }\n            return [2 /*return*/, res];\n          case 3:\n            error_1 = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState({\n              error: error_1,\n              loading: false\n            });\n            (_m = (_l = this.options).onError) === null || _m === void 0 ? void 0 : _m.call(_l, error_1, params);\n            this.runPluginHandler('onError', error_1, params);\n            (_p = (_o = this.options).onFinally) === null || _p === void 0 ? void 0 : _p.call(_o, params, undefined, error_1);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, undefined, error_1);\n            }\n            throw error_1;\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  Fetch.prototype.run = function () {\n    var _this = this;\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    this.runAsync.apply(this, __spreadArray([], __read(params), false)).catch(function (error) {\n      if (!_this.options.onError) {\n        console.error(error);\n      }\n    });\n  };\n  Fetch.prototype.cancel = function () {\n    this.count += 1;\n    this.setState({\n      loading: false\n    });\n    this.runPluginHandler('onCancel');\n  };\n  Fetch.prototype.refresh = function () {\n    // @ts-ignore\n    this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.refreshAsync = function () {\n    // @ts-ignore\n    return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.mutate = function (data) {\n    var targetData = isFunction(data) ? data(this.state.data) : data;\n    this.runPluginHandler('onMutate', targetData);\n    this.setState({\n      data: targetData\n    });\n  };\n  return Fetch;\n}();\nexport default Fetch;", "map": {"version": 3, "names": ["__assign", "__awaiter", "__generator", "__read", "__rest", "__spread<PERSON><PERSON>y", "isFunction", "<PERSON>tch", "serviceRef", "options", "subscribe", "initState", "count", "state", "loading", "params", "undefined", "data", "error", "manual", "prototype", "setState", "s", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "rest", "_i", "arguments", "length", "r", "pluginImpls", "map", "i", "_a", "call", "apply", "filter", "Boolean", "Object", "assign", "runAsync", "currentCount", "_b", "stopNow", "_c", "returnNow", "servicePromise", "res", "error_1", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "_l", "_m", "_o", "_p", "_q", "label", "Promise", "resolve", "onBefore", "trys", "push", "current", "sent", "onSuccess", "onFinally", "onError", "run", "_this", "catch", "console", "cancel", "refresh", "refreshAsync", "mutate", "targetData"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/src/Fetch.js"], "sourcesContent": ["import { __assign, __awaiter, __generator, __read, __rest, __spreadArray } from \"tslib\";\n/* eslint-disable @typescript-eslint/no-parameter-properties */\nimport { isFunction } from '../../utils';\nvar Fetch = /** @class */function () {\n  function Fetch(serviceRef, options, subscribe, initState) {\n    if (initState === void 0) {\n      initState = {};\n    }\n    this.serviceRef = serviceRef;\n    this.options = options;\n    this.subscribe = subscribe;\n    this.initState = initState;\n    this.count = 0;\n    this.state = {\n      loading: false,\n      params: undefined,\n      data: undefined,\n      error: undefined\n    };\n    this.state = __assign(__assign(__assign({}, this.state), {\n      loading: !options.manual\n    }), initState);\n  }\n  Fetch.prototype.setState = function (s) {\n    if (s === void 0) {\n      s = {};\n    }\n    this.state = __assign(__assign({}, this.state), s);\n    this.subscribe();\n  };\n  Fetch.prototype.runPluginHandler = function (event) {\n    var rest = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      rest[_i - 1] = arguments[_i];\n    }\n    // @ts-ignore\n    var r = this.pluginImpls.map(function (i) {\n      var _a;\n      return (_a = i[event]) === null || _a === void 0 ? void 0 : _a.call.apply(_a, __spreadArray([i], __read(rest), false));\n    }).filter(Boolean);\n    return Object.assign.apply(Object, __spreadArray([{}], __read(r), false));\n  };\n  Fetch.prototype.runAsync = function () {\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    return __awaiter(this, void 0, void 0, function () {\n      var currentCount, _a, _b, stopNow, _c, returnNow, state, servicePromise, res, error_1;\n      var _d;\n      var _e, _f, _g, _h, _j, _k, _l, _m, _o, _p;\n      return __generator(this, function (_q) {\n        switch (_q.label) {\n          case 0:\n            this.count += 1;\n            currentCount = this.count;\n            _a = this.runPluginHandler('onBefore', params), _b = _a.stopNow, stopNow = _b === void 0 ? false : _b, _c = _a.returnNow, returnNow = _c === void 0 ? false : _c, state = __rest(_a, [\"stopNow\", \"returnNow\"]);\n            // stop request\n            if (stopNow) {\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState(__assign({\n              loading: true,\n              params: params\n            }, state));\n            // return now\n            if (returnNow) {\n              return [2 /*return*/, Promise.resolve(state.data)];\n            }\n            (_f = (_e = this.options).onBefore) === null || _f === void 0 ? void 0 : _f.call(_e, params);\n            _q.label = 1;\n          case 1:\n            _q.trys.push([1, 3,, 4]);\n            servicePromise = this.runPluginHandler('onRequest', this.serviceRef.current, params).servicePromise;\n            if (!servicePromise) {\n              servicePromise = (_d = this.serviceRef).current.apply(_d, __spreadArray([], __read(params), false));\n            }\n            return [4 /*yield*/, servicePromise];\n          case 2:\n            res = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            // const formattedResult = this.options.formatResultRef.current ? this.options.formatResultRef.current(res) : res;\n            this.setState({\n              data: res,\n              error: undefined,\n              loading: false\n            });\n            (_h = (_g = this.options).onSuccess) === null || _h === void 0 ? void 0 : _h.call(_g, res, params);\n            this.runPluginHandler('onSuccess', res, params);\n            (_k = (_j = this.options).onFinally) === null || _k === void 0 ? void 0 : _k.call(_j, params, res, undefined);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, res, undefined);\n            }\n            return [2 /*return*/, res];\n          case 3:\n            error_1 = _q.sent();\n            if (currentCount !== this.count) {\n              // prevent run.then when request is canceled\n              return [2 /*return*/, new Promise(function () {})];\n            }\n            this.setState({\n              error: error_1,\n              loading: false\n            });\n            (_m = (_l = this.options).onError) === null || _m === void 0 ? void 0 : _m.call(_l, error_1, params);\n            this.runPluginHandler('onError', error_1, params);\n            (_p = (_o = this.options).onFinally) === null || _p === void 0 ? void 0 : _p.call(_o, params, undefined, error_1);\n            if (currentCount === this.count) {\n              this.runPluginHandler('onFinally', params, undefined, error_1);\n            }\n            throw error_1;\n          case 4:\n            return [2 /*return*/];\n        }\n      });\n    });\n  };\n  Fetch.prototype.run = function () {\n    var _this = this;\n    var params = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      params[_i] = arguments[_i];\n    }\n    this.runAsync.apply(this, __spreadArray([], __read(params), false)).catch(function (error) {\n      if (!_this.options.onError) {\n        console.error(error);\n      }\n    });\n  };\n  Fetch.prototype.cancel = function () {\n    this.count += 1;\n    this.setState({\n      loading: false\n    });\n    this.runPluginHandler('onCancel');\n  };\n  Fetch.prototype.refresh = function () {\n    // @ts-ignore\n    this.run.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.refreshAsync = function () {\n    // @ts-ignore\n    return this.runAsync.apply(this, __spreadArray([], __read(this.state.params || []), false));\n  };\n  Fetch.prototype.mutate = function (data) {\n    var targetData = isFunction(data) ? data(this.state.data) : data;\n    this.runPluginHandler('onMutate', targetData);\n    this.setState({\n      data: targetData\n    });\n  };\n  return Fetch;\n}();\nexport default Fetch;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AACvF;AACA,SAASC,UAAU,QAAQ,aAAa;AACxC,IAAIC,KAAK,GAAG,aAAa,YAAY;EACnC,SAASA,KAAKA,CAACC,UAAU,EAAEC,OAAO,EAAEC,SAAS,EAAEC,SAAS,EAAE;IACxD,IAAIA,SAAS,KAAK,KAAK,CAAC,EAAE;MACxBA,SAAS,GAAG,CAAC,CAAC;IAChB;IACA,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG;MACXC,OAAO,EAAE,KAAK;MACdC,MAAM,EAAEC,SAAS;MACjBC,IAAI,EAAED,SAAS;MACfE,KAAK,EAAEF;IACT,CAAC;IACD,IAAI,CAACH,KAAK,GAAGb,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,EAAE;MACvDC,OAAO,EAAE,CAACL,OAAO,CAACU;IACpB,CAAC,CAAC,EAAER,SAAS,CAAC;EAChB;EACAJ,KAAK,CAACa,SAAS,CAACC,QAAQ,GAAG,UAAUC,CAAC,EAAE;IACtC,IAAIA,CAAC,KAAK,KAAK,CAAC,EAAE;MAChBA,CAAC,GAAG,CAAC,CAAC;IACR;IACA,IAAI,CAACT,KAAK,GAAGb,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAE,IAAI,CAACa,KAAK,CAAC,EAAES,CAAC,CAAC;IAClD,IAAI,CAACZ,SAAS,CAAC,CAAC;EAClB,CAAC;EACDH,KAAK,CAACa,SAAS,CAACG,gBAAgB,GAAG,UAAUC,KAAK,EAAE;IAClD,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;IACA;IACA,IAAIG,CAAC,GAAG,IAAI,CAACC,WAAW,CAACC,GAAG,CAAC,UAAUC,CAAC,EAAE;MACxC,IAAIC,EAAE;MACN,OAAO,CAACA,EAAE,GAAGD,CAAC,CAACR,KAAK,CAAC,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACC,IAAI,CAACC,KAAK,CAACF,EAAE,EAAE5B,aAAa,CAAC,CAAC2B,CAAC,CAAC,EAAE7B,MAAM,CAACsB,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACxH,CAAC,CAAC,CAACW,MAAM,CAACC,OAAO,CAAC;IAClB,OAAOC,MAAM,CAACC,MAAM,CAACJ,KAAK,CAACG,MAAM,EAAEjC,aAAa,CAAC,CAAC,CAAC,CAAC,CAAC,EAAEF,MAAM,CAAC0B,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;EAC3E,CAAC;EACDtB,KAAK,CAACa,SAAS,CAACoB,QAAQ,GAAG,YAAY;IACrC,IAAIzB,MAAM,GAAG,EAAE;IACf,KAAK,IAAIW,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CX,MAAM,CAACW,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA,OAAOzB,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MACjD,IAAIwC,YAAY,EAAER,EAAE,EAAES,EAAE,EAAEC,OAAO,EAAEC,EAAE,EAAEC,SAAS,EAAEhC,KAAK,EAAEiC,cAAc,EAAEC,GAAG,EAAEC,OAAO;MACrF,IAAIC,EAAE;MACN,IAAIC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;MAC1C,OAAOzD,WAAW,CAAC,IAAI,EAAE,UAAU0D,EAAE,EAAE;QACrC,QAAQA,EAAE,CAACC,KAAK;UACd,KAAK,CAAC;YACJ,IAAI,CAACjD,KAAK,IAAI,CAAC;YACf6B,YAAY,GAAG,IAAI,CAAC7B,KAAK;YACzBqB,EAAE,GAAG,IAAI,CAACV,gBAAgB,CAAC,UAAU,EAAER,MAAM,CAAC,EAAE2B,EAAE,GAAGT,EAAE,CAACU,OAAO,EAAEA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE,EAAEE,EAAE,GAAGX,EAAE,CAACY,SAAS,EAAEA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE,EAAE/B,KAAK,GAAGT,MAAM,CAAC6B,EAAE,EAAE,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAC9M;YACA,IAAIU,OAAO,EAAE;cACX,OAAO,CAAC,CAAC,CAAC,YAAY,IAAImB,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD;YACA,IAAI,CAACzC,QAAQ,CAACrB,QAAQ,CAAC;cACrBc,OAAO,EAAE,IAAI;cACbC,MAAM,EAAEA;YACV,CAAC,EAAEF,KAAK,CAAC,CAAC;YACV;YACA,IAAIgC,SAAS,EAAE;cACb,OAAO,CAAC,CAAC,CAAC,YAAYiB,OAAO,CAACC,OAAO,CAAClD,KAAK,CAACI,IAAI,CAAC,CAAC;YACpD;YACA,CAACkC,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACzC,OAAO,EAAEuD,QAAQ,MAAM,IAAI,IAAIb,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACjB,IAAI,CAACgB,EAAE,EAAEnC,MAAM,CAAC;YAC5F6C,EAAE,CAACC,KAAK,GAAG,CAAC;UACd,KAAK,CAAC;YACJD,EAAE,CAACK,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;YACxBpB,cAAc,GAAG,IAAI,CAACvB,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACf,UAAU,CAAC2D,OAAO,EAAEpD,MAAM,CAAC,CAAC+B,cAAc;YACnG,IAAI,CAACA,cAAc,EAAE;cACnBA,cAAc,GAAG,CAACG,EAAE,GAAG,IAAI,CAACzC,UAAU,EAAE2D,OAAO,CAAChC,KAAK,CAACc,EAAE,EAAE5C,aAAa,CAAC,EAAE,EAAEF,MAAM,CAACY,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC;YACrG;YACA,OAAO,CAAC,CAAC,CAAC,WAAW+B,cAAc,CAAC;UACtC,KAAK,CAAC;YACJC,GAAG,GAAGa,EAAE,CAACQ,IAAI,CAAC,CAAC;YACf,IAAI3B,YAAY,KAAK,IAAI,CAAC7B,KAAK,EAAE;cAC/B;cACA,OAAO,CAAC,CAAC,CAAC,YAAY,IAAIkD,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD;YACA;YACA,IAAI,CAACzC,QAAQ,CAAC;cACZJ,IAAI,EAAE8B,GAAG;cACT7B,KAAK,EAAEF,SAAS;cAChBF,OAAO,EAAE;YACX,CAAC,CAAC;YACF,CAACuC,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC3C,OAAO,EAAE4D,SAAS,MAAM,IAAI,IAAIhB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnB,IAAI,CAACkB,EAAE,EAAEL,GAAG,EAAEhC,MAAM,CAAC;YAClG,IAAI,CAACQ,gBAAgB,CAAC,WAAW,EAAEwB,GAAG,EAAEhC,MAAM,CAAC;YAC/C,CAACwC,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC7C,OAAO,EAAE6D,SAAS,MAAM,IAAI,IAAIf,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACrB,IAAI,CAACoB,EAAE,EAAEvC,MAAM,EAAEgC,GAAG,EAAE/B,SAAS,CAAC;YAC7G,IAAIyB,YAAY,KAAK,IAAI,CAAC7B,KAAK,EAAE;cAC/B,IAAI,CAACW,gBAAgB,CAAC,WAAW,EAAER,MAAM,EAAEgC,GAAG,EAAE/B,SAAS,CAAC;YAC5D;YACA,OAAO,CAAC,CAAC,CAAC,YAAY+B,GAAG,CAAC;UAC5B,KAAK,CAAC;YACJC,OAAO,GAAGY,EAAE,CAACQ,IAAI,CAAC,CAAC;YACnB,IAAI3B,YAAY,KAAK,IAAI,CAAC7B,KAAK,EAAE;cAC/B;cACA,OAAO,CAAC,CAAC,CAAC,YAAY,IAAIkD,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,CAAC;YACpD;YACA,IAAI,CAACzC,QAAQ,CAAC;cACZH,KAAK,EAAE8B,OAAO;cACdlC,OAAO,EAAE;YACX,CAAC,CAAC;YACF,CAAC2C,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAAC/C,OAAO,EAAE8D,OAAO,MAAM,IAAI,IAAId,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACvB,IAAI,CAACsB,EAAE,EAAER,OAAO,EAAEjC,MAAM,CAAC;YACpG,IAAI,CAACQ,gBAAgB,CAAC,SAAS,EAAEyB,OAAO,EAAEjC,MAAM,CAAC;YACjD,CAAC4C,EAAE,GAAG,CAACD,EAAE,GAAG,IAAI,CAACjD,OAAO,EAAE6D,SAAS,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACzB,IAAI,CAACwB,EAAE,EAAE3C,MAAM,EAAEC,SAAS,EAAEgC,OAAO,CAAC;YACjH,IAAIP,YAAY,KAAK,IAAI,CAAC7B,KAAK,EAAE;cAC/B,IAAI,CAACW,gBAAgB,CAAC,WAAW,EAAER,MAAM,EAAEC,SAAS,EAAEgC,OAAO,CAAC;YAChE;YACA,MAAMA,OAAO;UACf,KAAK,CAAC;YACJ,OAAO,CAAC,CAAC,CAAC,WAAW;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EACDzC,KAAK,CAACa,SAAS,CAACoD,GAAG,GAAG,YAAY;IAChC,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI1D,MAAM,GAAG,EAAE;IACf,KAAK,IAAIW,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CX,MAAM,CAACW,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC5B;IACA,IAAI,CAACc,QAAQ,CAACL,KAAK,CAAC,IAAI,EAAE9B,aAAa,CAAC,EAAE,EAAEF,MAAM,CAACY,MAAM,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC2D,KAAK,CAAC,UAAUxD,KAAK,EAAE;MACzF,IAAI,CAACuD,KAAK,CAAChE,OAAO,CAAC8D,OAAO,EAAE;QAC1BI,OAAO,CAACzD,KAAK,CAACA,KAAK,CAAC;MACtB;IACF,CAAC,CAAC;EACJ,CAAC;EACDX,KAAK,CAACa,SAAS,CAACwD,MAAM,GAAG,YAAY;IACnC,IAAI,CAAChE,KAAK,IAAI,CAAC;IACf,IAAI,CAACS,QAAQ,CAAC;MACZP,OAAO,EAAE;IACX,CAAC,CAAC;IACF,IAAI,CAACS,gBAAgB,CAAC,UAAU,CAAC;EACnC,CAAC;EACDhB,KAAK,CAACa,SAAS,CAACyD,OAAO,GAAG,YAAY;IACpC;IACA,IAAI,CAACL,GAAG,CAACrC,KAAK,CAAC,IAAI,EAAE9B,aAAa,CAAC,EAAE,EAAEF,MAAM,CAAC,IAAI,CAACU,KAAK,CAACE,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;EACjF,CAAC;EACDR,KAAK,CAACa,SAAS,CAAC0D,YAAY,GAAG,YAAY;IACzC;IACA,OAAO,IAAI,CAACtC,QAAQ,CAACL,KAAK,CAAC,IAAI,EAAE9B,aAAa,CAAC,EAAE,EAAEF,MAAM,CAAC,IAAI,CAACU,KAAK,CAACE,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;EAC7F,CAAC;EACDR,KAAK,CAACa,SAAS,CAAC2D,MAAM,GAAG,UAAU9D,IAAI,EAAE;IACvC,IAAI+D,UAAU,GAAG1E,UAAU,CAACW,IAAI,CAAC,GAAGA,IAAI,CAAC,IAAI,CAACJ,KAAK,CAACI,IAAI,CAAC,GAAGA,IAAI;IAChE,IAAI,CAACM,gBAAgB,CAAC,UAAU,EAAEyD,UAAU,CAAC;IAC7C,IAAI,CAAC3D,QAAQ,CAAC;MACZJ,IAAI,EAAE+D;IACR,CAAC,CAAC;EACJ,CAAC;EACD,OAAOzE,KAAK;AACd,CAAC,CAAC,CAAC;AACH,eAAeA,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}