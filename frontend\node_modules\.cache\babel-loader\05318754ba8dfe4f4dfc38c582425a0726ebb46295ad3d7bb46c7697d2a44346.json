{"ast": null, "code": "!function (e, n) {\n  \"object\" == typeof exports && \"undefined\" != typeof module ? module.exports = n() : \"function\" == typeof define && define.amd ? define(n) : (e = \"undefined\" != typeof globalThis ? globalThis : e || self).dayjs_plugin_isoWeeksInYear = n();\n}(this, function () {\n  \"use strict\";\n\n  return function (e, n) {\n    n.prototype.isoWeeksInYear = function () {\n      var e = this.isLeapYear(),\n        n = this.endOf(\"y\").day();\n      return 4 === n || e && 5 === n ? 53 : 52;\n    };\n  };\n});", "map": {"version": 3, "names": ["e", "n", "exports", "module", "define", "amd", "globalThis", "self", "dayjs_plugin_isoWeeksInYear", "prototype", "isoWeeksInYear", "isLeapYear", "endOf", "day"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/dayjs/plugin/isoWeeksInYear.js"], "sourcesContent": ["!function(e,n){\"object\"==typeof exports&&\"undefined\"!=typeof module?module.exports=n():\"function\"==typeof define&&define.amd?define(n):(e=\"undefined\"!=typeof globalThis?globalThis:e||self).dayjs_plugin_isoWeeksInYear=n()}(this,(function(){\"use strict\";return function(e,n){n.prototype.isoWeeksInYear=function(){var e=this.isLeapYear(),n=this.endOf(\"y\").day();return 4===n||e&&5===n?53:52}}}));"], "mappings": "AAAA,CAAC,UAASA,CAAC,EAACC,CAAC,EAAC;EAAC,QAAQ,IAAE,OAAOC,OAAO,IAAE,WAAW,IAAE,OAAOC,MAAM,GAACA,MAAM,CAACD,OAAO,GAACD,CAAC,CAAC,CAAC,GAAC,UAAU,IAAE,OAAOG,MAAM,IAAEA,MAAM,CAACC,GAAG,GAACD,MAAM,CAACH,CAAC,CAAC,GAAC,CAACD,CAAC,GAAC,WAAW,IAAE,OAAOM,UAAU,GAACA,UAAU,GAACN,CAAC,IAAEO,IAAI,EAAEC,2BAA2B,GAACP,CAAC,CAAC,CAAC;AAAA,CAAC,CAAC,IAAI,EAAE,YAAU;EAAC,YAAY;;EAAC,OAAO,UAASD,CAAC,EAACC,CAAC,EAAC;IAACA,CAAC,CAACQ,SAAS,CAACC,cAAc,GAAC,YAAU;MAAC,IAAIV,CAAC,GAAC,IAAI,CAACW,UAAU,CAAC,CAAC;QAACV,CAAC,GAAC,IAAI,CAACW,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC;MAAC,OAAO,CAAC,KAAGZ,CAAC,IAAED,CAAC,IAAE,CAAC,KAAGC,CAAC,GAAC,EAAE,GAAC,EAAE;IAAA,CAAC;EAAA,CAAC;AAAA,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}