{"ast": null, "code": "/**\n * UI組件統一導出\n */\n\nexport { default as LoadingSpinner } from './LoadingSpinner';\nexport { default as ErrorMessage } from './ErrorMessage';\nexport { default as SuccessMessage } from './SuccessMessage';\nexport default {\n  LoadingSpinner,\n  ErrorMessage,\n  SuccessMessage\n};", "map": {"version": 3, "names": ["default", "LoadingSpinner", "ErrorMessage", "SuccessMessage"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/UI/index.js"], "sourcesContent": ["/**\n * UI組件統一導出\n */\n\nexport { default as LoadingSpinner } from './LoadingSpinner';\nexport { default as ErrorMessage } from './ErrorMessage';\nexport { default as SuccessMessage } from './SuccessMessage';\n\nexport default {\n  LoadingSpinner,\n  ErrorMessage,\n  SuccessMessage\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,OAAO,IAAIC,cAAc,QAAQ,kBAAkB;AAC5D,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB;AACxD,SAASF,OAAO,IAAIG,cAAc,QAAQ,kBAAkB;AAE5D,eAAe;EACbF,cAAc;EACdC,YAAY;EACZC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}