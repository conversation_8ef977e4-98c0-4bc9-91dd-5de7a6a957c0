{"ast": null, "code": "import { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport useLatest from '../useLatest';\nvar useMutationObserver = function (callback, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var callbackRef = useLatest(callback);\n  useDeepCompareEffectWithTarget(function () {\n    var element = getTargetElement(target);\n    if (!element) {\n      return;\n    }\n    var observer = new MutationObserver(callbackRef.current);\n    observer.observe(element, options);\n    return function () {\n      observer === null || observer === void 0 ? void 0 : observer.disconnect();\n    };\n  }, [options], target);\n};\nexport default useMutationObserver;", "map": {"version": 3, "names": ["getTargetElement", "useDeepCompareEffectWithTarget", "useLatest", "useMutationObserver", "callback", "target", "options", "callback<PERSON><PERSON>", "element", "observer", "MutationObserver", "current", "observe", "disconnect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useMutationObserver/index.js"], "sourcesContent": ["import { getTargetElement } from '../utils/domTarget';\nimport useDeepCompareEffectWithTarget from '../utils/useDeepCompareWithTarget';\nimport useLatest from '../useLatest';\nvar useMutationObserver = function (callback, target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var callbackRef = useLatest(callback);\n  useDeepCompareEffectWithTarget(function () {\n    var element = getTargetElement(target);\n    if (!element) {\n      return;\n    }\n    var observer = new MutationObserver(callbackRef.current);\n    observer.observe(element, options);\n    return function () {\n      observer === null || observer === void 0 ? void 0 : observer.disconnect();\n    };\n  }, [options], target);\n};\nexport default useMutationObserver;"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,8BAA8B,MAAM,mCAAmC;AAC9E,OAAOC,SAAS,MAAM,cAAc;AACpC,IAAIC,mBAAmB,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,MAAM,EAAEC,OAAO,EAAE;EAC7D,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,WAAW,GAAGL,SAAS,CAACE,QAAQ,CAAC;EACrCH,8BAA8B,CAAC,YAAY;IACzC,IAAIO,OAAO,GAAGR,gBAAgB,CAACK,MAAM,CAAC;IACtC,IAAI,CAACG,OAAO,EAAE;MACZ;IACF;IACA,IAAIC,QAAQ,GAAG,IAAIC,gBAAgB,CAACH,WAAW,CAACI,OAAO,CAAC;IACxDF,QAAQ,CAACG,OAAO,CAACJ,OAAO,EAAEF,OAAO,CAAC;IAClC,OAAO,YAAY;MACjBG,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACI,UAAU,CAAC,CAAC;IAC3E,CAAC;EACH,CAAC,EAAE,CAACP,OAAO,CAAC,EAAED,MAAM,CAAC;AACvB,CAAC;AACD,eAAeF,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}