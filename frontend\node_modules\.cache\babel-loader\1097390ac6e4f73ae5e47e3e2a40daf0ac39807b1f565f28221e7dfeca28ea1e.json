{"ast": null, "code": "import { __read, __values } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar subscribers = new Set();\nvar info;\nvar responsiveConfig = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nfunction handleResize() {\n  var e_1, _a;\n  var oldInfo = info;\n  calculate();\n  if (oldInfo === info) return;\n  try {\n    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {\n      var subscriber = subscribers_1_1.value;\n      subscriber();\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1.return)) _a.call(subscribers_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n}\nvar listening = false;\nfunction calculate() {\n  var e_2, _a;\n  var width = window.innerWidth;\n  var newInfo = {};\n  var shouldUpdate = false;\n  try {\n    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {\n      var key = _c.value;\n      newInfo[key] = width >= responsiveConfig[key];\n      if (newInfo[key] !== info[key]) {\n        shouldUpdate = true;\n      }\n    }\n  } catch (e_2_1) {\n    e_2 = {\n      error: e_2_1\n    };\n  } finally {\n    try {\n      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n    } finally {\n      if (e_2) throw e_2.error;\n    }\n  }\n  if (shouldUpdate) {\n    info = newInfo;\n  }\n}\nexport function configResponsive(config) {\n  responsiveConfig = config;\n  if (info) calculate();\n}\nfunction useResponsive() {\n  if (isBrowser && !listening) {\n    info = {};\n    calculate();\n    window.addEventListener('resize', handleResize);\n    listening = true;\n  }\n  var _a = __read(useState(info), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    if (!isBrowser) return;\n    // In React 18's StrictMode, useEffect perform twice, resize listener is remove, so handleResize is never perform.\n    // https://github.com/alibaba/hooks/issues/1910\n    if (!listening) {\n      window.addEventListener('resize', handleResize);\n    }\n    var subscriber = function () {\n      setState(info);\n    };\n    subscribers.add(subscriber);\n    return function () {\n      subscribers.delete(subscriber);\n      if (subscribers.size === 0) {\n        window.removeEventListener('resize', handleResize);\n        listening = false;\n      }\n    };\n  }, []);\n  return state;\n}\nexport default useResponsive;", "map": {"version": 3, "names": ["__read", "__values", "useEffect", "useState", "<PERSON><PERSON><PERSON><PERSON>", "subscribers", "Set", "info", "responsiveConfig", "xs", "sm", "md", "lg", "xl", "handleResize", "e_1", "_a", "oldInfo", "calculate", "subscribers_1", "subscribers_1_1", "next", "done", "subscriber", "value", "e_1_1", "error", "return", "call", "listening", "e_2", "width", "window", "innerWidth", "newInfo", "shouldUpdate", "_b", "Object", "keys", "_c", "key", "e_2_1", "configResponsive", "config", "useResponsive", "addEventListener", "state", "setState", "add", "delete", "size", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useResponsive/index.js"], "sourcesContent": ["import { __read, __values } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport isBrowser from '../utils/isBrowser';\nvar subscribers = new Set();\nvar info;\nvar responsiveConfig = {\n  xs: 0,\n  sm: 576,\n  md: 768,\n  lg: 992,\n  xl: 1200\n};\nfunction handleResize() {\n  var e_1, _a;\n  var oldInfo = info;\n  calculate();\n  if (oldInfo === info) return;\n  try {\n    for (var subscribers_1 = __values(subscribers), subscribers_1_1 = subscribers_1.next(); !subscribers_1_1.done; subscribers_1_1 = subscribers_1.next()) {\n      var subscriber = subscribers_1_1.value;\n      subscriber();\n    }\n  } catch (e_1_1) {\n    e_1 = {\n      error: e_1_1\n    };\n  } finally {\n    try {\n      if (subscribers_1_1 && !subscribers_1_1.done && (_a = subscribers_1.return)) _a.call(subscribers_1);\n    } finally {\n      if (e_1) throw e_1.error;\n    }\n  }\n}\nvar listening = false;\nfunction calculate() {\n  var e_2, _a;\n  var width = window.innerWidth;\n  var newInfo = {};\n  var shouldUpdate = false;\n  try {\n    for (var _b = __values(Object.keys(responsiveConfig)), _c = _b.next(); !_c.done; _c = _b.next()) {\n      var key = _c.value;\n      newInfo[key] = width >= responsiveConfig[key];\n      if (newInfo[key] !== info[key]) {\n        shouldUpdate = true;\n      }\n    }\n  } catch (e_2_1) {\n    e_2 = {\n      error: e_2_1\n    };\n  } finally {\n    try {\n      if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n    } finally {\n      if (e_2) throw e_2.error;\n    }\n  }\n  if (shouldUpdate) {\n    info = newInfo;\n  }\n}\nexport function configResponsive(config) {\n  responsiveConfig = config;\n  if (info) calculate();\n}\nfunction useResponsive() {\n  if (isBrowser && !listening) {\n    info = {};\n    calculate();\n    window.addEventListener('resize', handleResize);\n    listening = true;\n  }\n  var _a = __read(useState(info), 2),\n    state = _a[0],\n    setState = _a[1];\n  useEffect(function () {\n    if (!isBrowser) return;\n    // In React 18's StrictMode, useEffect perform twice, resize listener is remove, so handleResize is never perform.\n    // https://github.com/alibaba/hooks/issues/1910\n    if (!listening) {\n      window.addEventListener('resize', handleResize);\n    }\n    var subscriber = function () {\n      setState(info);\n    };\n    subscribers.add(subscriber);\n    return function () {\n      subscribers.delete(subscriber);\n      if (subscribers.size === 0) {\n        window.removeEventListener('resize', handleResize);\n        listening = false;\n      }\n    };\n  }, []);\n  return state;\n}\nexport default useResponsive;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,WAAW,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B,IAAIC,IAAI;AACR,IAAIC,gBAAgB,GAAG;EACrBC,EAAE,EAAE,CAAC;EACLC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE,GAAG;EACPC,EAAE,EAAE;AACN,CAAC;AACD,SAASC,YAAYA,CAAA,EAAG;EACtB,IAAIC,GAAG,EAAEC,EAAE;EACX,IAAIC,OAAO,GAAGV,IAAI;EAClBW,SAAS,CAAC,CAAC;EACX,IAAID,OAAO,KAAKV,IAAI,EAAE;EACtB,IAAI;IACF,KAAK,IAAIY,aAAa,GAAGlB,QAAQ,CAACI,WAAW,CAAC,EAAEe,eAAe,GAAGD,aAAa,CAACE,IAAI,CAAC,CAAC,EAAE,CAACD,eAAe,CAACE,IAAI,EAAEF,eAAe,GAAGD,aAAa,CAACE,IAAI,CAAC,CAAC,EAAE;MACrJ,IAAIE,UAAU,GAAGH,eAAe,CAACI,KAAK;MACtCD,UAAU,CAAC,CAAC;IACd;EACF,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdV,GAAG,GAAG;MACJW,KAAK,EAAED;IACT,CAAC;EACH,CAAC,SAAS;IACR,IAAI;MACF,IAAIL,eAAe,IAAI,CAACA,eAAe,CAACE,IAAI,KAAKN,EAAE,GAAGG,aAAa,CAACQ,MAAM,CAAC,EAAEX,EAAE,CAACY,IAAI,CAACT,aAAa,CAAC;IACrG,CAAC,SAAS;MACR,IAAIJ,GAAG,EAAE,MAAMA,GAAG,CAACW,KAAK;IAC1B;EACF;AACF;AACA,IAAIG,SAAS,GAAG,KAAK;AACrB,SAASX,SAASA,CAAA,EAAG;EACnB,IAAIY,GAAG,EAAEd,EAAE;EACX,IAAIe,KAAK,GAAGC,MAAM,CAACC,UAAU;EAC7B,IAAIC,OAAO,GAAG,CAAC,CAAC;EAChB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAI;IACF,KAAK,IAAIC,EAAE,GAAGnC,QAAQ,CAACoC,MAAM,CAACC,IAAI,CAAC9B,gBAAgB,CAAC,CAAC,EAAE+B,EAAE,GAAGH,EAAE,CAACf,IAAI,CAAC,CAAC,EAAE,CAACkB,EAAE,CAACjB,IAAI,EAAEiB,EAAE,GAAGH,EAAE,CAACf,IAAI,CAAC,CAAC,EAAE;MAC/F,IAAImB,GAAG,GAAGD,EAAE,CAACf,KAAK;MAClBU,OAAO,CAACM,GAAG,CAAC,GAAGT,KAAK,IAAIvB,gBAAgB,CAACgC,GAAG,CAAC;MAC7C,IAAIN,OAAO,CAACM,GAAG,CAAC,KAAKjC,IAAI,CAACiC,GAAG,CAAC,EAAE;QAC9BL,YAAY,GAAG,IAAI;MACrB;IACF;EACF,CAAC,CAAC,OAAOM,KAAK,EAAE;IACdX,GAAG,GAAG;MACJJ,KAAK,EAAEe;IACT,CAAC;EACH,CAAC,SAAS;IACR,IAAI;MACF,IAAIF,EAAE,IAAI,CAACA,EAAE,CAACjB,IAAI,KAAKN,EAAE,GAAGoB,EAAE,CAACT,MAAM,CAAC,EAAEX,EAAE,CAACY,IAAI,CAACQ,EAAE,CAAC;IACrD,CAAC,SAAS;MACR,IAAIN,GAAG,EAAE,MAAMA,GAAG,CAACJ,KAAK;IAC1B;EACF;EACA,IAAIS,YAAY,EAAE;IAChB5B,IAAI,GAAG2B,OAAO;EAChB;AACF;AACA,OAAO,SAASQ,gBAAgBA,CAACC,MAAM,EAAE;EACvCnC,gBAAgB,GAAGmC,MAAM;EACzB,IAAIpC,IAAI,EAAEW,SAAS,CAAC,CAAC;AACvB;AACA,SAAS0B,aAAaA,CAAA,EAAG;EACvB,IAAIxC,SAAS,IAAI,CAACyB,SAAS,EAAE;IAC3BtB,IAAI,GAAG,CAAC,CAAC;IACTW,SAAS,CAAC,CAAC;IACXc,MAAM,CAACa,gBAAgB,CAAC,QAAQ,EAAE/B,YAAY,CAAC;IAC/Ce,SAAS,GAAG,IAAI;EAClB;EACA,IAAIb,EAAE,GAAGhB,MAAM,CAACG,QAAQ,CAACI,IAAI,CAAC,EAAE,CAAC,CAAC;IAChCuC,KAAK,GAAG9B,EAAE,CAAC,CAAC,CAAC;IACb+B,QAAQ,GAAG/B,EAAE,CAAC,CAAC,CAAC;EAClBd,SAAS,CAAC,YAAY;IACpB,IAAI,CAACE,SAAS,EAAE;IAChB;IACA;IACA,IAAI,CAACyB,SAAS,EAAE;MACdG,MAAM,CAACa,gBAAgB,CAAC,QAAQ,EAAE/B,YAAY,CAAC;IACjD;IACA,IAAIS,UAAU,GAAG,SAAAA,CAAA,EAAY;MAC3BwB,QAAQ,CAACxC,IAAI,CAAC;IAChB,CAAC;IACDF,WAAW,CAAC2C,GAAG,CAACzB,UAAU,CAAC;IAC3B,OAAO,YAAY;MACjBlB,WAAW,CAAC4C,MAAM,CAAC1B,UAAU,CAAC;MAC9B,IAAIlB,WAAW,CAAC6C,IAAI,KAAK,CAAC,EAAE;QAC1BlB,MAAM,CAACmB,mBAAmB,CAAC,QAAQ,EAAErC,YAAY,CAAC;QAClDe,SAAS,GAAG,KAAK;MACnB;IACF,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOiB,KAAK;AACd;AACA,eAAeF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}