{"ast": null, "code": "import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nimport { useRef } from 'react';\nvar useDrop = function (target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  // https://stackoverflow.com/a/26459269\n  var dragEnterTarget = useRef();\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onData = function (dataTransfer, event) {\n      var uri = dataTransfer.getData('text/uri-list');\n      var dom = dataTransfer.getData('custom');\n      if (dom && optionsRef.current.onDom) {\n        var data = dom;\n        try {\n          data = JSON.parse(dom);\n        } catch (e) {\n          data = dom;\n        }\n        optionsRef.current.onDom(data, event);\n        return;\n      }\n      if (uri && optionsRef.current.onUri) {\n        optionsRef.current.onUri(uri, event);\n        return;\n      }\n      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {\n        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);\n        return;\n      }\n      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {\n        dataTransfer.items[0].getAsString(function (text) {\n          optionsRef.current.onText(text, event);\n        });\n      }\n    };\n    var onDragEnter = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      event.stopPropagation();\n      dragEnterTarget.current = event.target;\n      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragOver = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragLeave = function (event) {\n      var _a, _b;\n      if (event.target === dragEnterTarget.current) {\n        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n    };\n    var onDrop = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      onData(event.dataTransfer, event);\n      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onPaste = function (event) {\n      var _a, _b;\n      onData(event.clipboardData, event);\n      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.addEventListener('dragenter', onDragEnter);\n    targetElement.addEventListener('dragover', onDragOver);\n    targetElement.addEventListener('dragleave', onDragLeave);\n    targetElement.addEventListener('drop', onDrop);\n    targetElement.addEventListener('paste', onPaste);\n    return function () {\n      targetElement.removeEventListener('dragenter', onDragEnter);\n      targetElement.removeEventListener('dragover', onDragOver);\n      targetElement.removeEventListener('dragleave', onDragLeave);\n      targetElement.removeEventListener('drop', onDrop);\n      targetElement.removeEventListener('paste', onPaste);\n    };\n  }, [], target);\n};\nexport default useDrop;", "map": {"version": 3, "names": ["useLatest", "getTargetElement", "useEffectWithTarget", "useRef", "useDrop", "target", "options", "optionsRef", "dragEnterTarget", "targetElement", "addEventListener", "onData", "dataTransfer", "event", "uri", "getData", "dom", "current", "onDom", "data", "JSON", "parse", "e", "onUri", "files", "length", "onFiles", "Array", "from", "items", "onText", "getAsString", "text", "onDragEnter", "_a", "_b", "preventDefault", "stopPropagation", "call", "onDragOver", "onDragLeave", "onDrop", "onPaste", "clipboardData", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDrop/index.js"], "sourcesContent": ["import useLatest from '../useLatest';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nimport { useRef } from 'react';\nvar useDrop = function (target, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var optionsRef = useLatest(options);\n  // https://stackoverflow.com/a/26459269\n  var dragEnterTarget = useRef();\n  useEffectWithTarget(function () {\n    var targetElement = getTargetElement(target);\n    if (!(targetElement === null || targetElement === void 0 ? void 0 : targetElement.addEventListener)) {\n      return;\n    }\n    var onData = function (dataTransfer, event) {\n      var uri = dataTransfer.getData('text/uri-list');\n      var dom = dataTransfer.getData('custom');\n      if (dom && optionsRef.current.onDom) {\n        var data = dom;\n        try {\n          data = JSON.parse(dom);\n        } catch (e) {\n          data = dom;\n        }\n        optionsRef.current.onDom(data, event);\n        return;\n      }\n      if (uri && optionsRef.current.onUri) {\n        optionsRef.current.onUri(uri, event);\n        return;\n      }\n      if (dataTransfer.files && dataTransfer.files.length && optionsRef.current.onFiles) {\n        optionsRef.current.onFiles(Array.from(dataTransfer.files), event);\n        return;\n      }\n      if (dataTransfer.items && dataTransfer.items.length && optionsRef.current.onText) {\n        dataTransfer.items[0].getAsString(function (text) {\n          optionsRef.current.onText(text, event);\n        });\n      }\n    };\n    var onDragEnter = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      event.stopPropagation();\n      dragEnterTarget.current = event.target;\n      (_b = (_a = optionsRef.current).onDragEnter) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragOver = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      (_b = (_a = optionsRef.current).onDragOver) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onDragLeave = function (event) {\n      var _a, _b;\n      if (event.target === dragEnterTarget.current) {\n        (_b = (_a = optionsRef.current).onDragLeave) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n      }\n    };\n    var onDrop = function (event) {\n      var _a, _b;\n      event.preventDefault();\n      onData(event.dataTransfer, event);\n      (_b = (_a = optionsRef.current).onDrop) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    var onPaste = function (event) {\n      var _a, _b;\n      onData(event.clipboardData, event);\n      (_b = (_a = optionsRef.current).onPaste) === null || _b === void 0 ? void 0 : _b.call(_a, event);\n    };\n    targetElement.addEventListener('dragenter', onDragEnter);\n    targetElement.addEventListener('dragover', onDragOver);\n    targetElement.addEventListener('dragleave', onDragLeave);\n    targetElement.addEventListener('drop', onDrop);\n    targetElement.addEventListener('paste', onPaste);\n    return function () {\n      targetElement.removeEventListener('dragenter', onDragEnter);\n      targetElement.removeEventListener('dragover', onDragOver);\n      targetElement.removeEventListener('dragleave', onDragLeave);\n      targetElement.removeEventListener('drop', onDrop);\n      targetElement.removeEventListener('paste', onPaste);\n    };\n  }, [], target);\n};\nexport default useDrop;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,cAAc;AACpC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,SAASC,MAAM,QAAQ,OAAO;AAC9B,IAAIC,OAAO,GAAG,SAAAA,CAAUC,MAAM,EAAEC,OAAO,EAAE;EACvC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,UAAU,GAAGP,SAAS,CAACM,OAAO,CAAC;EACnC;EACA,IAAIE,eAAe,GAAGL,MAAM,CAAC,CAAC;EAC9BD,mBAAmB,CAAC,YAAY;IAC9B,IAAIO,aAAa,GAAGR,gBAAgB,CAACI,MAAM,CAAC;IAC5C,IAAI,EAAEI,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACC,gBAAgB,CAAC,EAAE;MACnG;IACF;IACA,IAAIC,MAAM,GAAG,SAAAA,CAAUC,YAAY,EAAEC,KAAK,EAAE;MAC1C,IAAIC,GAAG,GAAGF,YAAY,CAACG,OAAO,CAAC,eAAe,CAAC;MAC/C,IAAIC,GAAG,GAAGJ,YAAY,CAACG,OAAO,CAAC,QAAQ,CAAC;MACxC,IAAIC,GAAG,IAAIT,UAAU,CAACU,OAAO,CAACC,KAAK,EAAE;QACnC,IAAIC,IAAI,GAAGH,GAAG;QACd,IAAI;UACFG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,GAAG,CAAC;QACxB,CAAC,CAAC,OAAOM,CAAC,EAAE;UACVH,IAAI,GAAGH,GAAG;QACZ;QACAT,UAAU,CAACU,OAAO,CAACC,KAAK,CAACC,IAAI,EAAEN,KAAK,CAAC;QACrC;MACF;MACA,IAAIC,GAAG,IAAIP,UAAU,CAACU,OAAO,CAACM,KAAK,EAAE;QACnChB,UAAU,CAACU,OAAO,CAACM,KAAK,CAACT,GAAG,EAAED,KAAK,CAAC;QACpC;MACF;MACA,IAAID,YAAY,CAACY,KAAK,IAAIZ,YAAY,CAACY,KAAK,CAACC,MAAM,IAAIlB,UAAU,CAACU,OAAO,CAACS,OAAO,EAAE;QACjFnB,UAAU,CAACU,OAAO,CAACS,OAAO,CAACC,KAAK,CAACC,IAAI,CAAChB,YAAY,CAACY,KAAK,CAAC,EAAEX,KAAK,CAAC;QACjE;MACF;MACA,IAAID,YAAY,CAACiB,KAAK,IAAIjB,YAAY,CAACiB,KAAK,CAACJ,MAAM,IAAIlB,UAAU,CAACU,OAAO,CAACa,MAAM,EAAE;QAChFlB,YAAY,CAACiB,KAAK,CAAC,CAAC,CAAC,CAACE,WAAW,CAAC,UAAUC,IAAI,EAAE;UAChDzB,UAAU,CAACU,OAAO,CAACa,MAAM,CAACE,IAAI,EAAEnB,KAAK,CAAC;QACxC,CAAC,CAAC;MACJ;IACF,CAAC;IACD,IAAIoB,WAAW,GAAG,SAAAA,CAAUpB,KAAK,EAAE;MACjC,IAAIqB,EAAE,EAAEC,EAAE;MACVtB,KAAK,CAACuB,cAAc,CAAC,CAAC;MACtBvB,KAAK,CAACwB,eAAe,CAAC,CAAC;MACvB7B,eAAe,CAACS,OAAO,GAAGJ,KAAK,CAACR,MAAM;MACtC,CAAC8B,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAO,EAAEgB,WAAW,MAAM,IAAI,IAAIE,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACJ,EAAE,EAAErB,KAAK,CAAC;IACtG,CAAC;IACD,IAAI0B,UAAU,GAAG,SAAAA,CAAU1B,KAAK,EAAE;MAChC,IAAIqB,EAAE,EAAEC,EAAE;MACVtB,KAAK,CAACuB,cAAc,CAAC,CAAC;MACtB,CAACD,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAO,EAAEsB,UAAU,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACJ,EAAE,EAAErB,KAAK,CAAC;IACrG,CAAC;IACD,IAAI2B,WAAW,GAAG,SAAAA,CAAU3B,KAAK,EAAE;MACjC,IAAIqB,EAAE,EAAEC,EAAE;MACV,IAAItB,KAAK,CAACR,MAAM,KAAKG,eAAe,CAACS,OAAO,EAAE;QAC5C,CAACkB,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAO,EAAEuB,WAAW,MAAM,IAAI,IAAIL,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACJ,EAAE,EAAErB,KAAK,CAAC;MACtG;IACF,CAAC;IACD,IAAI4B,MAAM,GAAG,SAAAA,CAAU5B,KAAK,EAAE;MAC5B,IAAIqB,EAAE,EAAEC,EAAE;MACVtB,KAAK,CAACuB,cAAc,CAAC,CAAC;MACtBzB,MAAM,CAACE,KAAK,CAACD,YAAY,EAAEC,KAAK,CAAC;MACjC,CAACsB,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAO,EAAEwB,MAAM,MAAM,IAAI,IAAIN,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACJ,EAAE,EAAErB,KAAK,CAAC;IACjG,CAAC;IACD,IAAI6B,OAAO,GAAG,SAAAA,CAAU7B,KAAK,EAAE;MAC7B,IAAIqB,EAAE,EAAEC,EAAE;MACVxB,MAAM,CAACE,KAAK,CAAC8B,aAAa,EAAE9B,KAAK,CAAC;MAClC,CAACsB,EAAE,GAAG,CAACD,EAAE,GAAG3B,UAAU,CAACU,OAAO,EAAEyB,OAAO,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,IAAI,CAACJ,EAAE,EAAErB,KAAK,CAAC;IAClG,CAAC;IACDJ,aAAa,CAACC,gBAAgB,CAAC,WAAW,EAAEuB,WAAW,CAAC;IACxDxB,aAAa,CAACC,gBAAgB,CAAC,UAAU,EAAE6B,UAAU,CAAC;IACtD9B,aAAa,CAACC,gBAAgB,CAAC,WAAW,EAAE8B,WAAW,CAAC;IACxD/B,aAAa,CAACC,gBAAgB,CAAC,MAAM,EAAE+B,MAAM,CAAC;IAC9ChC,aAAa,CAACC,gBAAgB,CAAC,OAAO,EAAEgC,OAAO,CAAC;IAChD,OAAO,YAAY;MACjBjC,aAAa,CAACmC,mBAAmB,CAAC,WAAW,EAAEX,WAAW,CAAC;MAC3DxB,aAAa,CAACmC,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAAC;MACzD9B,aAAa,CAACmC,mBAAmB,CAAC,WAAW,EAAEJ,WAAW,CAAC;MAC3D/B,aAAa,CAACmC,mBAAmB,CAAC,MAAM,EAAEH,MAAM,CAAC;MACjDhC,aAAa,CAACmC,mBAAmB,CAAC,OAAO,EAAEF,OAAO,CAAC;IACrD,CAAC;EACH,CAAC,EAAE,EAAE,EAAErC,MAAM,CAAC;AAChB,CAAC;AACD,eAAeD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}