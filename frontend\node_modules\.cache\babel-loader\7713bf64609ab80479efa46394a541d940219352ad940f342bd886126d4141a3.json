{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nvar useUpdate = function () {\n  var _a = __read(useState({}), 2),\n    setState = _a[1];\n  return useCallback(function () {\n    return setState({});\n  }, []);\n};\nexport default useUpdate;", "map": {"version": 3, "names": ["__read", "useCallback", "useState", "useUpdate", "_a", "setState"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useUpdate/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useCallback, useState } from 'react';\nvar useUpdate = function () {\n  var _a = __read(useState({}), 2),\n    setState = _a[1];\n  return useCallback(function () {\n    return setState({});\n  }, []);\n};\nexport default useUpdate;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,WAAW,EAAEC,QAAQ,QAAQ,OAAO;AAC7C,IAAIC,SAAS,GAAG,SAAAA,CAAA,EAAY;EAC1B,IAAIC,EAAE,GAAGJ,MAAM,CAACE,QAAQ,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC9BG,QAAQ,GAAGD,EAAE,CAAC,CAAC,CAAC;EAClB,OAAOH,WAAW,CAAC,YAAY;IAC7B,OAAOI,QAAQ,CAAC,CAAC,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;AACR,CAAC;AACD,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}