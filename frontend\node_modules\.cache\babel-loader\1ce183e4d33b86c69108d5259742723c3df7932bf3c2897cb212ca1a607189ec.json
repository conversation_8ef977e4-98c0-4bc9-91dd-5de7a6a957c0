{"ast": null, "code": "export function withCache(generate) {\n  let cache = null;\n  return () => {\n    if (cache === null) {\n      cache = generate();\n    }\n    return cache;\n  };\n}", "map": {"version": 3, "names": ["with<PERSON><PERSON>", "generate", "cache"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/utils/with-cache.js"], "sourcesContent": ["export function withCache(generate) {\n  let cache = null;\n  return () => {\n    if (cache === null) {\n      cache = generate();\n    }\n    return cache;\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,SAASA,CAACC,QAAQ,EAAE;EAClC,IAAIC,KAAK,GAAG,IAAI;EAChB,OAAO,MAAM;IACX,IAAIA,KAAK,KAAK,IAAI,EAAE;MAClBA,KAAK,GAAGD,QAAQ,CAAC,CAAC;IACpB;IACA,OAAOC,KAAK;EACd,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}