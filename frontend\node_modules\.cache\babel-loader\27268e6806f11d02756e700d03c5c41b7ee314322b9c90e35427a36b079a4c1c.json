{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport var STATUS_ADD = 'add';\nexport var STATUS_KEEP = 'keep';\nexport var STATUS_REMOVE = 'remove';\nexport var STATUS_REMOVED = 'removed';\nexport function wrapKeyToObject(key) {\n  var keyObj;\n  if (key && _typeof(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return _objectSpread(_objectSpread({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nexport function parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nexport function diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return _objectSpread(_objectSpread({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push(_objectSpread(_objectSpread({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push(_objectSpread(_objectSpread({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return _objectSpread(_objectSpread({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}", "map": {"version": 3, "names": ["_objectSpread", "_typeof", "STATUS_ADD", "STATUS_KEEP", "STATUS_REMOVE", "STATUS_REMOVED", "wrapKeyToObject", "key", "key<PERSON>bj", "String", "parse<PERSON>eys", "keys", "arguments", "length", "undefined", "map", "diff<PERSON>eys", "prevKeys", "currentKeys", "list", "currentIndex", "currentLen", "prevKeyObjects", "currentKeyObjects", "for<PERSON>ach", "hit", "i", "currentKeyObj", "concat", "slice", "obj", "status", "push", "_ref", "duplicatedKeys", "Object", "filter", "matchKey", "_ref2", "node"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-motion/es/util/diff.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _typeof from \"@babel/runtime/helpers/esm/typeof\";\nexport var STATUS_ADD = 'add';\nexport var STATUS_KEEP = 'keep';\nexport var STATUS_REMOVE = 'remove';\nexport var STATUS_REMOVED = 'removed';\nexport function wrapKeyToObject(key) {\n  var keyObj;\n  if (key && _typeof(key) === 'object' && 'key' in key) {\n    keyObj = key;\n  } else {\n    keyObj = {\n      key: key\n    };\n  }\n  return _objectSpread(_objectSpread({}, keyObj), {}, {\n    key: String(keyObj.key)\n  });\n}\nexport function parseKeys() {\n  var keys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  return keys.map(wrapKeyToObject);\n}\nexport function diffKeys() {\n  var prevKeys = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n  var currentKeys = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n  var list = [];\n  var currentIndex = 0;\n  var currentLen = currentKeys.length;\n  var prevKeyObjects = parseKeys(prevKeys);\n  var currentKeyObjects = parseKeys(currentKeys);\n\n  // Check prev keys to insert or keep\n  prevKeyObjects.forEach(function (keyObj) {\n    var hit = false;\n    for (var i = currentIndex; i < currentLen; i += 1) {\n      var currentKeyObj = currentKeyObjects[i];\n      if (currentKeyObj.key === keyObj.key) {\n        // New added keys should add before current key\n        if (currentIndex < i) {\n          list = list.concat(currentKeyObjects.slice(currentIndex, i).map(function (obj) {\n            return _objectSpread(_objectSpread({}, obj), {}, {\n              status: STATUS_ADD\n            });\n          }));\n          currentIndex = i;\n        }\n        list.push(_objectSpread(_objectSpread({}, currentKeyObj), {}, {\n          status: STATUS_KEEP\n        }));\n        currentIndex += 1;\n        hit = true;\n        break;\n      }\n    }\n\n    // If not hit, it means key is removed\n    if (!hit) {\n      list.push(_objectSpread(_objectSpread({}, keyObj), {}, {\n        status: STATUS_REMOVE\n      }));\n    }\n  });\n\n  // Add rest to the list\n  if (currentIndex < currentLen) {\n    list = list.concat(currentKeyObjects.slice(currentIndex).map(function (obj) {\n      return _objectSpread(_objectSpread({}, obj), {}, {\n        status: STATUS_ADD\n      });\n    }));\n  }\n\n  /**\n   * Merge same key when it remove and add again:\n   *    [1 - add, 2 - keep, 1 - remove] -> [1 - keep, 2 - keep]\n   */\n  var keys = {};\n  list.forEach(function (_ref) {\n    var key = _ref.key;\n    keys[key] = (keys[key] || 0) + 1;\n  });\n  var duplicatedKeys = Object.keys(keys).filter(function (key) {\n    return keys[key] > 1;\n  });\n  duplicatedKeys.forEach(function (matchKey) {\n    // Remove `STATUS_REMOVE` node.\n    list = list.filter(function (_ref2) {\n      var key = _ref2.key,\n        status = _ref2.status;\n      return key !== matchKey || status !== STATUS_REMOVE;\n    });\n\n    // Update `STATUS_ADD` to `STATUS_KEEP`\n    list.forEach(function (node) {\n      if (node.key === matchKey) {\n        // eslint-disable-next-line no-param-reassign\n        node.status = STATUS_KEEP;\n      }\n    });\n  });\n  return list;\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,0CAA0C;AACpE,OAAOC,OAAO,MAAM,mCAAmC;AACvD,OAAO,IAAIC,UAAU,GAAG,KAAK;AAC7B,OAAO,IAAIC,WAAW,GAAG,MAAM;AAC/B,OAAO,IAAIC,aAAa,GAAG,QAAQ;AACnC,OAAO,IAAIC,cAAc,GAAG,SAAS;AACrC,OAAO,SAASC,eAAeA,CAACC,GAAG,EAAE;EACnC,IAAIC,MAAM;EACV,IAAID,GAAG,IAAIN,OAAO,CAACM,GAAG,CAAC,KAAK,QAAQ,IAAI,KAAK,IAAIA,GAAG,EAAE;IACpDC,MAAM,GAAGD,GAAG;EACd,CAAC,MAAM;IACLC,MAAM,GAAG;MACPD,GAAG,EAAEA;IACP,CAAC;EACH;EACA,OAAOP,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEQ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;IAClDD,GAAG,EAAEE,MAAM,CAACD,MAAM,CAACD,GAAG;EACxB,CAAC,CAAC;AACJ;AACA,OAAO,SAASG,SAASA,CAAA,EAAG;EAC1B,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACjF,OAAOD,IAAI,CAACI,GAAG,CAACT,eAAe,CAAC;AAClC;AACA,OAAO,SAASU,QAAQA,CAAA,EAAG;EACzB,IAAIC,QAAQ,GAAGL,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACrF,IAAIM,WAAW,GAAGN,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;EACxF,IAAIO,IAAI,GAAG,EAAE;EACb,IAAIC,YAAY,GAAG,CAAC;EACpB,IAAIC,UAAU,GAAGH,WAAW,CAACL,MAAM;EACnC,IAAIS,cAAc,GAAGZ,SAAS,CAACO,QAAQ,CAAC;EACxC,IAAIM,iBAAiB,GAAGb,SAAS,CAACQ,WAAW,CAAC;;EAE9C;EACAI,cAAc,CAACE,OAAO,CAAC,UAAUhB,MAAM,EAAE;IACvC,IAAIiB,GAAG,GAAG,KAAK;IACf,KAAK,IAAIC,CAAC,GAAGN,YAAY,EAAEM,CAAC,GAAGL,UAAU,EAAEK,CAAC,IAAI,CAAC,EAAE;MACjD,IAAIC,aAAa,GAAGJ,iBAAiB,CAACG,CAAC,CAAC;MACxC,IAAIC,aAAa,CAACpB,GAAG,KAAKC,MAAM,CAACD,GAAG,EAAE;QACpC;QACA,IAAIa,YAAY,GAAGM,CAAC,EAAE;UACpBP,IAAI,GAAGA,IAAI,CAACS,MAAM,CAACL,iBAAiB,CAACM,KAAK,CAACT,YAAY,EAAEM,CAAC,CAAC,CAACX,GAAG,CAAC,UAAUe,GAAG,EAAE;YAC7E,OAAO9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;cAC/CC,MAAM,EAAE7B;YACV,CAAC,CAAC;UACJ,CAAC,CAAC,CAAC;UACHkB,YAAY,GAAGM,CAAC;QAClB;QACAP,IAAI,CAACa,IAAI,CAAChC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE2B,aAAa,CAAC,EAAE,CAAC,CAAC,EAAE;UAC5DI,MAAM,EAAE5B;QACV,CAAC,CAAC,CAAC;QACHiB,YAAY,IAAI,CAAC;QACjBK,GAAG,GAAG,IAAI;QACV;MACF;IACF;;IAEA;IACA,IAAI,CAACA,GAAG,EAAE;MACRN,IAAI,CAACa,IAAI,CAAChC,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEQ,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;QACrDuB,MAAM,EAAE3B;MACV,CAAC,CAAC,CAAC;IACL;EACF,CAAC,CAAC;;EAEF;EACA,IAAIgB,YAAY,GAAGC,UAAU,EAAE;IAC7BF,IAAI,GAAGA,IAAI,CAACS,MAAM,CAACL,iBAAiB,CAACM,KAAK,CAACT,YAAY,CAAC,CAACL,GAAG,CAAC,UAAUe,GAAG,EAAE;MAC1E,OAAO9B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE8B,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE;QAC/CC,MAAM,EAAE7B;MACV,CAAC,CAAC;IACJ,CAAC,CAAC,CAAC;EACL;;EAEA;AACF;AACA;AACA;EACE,IAAIS,IAAI,GAAG,CAAC,CAAC;EACbQ,IAAI,CAACK,OAAO,CAAC,UAAUS,IAAI,EAAE;IAC3B,IAAI1B,GAAG,GAAG0B,IAAI,CAAC1B,GAAG;IAClBI,IAAI,CAACJ,GAAG,CAAC,GAAG,CAACI,IAAI,CAACJ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC;EAClC,CAAC,CAAC;EACF,IAAI2B,cAAc,GAAGC,MAAM,CAACxB,IAAI,CAACA,IAAI,CAAC,CAACyB,MAAM,CAAC,UAAU7B,GAAG,EAAE;IAC3D,OAAOI,IAAI,CAACJ,GAAG,CAAC,GAAG,CAAC;EACtB,CAAC,CAAC;EACF2B,cAAc,CAACV,OAAO,CAAC,UAAUa,QAAQ,EAAE;IACzC;IACAlB,IAAI,GAAGA,IAAI,CAACiB,MAAM,CAAC,UAAUE,KAAK,EAAE;MAClC,IAAI/B,GAAG,GAAG+B,KAAK,CAAC/B,GAAG;QACjBwB,MAAM,GAAGO,KAAK,CAACP,MAAM;MACvB,OAAOxB,GAAG,KAAK8B,QAAQ,IAAIN,MAAM,KAAK3B,aAAa;IACrD,CAAC,CAAC;;IAEF;IACAe,IAAI,CAACK,OAAO,CAAC,UAAUe,IAAI,EAAE;MAC3B,IAAIA,IAAI,CAAChC,GAAG,KAAK8B,QAAQ,EAAE;QACzB;QACAE,IAAI,CAACR,MAAM,GAAG5B,WAAW;MAC3B;IACF,CAAC,CAAC;EACJ,CAAC,CAAC;EACF,OAAOgB,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}