/**
 * 圖片預覽組件
 */

import React from 'react';
import { Card, Button, Space, Image } from 'antd-mobile';
import { DeleteOutline, ReloadOutline, EyeOutline } from 'antd-mobile-icons';

const ImagePreview = ({
  image,
  side = 'front',
  onRemove,
  onRetake,
  onPreview,
  showActions = true,
  style = {},
  className = ''
}) => {
  if (!image || !image.preview) {
    return null;
  }

  const sideText = side === 'front' ? '正面' : '反面';

  return (
    <Card 
      className={`image-preview ${className}`}
      style={{
        marginBottom: '16px',
        ...style
      }}
      title={`${sideText}名片`}
    >
      <div style={{ textAlign: 'center' }}>
        {/* 圖片預覽 */}
        <div 
          style={{
            position: 'relative',
            display: 'inline-block',
            marginBottom: showActions ? '16px' : '0'
          }}
        >
          <Image
            src={image.preview}
            alt={`${sideText}名片`}
            style={{
              maxWidth: '100%',
              maxHeight: '200px',
              borderRadius: '8px',
              border: '1px solid #d9d9d9'
            }}
            fit="contain"
            onClick={onPreview}
          />
          
          {/* OCR狀態指示器 */}
          {image.parseStatus && (
            <div
              style={{
                position: 'absolute',
                top: '8px',
                right: '8px',
                padding: '4px 8px',
                borderRadius: '12px',
                fontSize: '12px',
                color: 'white',
                backgroundColor: 
                  image.parseStatus === 'success' ? '#52c41a' :
                  image.parseStatus === 'processing' ? '#1677ff' :
                  image.parseStatus === 'error' ? '#ff4d4f' : '#8c8c8c'
              }}
            >
              {image.parseStatus === 'success' ? '已識別' :
               image.parseStatus === 'processing' ? '識別中' :
               image.parseStatus === 'error' ? '識別失敗' : '待識別'}
            </div>
          )}
        </div>

        {/* 圖片信息 */}
        <div style={{ marginBottom: showActions ? '16px' : '0' }}>
          {image.file && (
            <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
              {image.file.name} ({Math.round(image.file.size / 1024)}KB)
            </div>
          )}
          
          {image.ocrText && (
            <div 
              style={{ 
                fontSize: '12px', 
                color: '#52c41a',
                marginTop: '4px'
              }}
            >
              OCR識別完成
            </div>
          )}
        </div>

        {/* 操作按鈕 */}
        {showActions && (
          <Space>
            {onPreview && (
              <Button
                size="small"
                fill="outline"
                onClick={onPreview}
              >
                <EyeOutline /> 查看
              </Button>
            )}
            
            {onRetake && (
              <Button
                size="small"
                color="primary"
                fill="outline"
                onClick={() => onRetake(side)}
              >
                <ReloadOutline /> 重拍
              </Button>
            )}
            
            {onRemove && (
              <Button
                size="small"
                color="danger"
                fill="outline"
                onClick={() => onRemove(side)}
              >
                <DeleteOutline /> 移除
              </Button>
            )}
          </Space>
        )}
      </div>
    </Card>
  );
};

export default ImagePreview;
