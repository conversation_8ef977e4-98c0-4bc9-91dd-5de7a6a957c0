{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdate from '../useUpdate';\nfunction useControllableValue(defaultProps, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var props = defaultProps !== null && defaultProps !== void 0 ? defaultProps : {};\n  var defaultValue = options.defaultValue,\n    _a = options.defaultValuePropName,\n    defaultValuePropName = _a === void 0 ? 'defaultValue' : _a,\n    _b = options.valuePropName,\n    valuePropName = _b === void 0 ? 'value' : _b,\n    _c = options.trigger,\n    trigger = _c === void 0 ? 'onChange' : _c;\n  var value = props[valuePropName];\n  var isControlled = Object.prototype.hasOwnProperty.call(props, valuePropName);\n  var initialValue = useMemo(function () {\n    if (isControlled) {\n      return value;\n    }\n    if (Object.prototype.hasOwnProperty.call(props, defaultValuePropName)) {\n      return props[defaultValuePropName];\n    }\n    return defaultValue;\n  }, []);\n  var stateRef = useRef(initialValue);\n  if (isControlled) {\n    stateRef.current = value;\n  }\n  var update = useUpdate();\n  function setState(v) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    var r = isFunction(v) ? v(stateRef.current) : v;\n    if (!isControlled) {\n      stateRef.current = r;\n      update();\n    }\n    if (props[trigger]) {\n      props[trigger].apply(props, __spreadArray([r], __read(args), false));\n    }\n  }\n  return [stateRef.current, useMemoizedFn(setState)];\n}\nexport default useControllableValue;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "useMemo", "useRef", "isFunction", "useMemoizedFn", "useUpdate", "useControllableValue", "defaultProps", "options", "props", "defaultValue", "_a", "defaultValuePropName", "_b", "valuePropName", "_c", "trigger", "value", "isControlled", "Object", "prototype", "hasOwnProperty", "call", "initialValue", "stateRef", "current", "update", "setState", "v", "args", "_i", "arguments", "length", "r", "apply"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useControllableValue/index.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { useMemo, useRef } from 'react';\nimport { isFunction } from '../utils';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdate from '../useUpdate';\nfunction useControllableValue(defaultProps, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var props = defaultProps !== null && defaultProps !== void 0 ? defaultProps : {};\n  var defaultValue = options.defaultValue,\n    _a = options.defaultValuePropName,\n    defaultValuePropName = _a === void 0 ? 'defaultValue' : _a,\n    _b = options.valuePropName,\n    valuePropName = _b === void 0 ? 'value' : _b,\n    _c = options.trigger,\n    trigger = _c === void 0 ? 'onChange' : _c;\n  var value = props[valuePropName];\n  var isControlled = Object.prototype.hasOwnProperty.call(props, valuePropName);\n  var initialValue = useMemo(function () {\n    if (isControlled) {\n      return value;\n    }\n    if (Object.prototype.hasOwnProperty.call(props, defaultValuePropName)) {\n      return props[defaultValuePropName];\n    }\n    return defaultValue;\n  }, []);\n  var stateRef = useRef(initialValue);\n  if (isControlled) {\n    stateRef.current = value;\n  }\n  var update = useUpdate();\n  function setState(v) {\n    var args = [];\n    for (var _i = 1; _i < arguments.length; _i++) {\n      args[_i - 1] = arguments[_i];\n    }\n    var r = isFunction(v) ? v(stateRef.current) : v;\n    if (!isControlled) {\n      stateRef.current = r;\n      update();\n    }\n    if (props[trigger]) {\n      props[trigger].apply(props, __spreadArray([r], __read(args), false));\n    }\n  }\n  return [stateRef.current, useMemoizedFn(setState)];\n}\nexport default useControllableValue;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,OAAO,EAAEC,MAAM,QAAQ,OAAO;AACvC,SAASC,UAAU,QAAQ,UAAU;AACrC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,oBAAoBA,CAACC,YAAY,EAAEC,OAAO,EAAE;EACnD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,KAAK,GAAGF,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAGA,YAAY,GAAG,CAAC,CAAC;EAChF,IAAIG,YAAY,GAAGF,OAAO,CAACE,YAAY;IACrCC,EAAE,GAAGH,OAAO,CAACI,oBAAoB;IACjCA,oBAAoB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,cAAc,GAAGA,EAAE;IAC1DE,EAAE,GAAGL,OAAO,CAACM,aAAa;IAC1BA,aAAa,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,OAAO,GAAGA,EAAE;IAC5CE,EAAE,GAAGP,OAAO,CAACQ,OAAO;IACpBA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,UAAU,GAAGA,EAAE;EAC3C,IAAIE,KAAK,GAAGR,KAAK,CAACK,aAAa,CAAC;EAChC,IAAII,YAAY,GAAGC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACb,KAAK,EAAEK,aAAa,CAAC;EAC7E,IAAIS,YAAY,GAAGtB,OAAO,CAAC,YAAY;IACrC,IAAIiB,YAAY,EAAE;MAChB,OAAOD,KAAK;IACd;IACA,IAAIE,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACb,KAAK,EAAEG,oBAAoB,CAAC,EAAE;MACrE,OAAOH,KAAK,CAACG,oBAAoB,CAAC;IACpC;IACA,OAAOF,YAAY;EACrB,CAAC,EAAE,EAAE,CAAC;EACN,IAAIc,QAAQ,GAAGtB,MAAM,CAACqB,YAAY,CAAC;EACnC,IAAIL,YAAY,EAAE;IAChBM,QAAQ,CAACC,OAAO,GAAGR,KAAK;EAC1B;EACA,IAAIS,MAAM,GAAGrB,SAAS,CAAC,CAAC;EACxB,SAASsB,QAAQA,CAACC,CAAC,EAAE;IACnB,IAAIC,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,GAAG,CAAC,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC9B;IACA,IAAIG,CAAC,GAAG9B,UAAU,CAACyB,CAAC,CAAC,GAAGA,CAAC,CAACJ,QAAQ,CAACC,OAAO,CAAC,GAAGG,CAAC;IAC/C,IAAI,CAACV,YAAY,EAAE;MACjBM,QAAQ,CAACC,OAAO,GAAGQ,CAAC;MACpBP,MAAM,CAAC,CAAC;IACV;IACA,IAAIjB,KAAK,CAACO,OAAO,CAAC,EAAE;MAClBP,KAAK,CAACO,OAAO,CAAC,CAACkB,KAAK,CAACzB,KAAK,EAAET,aAAa,CAAC,CAACiC,CAAC,CAAC,EAAElC,MAAM,CAAC8B,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC;IACtE;EACF;EACA,OAAO,CAACL,QAAQ,CAACC,OAAO,EAAErB,aAAa,CAACuB,QAAQ,CAAC,CAAC;AACpD;AACA,eAAerB,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}