{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState } from \"react\";\nimport useMemoizedFn from \"../useMemoizedFn\";\nimport isBrowser from \"../utils/isBrowser\";\nexport var ThemeMode;\n(function (ThemeMode) {\n  ThemeMode[\"LIGHT\"] = \"light\";\n  ThemeMode[\"DARK\"] = \"dark\";\n  ThemeMode[\"SYSTEM\"] = \"system\";\n})(ThemeMode || (ThemeMode = {}));\nvar useCurrentTheme = function () {\n  var matchMedia = isBrowser ? window.matchMedia(\"(prefers-color-scheme: dark)\") : undefined;\n  var _a = __read(useState(function () {\n      if (isBrowser) {\n        return (matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.matches) ? ThemeMode.DARK : ThemeMode.LIGHT;\n      } else {\n        return ThemeMode.LIGHT;\n      }\n    }), 2),\n    theme = _a[0],\n    setTheme = _a[1];\n  useEffect(function () {\n    var onThemeChange = function (event) {\n      if (event.matches) {\n        setTheme(ThemeMode.DARK);\n      } else {\n        setTheme(ThemeMode.LIGHT);\n      }\n    };\n    matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.addEventListener(\"change\", onThemeChange);\n    return function () {\n      matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.removeEventListener(\"change\", onThemeChange);\n    };\n  }, []);\n  return theme;\n};\nexport default function useTheme(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var localStorageKey = options.localStorageKey;\n  var _a = __read(useState(function () {\n      var preferredThemeMode = (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) && localStorage.getItem(localStorageKey);\n      return preferredThemeMode ? preferredThemeMode : ThemeMode.SYSTEM;\n    }), 2),\n    themeMode = _a[0],\n    setThemeMode = _a[1];\n  var setThemeModeWithLocalStorage = function (mode) {\n    setThemeMode(mode);\n    if (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) {\n      localStorage.setItem(localStorageKey, mode);\n    }\n  };\n  var currentTheme = useCurrentTheme();\n  var theme = themeMode === ThemeMode.SYSTEM ? currentTheme : themeMode;\n  return {\n    theme: theme,\n    themeMode: themeMode,\n    setThemeMode: useMemoizedFn(setThemeModeWithLocalStorage)\n  };\n}", "map": {"version": 3, "names": ["__read", "useEffect", "useState", "useMemoizedFn", "<PERSON><PERSON><PERSON><PERSON>", "ThemeMode", "useCurrentTheme", "matchMedia", "window", "undefined", "_a", "matches", "DARK", "LIGHT", "theme", "setTheme", "onThemeChange", "event", "addEventListener", "removeEventListener", "useTheme", "options", "localStorageKey", "preferredThemeMode", "length", "localStorage", "getItem", "SYSTEM", "themeMode", "setThemeMode", "setThemeModeWithLocalStorage", "mode", "setItem", "currentTheme"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useTheme/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useState } from \"react\";\nimport useMemoizedFn from \"../useMemoizedFn\";\nimport isBrowser from \"../utils/isBrowser\";\nexport var ThemeMode;\n(function (ThemeMode) {\n  ThemeMode[\"LIGHT\"] = \"light\";\n  ThemeMode[\"DARK\"] = \"dark\";\n  ThemeMode[\"SYSTEM\"] = \"system\";\n})(ThemeMode || (ThemeMode = {}));\nvar useCurrentTheme = function () {\n  var matchMedia = isBrowser ? window.matchMedia(\"(prefers-color-scheme: dark)\") : undefined;\n  var _a = __read(useState(function () {\n      if (isBrowser) {\n        return (matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.matches) ? ThemeMode.DARK : ThemeMode.LIGHT;\n      } else {\n        return ThemeMode.LIGHT;\n      }\n    }), 2),\n    theme = _a[0],\n    setTheme = _a[1];\n  useEffect(function () {\n    var onThemeChange = function (event) {\n      if (event.matches) {\n        setTheme(ThemeMode.DARK);\n      } else {\n        setTheme(ThemeMode.LIGHT);\n      }\n    };\n    matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.addEventListener(\"change\", onThemeChange);\n    return function () {\n      matchMedia === null || matchMedia === void 0 ? void 0 : matchMedia.removeEventListener(\"change\", onThemeChange);\n    };\n  }, []);\n  return theme;\n};\nexport default function useTheme(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var localStorageKey = options.localStorageKey;\n  var _a = __read(useState(function () {\n      var preferredThemeMode = (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) && localStorage.getItem(localStorageKey);\n      return preferredThemeMode ? preferredThemeMode : ThemeMode.SYSTEM;\n    }), 2),\n    themeMode = _a[0],\n    setThemeMode = _a[1];\n  var setThemeModeWithLocalStorage = function (mode) {\n    setThemeMode(mode);\n    if (localStorageKey === null || localStorageKey === void 0 ? void 0 : localStorageKey.length) {\n      localStorage.setItem(localStorageKey, mode);\n    }\n  };\n  var currentTheme = useCurrentTheme();\n  var theme = themeMode === ThemeMode.SYSTEM ? currentTheme : themeMode;\n  return {\n    theme: theme,\n    themeMode: themeMode,\n    setThemeMode: useMemoizedFn(setThemeModeWithLocalStorage)\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAO,IAAIC,SAAS;AACpB,CAAC,UAAUA,SAAS,EAAE;EACpBA,SAAS,CAAC,OAAO,CAAC,GAAG,OAAO;EAC5BA,SAAS,CAAC,MAAM,CAAC,GAAG,MAAM;EAC1BA,SAAS,CAAC,QAAQ,CAAC,GAAG,QAAQ;AAChC,CAAC,EAAEA,SAAS,KAAKA,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC;AACjC,IAAIC,eAAe,GAAG,SAAAA,CAAA,EAAY;EAChC,IAAIC,UAAU,GAAGH,SAAS,GAAGI,MAAM,CAACD,UAAU,CAAC,8BAA8B,CAAC,GAAGE,SAAS;EAC1F,IAAIC,EAAE,GAAGV,MAAM,CAACE,QAAQ,CAAC,YAAY;MACjC,IAAIE,SAAS,EAAE;QACb,OAAO,CAACG,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACI,OAAO,IAAIN,SAAS,CAACO,IAAI,GAAGP,SAAS,CAACQ,KAAK;MACxH,CAAC,MAAM;QACL,OAAOR,SAAS,CAACQ,KAAK;MACxB;IACF,CAAC,CAAC,EAAE,CAAC,CAAC;IACNC,KAAK,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACbK,QAAQ,GAAGL,EAAE,CAAC,CAAC,CAAC;EAClBT,SAAS,CAAC,YAAY;IACpB,IAAIe,aAAa,GAAG,SAAAA,CAAUC,KAAK,EAAE;MACnC,IAAIA,KAAK,CAACN,OAAO,EAAE;QACjBI,QAAQ,CAACV,SAAS,CAACO,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLG,QAAQ,CAACV,SAAS,CAACQ,KAAK,CAAC;MAC3B;IACF,CAAC;IACDN,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACW,gBAAgB,CAAC,QAAQ,EAAEF,aAAa,CAAC;IAC5G,OAAO,YAAY;MACjBT,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACY,mBAAmB,CAAC,QAAQ,EAAEH,aAAa,CAAC;IACjH,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOF,KAAK;AACd,CAAC;AACD,eAAe,SAASM,QAAQA,CAACC,OAAO,EAAE;EACxC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,eAAe,GAAGD,OAAO,CAACC,eAAe;EAC7C,IAAIZ,EAAE,GAAGV,MAAM,CAACE,QAAQ,CAAC,YAAY;MACjC,IAAIqB,kBAAkB,GAAG,CAACD,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACE,MAAM,KAAKC,YAAY,CAACC,OAAO,CAACJ,eAAe,CAAC;MAC5J,OAAOC,kBAAkB,GAAGA,kBAAkB,GAAGlB,SAAS,CAACsB,MAAM;IACnE,CAAC,CAAC,EAAE,CAAC,CAAC;IACNC,SAAS,GAAGlB,EAAE,CAAC,CAAC,CAAC;IACjBmB,YAAY,GAAGnB,EAAE,CAAC,CAAC,CAAC;EACtB,IAAIoB,4BAA4B,GAAG,SAAAA,CAAUC,IAAI,EAAE;IACjDF,YAAY,CAACE,IAAI,CAAC;IAClB,IAAIT,eAAe,KAAK,IAAI,IAAIA,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAACE,MAAM,EAAE;MAC5FC,YAAY,CAACO,OAAO,CAACV,eAAe,EAAES,IAAI,CAAC;IAC7C;EACF,CAAC;EACD,IAAIE,YAAY,GAAG3B,eAAe,CAAC,CAAC;EACpC,IAAIQ,KAAK,GAAGc,SAAS,KAAKvB,SAAS,CAACsB,MAAM,GAAGM,YAAY,GAAGL,SAAS;EACrE,OAAO;IACLd,KAAK,EAAEA,KAAK;IACZc,SAAS,EAAEA,SAAS;IACpBC,YAAY,EAAE1B,aAAa,CAAC2B,4BAA4B;EAC1D,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}