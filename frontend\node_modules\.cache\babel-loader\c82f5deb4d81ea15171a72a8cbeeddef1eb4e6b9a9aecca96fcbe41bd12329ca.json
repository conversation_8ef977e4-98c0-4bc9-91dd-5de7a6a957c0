{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.call = call;\nexports.default = void 0;\nexports.note = note;\nexports.noteOnce = noteOnce;\nexports.preMessage = void 0;\nexports.resetWarned = resetWarned;\nexports.warning = warning;\nexports.warningOnce = warningOnce;\n/* eslint-disable no-console */\nvar warned = {};\nvar preWarningFns = [];\n\n/**\n * Pre warning enable you to parse content before console.error.\n * Modify to null will prevent warning.\n */\nvar preMessage = exports.preMessage = function preMessage(fn) {\n  preWarningFns.push(fn);\n};\n\n/**\n * Warning if condition not match.\n * @param valid Condition\n * @param message Warning message\n * @example\n * ```js\n * warning(false, 'some error'); // print some error\n * warning(true, 'some error'); // print nothing\n * warning(1 === 2, 'some error'); // print some error\n * ```\n */\nfunction warning(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'warning');\n    }, message);\n    if (finalMessage) {\n      console.error(\"Warning: \".concat(finalMessage));\n    }\n  }\n}\n\n/** @see Similar to {@link warning} */\nfunction note(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'note');\n    }, message);\n    if (finalMessage) {\n      console.warn(\"Note: \".concat(finalMessage));\n    }\n  }\n}\nfunction resetWarned() {\n  warned = {};\n}\nfunction call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nfunction warningOnce(valid, message) {\n  call(warning, valid, message);\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nfunction noteOnce(valid, message) {\n  call(note, valid, message);\n}\nwarningOnce.preMessage = preMessage;\nwarningOnce.resetWarned = resetWarned;\nwarningOnce.noteOnce = noteOnce;\nvar _default = exports.default = warningOnce;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "call", "default", "note", "noteOnce", "preMessage", "resetWarned", "warning", "warningOnce", "warned", "preWarningFns", "fn", "push", "valid", "message", "process", "env", "NODE_ENV", "console", "undefined", "finalMessage", "reduce", "msg", "preMessageFn", "error", "concat", "warn", "method", "_default"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-util/lib/warning.js"], "sourcesContent": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.call = call;\nexports.default = void 0;\nexports.note = note;\nexports.noteOnce = noteOnce;\nexports.preMessage = void 0;\nexports.resetWarned = resetWarned;\nexports.warning = warning;\nexports.warningOnce = warningOnce;\n/* eslint-disable no-console */\nvar warned = {};\nvar preWarningFns = [];\n\n/**\n * Pre warning enable you to parse content before console.error.\n * Modify to null will prevent warning.\n */\nvar preMessage = exports.preMessage = function preMessage(fn) {\n  preWarningFns.push(fn);\n};\n\n/**\n * Warning if condition not match.\n * @param valid Condition\n * @param message Warning message\n * @example\n * ```js\n * warning(false, 'some error'); // print some error\n * warning(true, 'some error'); // print nothing\n * warning(1 === 2, 'some error'); // print some error\n * ```\n */\nfunction warning(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'warning');\n    }, message);\n    if (finalMessage) {\n      console.error(\"Warning: \".concat(finalMessage));\n    }\n  }\n}\n\n/** @see Similar to {@link warning} */\nfunction note(valid, message) {\n  if (process.env.NODE_ENV !== 'production' && !valid && console !== undefined) {\n    var finalMessage = preWarningFns.reduce(function (msg, preMessageFn) {\n      return preMessageFn(msg !== null && msg !== void 0 ? msg : '', 'note');\n    }, message);\n    if (finalMessage) {\n      console.warn(\"Note: \".concat(finalMessage));\n    }\n  }\n}\nfunction resetWarned() {\n  warned = {};\n}\nfunction call(method, valid, message) {\n  if (!valid && !warned[message]) {\n    method(false, message);\n    warned[message] = true;\n  }\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nfunction warningOnce(valid, message) {\n  call(warning, valid, message);\n}\n\n/** @see Same as {@link warning}, but only warn once for the same message */\nfunction noteOnce(valid, message) {\n  call(note, valid, message);\n}\nwarningOnce.preMessage = preMessage;\nwarningOnce.resetWarned = resetWarned;\nwarningOnce.noteOnce = noteOnce;\nvar _default = exports.default = warningOnce;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AACFD,OAAO,CAACE,IAAI,GAAGA,IAAI;AACnBF,OAAO,CAACG,OAAO,GAAG,KAAK,CAAC;AACxBH,OAAO,CAACI,IAAI,GAAGA,IAAI;AACnBJ,OAAO,CAACK,QAAQ,GAAGA,QAAQ;AAC3BL,OAAO,CAACM,UAAU,GAAG,KAAK,CAAC;AAC3BN,OAAO,CAACO,WAAW,GAAGA,WAAW;AACjCP,OAAO,CAACQ,OAAO,GAAGA,OAAO;AACzBR,OAAO,CAACS,WAAW,GAAGA,WAAW;AACjC;AACA,IAAIC,MAAM,GAAG,CAAC,CAAC;AACf,IAAIC,aAAa,GAAG,EAAE;;AAEtB;AACA;AACA;AACA;AACA,IAAIL,UAAU,GAAGN,OAAO,CAACM,UAAU,GAAG,SAASA,UAAUA,CAACM,EAAE,EAAE;EAC5DD,aAAa,CAACE,IAAI,CAACD,EAAE,CAAC;AACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASJ,OAAOA,CAACM,KAAK,EAAEC,OAAO,EAAE;EAC/B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5E,IAAIC,YAAY,GAAGV,aAAa,CAACW,MAAM,CAAC,UAAUC,GAAG,EAAEC,YAAY,EAAE;MACnE,OAAOA,YAAY,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,EAAE,EAAE,SAAS,CAAC;IAC3E,CAAC,EAAER,OAAO,CAAC;IACX,IAAIM,YAAY,EAAE;MAChBF,OAAO,CAACM,KAAK,CAAC,WAAW,CAACC,MAAM,CAACL,YAAY,CAAC,CAAC;IACjD;EACF;AACF;;AAEA;AACA,SAASjB,IAAIA,CAACU,KAAK,EAAEC,OAAO,EAAE;EAC5B,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAI,CAACJ,KAAK,IAAIK,OAAO,KAAKC,SAAS,EAAE;IAC5E,IAAIC,YAAY,GAAGV,aAAa,CAACW,MAAM,CAAC,UAAUC,GAAG,EAAEC,YAAY,EAAE;MACnE,OAAOA,YAAY,CAACD,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAGA,GAAG,GAAG,EAAE,EAAE,MAAM,CAAC;IACxE,CAAC,EAAER,OAAO,CAAC;IACX,IAAIM,YAAY,EAAE;MAChBF,OAAO,CAACQ,IAAI,CAAC,QAAQ,CAACD,MAAM,CAACL,YAAY,CAAC,CAAC;IAC7C;EACF;AACF;AACA,SAASd,WAAWA,CAAA,EAAG;EACrBG,MAAM,GAAG,CAAC,CAAC;AACb;AACA,SAASR,IAAIA,CAAC0B,MAAM,EAAEd,KAAK,EAAEC,OAAO,EAAE;EACpC,IAAI,CAACD,KAAK,IAAI,CAACJ,MAAM,CAACK,OAAO,CAAC,EAAE;IAC9Ba,MAAM,CAAC,KAAK,EAAEb,OAAO,CAAC;IACtBL,MAAM,CAACK,OAAO,CAAC,GAAG,IAAI;EACxB;AACF;;AAEA;AACA,SAASN,WAAWA,CAACK,KAAK,EAAEC,OAAO,EAAE;EACnCb,IAAI,CAACM,OAAO,EAAEM,KAAK,EAAEC,OAAO,CAAC;AAC/B;;AAEA;AACA,SAASV,QAAQA,CAACS,KAAK,EAAEC,OAAO,EAAE;EAChCb,IAAI,CAACE,IAAI,EAAEU,KAAK,EAAEC,OAAO,CAAC;AAC5B;AACAN,WAAW,CAACH,UAAU,GAAGA,UAAU;AACnCG,WAAW,CAACF,WAAW,GAAGA,WAAW;AACrCE,WAAW,CAACJ,QAAQ,GAAGA,QAAQ;AAC/B,IAAIwB,QAAQ,GAAG7B,OAAO,CAACG,OAAO,GAAGM,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}