{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\Form\\\\FormField.js\";\n/**\n * 可重用的表單欄位組件\n */\n\nimport React from 'react';\nimport { Form, Input, TextArea } from 'antd-mobile';\nimport { validateField, getFieldDisplayName } from '../../utils/validation';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FormField = ({\n  name,\n  value,\n  onChange,\n  label,\n  placeholder,\n  type = 'text',\n  required = false,\n  disabled = false,\n  multiline = false,\n  maxLength,\n  showError = true,\n  style = {},\n  className = ''\n}) => {\n  // 獲取顯示標籤\n  const displayLabel = label || getFieldDisplayName(name);\n\n  // 驗證欄位\n  const validation = validateField(name, value);\n  const hasError = showError && !validation.isValid;\n\n  // 處理值變更\n  const handleChange = newValue => {\n    if (onChange) {\n      onChange(name, newValue);\n    }\n  };\n\n  // 生成placeholder\n  const fieldPlaceholder = placeholder || `請輸入${displayLabel}`;\n  return /*#__PURE__*/_jsxDEV(Form.Item, {\n    name: name,\n    label: displayLabel,\n    className: `form-field ${className} ${hasError ? 'has-error' : ''}`,\n    style: style,\n    help: hasError ? validation.error : undefined,\n    validateStatus: hasError ? 'error' : undefined,\n    children: multiline ? /*#__PURE__*/_jsxDEV(TextArea, {\n      value: value || '',\n      onChange: handleChange,\n      placeholder: fieldPlaceholder,\n      disabled: disabled,\n      maxLength: maxLength,\n      rows: 3,\n      showCount: !!maxLength,\n      autoSize: {\n        minRows: 2,\n        maxRows: 6\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 51,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(Input, {\n      value: value || '',\n      onChange: handleChange,\n      placeholder: fieldPlaceholder,\n      disabled: disabled,\n      maxLength: maxLength,\n      type: type,\n      clearable: true\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 62,\n      columnNumber: 9\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 42,\n    columnNumber: 5\n  }, this);\n};\n_c = FormField;\nexport default FormField;\nvar _c;\n$RefreshReg$(_c, \"FormField\");", "map": {"version": 3, "names": ["React", "Form", "Input", "TextArea", "validateField", "getFieldDisplayName", "jsxDEV", "_jsxDEV", "FormField", "name", "value", "onChange", "label", "placeholder", "type", "required", "disabled", "multiline", "max<PERSON><PERSON><PERSON>", "showError", "style", "className", "displayLabel", "validation", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "handleChange", "newValue", "fieldPlaceholder", "<PERSON><PERSON>", "help", "error", "undefined", "validateStatus", "children", "rows", "showCount", "autoSize", "minRows", "maxRows", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "clearable", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/Form/FormField.js"], "sourcesContent": ["/**\n * 可重用的表單欄位組件\n */\n\nimport React from 'react';\nimport { Form, Input, TextArea } from 'antd-mobile';\nimport { validateField, getFieldDisplayName } from '../../utils/validation';\n\nconst FormField = ({\n  name,\n  value,\n  onChange,\n  label,\n  placeholder,\n  type = 'text',\n  required = false,\n  disabled = false,\n  multiline = false,\n  maxLength,\n  showError = true,\n  style = {},\n  className = ''\n}) => {\n  // 獲取顯示標籤\n  const displayLabel = label || getFieldDisplayName(name);\n  \n  // 驗證欄位\n  const validation = validateField(name, value);\n  const hasError = showError && !validation.isValid;\n\n  // 處理值變更\n  const handleChange = (newValue) => {\n    if (onChange) {\n      onChange(name, newValue);\n    }\n  };\n\n  // 生成placeholder\n  const fieldPlaceholder = placeholder || `請輸入${displayLabel}`;\n\n  return (\n    <Form.Item\n      name={name}\n      label={displayLabel}\n      className={`form-field ${className} ${hasError ? 'has-error' : ''}`}\n      style={style}\n      help={hasError ? validation.error : undefined}\n      validateStatus={hasError ? 'error' : undefined}\n    >\n      {multiline ? (\n        <TextArea\n          value={value || ''}\n          onChange={handleChange}\n          placeholder={fieldPlaceholder}\n          disabled={disabled}\n          maxLength={maxLength}\n          rows={3}\n          showCount={!!maxLength}\n          autoSize={{ minRows: 2, maxRows: 6 }}\n        />\n      ) : (\n        <Input\n          value={value || ''}\n          onChange={handleChange}\n          placeholder={fieldPlaceholder}\n          disabled={disabled}\n          maxLength={maxLength}\n          type={type}\n          clearable\n        />\n      )}\n    </Form.Item>\n  );\n};\n\nexport default FormField;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,aAAa;AACnD,SAASC,aAAa,EAAEC,mBAAmB,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,SAAS,GAAGA,CAAC;EACjBC,IAAI;EACJC,KAAK;EACLC,QAAQ;EACRC,KAAK;EACLC,WAAW;EACXC,IAAI,GAAG,MAAM;EACbC,QAAQ,GAAG,KAAK;EAChBC,QAAQ,GAAG,KAAK;EAChBC,SAAS,GAAG,KAAK;EACjBC,SAAS;EACTC,SAAS,GAAG,IAAI;EAChBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ;EACA,MAAMC,YAAY,GAAGV,KAAK,IAAIP,mBAAmB,CAACI,IAAI,CAAC;;EAEvD;EACA,MAAMc,UAAU,GAAGnB,aAAa,CAACK,IAAI,EAAEC,KAAK,CAAC;EAC7C,MAAMc,QAAQ,GAAGL,SAAS,IAAI,CAACI,UAAU,CAACE,OAAO;;EAEjD;EACA,MAAMC,YAAY,GAAIC,QAAQ,IAAK;IACjC,IAAIhB,QAAQ,EAAE;MACZA,QAAQ,CAACF,IAAI,EAAEkB,QAAQ,CAAC;IAC1B;EACF,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAGf,WAAW,IAAI,MAAMS,YAAY,EAAE;EAE5D,oBACEf,OAAA,CAACN,IAAI,CAAC4B,IAAI;IACRpB,IAAI,EAAEA,IAAK;IACXG,KAAK,EAAEU,YAAa;IACpBD,SAAS,EAAE,cAAcA,SAAS,IAAIG,QAAQ,GAAG,WAAW,GAAG,EAAE,EAAG;IACpEJ,KAAK,EAAEA,KAAM;IACbU,IAAI,EAAEN,QAAQ,GAAGD,UAAU,CAACQ,KAAK,GAAGC,SAAU;IAC9CC,cAAc,EAAET,QAAQ,GAAG,OAAO,GAAGQ,SAAU;IAAAE,QAAA,EAE9CjB,SAAS,gBACRV,OAAA,CAACJ,QAAQ;MACPO,KAAK,EAAEA,KAAK,IAAI,EAAG;MACnBC,QAAQ,EAAEe,YAAa;MACvBb,WAAW,EAAEe,gBAAiB;MAC9BZ,QAAQ,EAAEA,QAAS;MACnBE,SAAS,EAAEA,SAAU;MACrBiB,IAAI,EAAE,CAAE;MACRC,SAAS,EAAE,CAAC,CAAClB,SAAU;MACvBmB,QAAQ,EAAE;QAAEC,OAAO,EAAE,CAAC;QAAEC,OAAO,EAAE;MAAE;IAAE;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtC,CAAC,gBAEFpC,OAAA,CAACL,KAAK;MACJQ,KAAK,EAAEA,KAAK,IAAI,EAAG;MACnBC,QAAQ,EAAEe,YAAa;MACvBb,WAAW,EAAEe,gBAAiB;MAC9BZ,QAAQ,EAAEA,QAAS;MACnBE,SAAS,EAAEA,SAAU;MACrBJ,IAAI,EAAEA,IAAK;MACX8B,SAAS;IAAA;MAAAJ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV;EACF;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACQ,CAAC;AAEhB,CAAC;AAACE,EAAA,GAjEIrC,SAAS;AAmEf,eAAeA,SAAS;AAAC,IAAAqC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}