{"ast": null, "code": "/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) <PERSON>dre Sorhus; MIT License\n*/\n(function () {\n  'use strict';\n\n  var document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n  var isCommonjs = typeof module !== 'undefined' && module.exports;\n  var fn = function () {\n    var val;\n    var fnMap = [['requestFullscreen', 'exitFullscreen', 'fullscreenElement', 'fullscreenEnabled', 'fullscreenchange', 'fullscreenerror'],\n    // New WebKit\n    ['webkitRequestFullscreen', 'webkitExitFullscreen', 'webkitFullscreenElement', 'webkitFullscreenEnabled', 'webkitfullscreenchange', 'webkitfullscreenerror'],\n    // Old WebKit\n    ['webkitRequestFullScreen', 'webkitCancelFullScreen', 'webkitCurrentFullScreenElement', 'webkitCancelFullScreen', 'webkitfullscreenchange', 'webkitfullscreenerror'], ['mozRequestFullScreen', 'mozCancelFullScreen', 'mozFullScreenElement', 'mozFullScreenEnabled', 'mozfullscreenchange', 'mozfullscreenerror'], ['msRequestFullscreen', 'msExitFullscreen', 'msFullscreenElement', 'msFullscreenEnabled', 'MSFullscreenChange', 'MSFullscreenError']];\n    var i = 0;\n    var l = fnMap.length;\n    var ret = {};\n    for (; i < l; i++) {\n      val = fnMap[i];\n      if (val && val[1] in document) {\n        for (i = 0; i < val.length; i++) {\n          ret[fnMap[0][i]] = val[i];\n        }\n        return ret;\n      }\n    }\n    return false;\n  }();\n  var eventNameMap = {\n    change: fn.fullscreenchange,\n    error: fn.fullscreenerror\n  };\n  var screenfull = {\n    request: function (element, options) {\n      return new Promise(function (resolve, reject) {\n        var onFullScreenEntered = function () {\n          this.off('change', onFullScreenEntered);\n          resolve();\n        }.bind(this);\n        this.on('change', onFullScreenEntered);\n        element = element || document.documentElement;\n        var returnPromise = element[fn.requestFullscreen](options);\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenEntered).catch(reject);\n        }\n      }.bind(this));\n    },\n    exit: function () {\n      return new Promise(function (resolve, reject) {\n        if (!this.isFullscreen) {\n          resolve();\n          return;\n        }\n        var onFullScreenExit = function () {\n          this.off('change', onFullScreenExit);\n          resolve();\n        }.bind(this);\n        this.on('change', onFullScreenExit);\n        var returnPromise = document[fn.exitFullscreen]();\n        if (returnPromise instanceof Promise) {\n          returnPromise.then(onFullScreenExit).catch(reject);\n        }\n      }.bind(this));\n    },\n    toggle: function (element, options) {\n      return this.isFullscreen ? this.exit() : this.request(element, options);\n    },\n    onchange: function (callback) {\n      this.on('change', callback);\n    },\n    onerror: function (callback) {\n      this.on('error', callback);\n    },\n    on: function (event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.addEventListener(eventName, callback, false);\n      }\n    },\n    off: function (event, callback) {\n      var eventName = eventNameMap[event];\n      if (eventName) {\n        document.removeEventListener(eventName, callback, false);\n      }\n    },\n    raw: fn\n  };\n  if (!fn) {\n    if (isCommonjs) {\n      module.exports = {\n        isEnabled: false\n      };\n    } else {\n      window.screenfull = {\n        isEnabled: false\n      };\n    }\n    return;\n  }\n  Object.defineProperties(screenfull, {\n    isFullscreen: {\n      get: function () {\n        return Boolean(document[fn.fullscreenElement]);\n      }\n    },\n    element: {\n      enumerable: true,\n      get: function () {\n        return document[fn.fullscreenElement];\n      }\n    },\n    isEnabled: {\n      enumerable: true,\n      get: function () {\n        // Coerce to boolean in case of old WebKit\n        return Boolean(document[fn.fullscreenEnabled]);\n      }\n    }\n  });\n  if (isCommonjs) {\n    module.exports = screenfull;\n  } else {\n    window.screenfull = screenfull;\n  }\n})();", "map": {"version": 3, "names": ["document", "window", "is<PERSON><PERSON><PERSON>j<PERSON>", "module", "exports", "fn", "val", "fnMap", "i", "l", "length", "ret", "eventNameMap", "change", "fullscreenchange", "error", "fullscreenerror", "screenfull", "request", "element", "options", "Promise", "resolve", "reject", "onFullScreenEntered", "off", "bind", "on", "documentElement", "returnPromise", "requestFullscreen", "then", "catch", "exit", "isFullscreen", "onFullScreenExit", "exitFullscreen", "toggle", "onchange", "callback", "onerror", "event", "eventName", "addEventListener", "removeEventListener", "raw", "isEnabled", "Object", "defineProperties", "get", "Boolean", "fullscreenElement", "enumerable", "fullscreenEnabled"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/screenfull/dist/screenfull.js"], "sourcesContent": ["/*!\n* screenfull\n* v5.2.0 - 2021-11-03\n* (c) <PERSON>dre Sorhus; MIT License\n*/\n(function () {\n\t'use strict';\n\n\tvar document = typeof window !== 'undefined' && typeof window.document !== 'undefined' ? window.document : {};\n\tvar isCommonjs = typeof module !== 'undefined' && module.exports;\n\n\tvar fn = (function () {\n\t\tvar val;\n\n\t\tvar fnMap = [\n\t\t\t[\n\t\t\t\t'requestFullscreen',\n\t\t\t\t'exitFullscreen',\n\t\t\t\t'fullscreenElement',\n\t\t\t\t'fullscreenEnabled',\n\t\t\t\t'fullscreenchange',\n\t\t\t\t'fullscreenerror'\n\t\t\t],\n\t\t\t// New WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullscreen',\n\t\t\t\t'webkitExitFullscreen',\n\t\t\t\t'webkitFullscreenElement',\n\t\t\t\t'webkitFullscreenEnabled',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t// Old WebKit\n\t\t\t[\n\t\t\t\t'webkitRequestFullScreen',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitCurrentFullScreenElement',\n\t\t\t\t'webkitCancelFullScreen',\n\t\t\t\t'webkitfullscreenchange',\n\t\t\t\t'webkitfullscreenerror'\n\n\t\t\t],\n\t\t\t[\n\t\t\t\t'mozRequestFullScreen',\n\t\t\t\t'mozCancelFullScreen',\n\t\t\t\t'mozFullScreenElement',\n\t\t\t\t'mozFullScreenEnabled',\n\t\t\t\t'mozfullscreenchange',\n\t\t\t\t'mozfullscreenerror'\n\t\t\t],\n\t\t\t[\n\t\t\t\t'msRequestFullscreen',\n\t\t\t\t'msExitFullscreen',\n\t\t\t\t'msFullscreenElement',\n\t\t\t\t'msFullscreenEnabled',\n\t\t\t\t'MSFullscreenChange',\n\t\t\t\t'MSFullscreenError'\n\t\t\t]\n\t\t];\n\n\t\tvar i = 0;\n\t\tvar l = fnMap.length;\n\t\tvar ret = {};\n\n\t\tfor (; i < l; i++) {\n\t\t\tval = fnMap[i];\n\t\t\tif (val && val[1] in document) {\n\t\t\t\tfor (i = 0; i < val.length; i++) {\n\t\t\t\t\tret[fnMap[0][i]] = val[i];\n\t\t\t\t}\n\t\t\t\treturn ret;\n\t\t\t}\n\t\t}\n\n\t\treturn false;\n\t})();\n\n\tvar eventNameMap = {\n\t\tchange: fn.fullscreenchange,\n\t\terror: fn.fullscreenerror\n\t};\n\n\tvar screenfull = {\n\t\trequest: function (element, options) {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tvar onFullScreenEntered = function () {\n\t\t\t\t\tthis.off('change', onFullScreenEntered);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenEntered);\n\n\t\t\t\telement = element || document.documentElement;\n\n\t\t\t\tvar returnPromise = element[fn.requestFullscreen](options);\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenEntered).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\texit: function () {\n\t\t\treturn new Promise(function (resolve, reject) {\n\t\t\t\tif (!this.isFullscreen) {\n\t\t\t\t\tresolve();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar onFullScreenExit = function () {\n\t\t\t\t\tthis.off('change', onFullScreenExit);\n\t\t\t\t\tresolve();\n\t\t\t\t}.bind(this);\n\n\t\t\t\tthis.on('change', onFullScreenExit);\n\n\t\t\t\tvar returnPromise = document[fn.exitFullscreen]();\n\n\t\t\t\tif (returnPromise instanceof Promise) {\n\t\t\t\t\treturnPromise.then(onFullScreenExit).catch(reject);\n\t\t\t\t}\n\t\t\t}.bind(this));\n\t\t},\n\t\ttoggle: function (element, options) {\n\t\t\treturn this.isFullscreen ? this.exit() : this.request(element, options);\n\t\t},\n\t\tonchange: function (callback) {\n\t\t\tthis.on('change', callback);\n\t\t},\n\t\tonerror: function (callback) {\n\t\t\tthis.on('error', callback);\n\t\t},\n\t\ton: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.addEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\toff: function (event, callback) {\n\t\t\tvar eventName = eventNameMap[event];\n\t\t\tif (eventName) {\n\t\t\t\tdocument.removeEventListener(eventName, callback, false);\n\t\t\t}\n\t\t},\n\t\traw: fn\n\t};\n\n\tif (!fn) {\n\t\tif (isCommonjs) {\n\t\t\tmodule.exports = {isEnabled: false};\n\t\t} else {\n\t\t\twindow.screenfull = {isEnabled: false};\n\t\t}\n\n\t\treturn;\n\t}\n\n\tObject.defineProperties(screenfull, {\n\t\tisFullscreen: {\n\t\t\tget: function () {\n\t\t\t\treturn Boolean(document[fn.fullscreenElement]);\n\t\t\t}\n\t\t},\n\t\telement: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\treturn document[fn.fullscreenElement];\n\t\t\t}\n\t\t},\n\t\tisEnabled: {\n\t\t\tenumerable: true,\n\t\t\tget: function () {\n\t\t\t\t// Coerce to boolean in case of old WebKit\n\t\t\t\treturn Boolean(document[fn.fullscreenEnabled]);\n\t\t\t}\n\t\t}\n\t});\n\n\tif (isCommonjs) {\n\t\tmodule.exports = screenfull;\n\t} else {\n\t\twindow.screenfull = screenfull;\n\t}\n})();\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,CAAC,YAAY;EACZ,YAAY;;EAEZ,IAAIA,QAAQ,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACD,QAAQ,KAAK,WAAW,GAAGC,MAAM,CAACD,QAAQ,GAAG,CAAC,CAAC;EAC7G,IAAIE,UAAU,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,OAAO;EAEhE,IAAIC,EAAE,GAAI,YAAY;IACrB,IAAIC,GAAG;IAEP,IAAIC,KAAK,GAAG,CACX,CACC,mBAAmB,EACnB,gBAAgB,EAChB,mBAAmB,EACnB,mBAAmB,EACnB,kBAAkB,EAClB,iBAAiB,CACjB;IACD;IACA,CACC,yBAAyB,EACzB,sBAAsB,EACtB,yBAAyB,EACzB,yBAAyB,EACzB,wBAAwB,EACxB,uBAAuB,CAEvB;IACD;IACA,CACC,yBAAyB,EACzB,wBAAwB,EACxB,gCAAgC,EAChC,wBAAwB,EACxB,wBAAwB,EACxB,uBAAuB,CAEvB,EACD,CACC,sBAAsB,EACtB,qBAAqB,EACrB,sBAAsB,EACtB,sBAAsB,EACtB,qBAAqB,EACrB,oBAAoB,CACpB,EACD,CACC,qBAAqB,EACrB,kBAAkB,EAClB,qBAAqB,EACrB,qBAAqB,EACrB,oBAAoB,EACpB,mBAAmB,CACnB,CACD;IAED,IAAIC,CAAC,GAAG,CAAC;IACT,IAAIC,CAAC,GAAGF,KAAK,CAACG,MAAM;IACpB,IAAIC,GAAG,GAAG,CAAC,CAAC;IAEZ,OAAOH,CAAC,GAAGC,CAAC,EAAED,CAAC,EAAE,EAAE;MAClBF,GAAG,GAAGC,KAAK,CAACC,CAAC,CAAC;MACd,IAAIF,GAAG,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAIN,QAAQ,EAAE;QAC9B,KAAKQ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,GAAG,CAACI,MAAM,EAAEF,CAAC,EAAE,EAAE;UAChCG,GAAG,CAACJ,KAAK,CAAC,CAAC,CAAC,CAACC,CAAC,CAAC,CAAC,GAAGF,GAAG,CAACE,CAAC,CAAC;QAC1B;QACA,OAAOG,GAAG;MACX;IACD;IAEA,OAAO,KAAK;EACb,CAAC,CAAE,CAAC;EAEJ,IAAIC,YAAY,GAAG;IAClBC,MAAM,EAAER,EAAE,CAACS,gBAAgB;IAC3BC,KAAK,EAAEV,EAAE,CAACW;EACX,CAAC;EAED,IAAIC,UAAU,GAAG;IAChBC,OAAO,EAAE,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;MACpC,OAAO,IAAIC,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QAC7C,IAAIC,mBAAmB,GAAG,YAAY;UACrC,IAAI,CAACC,GAAG,CAAC,QAAQ,EAAED,mBAAmB,CAAC;UACvCF,OAAO,CAAC,CAAC;QACV,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC;QAEZ,IAAI,CAACC,EAAE,CAAC,QAAQ,EAAEH,mBAAmB,CAAC;QAEtCL,OAAO,GAAGA,OAAO,IAAInB,QAAQ,CAAC4B,eAAe;QAE7C,IAAIC,aAAa,GAAGV,OAAO,CAACd,EAAE,CAACyB,iBAAiB,CAAC,CAACV,OAAO,CAAC;QAE1D,IAAIS,aAAa,YAAYR,OAAO,EAAE;UACrCQ,aAAa,CAACE,IAAI,CAACP,mBAAmB,CAAC,CAACQ,KAAK,CAACT,MAAM,CAAC;QACtD;MACD,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IACDO,IAAI,EAAE,SAAAA,CAAA,EAAY;MACjB,OAAO,IAAIZ,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QAC7C,IAAI,CAAC,IAAI,CAACW,YAAY,EAAE;UACvBZ,OAAO,CAAC,CAAC;UACT;QACD;QAEA,IAAIa,gBAAgB,GAAG,YAAY;UAClC,IAAI,CAACV,GAAG,CAAC,QAAQ,EAAEU,gBAAgB,CAAC;UACpCb,OAAO,CAAC,CAAC;QACV,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC;QAEZ,IAAI,CAACC,EAAE,CAAC,QAAQ,EAAEQ,gBAAgB,CAAC;QAEnC,IAAIN,aAAa,GAAG7B,QAAQ,CAACK,EAAE,CAAC+B,cAAc,CAAC,CAAC,CAAC;QAEjD,IAAIP,aAAa,YAAYR,OAAO,EAAE;UACrCQ,aAAa,CAACE,IAAI,CAACI,gBAAgB,CAAC,CAACH,KAAK,CAACT,MAAM,CAAC;QACnD;MACD,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAAC;IACd,CAAC;IACDW,MAAM,EAAE,SAAAA,CAAUlB,OAAO,EAAEC,OAAO,EAAE;MACnC,OAAO,IAAI,CAACc,YAAY,GAAG,IAAI,CAACD,IAAI,CAAC,CAAC,GAAG,IAAI,CAACf,OAAO,CAACC,OAAO,EAAEC,OAAO,CAAC;IACxE,CAAC;IACDkB,QAAQ,EAAE,SAAAA,CAAUC,QAAQ,EAAE;MAC7B,IAAI,CAACZ,EAAE,CAAC,QAAQ,EAAEY,QAAQ,CAAC;IAC5B,CAAC;IACDC,OAAO,EAAE,SAAAA,CAAUD,QAAQ,EAAE;MAC5B,IAAI,CAACZ,EAAE,CAAC,OAAO,EAAEY,QAAQ,CAAC;IAC3B,CAAC;IACDZ,EAAE,EAAE,SAAAA,CAAUc,KAAK,EAAEF,QAAQ,EAAE;MAC9B,IAAIG,SAAS,GAAG9B,YAAY,CAAC6B,KAAK,CAAC;MACnC,IAAIC,SAAS,EAAE;QACd1C,QAAQ,CAAC2C,gBAAgB,CAACD,SAAS,EAAEH,QAAQ,EAAE,KAAK,CAAC;MACtD;IACD,CAAC;IACDd,GAAG,EAAE,SAAAA,CAAUgB,KAAK,EAAEF,QAAQ,EAAE;MAC/B,IAAIG,SAAS,GAAG9B,YAAY,CAAC6B,KAAK,CAAC;MACnC,IAAIC,SAAS,EAAE;QACd1C,QAAQ,CAAC4C,mBAAmB,CAACF,SAAS,EAAEH,QAAQ,EAAE,KAAK,CAAC;MACzD;IACD,CAAC;IACDM,GAAG,EAAExC;EACN,CAAC;EAED,IAAI,CAACA,EAAE,EAAE;IACR,IAAIH,UAAU,EAAE;MACfC,MAAM,CAACC,OAAO,GAAG;QAAC0C,SAAS,EAAE;MAAK,CAAC;IACpC,CAAC,MAAM;MACN7C,MAAM,CAACgB,UAAU,GAAG;QAAC6B,SAAS,EAAE;MAAK,CAAC;IACvC;IAEA;EACD;EAEAC,MAAM,CAACC,gBAAgB,CAAC/B,UAAU,EAAE;IACnCiB,YAAY,EAAE;MACbe,GAAG,EAAE,SAAAA,CAAA,EAAY;QAChB,OAAOC,OAAO,CAAClD,QAAQ,CAACK,EAAE,CAAC8C,iBAAiB,CAAC,CAAC;MAC/C;IACD,CAAC;IACDhC,OAAO,EAAE;MACRiC,UAAU,EAAE,IAAI;MAChBH,GAAG,EAAE,SAAAA,CAAA,EAAY;QAChB,OAAOjD,QAAQ,CAACK,EAAE,CAAC8C,iBAAiB,CAAC;MACtC;IACD,CAAC;IACDL,SAAS,EAAE;MACVM,UAAU,EAAE,IAAI;MAChBH,GAAG,EAAE,SAAAA,CAAA,EAAY;QAChB;QACA,OAAOC,OAAO,CAAClD,QAAQ,CAACK,EAAE,CAACgD,iBAAiB,CAAC,CAAC;MAC/C;IACD;EACD,CAAC,CAAC;EAEF,IAAInD,UAAU,EAAE;IACfC,MAAM,CAACC,OAAO,GAAGa,UAAU;EAC5B,CAAC,MAAM;IACNhB,MAAM,CAACgB,UAAU,GAAGA,UAAU;EAC/B;AACD,CAAC,EAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}