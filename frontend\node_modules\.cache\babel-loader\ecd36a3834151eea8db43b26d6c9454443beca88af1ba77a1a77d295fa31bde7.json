{"ast": null, "code": "import { isFunction } from './index';\nimport isBrowser from './isBrowser';\nexport function getTargetElement(target, defaultElement) {\n  if (!isBrowser) {\n    return undefined;\n  }\n  if (!target) {\n    return defaultElement;\n  }\n  var targetElement;\n  if (isFunction(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n  return targetElement;\n}", "map": {"version": 3, "names": ["isFunction", "<PERSON><PERSON><PERSON><PERSON>", "getTargetElement", "target", "defaultElement", "undefined", "targetElement", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/domTarget.js"], "sourcesContent": ["import { isFunction } from './index';\nimport isBrowser from './isBrowser';\nexport function getTargetElement(target, defaultElement) {\n  if (!isBrowser) {\n    return undefined;\n  }\n  if (!target) {\n    return defaultElement;\n  }\n  var targetElement;\n  if (isFunction(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n  return targetElement;\n}"], "mappings": "AAAA,SAASA,UAAU,QAAQ,SAAS;AACpC,OAAOC,SAAS,MAAM,aAAa;AACnC,OAAO,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,cAAc,EAAE;EACvD,IAAI,CAACH,SAAS,EAAE;IACd,OAAOI,SAAS;EAClB;EACA,IAAI,CAACF,MAAM,EAAE;IACX,OAAOC,cAAc;EACvB;EACA,IAAIE,aAAa;EACjB,IAAIN,UAAU,CAACG,MAAM,CAAC,EAAE;IACtBG,aAAa,GAAGH,MAAM,CAAC,CAAC;EAC1B,CAAC,MAAM,IAAI,SAAS,IAAIA,MAAM,EAAE;IAC9BG,aAAa,GAAGH,MAAM,CAACI,OAAO;EAChC,CAAC,MAAM;IACLD,aAAa,GAAGH,MAAM;EACxB;EACA,OAAOG,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}