{"ast": null, "code": "import { __read } from \"tslib\";\nimport useBoolean from '../useBoolean';\nimport useEventListener from '../useEventListener';\nexport default (function (target, options) {\n  var _a = options || {},\n    onEnter = _a.onEnter,\n    onLeave = _a.onLeave,\n    onChange = _a.onChange;\n  var _b = __read(useBoolean(false), 2),\n    state = _b[0],\n    _c = _b[1],\n    setTrue = _c.setTrue,\n    setFalse = _c.setFalse;\n  useEventListener('mouseenter', function () {\n    onEnter === null || onEnter === void 0 ? void 0 : onEnter();\n    setTrue();\n    onChange === null || onChange === void 0 ? void 0 : onChange(true);\n  }, {\n    target: target\n  });\n  useEventListener('mouseleave', function () {\n    onLeave === null || onLeave === void 0 ? void 0 : onLeave();\n    setFalse();\n    onChange === null || onChange === void 0 ? void 0 : onChange(false);\n  }, {\n    target: target\n  });\n  return state;\n});", "map": {"version": 3, "names": ["__read", "useBoolean", "useEventListener", "target", "options", "_a", "onEnter", "onLeave", "onChange", "_b", "state", "_c", "setTrue", "setFalse"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useHover/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport useBoolean from '../useBoolean';\nimport useEventListener from '../useEventListener';\nexport default (function (target, options) {\n  var _a = options || {},\n    onEnter = _a.onEnter,\n    onLeave = _a.onLeave,\n    onChange = _a.onChange;\n  var _b = __read(useBoolean(false), 2),\n    state = _b[0],\n    _c = _b[1],\n    setTrue = _c.setTrue,\n    setFalse = _c.setFalse;\n  useEventListener('mouseenter', function () {\n    onEnter === null || onEnter === void 0 ? void 0 : onEnter();\n    setTrue();\n    onChange === null || onChange === void 0 ? void 0 : onChange(true);\n  }, {\n    target: target\n  });\n  useEventListener('mouseleave', function () {\n    onLeave === null || onLeave === void 0 ? void 0 : onLeave();\n    setFalse();\n    onChange === null || onChange === void 0 ? void 0 : onChange(false);\n  }, {\n    target: target\n  });\n  return state;\n});"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,gBAAgB,UAAUC,MAAM,EAAEC,OAAO,EAAE;EACzC,IAAIC,EAAE,GAAGD,OAAO,IAAI,CAAC,CAAC;IACpBE,OAAO,GAAGD,EAAE,CAACC,OAAO;IACpBC,OAAO,GAAGF,EAAE,CAACE,OAAO;IACpBC,QAAQ,GAAGH,EAAE,CAACG,QAAQ;EACxB,IAAIC,EAAE,GAAGT,MAAM,CAACC,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;IACnCS,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,EAAE,GAAGF,EAAE,CAAC,CAAC,CAAC;IACVG,OAAO,GAAGD,EAAE,CAACC,OAAO;IACpBC,QAAQ,GAAGF,EAAE,CAACE,QAAQ;EACxBX,gBAAgB,CAAC,YAAY,EAAE,YAAY;IACzCI,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC3DM,OAAO,CAAC,CAAC;IACTJ,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,IAAI,CAAC;EACpE,CAAC,EAAE;IACDL,MAAM,EAAEA;EACV,CAAC,CAAC;EACFD,gBAAgB,CAAC,YAAY,EAAE,YAAY;IACzCK,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC,CAAC;IAC3DM,QAAQ,CAAC,CAAC;IACVL,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAAC,KAAK,CAAC;EACrE,CAAC,EAAE;IACDL,MAAM,EAAEA;EACV,CAAC,CAAC;EACF,OAAOO,KAAK;AACd,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}