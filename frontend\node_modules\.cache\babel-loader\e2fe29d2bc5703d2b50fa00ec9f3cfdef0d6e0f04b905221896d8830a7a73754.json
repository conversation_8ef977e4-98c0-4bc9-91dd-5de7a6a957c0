{"ast": null, "code": "import { useRef } from 'react';\nexport var createUpdateEffect = function (hook) {\n  return function (effect, deps) {\n    var isMounted = useRef(false);\n    // for react-refresh\n    hook(function () {\n      return function () {\n        isMounted.current = false;\n      };\n    }, []);\n    hook(function () {\n      if (!isMounted.current) {\n        isMounted.current = true;\n      } else {\n        return effect();\n      }\n    }, deps);\n  };\n};\nexport default createUpdateEffect;", "map": {"version": 3, "names": ["useRef", "createUpdateEffect", "hook", "effect", "deps", "isMounted", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/createUpdateEffect/index.js"], "sourcesContent": ["import { useRef } from 'react';\nexport var createUpdateEffect = function (hook) {\n  return function (effect, deps) {\n    var isMounted = useRef(false);\n    // for react-refresh\n    hook(function () {\n      return function () {\n        isMounted.current = false;\n      };\n    }, []);\n    hook(function () {\n      if (!isMounted.current) {\n        isMounted.current = true;\n      } else {\n        return effect();\n      }\n    }, deps);\n  };\n};\nexport default createUpdateEffect;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAO,IAAIC,kBAAkB,GAAG,SAAAA,CAAUC,IAAI,EAAE;EAC9C,OAAO,UAAUC,MAAM,EAAEC,IAAI,EAAE;IAC7B,IAAIC,SAAS,GAAGL,MAAM,CAAC,KAAK,CAAC;IAC7B;IACAE,IAAI,CAAC,YAAY;MACf,OAAO,YAAY;QACjBG,SAAS,CAACC,OAAO,GAAG,KAAK;MAC3B,CAAC;IACH,CAAC,EAAE,EAAE,CAAC;IACNJ,IAAI,CAAC,YAAY;MACf,IAAI,CAACG,SAAS,CAACC,OAAO,EAAE;QACtBD,SAAS,CAACC,OAAO,GAAG,IAAI;MAC1B,CAAC,MAAM;QACL,OAAOH,MAAM,CAAC,CAAC;MACjB;IACF,CAAC,EAAEC,IAAI,CAAC;EACV,CAAC;AACH,CAAC;AACD,eAAeH,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}