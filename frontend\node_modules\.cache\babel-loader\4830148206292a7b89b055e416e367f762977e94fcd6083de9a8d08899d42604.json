{"ast": null, "code": "/**\n * 圖片處理工具函數\n */\n\nimport { IMAGE_CONFIG, ERROR_MESSAGES } from './constants';\n\n/**\n * 驗證圖片文件\n * @param {File} file - 圖片文件\n * @returns {Object} 驗證結果 { isValid: boolean, error: string }\n */\nexport const validateImageFile = file => {\n  if (!file) {\n    return {\n      isValid: false,\n      error: '請選擇圖片文件'\n    };\n  }\n\n  // 檢查文件大小\n  if (file.size > IMAGE_CONFIG.MAX_FILE_SIZE) {\n    return {\n      isValid: false,\n      error: `${ERROR_MESSAGES.FILE_TOO_LARGE}（最大${Math.round(IMAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024)}MB）`\n    };\n  }\n\n  // 檢查文件格式\n  if (!IMAGE_CONFIG.ACCEPTED_FORMATS.includes(file.type)) {\n    return {\n      isValid: false,\n      error: `${ERROR_MESSAGES.INVALID_FILE_FORMAT}，支持格式：${IMAGE_CONFIG.ACCEPTED_FORMATS.join(', ')}`\n    };\n  }\n  return {\n    isValid: true,\n    error: null\n  };\n};\n\n/**\n * 壓縮圖片\n * @param {File} file - 原始圖片文件\n * @param {Object} options - 壓縮選項\n * @returns {Promise<File>} 壓縮後的圖片文件\n */\nexport const compressImage = (file, options = {}) => {\n  return new Promise((resolve, reject) => {\n    const {\n      maxWidth = IMAGE_CONFIG.MAX_WIDTH,\n      maxHeight = IMAGE_CONFIG.MAX_HEIGHT,\n      quality = IMAGE_CONFIG.COMPRESSION_QUALITY\n    } = options;\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    img.onload = () => {\n      // 計算新的尺寸\n      let {\n        width,\n        height\n      } = img;\n      if (width > maxWidth || height > maxHeight) {\n        const ratio = Math.min(maxWidth / width, maxHeight / height);\n        width *= ratio;\n        height *= ratio;\n      }\n\n      // 設置畫布尺寸\n      canvas.width = width;\n      canvas.height = height;\n\n      // 繪製圖片\n      ctx.drawImage(img, 0, 0, width, height);\n\n      // 轉換為Blob\n      canvas.toBlob(blob => {\n        if (blob) {\n          // 創建新的File對象\n          const compressedFile = new File([blob], file.name, {\n            type: file.type,\n            lastModified: Date.now()\n          });\n          resolve(compressedFile);\n        } else {\n          reject(new Error('圖片壓縮失敗'));\n        }\n      }, file.type, quality);\n    };\n    img.onerror = () => {\n      reject(new Error('圖片加載失敗'));\n    };\n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * 獲取圖片預覽URL\n * @param {File} file - 圖片文件\n * @returns {string} 預覽URL\n */\nexport const getImagePreviewUrl = file => {\n  if (!file) return null;\n  return URL.createObjectURL(file);\n};\n\n/**\n * 釋放圖片預覽URL\n * @param {string} url - 預覽URL\n */\nexport const revokeImagePreviewUrl = url => {\n  if (url) {\n    URL.revokeObjectURL(url);\n  }\n};\n\n/**\n * 將圖片轉換為Base64\n * @param {File} file - 圖片文件\n * @returns {Promise<string>} Base64字符串\n */\nexport const imageToBase64 = file => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n      resolve(reader.result);\n    };\n    reader.onerror = () => {\n      reject(new Error('圖片轉換失敗'));\n    };\n    reader.readAsDataURL(file);\n  });\n};\n\n/**\n * 從Base64創建圖片文件\n * @param {string} base64 - Base64字符串\n * @param {string} filename - 文件名\n * @returns {File} 圖片文件\n */\nexport const base64ToImageFile = (base64, filename = 'image.jpg') => {\n  const arr = base64.split(',');\n  const mime = arr[0].match(/:(.*?);/)[1];\n  const bstr = atob(arr[1]);\n  let n = bstr.length;\n  const u8arr = new Uint8Array(n);\n  while (n--) {\n    u8arr[n] = bstr.charCodeAt(n);\n  }\n  return new File([u8arr], filename, {\n    type: mime\n  });\n};\n\n/**\n * 獲取圖片尺寸信息\n * @param {File} file - 圖片文件\n * @returns {Promise<Object>} 尺寸信息 { width, height }\n */\nexport const getImageDimensions = file => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    img.onload = () => {\n      resolve({\n        width: img.naturalWidth,\n        height: img.naturalHeight\n      });\n    };\n    img.onerror = () => {\n      reject(new Error('無法獲取圖片尺寸'));\n    };\n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * 旋轉圖片\n * @param {File} file - 圖片文件\n * @param {number} degrees - 旋轉角度（90, 180, 270）\n * @returns {Promise<File>} 旋轉後的圖片文件\n */\nexport const rotateImage = (file, degrees) => {\n  return new Promise((resolve, reject) => {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n    img.onload = () => {\n      const {\n        width,\n        height\n      } = img;\n\n      // 根據旋轉角度設置畫布尺寸\n      if (degrees === 90 || degrees === 270) {\n        canvas.width = height;\n        canvas.height = width;\n      } else {\n        canvas.width = width;\n        canvas.height = height;\n      }\n\n      // 移動到畫布中心\n      ctx.translate(canvas.width / 2, canvas.height / 2);\n\n      // 旋轉\n      ctx.rotate(degrees * Math.PI / 180);\n\n      // 繪製圖片\n      ctx.drawImage(img, -width / 2, -height / 2);\n\n      // 轉換為Blob\n      canvas.toBlob(blob => {\n        if (blob) {\n          const rotatedFile = new File([blob], file.name, {\n            type: file.type,\n            lastModified: Date.now()\n          });\n          resolve(rotatedFile);\n        } else {\n          reject(new Error('圖片旋轉失敗'));\n        }\n      }, file.type, IMAGE_CONFIG.COMPRESSION_QUALITY);\n    };\n    img.onerror = () => {\n      reject(new Error('圖片加載失敗'));\n    };\n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * 格式化文件大小\n * @param {number} bytes - 字節數\n * @returns {string} 格式化後的大小\n */\nexport const formatFileSize = bytes => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\nexport default {\n  validateImageFile,\n  compressImage,\n  getImagePreviewUrl,\n  revokeImagePreviewUrl,\n  imageToBase64,\n  base64ToImageFile,\n  getImageDimensions,\n  rotateImage,\n  formatFileSize\n};", "map": {"version": 3, "names": ["IMAGE_CONFIG", "ERROR_MESSAGES", "validateImageFile", "file", "<PERSON><PERSON><PERSON><PERSON>", "error", "size", "MAX_FILE_SIZE", "FILE_TOO_LARGE", "Math", "round", "ACCEPTED_FORMATS", "includes", "type", "INVALID_FILE_FORMAT", "join", "compressImage", "options", "Promise", "resolve", "reject", "max<PERSON><PERSON><PERSON>", "MAX_WIDTH", "maxHeight", "MAX_HEIGHT", "quality", "COMPRESSION_QUALITY", "canvas", "document", "createElement", "ctx", "getContext", "img", "Image", "onload", "width", "height", "ratio", "min", "drawImage", "toBlob", "blob", "compressedFile", "File", "name", "lastModified", "Date", "now", "Error", "onerror", "src", "URL", "createObjectURL", "getImagePreviewUrl", "revokeImagePreviewUrl", "url", "revokeObjectURL", "imageToBase64", "reader", "FileReader", "result", "readAsDataURL", "base64ToImageFile", "base64", "filename", "arr", "split", "mime", "match", "bstr", "atob", "n", "length", "u8arr", "Uint8Array", "charCodeAt", "getImageDimensions", "naturalWidth", "naturalHeight", "rotateImage", "degrees", "translate", "rotate", "PI", "rotatedFile", "formatFileSize", "bytes", "k", "sizes", "i", "floor", "log", "parseFloat", "pow", "toFixed"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/utils/imageUtils.js"], "sourcesContent": ["/**\n * 圖片處理工具函數\n */\n\nimport { IMAGE_CONFIG, ERROR_MESSAGES } from './constants';\n\n/**\n * 驗證圖片文件\n * @param {File} file - 圖片文件\n * @returns {Object} 驗證結果 { isValid: boolean, error: string }\n */\nexport const validateImageFile = (file) => {\n  if (!file) {\n    return { isValid: false, error: '請選擇圖片文件' };\n  }\n\n  // 檢查文件大小\n  if (file.size > IMAGE_CONFIG.MAX_FILE_SIZE) {\n    return { \n      isValid: false, \n      error: `${ERROR_MESSAGES.FILE_TOO_LARGE}（最大${Math.round(IMAGE_CONFIG.MAX_FILE_SIZE / 1024 / 1024)}MB）` \n    };\n  }\n\n  // 檢查文件格式\n  if (!IMAGE_CONFIG.ACCEPTED_FORMATS.includes(file.type)) {\n    return { \n      isValid: false, \n      error: `${ERROR_MESSAGES.INVALID_FILE_FORMAT}，支持格式：${IMAGE_CONFIG.ACCEPTED_FORMATS.join(', ')}` \n    };\n  }\n\n  return { isValid: true, error: null };\n};\n\n/**\n * 壓縮圖片\n * @param {File} file - 原始圖片文件\n * @param {Object} options - 壓縮選項\n * @returns {Promise<File>} 壓縮後的圖片文件\n */\nexport const compressImage = (file, options = {}) => {\n  return new Promise((resolve, reject) => {\n    const {\n      maxWidth = IMAGE_CONFIG.MAX_WIDTH,\n      maxHeight = IMAGE_CONFIG.MAX_HEIGHT,\n      quality = IMAGE_CONFIG.COMPRESSION_QUALITY\n    } = options;\n\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n\n    img.onload = () => {\n      // 計算新的尺寸\n      let { width, height } = img;\n      \n      if (width > maxWidth || height > maxHeight) {\n        const ratio = Math.min(maxWidth / width, maxHeight / height);\n        width *= ratio;\n        height *= ratio;\n      }\n\n      // 設置畫布尺寸\n      canvas.width = width;\n      canvas.height = height;\n\n      // 繪製圖片\n      ctx.drawImage(img, 0, 0, width, height);\n\n      // 轉換為Blob\n      canvas.toBlob(\n        (blob) => {\n          if (blob) {\n            // 創建新的File對象\n            const compressedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(compressedFile);\n          } else {\n            reject(new Error('圖片壓縮失敗'));\n          }\n        },\n        file.type,\n        quality\n      );\n    };\n\n    img.onerror = () => {\n      reject(new Error('圖片加載失敗'));\n    };\n\n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * 獲取圖片預覽URL\n * @param {File} file - 圖片文件\n * @returns {string} 預覽URL\n */\nexport const getImagePreviewUrl = (file) => {\n  if (!file) return null;\n  return URL.createObjectURL(file);\n};\n\n/**\n * 釋放圖片預覽URL\n * @param {string} url - 預覽URL\n */\nexport const revokeImagePreviewUrl = (url) => {\n  if (url) {\n    URL.revokeObjectURL(url);\n  }\n};\n\n/**\n * 將圖片轉換為Base64\n * @param {File} file - 圖片文件\n * @returns {Promise<string>} Base64字符串\n */\nexport const imageToBase64 = (file) => {\n  return new Promise((resolve, reject) => {\n    const reader = new FileReader();\n    \n    reader.onload = () => {\n      resolve(reader.result);\n    };\n    \n    reader.onerror = () => {\n      reject(new Error('圖片轉換失敗'));\n    };\n    \n    reader.readAsDataURL(file);\n  });\n};\n\n/**\n * 從Base64創建圖片文件\n * @param {string} base64 - Base64字符串\n * @param {string} filename - 文件名\n * @returns {File} 圖片文件\n */\nexport const base64ToImageFile = (base64, filename = 'image.jpg') => {\n  const arr = base64.split(',');\n  const mime = arr[0].match(/:(.*?);/)[1];\n  const bstr = atob(arr[1]);\n  let n = bstr.length;\n  const u8arr = new Uint8Array(n);\n  \n  while (n--) {\n    u8arr[n] = bstr.charCodeAt(n);\n  }\n  \n  return new File([u8arr], filename, { type: mime });\n};\n\n/**\n * 獲取圖片尺寸信息\n * @param {File} file - 圖片文件\n * @returns {Promise<Object>} 尺寸信息 { width, height }\n */\nexport const getImageDimensions = (file) => {\n  return new Promise((resolve, reject) => {\n    const img = new Image();\n    \n    img.onload = () => {\n      resolve({\n        width: img.naturalWidth,\n        height: img.naturalHeight\n      });\n    };\n    \n    img.onerror = () => {\n      reject(new Error('無法獲取圖片尺寸'));\n    };\n    \n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * 旋轉圖片\n * @param {File} file - 圖片文件\n * @param {number} degrees - 旋轉角度（90, 180, 270）\n * @returns {Promise<File>} 旋轉後的圖片文件\n */\nexport const rotateImage = (file, degrees) => {\n  return new Promise((resolve, reject) => {\n    const canvas = document.createElement('canvas');\n    const ctx = canvas.getContext('2d');\n    const img = new Image();\n\n    img.onload = () => {\n      const { width, height } = img;\n      \n      // 根據旋轉角度設置畫布尺寸\n      if (degrees === 90 || degrees === 270) {\n        canvas.width = height;\n        canvas.height = width;\n      } else {\n        canvas.width = width;\n        canvas.height = height;\n      }\n\n      // 移動到畫布中心\n      ctx.translate(canvas.width / 2, canvas.height / 2);\n      \n      // 旋轉\n      ctx.rotate((degrees * Math.PI) / 180);\n      \n      // 繪製圖片\n      ctx.drawImage(img, -width / 2, -height / 2);\n\n      // 轉換為Blob\n      canvas.toBlob(\n        (blob) => {\n          if (blob) {\n            const rotatedFile = new File([blob], file.name, {\n              type: file.type,\n              lastModified: Date.now()\n            });\n            resolve(rotatedFile);\n          } else {\n            reject(new Error('圖片旋轉失敗'));\n          }\n        },\n        file.type,\n        IMAGE_CONFIG.COMPRESSION_QUALITY\n      );\n    };\n\n    img.onerror = () => {\n      reject(new Error('圖片加載失敗'));\n    };\n\n    img.src = URL.createObjectURL(file);\n  });\n};\n\n/**\n * 格式化文件大小\n * @param {number} bytes - 字節數\n * @returns {string} 格式化後的大小\n */\nexport const formatFileSize = (bytes) => {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\nexport default {\n  validateImageFile,\n  compressImage,\n  getImagePreviewUrl,\n  revokeImagePreviewUrl,\n  imageToBase64,\n  base64ToImageFile,\n  getImageDimensions,\n  rotateImage,\n  formatFileSize\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA,SAASA,YAAY,EAAEC,cAAc,QAAQ,aAAa;;AAE1D;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAIC,IAAI,IAAK;EACzC,IAAI,CAACA,IAAI,EAAE;IACT,OAAO;MAAEC,OAAO,EAAE,KAAK;MAAEC,KAAK,EAAE;IAAU,CAAC;EAC7C;;EAEA;EACA,IAAIF,IAAI,CAACG,IAAI,GAAGN,YAAY,CAACO,aAAa,EAAE;IAC1C,OAAO;MACLH,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,GAAGJ,cAAc,CAACO,cAAc,MAAMC,IAAI,CAACC,KAAK,CAACV,YAAY,CAACO,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC;IACnG,CAAC;EACH;;EAEA;EACA,IAAI,CAACP,YAAY,CAACW,gBAAgB,CAACC,QAAQ,CAACT,IAAI,CAACU,IAAI,CAAC,EAAE;IACtD,OAAO;MACLT,OAAO,EAAE,KAAK;MACdC,KAAK,EAAE,GAAGJ,cAAc,CAACa,mBAAmB,SAASd,YAAY,CAACW,gBAAgB,CAACI,IAAI,CAAC,IAAI,CAAC;IAC/F,CAAC;EACH;EAEA,OAAO;IAAEX,OAAO,EAAE,IAAI;IAAEC,KAAK,EAAE;EAAK,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,aAAa,GAAGA,CAACb,IAAI,EAAEc,OAAO,GAAG,CAAC,CAAC,KAAK;EACnD,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAM;MACJC,QAAQ,GAAGrB,YAAY,CAACsB,SAAS;MACjCC,SAAS,GAAGvB,YAAY,CAACwB,UAAU;MACnCC,OAAO,GAAGzB,YAAY,CAAC0B;IACzB,CAAC,GAAGT,OAAO;IAEX,MAAMU,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IAEvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB;MACA,IAAI;QAAEC,KAAK;QAAEC;MAAO,CAAC,GAAGJ,GAAG;MAE3B,IAAIG,KAAK,GAAGd,QAAQ,IAAIe,MAAM,GAAGb,SAAS,EAAE;QAC1C,MAAMc,KAAK,GAAG5B,IAAI,CAAC6B,GAAG,CAACjB,QAAQ,GAAGc,KAAK,EAAEZ,SAAS,GAAGa,MAAM,CAAC;QAC5DD,KAAK,IAAIE,KAAK;QACdD,MAAM,IAAIC,KAAK;MACjB;;MAEA;MACAV,MAAM,CAACQ,KAAK,GAAGA,KAAK;MACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;;MAEtB;MACAN,GAAG,CAACS,SAAS,CAACP,GAAG,EAAE,CAAC,EAAE,CAAC,EAAEG,KAAK,EAAEC,MAAM,CAAC;;MAEvC;MACAT,MAAM,CAACa,MAAM,CACVC,IAAI,IAAK;QACR,IAAIA,IAAI,EAAE;UACR;UACA,MAAMC,cAAc,GAAG,IAAIC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAEtC,IAAI,CAACyC,IAAI,EAAE;YACjD/B,IAAI,EAAEV,IAAI,CAACU,IAAI;YACfgC,YAAY,EAAEC,IAAI,CAACC,GAAG,CAAC;UACzB,CAAC,CAAC;UACF5B,OAAO,CAACuB,cAAc,CAAC;QACzB,CAAC,MAAM;UACLtB,MAAM,CAAC,IAAI4B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B;MACF,CAAC,EACD7C,IAAI,CAACU,IAAI,EACTY,OACF,CAAC;IACH,CAAC;IAEDO,GAAG,CAACiB,OAAO,GAAG,MAAM;MAClB7B,MAAM,CAAC,IAAI4B,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEDhB,GAAG,CAACkB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACjD,IAAI,CAAC;EACrC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkD,kBAAkB,GAAIlD,IAAI,IAAK;EAC1C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;EACtB,OAAOgD,GAAG,CAACC,eAAe,CAACjD,IAAI,CAAC;AAClC,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMmD,qBAAqB,GAAIC,GAAG,IAAK;EAC5C,IAAIA,GAAG,EAAE;IACPJ,GAAG,CAACK,eAAe,CAACD,GAAG,CAAC;EAC1B;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAItD,IAAI,IAAK;EACrC,OAAO,IAAIe,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMsC,MAAM,GAAG,IAAIC,UAAU,CAAC,CAAC;IAE/BD,MAAM,CAACxB,MAAM,GAAG,MAAM;MACpBf,OAAO,CAACuC,MAAM,CAACE,MAAM,CAAC;IACxB,CAAC;IAEDF,MAAM,CAACT,OAAO,GAAG,MAAM;MACrB7B,MAAM,CAAC,IAAI4B,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEDU,MAAM,CAACG,aAAa,CAAC1D,IAAI,CAAC;EAC5B,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM2D,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,QAAQ,GAAG,WAAW,KAAK;EACnE,MAAMC,GAAG,GAAGF,MAAM,CAACG,KAAK,CAAC,GAAG,CAAC;EAC7B,MAAMC,IAAI,GAAGF,GAAG,CAAC,CAAC,CAAC,CAACG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC;EACvC,MAAMC,IAAI,GAAGC,IAAI,CAACL,GAAG,CAAC,CAAC,CAAC,CAAC;EACzB,IAAIM,CAAC,GAAGF,IAAI,CAACG,MAAM;EACnB,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAACH,CAAC,CAAC;EAE/B,OAAOA,CAAC,EAAE,EAAE;IACVE,KAAK,CAACF,CAAC,CAAC,GAAGF,IAAI,CAACM,UAAU,CAACJ,CAAC,CAAC;EAC/B;EAEA,OAAO,IAAI5B,IAAI,CAAC,CAAC8B,KAAK,CAAC,EAAET,QAAQ,EAAE;IAAEnD,IAAI,EAAEsD;EAAK,CAAC,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,kBAAkB,GAAIzE,IAAI,IAAK;EAC1C,OAAO,IAAIe,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMY,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IAEvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjBf,OAAO,CAAC;QACNgB,KAAK,EAAEH,GAAG,CAAC6C,YAAY;QACvBzC,MAAM,EAAEJ,GAAG,CAAC8C;MACd,CAAC,CAAC;IACJ,CAAC;IAED9C,GAAG,CAACiB,OAAO,GAAG,MAAM;MAClB7B,MAAM,CAAC,IAAI4B,KAAK,CAAC,UAAU,CAAC,CAAC;IAC/B,CAAC;IAEDhB,GAAG,CAACkB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACjD,IAAI,CAAC;EACrC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM4E,WAAW,GAAGA,CAAC5E,IAAI,EAAE6E,OAAO,KAAK;EAC5C,OAAO,IAAI9D,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;IACtC,MAAMO,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;IAC/C,MAAMC,GAAG,GAAGH,MAAM,CAACI,UAAU,CAAC,IAAI,CAAC;IACnC,MAAMC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;IAEvBD,GAAG,CAACE,MAAM,GAAG,MAAM;MACjB,MAAM;QAAEC,KAAK;QAAEC;MAAO,CAAC,GAAGJ,GAAG;;MAE7B;MACA,IAAIgD,OAAO,KAAK,EAAE,IAAIA,OAAO,KAAK,GAAG,EAAE;QACrCrD,MAAM,CAACQ,KAAK,GAAGC,MAAM;QACrBT,MAAM,CAACS,MAAM,GAAGD,KAAK;MACvB,CAAC,MAAM;QACLR,MAAM,CAACQ,KAAK,GAAGA,KAAK;QACpBR,MAAM,CAACS,MAAM,GAAGA,MAAM;MACxB;;MAEA;MACAN,GAAG,CAACmD,SAAS,CAACtD,MAAM,CAACQ,KAAK,GAAG,CAAC,EAAER,MAAM,CAACS,MAAM,GAAG,CAAC,CAAC;;MAElD;MACAN,GAAG,CAACoD,MAAM,CAAEF,OAAO,GAAGvE,IAAI,CAAC0E,EAAE,GAAI,GAAG,CAAC;;MAErC;MACArD,GAAG,CAACS,SAAS,CAACP,GAAG,EAAE,CAACG,KAAK,GAAG,CAAC,EAAE,CAACC,MAAM,GAAG,CAAC,CAAC;;MAE3C;MACAT,MAAM,CAACa,MAAM,CACVC,IAAI,IAAK;QACR,IAAIA,IAAI,EAAE;UACR,MAAM2C,WAAW,GAAG,IAAIzC,IAAI,CAAC,CAACF,IAAI,CAAC,EAAEtC,IAAI,CAACyC,IAAI,EAAE;YAC9C/B,IAAI,EAAEV,IAAI,CAACU,IAAI;YACfgC,YAAY,EAAEC,IAAI,CAACC,GAAG,CAAC;UACzB,CAAC,CAAC;UACF5B,OAAO,CAACiE,WAAW,CAAC;QACtB,CAAC,MAAM;UACLhE,MAAM,CAAC,IAAI4B,KAAK,CAAC,QAAQ,CAAC,CAAC;QAC7B;MACF,CAAC,EACD7C,IAAI,CAACU,IAAI,EACTb,YAAY,CAAC0B,mBACf,CAAC;IACH,CAAC;IAEDM,GAAG,CAACiB,OAAO,GAAG,MAAM;MAClB7B,MAAM,CAAC,IAAI4B,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC7B,CAAC;IAEDhB,GAAG,CAACkB,GAAG,GAAGC,GAAG,CAACC,eAAe,CAACjD,IAAI,CAAC;EACrC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkF,cAAc,GAAIC,KAAK,IAAK;EACvC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAMC,CAAC,GAAG,IAAI;EACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,CAAC,GAAGhF,IAAI,CAACiF,KAAK,CAACjF,IAAI,CAACkF,GAAG,CAACL,KAAK,CAAC,GAAG7E,IAAI,CAACkF,GAAG,CAACJ,CAAC,CAAC,CAAC;EAEnD,OAAOK,UAAU,CAAC,CAACN,KAAK,GAAG7E,IAAI,CAACoF,GAAG,CAACN,CAAC,EAAEE,CAAC,CAAC,EAAEK,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGN,KAAK,CAACC,CAAC,CAAC;AACzE,CAAC;AAED,eAAe;EACbvF,iBAAiB;EACjBc,aAAa;EACbqC,kBAAkB;EAClBC,qBAAqB;EACrBG,aAAa;EACbK,iBAAiB;EACjBc,kBAAkB;EAClBG,WAAW;EACXM;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}