{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState, useRef } from 'react';\nimport screenfull from 'screenfull';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isBoolean } from '../utils';\nvar useFullscreen = function (target, options) {\n  var _a = options || {},\n    onExit = _a.onExit,\n    onEnter = _a.onEnter,\n    _b = _a.pageFullscreen,\n    pageFullscreen = _b === void 0 ? false : _b;\n  var _c = isBoolean(pageFullscreen) || !pageFullscreen ? {} : pageFullscreen,\n    _d = _c.className,\n    className = _d === void 0 ? 'ahooks-page-fullscreen' : _d,\n    _e = _c.zIndex,\n    zIndex = _e === void 0 ? 999999 : _e;\n  var onExitRef = useLatest(onExit);\n  var onEnterRef = useLatest(onEnter);\n  // The state of full screen may be changed by other scripts/components,\n  // so the initial value needs to be computed dynamically.\n  var _f = __read(useState(getIsFullscreen), 2),\n    state = _f[0],\n    setState = _f[1];\n  var stateRef = useRef(getIsFullscreen());\n  function getIsFullscreen() {\n    return screenfull.isEnabled && !!screenfull.element && screenfull.element === getTargetElement(target);\n  }\n  var invokeCallback = function (fullscreen) {\n    var _a, _b;\n    if (fullscreen) {\n      (_a = onEnterRef.current) === null || _a === void 0 ? void 0 : _a.call(onEnterRef);\n    } else {\n      (_b = onExitRef.current) === null || _b === void 0 ? void 0 : _b.call(onExitRef);\n    }\n  };\n  var updateFullscreenState = function (fullscreen) {\n    // Prevent repeated calls when the state is not changed.\n    if (stateRef.current !== fullscreen) {\n      invokeCallback(fullscreen);\n      setState(fullscreen);\n      stateRef.current = fullscreen;\n    }\n  };\n  var onScreenfullChange = function () {\n    var fullscreen = getIsFullscreen();\n    updateFullscreenState(fullscreen);\n  };\n  var togglePageFullscreen = function (fullscreen) {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var styleElem = document.getElementById(className);\n    if (fullscreen) {\n      el.classList.add(className);\n      if (!styleElem) {\n        styleElem = document.createElement('style');\n        styleElem.setAttribute('id', className);\n        styleElem.textContent = \"\\n          .\".concat(className, \" {\\n            position: fixed; left: 0; top: 0; right: 0; bottom: 0;\\n            width: 100% !important; height: 100% !important;\\n            z-index: \").concat(zIndex, \";\\n          }\");\n        el.appendChild(styleElem);\n      }\n    } else {\n      el.classList.remove(className);\n      if (styleElem) {\n        styleElem.remove();\n      }\n    }\n    updateFullscreenState(fullscreen);\n  };\n  var enterFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(true);\n      return;\n    }\n    if (screenfull.isEnabled) {\n      try {\n        screenfull.request(el);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  var exitFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(false);\n      return;\n    }\n    if (screenfull.isEnabled && screenfull.element === el) {\n      screenfull.exit();\n    }\n  };\n  var toggleFullscreen = function () {\n    if (state) {\n      exitFullscreen();\n    } else {\n      enterFullscreen();\n    }\n  };\n  useEffect(function () {\n    if (!screenfull.isEnabled || pageFullscreen) {\n      return;\n    }\n    screenfull.on('change', onScreenfullChange);\n    return function () {\n      screenfull.off('change', onScreenfullChange);\n    };\n  }, []);\n  return [state, {\n    enterFullscreen: useMemoizedFn(enterFullscreen),\n    exitFullscreen: useMemoizedFn(exitFullscreen),\n    toggleFullscreen: useMemoizedFn(toggleFullscreen),\n    isEnabled: screenfull.isEnabled\n  }];\n};\nexport default useFullscreen;", "map": {"version": 3, "names": ["__read", "useEffect", "useState", "useRef", "screenfull", "useLatest", "useMemoizedFn", "getTargetElement", "isBoolean", "useFullscreen", "target", "options", "_a", "onExit", "onEnter", "_b", "pageFullscreen", "_c", "_d", "className", "_e", "zIndex", "onExitRef", "onEnterRef", "_f", "getIsFullscreen", "state", "setState", "stateRef", "isEnabled", "element", "invokeCallback", "fullscreen", "current", "call", "updateFullscreenState", "onScreenfullChange", "togglePageFullscreen", "el", "styleElem", "document", "getElementById", "classList", "add", "createElement", "setAttribute", "textContent", "concat", "append<PERSON><PERSON><PERSON>", "remove", "enterFullscreen", "request", "error", "console", "exitFullscreen", "exit", "toggleFullscreen", "on", "off"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useFullscreen/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useState, useRef } from 'react';\nimport screenfull from 'screenfull';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport { getTargetElement } from '../utils/domTarget';\nimport { isBoolean } from '../utils';\nvar useFullscreen = function (target, options) {\n  var _a = options || {},\n    onExit = _a.onExit,\n    onEnter = _a.onEnter,\n    _b = _a.pageFullscreen,\n    pageFullscreen = _b === void 0 ? false : _b;\n  var _c = isBoolean(pageFullscreen) || !pageFullscreen ? {} : pageFullscreen,\n    _d = _c.className,\n    className = _d === void 0 ? 'ahooks-page-fullscreen' : _d,\n    _e = _c.zIndex,\n    zIndex = _e === void 0 ? 999999 : _e;\n  var onExitRef = useLatest(onExit);\n  var onEnterRef = useLatest(onEnter);\n  // The state of full screen may be changed by other scripts/components,\n  // so the initial value needs to be computed dynamically.\n  var _f = __read(useState(getIsFullscreen), 2),\n    state = _f[0],\n    setState = _f[1];\n  var stateRef = useRef(getIsFullscreen());\n  function getIsFullscreen() {\n    return screenfull.isEnabled && !!screenfull.element && screenfull.element === getTargetElement(target);\n  }\n  var invokeCallback = function (fullscreen) {\n    var _a, _b;\n    if (fullscreen) {\n      (_a = onEnterRef.current) === null || _a === void 0 ? void 0 : _a.call(onEnterRef);\n    } else {\n      (_b = onExitRef.current) === null || _b === void 0 ? void 0 : _b.call(onExitRef);\n    }\n  };\n  var updateFullscreenState = function (fullscreen) {\n    // Prevent repeated calls when the state is not changed.\n    if (stateRef.current !== fullscreen) {\n      invokeCallback(fullscreen);\n      setState(fullscreen);\n      stateRef.current = fullscreen;\n    }\n  };\n  var onScreenfullChange = function () {\n    var fullscreen = getIsFullscreen();\n    updateFullscreenState(fullscreen);\n  };\n  var togglePageFullscreen = function (fullscreen) {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var styleElem = document.getElementById(className);\n    if (fullscreen) {\n      el.classList.add(className);\n      if (!styleElem) {\n        styleElem = document.createElement('style');\n        styleElem.setAttribute('id', className);\n        styleElem.textContent = \"\\n          .\".concat(className, \" {\\n            position: fixed; left: 0; top: 0; right: 0; bottom: 0;\\n            width: 100% !important; height: 100% !important;\\n            z-index: \").concat(zIndex, \";\\n          }\");\n        el.appendChild(styleElem);\n      }\n    } else {\n      el.classList.remove(className);\n      if (styleElem) {\n        styleElem.remove();\n      }\n    }\n    updateFullscreenState(fullscreen);\n  };\n  var enterFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(true);\n      return;\n    }\n    if (screenfull.isEnabled) {\n      try {\n        screenfull.request(el);\n      } catch (error) {\n        console.error(error);\n      }\n    }\n  };\n  var exitFullscreen = function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    if (pageFullscreen) {\n      togglePageFullscreen(false);\n      return;\n    }\n    if (screenfull.isEnabled && screenfull.element === el) {\n      screenfull.exit();\n    }\n  };\n  var toggleFullscreen = function () {\n    if (state) {\n      exitFullscreen();\n    } else {\n      enterFullscreen();\n    }\n  };\n  useEffect(function () {\n    if (!screenfull.isEnabled || pageFullscreen) {\n      return;\n    }\n    screenfull.on('change', onScreenfullChange);\n    return function () {\n      screenfull.off('change', onScreenfullChange);\n    };\n  }, []);\n  return [state, {\n    enterFullscreen: useMemoizedFn(enterFullscreen),\n    exitFullscreen: useMemoizedFn(exitFullscreen),\n    toggleFullscreen: useMemoizedFn(toggleFullscreen),\n    isEnabled: screenfull.isEnabled\n  }];\n};\nexport default useFullscreen;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACnD,OAAOC,UAAU,MAAM,YAAY;AACnC,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,SAAS,QAAQ,UAAU;AACpC,IAAIC,aAAa,GAAG,SAAAA,CAAUC,MAAM,EAAEC,OAAO,EAAE;EAC7C,IAAIC,EAAE,GAAGD,OAAO,IAAI,CAAC,CAAC;IACpBE,MAAM,GAAGD,EAAE,CAACC,MAAM;IAClBC,OAAO,GAAGF,EAAE,CAACE,OAAO;IACpBC,EAAE,GAAGH,EAAE,CAACI,cAAc;IACtBA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;EAC7C,IAAIE,EAAE,GAAGT,SAAS,CAACQ,cAAc,CAAC,IAAI,CAACA,cAAc,GAAG,CAAC,CAAC,GAAGA,cAAc;IACzEE,EAAE,GAAGD,EAAE,CAACE,SAAS;IACjBA,SAAS,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,wBAAwB,GAAGA,EAAE;IACzDE,EAAE,GAAGH,EAAE,CAACI,MAAM;IACdA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,MAAM,GAAGA,EAAE;EACtC,IAAIE,SAAS,GAAGjB,SAAS,CAACQ,MAAM,CAAC;EACjC,IAAIU,UAAU,GAAGlB,SAAS,CAACS,OAAO,CAAC;EACnC;EACA;EACA,IAAIU,EAAE,GAAGxB,MAAM,CAACE,QAAQ,CAACuB,eAAe,CAAC,EAAE,CAAC,CAAC;IAC3CC,KAAK,GAAGF,EAAE,CAAC,CAAC,CAAC;IACbG,QAAQ,GAAGH,EAAE,CAAC,CAAC,CAAC;EAClB,IAAII,QAAQ,GAAGzB,MAAM,CAACsB,eAAe,CAAC,CAAC,CAAC;EACxC,SAASA,eAAeA,CAAA,EAAG;IACzB,OAAOrB,UAAU,CAACyB,SAAS,IAAI,CAAC,CAACzB,UAAU,CAAC0B,OAAO,IAAI1B,UAAU,CAAC0B,OAAO,KAAKvB,gBAAgB,CAACG,MAAM,CAAC;EACxG;EACA,IAAIqB,cAAc,GAAG,SAAAA,CAAUC,UAAU,EAAE;IACzC,IAAIpB,EAAE,EAAEG,EAAE;IACV,IAAIiB,UAAU,EAAE;MACd,CAACpB,EAAE,GAAGW,UAAU,CAACU,OAAO,MAAM,IAAI,IAAIrB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACsB,IAAI,CAACX,UAAU,CAAC;IACpF,CAAC,MAAM;MACL,CAACR,EAAE,GAAGO,SAAS,CAACW,OAAO,MAAM,IAAI,IAAIlB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACmB,IAAI,CAACZ,SAAS,CAAC;IAClF;EACF,CAAC;EACD,IAAIa,qBAAqB,GAAG,SAAAA,CAAUH,UAAU,EAAE;IAChD;IACA,IAAIJ,QAAQ,CAACK,OAAO,KAAKD,UAAU,EAAE;MACnCD,cAAc,CAACC,UAAU,CAAC;MAC1BL,QAAQ,CAACK,UAAU,CAAC;MACpBJ,QAAQ,CAACK,OAAO,GAAGD,UAAU;IAC/B;EACF,CAAC;EACD,IAAII,kBAAkB,GAAG,SAAAA,CAAA,EAAY;IACnC,IAAIJ,UAAU,GAAGP,eAAe,CAAC,CAAC;IAClCU,qBAAqB,CAACH,UAAU,CAAC;EACnC,CAAC;EACD,IAAIK,oBAAoB,GAAG,SAAAA,CAAUL,UAAU,EAAE;IAC/C,IAAIM,EAAE,GAAG/B,gBAAgB,CAACG,MAAM,CAAC;IACjC,IAAI,CAAC4B,EAAE,EAAE;MACP;IACF;IACA,IAAIC,SAAS,GAAGC,QAAQ,CAACC,cAAc,CAACtB,SAAS,CAAC;IAClD,IAAIa,UAAU,EAAE;MACdM,EAAE,CAACI,SAAS,CAACC,GAAG,CAACxB,SAAS,CAAC;MAC3B,IAAI,CAACoB,SAAS,EAAE;QACdA,SAAS,GAAGC,QAAQ,CAACI,aAAa,CAAC,OAAO,CAAC;QAC3CL,SAAS,CAACM,YAAY,CAAC,IAAI,EAAE1B,SAAS,CAAC;QACvCoB,SAAS,CAACO,WAAW,GAAG,eAAe,CAACC,MAAM,CAAC5B,SAAS,EAAE,6JAA6J,CAAC,CAAC4B,MAAM,CAAC1B,MAAM,EAAE,gBAAgB,CAAC;QACzPiB,EAAE,CAACU,WAAW,CAACT,SAAS,CAAC;MAC3B;IACF,CAAC,MAAM;MACLD,EAAE,CAACI,SAAS,CAACO,MAAM,CAAC9B,SAAS,CAAC;MAC9B,IAAIoB,SAAS,EAAE;QACbA,SAAS,CAACU,MAAM,CAAC,CAAC;MACpB;IACF;IACAd,qBAAqB,CAACH,UAAU,CAAC;EACnC,CAAC;EACD,IAAIkB,eAAe,GAAG,SAAAA,CAAA,EAAY;IAChC,IAAIZ,EAAE,GAAG/B,gBAAgB,CAACG,MAAM,CAAC;IACjC,IAAI,CAAC4B,EAAE,EAAE;MACP;IACF;IACA,IAAItB,cAAc,EAAE;MAClBqB,oBAAoB,CAAC,IAAI,CAAC;MAC1B;IACF;IACA,IAAIjC,UAAU,CAACyB,SAAS,EAAE;MACxB,IAAI;QACFzB,UAAU,CAAC+C,OAAO,CAACb,EAAE,CAAC;MACxB,CAAC,CAAC,OAAOc,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAACA,KAAK,CAAC;MACtB;IACF;EACF,CAAC;EACD,IAAIE,cAAc,GAAG,SAAAA,CAAA,EAAY;IAC/B,IAAIhB,EAAE,GAAG/B,gBAAgB,CAACG,MAAM,CAAC;IACjC,IAAI,CAAC4B,EAAE,EAAE;MACP;IACF;IACA,IAAItB,cAAc,EAAE;MAClBqB,oBAAoB,CAAC,KAAK,CAAC;MAC3B;IACF;IACA,IAAIjC,UAAU,CAACyB,SAAS,IAAIzB,UAAU,CAAC0B,OAAO,KAAKQ,EAAE,EAAE;MACrDlC,UAAU,CAACmD,IAAI,CAAC,CAAC;IACnB;EACF,CAAC;EACD,IAAIC,gBAAgB,GAAG,SAAAA,CAAA,EAAY;IACjC,IAAI9B,KAAK,EAAE;MACT4B,cAAc,CAAC,CAAC;IAClB,CAAC,MAAM;MACLJ,eAAe,CAAC,CAAC;IACnB;EACF,CAAC;EACDjD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,UAAU,CAACyB,SAAS,IAAIb,cAAc,EAAE;MAC3C;IACF;IACAZ,UAAU,CAACqD,EAAE,CAAC,QAAQ,EAAErB,kBAAkB,CAAC;IAC3C,OAAO,YAAY;MACjBhC,UAAU,CAACsD,GAAG,CAAC,QAAQ,EAAEtB,kBAAkB,CAAC;IAC9C,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACV,KAAK,EAAE;IACbwB,eAAe,EAAE5C,aAAa,CAAC4C,eAAe,CAAC;IAC/CI,cAAc,EAAEhD,aAAa,CAACgD,cAAc,CAAC;IAC7CE,gBAAgB,EAAElD,aAAa,CAACkD,gBAAgB,CAAC;IACjD3B,SAAS,EAAEzB,UAAU,CAACyB;EACxB,CAAC,CAAC;AACJ,CAAC;AACD,eAAepB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}