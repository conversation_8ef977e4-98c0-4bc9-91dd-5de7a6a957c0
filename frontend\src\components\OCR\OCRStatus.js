/**
 * OCR狀態顯示組件
 */

import React from 'react';
import { Card, ProgressBar } from 'antd-mobile';
import { 
  CheckCircleOutline, 
  ExclamationCircleOutline, 
  ScanningOutline 
} from 'antd-mobile-icons';
import { OCR_STATUS } from '../../utils/constants';

const OCRStatus = ({
  status = OCR_STATUS.IDLE,
  error = null,
  frontStatus = OCR_STATUS.IDLE,
  backStatus = OCR_STATUS.IDLE,
  showDetails = false,
  style = {},
  className = ''
}) => {
  // 獲取狀態圖標和顏色
  const getStatusInfo = (currentStatus) => {
    switch (currentStatus) {
      case OCR_STATUS.PROCESSING:
        return {
          icon: <ScanningOutline />,
          color: '#1677ff',
          text: '識別中...'
        };
      case OCR_STATUS.SUCCESS:
        return {
          icon: <CheckCircleOutline />,
          color: '#52c41a',
          text: '識別完成'
        };
      case OCR_STATUS.ERROR:
        return {
          icon: <ExclamationCircleOutline />,
          color: '#ff4d4f',
          text: '識別失敗'
        };
      default:
        return {
          icon: <ScanningOutline />,
          color: '#8c8c8c',
          text: '待識別'
        };
    }
  };

  const statusInfo = getStatusInfo(status);

  return (
    <Card 
      className={`ocr-status ${className}`}
      style={style}
    >
      <div style={{ textAlign: 'center', padding: '16px' }}>
        {/* 主狀態顯示 */}
        <div style={{ marginBottom: '16px' }}>
          <div 
            style={{ 
              fontSize: '48px', 
              color: statusInfo.color,
              marginBottom: '8px'
            }}
          >
            {statusInfo.icon}
          </div>
          
          <div 
            style={{ 
              fontSize: '16px', 
              fontWeight: 'bold',
              color: statusInfo.color,
              marginBottom: '4px'
            }}
          >
            {statusInfo.text}
          </div>

          {/* 處理中的進度條 */}
          {status === OCR_STATUS.PROCESSING && (
            <div style={{ margin: '16px 0' }}>
              <ProgressBar 
                percent={50} 
                style={{ 
                  '--track-width': '4px',
                  '--fill-color': statusInfo.color
                }}
              />
              <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '8px' }}>
                正在分析圖片內容...
              </div>
            </div>
          )}

          {/* 錯誤信息 */}
          {status === OCR_STATUS.ERROR && error && (
            <div 
              style={{ 
                fontSize: '14px', 
                color: '#ff4d4f',
                backgroundColor: '#fff2f0',
                padding: '8px 12px',
                borderRadius: '6px',
                border: '1px solid #ffccc7',
                marginTop: '8px'
              }}
            >
              {error}
            </div>
          )}
        </div>

        {/* 詳細狀態顯示 */}
        {showDetails && (
          <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>
            <div style={{ display: 'flex', justifyContent: 'space-around' }}>
              {/* 正面狀態 */}
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>
                  正面
                </div>
                <div 
                  style={{ 
                    fontSize: '24px', 
                    color: getStatusInfo(frontStatus).color 
                  }}
                >
                  {getStatusInfo(frontStatus).icon}
                </div>
                <div style={{ fontSize: '10px', color: '#8c8c8c' }}>
                  {getStatusInfo(frontStatus).text}
                </div>
              </div>

              {/* 反面狀態 */}
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>
                  反面
                </div>
                <div 
                  style={{ 
                    fontSize: '24px', 
                    color: getStatusInfo(backStatus).color 
                  }}
                >
                  {getStatusInfo(backStatus).icon}
                </div>
                <div style={{ fontSize: '10px', color: '#8c8c8c' }}>
                  {getStatusInfo(backStatus).text}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default OCRStatus;
