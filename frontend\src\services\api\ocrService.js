/**
 * OCR相關API服務
 * 處理圖片識別和智能解析功能
 */

import apiClient from './apiClient';

export const ocrService = {
  /**
   * OCR圖片識別
   * @param {File} imageFile - 圖片文件
   * @returns {Promise<Object>} OCR識別結果
   */
  processImage: async (imageFile) => {
    try {
      const formData = new FormData();
      formData.append('file', imageFile);
      
      const response = await apiClient.post('/ocr/image', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data;
    } catch (error) {
      console.error('OCR圖片識別失敗:', error);
      throw error;
    }
  },

  /**
   * 智能解析OCR文字到標準化欄位
   * @param {string} ocrText - OCR識別的文字
   * @param {string} side - 名片面（'front' 或 'back'）
   * @returns {Promise<Object>} 解析後的欄位數據
   */
  parseFields: async (ocrText, side = 'front') => {
    try {
      const response = await apiClient.post('/ocr/parse-fields', {
        ocr_text: ocrText,
        side: side,
      });
      
      return response.data;
    } catch (error) {
      console.error('OCR文字解析失敗:', error);
      throw error;
    }
  },

  /**
   * 批量處理圖片（如果需要）
   * @param {File[]} imageFiles - 圖片文件數組
   * @returns {Promise<Object[]>} 批量處理結果
   */
  processBatchImages: async (imageFiles) => {
    try {
      const promises = imageFiles.map(file => ocrService.processImage(file));
      const results = await Promise.all(promises);
      return results;
    } catch (error) {
      console.error('批量OCR處理失敗:', error);
      throw error;
    }
  }
};

export default ocrService;
