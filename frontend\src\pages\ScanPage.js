/**
 * 掃描頁面 - 重構版本
 * 集成相機拍攝、OCR處理和表單填寫功能
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Space, Divider } from 'antd-mobile';
import { PageContainer } from '../components/Layout';
import { CameraCapture } from '../components/Camera';
import { OCRProcessor } from '../components/OCR';
import { CardForm } from '../components/Form';
import { LoadingSpinner } from '../components/UI';
import { useCardData, useOCRState, useCameraState } from '../hooks';

const ScanPage = () => {
  const navigate = useNavigate();
  const { createCard, loading: cardLoading } = useCardData();
  const { 
    processImage, 
    frontOCR, 
    backOCR, 
    getMergedFields,
    isProcessing: ocrProcessing 
  } = useOCRState();
  const { 
    images, 
    getImagesForSubmit,
    updateOCRResult 
  } = useCameraState();

  const [formData, setFormData] = useState({
    name: '',
    company_name: '',
    position: '',
    mobile_phone: '',
    office_phone: '',
    email: '',
    line_id: '',
    notes: '',
    company_address_1: '',
    company_address_2: ''
  });

  // 處理圖片拍攝完成
  const handleImageCaptured = async (imageFile, side) => {
    console.log(`📸 圖片拍攝完成 - ${side}面:`, imageFile.name);
    
    try {
      // 更新圖片解析狀態為處理中
      updateOCRResult(side, '', 'processing');
      
      // 執行OCR處理
      const ocrText = await processImage(imageFile, side);
      
      // 更新OCR結果
      updateOCRResult(side, ocrText, 'success');
      
    } catch (error) {
      console.error(`❌ OCR處理失敗 - ${side}面:`, error);
      updateOCRResult(side, '', 'error');
    }
  };

  // 處理OCR解析完成
  const handleFieldsParsed = (mergedFields, frontFields, backFields) => {
    console.log('🧠 OCR解析完成:', { mergedFields, frontFields, backFields });
    
    // 自動填充表單（只填充空欄位）
    setFormData(prev => {
      const updated = { ...prev };
      
      Object.keys(mergedFields).forEach(key => {
        if (mergedFields[key] && (!prev[key] || prev[key].trim() === '')) {
          updated[key] = mergedFields[key];
        }
      });
      
      return updated;
    });
  };

  // 處理表單提交
  const handleFormSubmit = async (formDataToSubmit) => {
    try {
      const images = getImagesForSubmit();
      const newCard = await createCard(formDataToSubmit, images);
      
      console.log('✅ 名片創建成功:', newCard);
      
      // 導航到名片管理頁面
      navigate('/cards');
    } catch (error) {
      console.error('❌ 名片創建失敗:', error);
    }
  };

  // 處理表單數據變更
  const handleFormDataChange = (newFormData) => {
    setFormData(newFormData);
  };

  const isLoading = cardLoading || ocrProcessing;

  return (
    <PageContainer
      title="掃描名片"
      onBack={() => navigate('/')}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 相機拍攝區域 */}
        <CameraCapture
          onImageCaptured={handleImageCaptured}
          title="拍攝名片"
        />

        {/* OCR處理區域 */}
        {(images.front.file || images.back.file) && (
          <>
            <Divider>OCR識別結果</Divider>
            <OCRProcessor
              frontImage={images.front}
              backImage={images.back}
              onFieldsParsed={handleFieldsParsed}
              autoProcess={true}
              showStatus={true}
              showResults={true}
            />
          </>
        )}

        {/* 表單填寫區域 */}
        <Divider>名片資訊</Divider>
        
        {isLoading ? (
          <LoadingSpinner 
            text="處理中..." 
            style={{ minHeight: '200px' }}
          />
        ) : (
          <CardForm
            initialData={formData}
            onSubmit={handleFormSubmit}
            loading={cardLoading}
            submitText="保存名片"
            title="編輯名片資訊"
          />
        )}
      </Space>
    </PageContainer>
  );
};

export default ScanPage;
