{"ast": null, "code": "/**\n * 數據格式化工具函數\n */\n\n/**\n * 格式化電話號碼\n * @param {string} phone - 原始電話號碼\n * @returns {string} 格式化後的電話號碼\n */\nexport const formatPhone = phone => {\n  if (!phone) return '';\n\n  // 移除所有非數字字符\n  const cleaned = phone.replace(/\\D/g, '');\n\n  // 台灣手機號碼格式化 (09xx-xxx-xxx)\n  if (cleaned.length === 10 && cleaned.startsWith('09')) {\n    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\n  }\n\n  // 台灣市話格式化 (0x-xxxx-xxxx)\n  if (cleaned.length >= 8 && cleaned.startsWith('0')) {\n    if (cleaned.length === 8) {\n      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;\n    } else if (cleaned.length === 9) {\n      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;\n    } else if (cleaned.length === 10) {\n      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;\n    }\n  }\n\n  // 其他格式保持原樣\n  return phone;\n};\n\n/**\n * 格式化電子郵件（轉為小寫）\n * @param {string} email - 原始電子郵件\n * @returns {string} 格式化後的電子郵件\n */\nexport const formatEmail = email => {\n  if (!email) return '';\n  return email.toLowerCase().trim();\n};\n\n/**\n * 格式化姓名（移除多餘空格）\n * @param {string} name - 原始姓名\n * @returns {string} 格式化後的姓名\n */\nexport const formatName = name => {\n  if (!name) return '';\n  return name.trim().replace(/\\s+/g, ' ');\n};\n\n/**\n * 格式化公司名稱\n * @param {string} company - 原始公司名稱\n * @returns {string} 格式化後的公司名稱\n */\nexport const formatCompany = company => {\n  if (!company) return '';\n  return company.trim();\n};\n\n/**\n * 格式化地址\n * @param {string} address - 原始地址\n * @returns {string} 格式化後的地址\n */\nexport const formatAddress = address => {\n  if (!address) return '';\n  return address.trim().replace(/\\s+/g, ' ');\n};\n\n/**\n * 格式化Line ID\n * @param {string} lineId - 原始Line ID\n * @returns {string} 格式化後的Line ID\n */\nexport const formatLineId = lineId => {\n  if (!lineId) return '';\n  // 移除@符號（如果存在）\n  return lineId.replace(/^@/, '').trim();\n};\n\n/**\n * 格式化日期時間\n * @param {string|Date} date - 日期\n * @param {string} format - 格式類型 ('date', 'datetime', 'time')\n * @returns {string} 格式化後的日期時間\n */\nexport const formatDateTime = (date, format = 'datetime') => {\n  if (!date) return '';\n  const dateObj = new Date(date);\n  if (isNaN(dateObj.getTime())) return '';\n  const options = {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    timeZone: 'Asia/Taipei'\n  };\n  switch (format) {\n    case 'date':\n      return dateObj.toLocaleDateString('zh-TW', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit'\n      });\n    case 'time':\n      return dateObj.toLocaleTimeString('zh-TW', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    case 'datetime':\n    default:\n      return dateObj.toLocaleString('zh-TW', options);\n  }\n};\n\n/**\n * 格式化相對時間（多久之前）\n * @param {string|Date} date - 日期\n * @returns {string} 相對時間描述\n */\nexport const formatRelativeTime = date => {\n  if (!date) return '';\n  const dateObj = new Date(date);\n  if (isNaN(dateObj.getTime())) return '';\n  const now = new Date();\n  const diffMs = now.getTime() - dateObj.getTime();\n  const diffSeconds = Math.floor(diffMs / 1000);\n  const diffMinutes = Math.floor(diffSeconds / 60);\n  const diffHours = Math.floor(diffMinutes / 60);\n  const diffDays = Math.floor(diffHours / 24);\n  if (diffSeconds < 60) {\n    return '剛剛';\n  } else if (diffMinutes < 60) {\n    return `${diffMinutes}分鐘前`;\n  } else if (diffHours < 24) {\n    return `${diffHours}小時前`;\n  } else if (diffDays < 7) {\n    return `${diffDays}天前`;\n  } else {\n    return formatDateTime(date, 'date');\n  }\n};\n\n/**\n * 截斷文字並添加省略號\n * @param {string} text - 原始文字\n * @param {number} maxLength - 最大長度\n * @returns {string} 截斷後的文字\n */\nexport const truncateText = (text, maxLength = 50) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n};\n\n/**\n * 高亮搜索關鍵字\n * @param {string} text - 原始文字\n * @param {string} keyword - 搜索關鍵字\n * @returns {string} 包含高亮標記的HTML字符串\n */\nexport const highlightKeyword = (text, keyword) => {\n  if (!text || !keyword) return text;\n  const regex = new RegExp(`(${keyword})`, 'gi');\n  return text.replace(regex, '<mark>$1</mark>');\n};\n\n/**\n * 格式化文件大小\n * @param {number} bytes - 字節數\n * @returns {string} 格式化後的大小\n */\nexport const formatFileSize = bytes => {\n  if (bytes === 0) return '0 Bytes';\n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\n/**\n * 格式化百分比\n * @param {number} value - 數值\n * @param {number} total - 總數\n * @param {number} decimals - 小數位數\n * @returns {string} 百分比字符串\n */\nexport const formatPercentage = (value, total, decimals = 1) => {\n  if (!total || total === 0) return '0%';\n  const percentage = value / total * 100;\n  return `${percentage.toFixed(decimals)}%`;\n};\n\n/**\n * 清理和格式化表單數據\n * @param {Object} formData - 原始表單數據\n * @returns {Object} 格式化後的表單數據\n */\nexport const formatFormData = formData => {\n  const formatted = {};\n  Object.keys(formData).forEach(key => {\n    const value = formData[key];\n    if (value !== null && value !== undefined && value !== '') {\n      switch (key) {\n        case 'name':\n          formatted[key] = formatName(value);\n          break;\n        case 'company_name':\n          formatted[key] = formatCompany(value);\n          break;\n        case 'mobile_phone':\n        case 'office_phone':\n          formatted[key] = formatPhone(value);\n          break;\n        case 'email':\n          formatted[key] = formatEmail(value);\n          break;\n        case 'line_id':\n          formatted[key] = formatLineId(value);\n          break;\n        case 'company_address_1':\n        case 'company_address_2':\n          formatted[key] = formatAddress(value);\n          break;\n        default:\n          formatted[key] = typeof value === 'string' ? value.trim() : value;\n      }\n    }\n  });\n  return formatted;\n};\nexport default {\n  formatPhone,\n  formatEmail,\n  formatName,\n  formatCompany,\n  formatAddress,\n  formatLineId,\n  formatDateTime,\n  formatRelativeTime,\n  truncateText,\n  highlightKeyword,\n  formatFileSize,\n  formatPercentage,\n  formatFormData\n};", "map": {"version": 3, "names": ["formatPhone", "phone", "cleaned", "replace", "length", "startsWith", "slice", "formatEmail", "email", "toLowerCase", "trim", "formatName", "name", "formatCompany", "company", "formatAddress", "address", "formatLineId", "lineId", "formatDateTime", "date", "format", "date<PERSON><PERSON>j", "Date", "isNaN", "getTime", "options", "year", "month", "day", "hour", "minute", "second", "timeZone", "toLocaleDateString", "toLocaleTimeString", "toLocaleString", "formatRelativeTime", "now", "diffMs", "diffSeconds", "Math", "floor", "diffMinutes", "diffHours", "diffDays", "truncateText", "text", "max<PERSON><PERSON><PERSON>", "substring", "highlightKeyword", "keyword", "regex", "RegExp", "formatFileSize", "bytes", "k", "sizes", "i", "log", "parseFloat", "pow", "toFixed", "formatPercentage", "value", "total", "decimals", "percentage", "formatFormData", "formData", "formatted", "Object", "keys", "for<PERSON>ach", "key", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/utils/formatters.js"], "sourcesContent": ["/**\n * 數據格式化工具函數\n */\n\n/**\n * 格式化電話號碼\n * @param {string} phone - 原始電話號碼\n * @returns {string} 格式化後的電話號碼\n */\nexport const formatPhone = (phone) => {\n  if (!phone) return '';\n  \n  // 移除所有非數字字符\n  const cleaned = phone.replace(/\\D/g, '');\n  \n  // 台灣手機號碼格式化 (09xx-xxx-xxx)\n  if (cleaned.length === 10 && cleaned.startsWith('09')) {\n    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;\n  }\n  \n  // 台灣市話格式化 (0x-xxxx-xxxx)\n  if (cleaned.length >= 8 && cleaned.startsWith('0')) {\n    if (cleaned.length === 8) {\n      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;\n    } else if (cleaned.length === 9) {\n      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;\n    } else if (cleaned.length === 10) {\n      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;\n    }\n  }\n  \n  // 其他格式保持原樣\n  return phone;\n};\n\n/**\n * 格式化電子郵件（轉為小寫）\n * @param {string} email - 原始電子郵件\n * @returns {string} 格式化後的電子郵件\n */\nexport const formatEmail = (email) => {\n  if (!email) return '';\n  return email.toLowerCase().trim();\n};\n\n/**\n * 格式化姓名（移除多餘空格）\n * @param {string} name - 原始姓名\n * @returns {string} 格式化後的姓名\n */\nexport const formatName = (name) => {\n  if (!name) return '';\n  return name.trim().replace(/\\s+/g, ' ');\n};\n\n/**\n * 格式化公司名稱\n * @param {string} company - 原始公司名稱\n * @returns {string} 格式化後的公司名稱\n */\nexport const formatCompany = (company) => {\n  if (!company) return '';\n  return company.trim();\n};\n\n/**\n * 格式化地址\n * @param {string} address - 原始地址\n * @returns {string} 格式化後的地址\n */\nexport const formatAddress = (address) => {\n  if (!address) return '';\n  return address.trim().replace(/\\s+/g, ' ');\n};\n\n/**\n * 格式化Line ID\n * @param {string} lineId - 原始Line ID\n * @returns {string} 格式化後的Line ID\n */\nexport const formatLineId = (lineId) => {\n  if (!lineId) return '';\n  // 移除@符號（如果存在）\n  return lineId.replace(/^@/, '').trim();\n};\n\n/**\n * 格式化日期時間\n * @param {string|Date} date - 日期\n * @param {string} format - 格式類型 ('date', 'datetime', 'time')\n * @returns {string} 格式化後的日期時間\n */\nexport const formatDateTime = (date, format = 'datetime') => {\n  if (!date) return '';\n  \n  const dateObj = new Date(date);\n  if (isNaN(dateObj.getTime())) return '';\n  \n  const options = {\n    year: 'numeric',\n    month: '2-digit',\n    day: '2-digit',\n    hour: '2-digit',\n    minute: '2-digit',\n    second: '2-digit',\n    timeZone: 'Asia/Taipei'\n  };\n  \n  switch (format) {\n    case 'date':\n      return dateObj.toLocaleDateString('zh-TW', {\n        year: 'numeric',\n        month: '2-digit',\n        day: '2-digit'\n      });\n    case 'time':\n      return dateObj.toLocaleTimeString('zh-TW', {\n        hour: '2-digit',\n        minute: '2-digit'\n      });\n    case 'datetime':\n    default:\n      return dateObj.toLocaleString('zh-TW', options);\n  }\n};\n\n/**\n * 格式化相對時間（多久之前）\n * @param {string|Date} date - 日期\n * @returns {string} 相對時間描述\n */\nexport const formatRelativeTime = (date) => {\n  if (!date) return '';\n  \n  const dateObj = new Date(date);\n  if (isNaN(dateObj.getTime())) return '';\n  \n  const now = new Date();\n  const diffMs = now.getTime() - dateObj.getTime();\n  const diffSeconds = Math.floor(diffMs / 1000);\n  const diffMinutes = Math.floor(diffSeconds / 60);\n  const diffHours = Math.floor(diffMinutes / 60);\n  const diffDays = Math.floor(diffHours / 24);\n  \n  if (diffSeconds < 60) {\n    return '剛剛';\n  } else if (diffMinutes < 60) {\n    return `${diffMinutes}分鐘前`;\n  } else if (diffHours < 24) {\n    return `${diffHours}小時前`;\n  } else if (diffDays < 7) {\n    return `${diffDays}天前`;\n  } else {\n    return formatDateTime(date, 'date');\n  }\n};\n\n/**\n * 截斷文字並添加省略號\n * @param {string} text - 原始文字\n * @param {number} maxLength - 最大長度\n * @returns {string} 截斷後的文字\n */\nexport const truncateText = (text, maxLength = 50) => {\n  if (!text) return '';\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n};\n\n/**\n * 高亮搜索關鍵字\n * @param {string} text - 原始文字\n * @param {string} keyword - 搜索關鍵字\n * @returns {string} 包含高亮標記的HTML字符串\n */\nexport const highlightKeyword = (text, keyword) => {\n  if (!text || !keyword) return text;\n  \n  const regex = new RegExp(`(${keyword})`, 'gi');\n  return text.replace(regex, '<mark>$1</mark>');\n};\n\n/**\n * 格式化文件大小\n * @param {number} bytes - 字節數\n * @returns {string} 格式化後的大小\n */\nexport const formatFileSize = (bytes) => {\n  if (bytes === 0) return '0 Bytes';\n  \n  const k = 1024;\n  const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n  const i = Math.floor(Math.log(bytes) / Math.log(k));\n  \n  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n};\n\n/**\n * 格式化百分比\n * @param {number} value - 數值\n * @param {number} total - 總數\n * @param {number} decimals - 小數位數\n * @returns {string} 百分比字符串\n */\nexport const formatPercentage = (value, total, decimals = 1) => {\n  if (!total || total === 0) return '0%';\n  const percentage = (value / total) * 100;\n  return `${percentage.toFixed(decimals)}%`;\n};\n\n/**\n * 清理和格式化表單數據\n * @param {Object} formData - 原始表單數據\n * @returns {Object} 格式化後的表單數據\n */\nexport const formatFormData = (formData) => {\n  const formatted = {};\n  \n  Object.keys(formData).forEach(key => {\n    const value = formData[key];\n    if (value !== null && value !== undefined && value !== '') {\n      switch (key) {\n        case 'name':\n          formatted[key] = formatName(value);\n          break;\n        case 'company_name':\n          formatted[key] = formatCompany(value);\n          break;\n        case 'mobile_phone':\n        case 'office_phone':\n          formatted[key] = formatPhone(value);\n          break;\n        case 'email':\n          formatted[key] = formatEmail(value);\n          break;\n        case 'line_id':\n          formatted[key] = formatLineId(value);\n          break;\n        case 'company_address_1':\n        case 'company_address_2':\n          formatted[key] = formatAddress(value);\n          break;\n        default:\n          formatted[key] = typeof value === 'string' ? value.trim() : value;\n      }\n    }\n  });\n  \n  return formatted;\n};\n\nexport default {\n  formatPhone,\n  formatEmail,\n  formatName,\n  formatCompany,\n  formatAddress,\n  formatLineId,\n  formatDateTime,\n  formatRelativeTime,\n  truncateText,\n  highlightKeyword,\n  formatFileSize,\n  formatPercentage,\n  formatFormData\n};\n"], "mappings": "AAAA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMA,WAAW,GAAIC,KAAK,IAAK;EACpC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;;EAErB;EACA,MAAMC,OAAO,GAAGD,KAAK,CAACE,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC;;EAExC;EACA,IAAID,OAAO,CAACE,MAAM,KAAK,EAAE,IAAIF,OAAO,CAACG,UAAU,CAAC,IAAI,CAAC,EAAE;IACrD,OAAO,GAAGH,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;EAC5E;;EAEA;EACA,IAAIJ,OAAO,CAACE,MAAM,IAAI,CAAC,IAAIF,OAAO,CAACG,UAAU,CAAC,GAAG,CAAC,EAAE;IAClD,IAAIH,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;MACxB,OAAO,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;IAC5E,CAAC,MAAM,IAAIJ,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;MAC/B,OAAO,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;IAC5E,CAAC,MAAM,IAAIJ,OAAO,CAACE,MAAM,KAAK,EAAE,EAAE;MAChC,OAAO,GAAGF,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,IAAIJ,OAAO,CAACI,KAAK,CAAC,CAAC,CAAC,EAAE;IAC5E;EACF;;EAEA;EACA,OAAOL,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMM,WAAW,GAAIC,KAAK,IAAK;EACpC,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;EACrB,OAAOA,KAAK,CAACC,WAAW,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;AACnC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAIC,IAAI,IAAK;EAClC,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,OAAOA,IAAI,CAACF,IAAI,CAAC,CAAC,CAACP,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AACzC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMU,aAAa,GAAIC,OAAO,IAAK;EACxC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;EACvB,OAAOA,OAAO,CAACJ,IAAI,CAAC,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,aAAa,GAAIC,OAAO,IAAK;EACxC,IAAI,CAACA,OAAO,EAAE,OAAO,EAAE;EACvB,OAAOA,OAAO,CAACN,IAAI,CAAC,CAAC,CAACP,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;AAC5C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMc,YAAY,GAAIC,MAAM,IAAK;EACtC,IAAI,CAACA,MAAM,EAAE,OAAO,EAAE;EACtB;EACA,OAAOA,MAAM,CAACf,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAACO,IAAI,CAAC,CAAC;AACxC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,cAAc,GAAGA,CAACC,IAAI,EAAEC,MAAM,GAAG,UAAU,KAAK;EAC3D,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;EAEpB,MAAME,OAAO,GAAG,IAAIC,IAAI,CAACH,IAAI,CAAC;EAC9B,IAAII,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;EAEvC,MAAMC,OAAO,GAAG;IACdC,IAAI,EAAE,SAAS;IACfC,KAAK,EAAE,SAAS;IAChBC,GAAG,EAAE,SAAS;IACdC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,SAAS;IACjBC,MAAM,EAAE,SAAS;IACjBC,QAAQ,EAAE;EACZ,CAAC;EAED,QAAQZ,MAAM;IACZ,KAAK,MAAM;MACT,OAAOC,OAAO,CAACY,kBAAkB,CAAC,OAAO,EAAE;QACzCP,IAAI,EAAE,SAAS;QACfC,KAAK,EAAE,SAAS;QAChBC,GAAG,EAAE;MACP,CAAC,CAAC;IACJ,KAAK,MAAM;MACT,OAAOP,OAAO,CAACa,kBAAkB,CAAC,OAAO,EAAE;QACzCL,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;MACV,CAAC,CAAC;IACJ,KAAK,UAAU;IACf;MACE,OAAOT,OAAO,CAACc,cAAc,CAAC,OAAO,EAAEV,OAAO,CAAC;EACnD;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,kBAAkB,GAAIjB,IAAI,IAAK;EAC1C,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EAEpB,MAAME,OAAO,GAAG,IAAIC,IAAI,CAACH,IAAI,CAAC;EAC9B,IAAII,KAAK,CAACF,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;EAEvC,MAAMa,GAAG,GAAG,IAAIf,IAAI,CAAC,CAAC;EACtB,MAAMgB,MAAM,GAAGD,GAAG,CAACb,OAAO,CAAC,CAAC,GAAGH,OAAO,CAACG,OAAO,CAAC,CAAC;EAChD,MAAMe,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACH,MAAM,GAAG,IAAI,CAAC;EAC7C,MAAMI,WAAW,GAAGF,IAAI,CAACC,KAAK,CAACF,WAAW,GAAG,EAAE,CAAC;EAChD,MAAMI,SAAS,GAAGH,IAAI,CAACC,KAAK,CAACC,WAAW,GAAG,EAAE,CAAC;EAC9C,MAAME,QAAQ,GAAGJ,IAAI,CAACC,KAAK,CAACE,SAAS,GAAG,EAAE,CAAC;EAE3C,IAAIJ,WAAW,GAAG,EAAE,EAAE;IACpB,OAAO,IAAI;EACb,CAAC,MAAM,IAAIG,WAAW,GAAG,EAAE,EAAE;IAC3B,OAAO,GAAGA,WAAW,KAAK;EAC5B,CAAC,MAAM,IAAIC,SAAS,GAAG,EAAE,EAAE;IACzB,OAAO,GAAGA,SAAS,KAAK;EAC1B,CAAC,MAAM,IAAIC,QAAQ,GAAG,CAAC,EAAE;IACvB,OAAO,GAAGA,QAAQ,IAAI;EACxB,CAAC,MAAM;IACL,OAAO1B,cAAc,CAACC,IAAI,EAAE,MAAM,CAAC;EACrC;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAM0B,YAAY,GAAGA,CAACC,IAAI,EAAEC,SAAS,GAAG,EAAE,KAAK;EACpD,IAAI,CAACD,IAAI,EAAE,OAAO,EAAE;EACpB,IAAIA,IAAI,CAAC3C,MAAM,IAAI4C,SAAS,EAAE,OAAOD,IAAI;EACzC,OAAOA,IAAI,CAACE,SAAS,CAAC,CAAC,EAAED,SAAS,CAAC,GAAG,KAAK;AAC7C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,gBAAgB,GAAGA,CAACH,IAAI,EAAEI,OAAO,KAAK;EACjD,IAAI,CAACJ,IAAI,IAAI,CAACI,OAAO,EAAE,OAAOJ,IAAI;EAElC,MAAMK,KAAK,GAAG,IAAIC,MAAM,CAAC,IAAIF,OAAO,GAAG,EAAE,IAAI,CAAC;EAC9C,OAAOJ,IAAI,CAAC5C,OAAO,CAACiD,KAAK,EAAE,iBAAiB,CAAC;AAC/C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAIC,KAAK,IAAK;EACvC,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,SAAS;EAEjC,MAAMC,CAAC,GAAG,IAAI;EACd,MAAMC,KAAK,GAAG,CAAC,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;EACzC,MAAMC,CAAC,GAAGjB,IAAI,CAACC,KAAK,CAACD,IAAI,CAACkB,GAAG,CAACJ,KAAK,CAAC,GAAGd,IAAI,CAACkB,GAAG,CAACH,CAAC,CAAC,CAAC;EAEnD,OAAOI,UAAU,CAAC,CAACL,KAAK,GAAGd,IAAI,CAACoB,GAAG,CAACL,CAAC,EAAEE,CAAC,CAAC,EAAEI,OAAO,CAAC,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGL,KAAK,CAACC,CAAC,CAAC;AACzE,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,gBAAgB,GAAGA,CAACC,KAAK,EAAEC,KAAK,EAAEC,QAAQ,GAAG,CAAC,KAAK;EAC9D,IAAI,CAACD,KAAK,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;EACtC,MAAME,UAAU,GAAIH,KAAK,GAAGC,KAAK,GAAI,GAAG;EACxC,OAAO,GAAGE,UAAU,CAACL,OAAO,CAACI,QAAQ,CAAC,GAAG;AAC3C,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,cAAc,GAAIC,QAAQ,IAAK;EAC1C,MAAMC,SAAS,GAAG,CAAC,CAAC;EAEpBC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,OAAO,CAACC,GAAG,IAAI;IACnC,MAAMV,KAAK,GAAGK,QAAQ,CAACK,GAAG,CAAC;IAC3B,IAAIV,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKW,SAAS,IAAIX,KAAK,KAAK,EAAE,EAAE;MACzD,QAAQU,GAAG;QACT,KAAK,MAAM;UACTJ,SAAS,CAACI,GAAG,CAAC,GAAG/D,UAAU,CAACqD,KAAK,CAAC;UAClC;QACF,KAAK,cAAc;UACjBM,SAAS,CAACI,GAAG,CAAC,GAAG7D,aAAa,CAACmD,KAAK,CAAC;UACrC;QACF,KAAK,cAAc;QACnB,KAAK,cAAc;UACjBM,SAAS,CAACI,GAAG,CAAC,GAAG1E,WAAW,CAACgE,KAAK,CAAC;UACnC;QACF,KAAK,OAAO;UACVM,SAAS,CAACI,GAAG,CAAC,GAAGnE,WAAW,CAACyD,KAAK,CAAC;UACnC;QACF,KAAK,SAAS;UACZM,SAAS,CAACI,GAAG,CAAC,GAAGzD,YAAY,CAAC+C,KAAK,CAAC;UACpC;QACF,KAAK,mBAAmB;QACxB,KAAK,mBAAmB;UACtBM,SAAS,CAACI,GAAG,CAAC,GAAG3D,aAAa,CAACiD,KAAK,CAAC;UACrC;QACF;UACEM,SAAS,CAACI,GAAG,CAAC,GAAG,OAAOV,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACtD,IAAI,CAAC,CAAC,GAAGsD,KAAK;MACrE;IACF;EACF,CAAC,CAAC;EAEF,OAAOM,SAAS;AAClB,CAAC;AAED,eAAe;EACbtE,WAAW;EACXO,WAAW;EACXI,UAAU;EACVE,aAAa;EACbE,aAAa;EACbE,YAAY;EACZE,cAAc;EACdkB,kBAAkB;EAClBS,YAAY;EACZI,gBAAgB;EAChBI,cAAc;EACdS,gBAAgB;EAChBK;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}