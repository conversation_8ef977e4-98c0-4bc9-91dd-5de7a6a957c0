/**
 * OCR處理邏輯組件
 */

import React, { useEffect } from 'react';
import { useOCRState } from '../../hooks';
import OCRStatus from './OCRStatus';
import ParsedFieldsDisplay from './ParsedFieldsDisplay';

const OCRProcessor = ({
  frontImage,
  backImage,
  onFieldsParsed,
  onOCRCompleted,
  autoProcess = true,
  showStatus = true,
  showResults = true,
  style = {},
  className = ''
}) => {
  const {
    frontOCR,
    backOCR,
    processImage,
    parseFields,
    getOCRStatus,
    getMergedFields,
    isProcessing,
    hasError,
    hasResults
  } = useOCRState();

  // 自動處理圖片OCR
  useEffect(() => {
    if (autoProcess && frontImage && frontImage.file && !frontOCR.text) {
      processImage(frontImage.file, 'front').catch(console.error);
    }
  }, [autoProcess, frontImage, frontOCR.text, processImage]);

  useEffect(() => {
    if (autoProcess && backImage && backImage.file && !backOCR.text) {
      processImage(backImage.file, 'back').catch(console.error);
    }
  }, [autoProcess, backImage, backOCR.text, processImage]);

  // 當解析完成時通知父組件
  useEffect(() => {
    if (onFieldsParsed) {
      const mergedFields = getMergedFields();
      if (Object.keys(mergedFields).length > 0) {
        onFieldsParsed(mergedFields, frontOCR.parsedFields, backOCR.parsedFields);
      }
    }
  }, [frontOCR.parsedFields, backOCR.parsedFields, onFieldsParsed, getMergedFields]);

  // 當OCR完成時通知父組件
  useEffect(() => {
    if (onOCRCompleted && hasResults && !isProcessing) {
      onOCRCompleted({
        frontText: frontOCR.text,
        backText: backOCR.text,
        frontFields: frontOCR.parsedFields,
        backFields: backOCR.parsedFields,
        mergedFields: getMergedFields()
      });
    }
  }, [hasResults, isProcessing, onOCRCompleted, frontOCR, backOCR, getMergedFields]);

  // 手動處理OCR
  const handleProcessImage = async (imageFile, side) => {
    try {
      await processImage(imageFile, side);
    } catch (error) {
      console.error('OCR處理失敗:', error);
    }
  };

  // 重新處理OCR
  const handleRetryOCR = async (side) => {
    const image = side === 'front' ? frontImage : backImage;
    if (image && image.file) {
      await handleProcessImage(image.file, side);
    }
  };

  // 應用解析結果到表單
  const handleApplyToForm = (fields) => {
    if (onFieldsParsed) {
      onFieldsParsed(fields, frontOCR.parsedFields, backOCR.parsedFields);
    }
  };

  const overallStatus = getOCRStatus();
  const mergedFields = getMergedFields();

  return (
    <div className={`ocr-processor ${className}`} style={style}>
      {/* OCR狀態顯示 */}
      {showStatus && (
        <OCRStatus
          status={overallStatus}
          error={frontOCR.error || backOCR.error}
          frontStatus={frontOCR.status}
          backStatus={backOCR.status}
          showDetails={true}
          style={{ marginBottom: '16px' }}
        />
      )}

      {/* 解析結果顯示 */}
      {showResults && hasResults && (
        <ParsedFieldsDisplay
          frontFields={frontOCR.parsedFields}
          backFields={backOCR.parsedFields}
          mergedFields={mergedFields}
          onApplyToForm={handleApplyToForm}
          showMerged={true}
        />
      )}

      {/* 調試信息（開發環境） */}
      {process.env.NODE_ENV === 'development' && (
        <div style={{ marginTop: '16px', fontSize: '12px', color: '#8c8c8c' }}>
          <details>
            <summary>OCR調試信息</summary>
            <pre style={{ fontSize: '10px', overflow: 'auto' }}>
              {JSON.stringify({
                frontOCR: {
                  status: frontOCR.status,
                  hasText: !!frontOCR.text,
                  fieldsCount: Object.keys(frontOCR.parsedFields).length,
                  error: frontOCR.error
                },
                backOCR: {
                  status: backOCR.status,
                  hasText: !!backOCR.text,
                  fieldsCount: Object.keys(backOCR.parsedFields).length,
                  error: backOCR.error
                },
                merged: {
                  fieldsCount: Object.keys(mergedFields).length
                }
              }, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
};

export default OCRProcessor;
