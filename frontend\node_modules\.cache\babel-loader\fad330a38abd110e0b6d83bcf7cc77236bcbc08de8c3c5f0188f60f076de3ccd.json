{"ast": null, "code": "import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useSessionStorageState = createUseStorageState(function () {\n  return isBrowser ? sessionStorage : undefined;\n});\nexport default useSessionStorageState;", "map": {"version": 3, "names": ["createUseStorageState", "<PERSON><PERSON><PERSON><PERSON>", "useSessionStorageState", "sessionStorage", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useSessionStorageState/index.js"], "sourcesContent": ["import { createUseStorageState } from '../createUseStorageState';\nimport isBrowser from '../utils/isBrowser';\nvar useSessionStorageState = createUseStorageState(function () {\n  return isBrowser ? sessionStorage : undefined;\n});\nexport default useSessionStorageState;"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,0BAA0B;AAChE,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,sBAAsB,GAAGF,qBAAqB,CAAC,YAAY;EAC7D,OAAOC,SAAS,GAAGE,cAAc,GAAGC,SAAS;AAC/C,CAAC,CAAC;AACF,eAAeF,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}