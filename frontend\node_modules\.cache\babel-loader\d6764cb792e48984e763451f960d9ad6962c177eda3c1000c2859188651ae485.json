{"ast": null, "code": "import { __awaiter, __generator } from \"tslib\";\nimport { useEffect } from 'react';\nimport { isFunction } from '../utils';\nfunction isAsyncGenerator(val) {\n  return isFunction(val[Symbol.asyncIterator]);\n}\nfunction useAsyncEffect(effect, deps) {\n  useEffect(function () {\n    var e = effect();\n    var cancelled = false;\n    function execute() {\n      return __awaiter(this, void 0, void 0, function () {\n        var result;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              if (!isAsyncGenerator(e)) return [3 /*break*/, 4];\n              _a.label = 1;\n            case 1:\n              if (!true) return [3 /*break*/, 3];\n              return [4 /*yield*/, e.next()];\n            case 2:\n              result = _a.sent();\n              if (result.done || cancelled) {\n                return [3 /*break*/, 3];\n              }\n              return [3 /*break*/, 1];\n            case 3:\n              return [3 /*break*/, 6];\n            case 4:\n              return [4 /*yield*/, e];\n            case 5:\n              _a.sent();\n              _a.label = 6;\n            case 6:\n              return [2 /*return*/];\n          }\n        });\n      });\n    }\n    execute();\n    return function () {\n      cancelled = true;\n    };\n  }, deps);\n}\nexport default useAsyncEffect;", "map": {"version": 3, "names": ["__awaiter", "__generator", "useEffect", "isFunction", "isAsyncGenerator", "val", "Symbol", "asyncIterator", "useAsyncEffect", "effect", "deps", "e", "cancelled", "execute", "result", "_a", "label", "next", "sent", "done"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useAsyncEffect/index.js"], "sourcesContent": ["import { __awaiter, __generator } from \"tslib\";\nimport { useEffect } from 'react';\nimport { isFunction } from '../utils';\nfunction isAsyncGenerator(val) {\n  return isFunction(val[Symbol.asyncIterator]);\n}\nfunction useAsyncEffect(effect, deps) {\n  useEffect(function () {\n    var e = effect();\n    var cancelled = false;\n    function execute() {\n      return __awaiter(this, void 0, void 0, function () {\n        var result;\n        return __generator(this, function (_a) {\n          switch (_a.label) {\n            case 0:\n              if (!isAsyncGenerator(e)) return [3 /*break*/, 4];\n              _a.label = 1;\n            case 1:\n              if (!true) return [3 /*break*/, 3];\n              return [4 /*yield*/, e.next()];\n            case 2:\n              result = _a.sent();\n              if (result.done || cancelled) {\n                return [3 /*break*/, 3];\n              }\n              return [3 /*break*/, 1];\n            case 3:\n              return [3 /*break*/, 6];\n            case 4:\n              return [4 /*yield*/, e];\n            case 5:\n              _a.sent();\n              _a.label = 6;\n            case 6:\n              return [2 /*return*/];\n          }\n        });\n      });\n    }\n    execute();\n    return function () {\n      cancelled = true;\n    };\n  }, deps);\n}\nexport default useAsyncEffect;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,QAAQ,OAAO;AAC9C,SAASC,SAAS,QAAQ,OAAO;AACjC,SAASC,UAAU,QAAQ,UAAU;AACrC,SAASC,gBAAgBA,CAACC,GAAG,EAAE;EAC7B,OAAOF,UAAU,CAACE,GAAG,CAACC,MAAM,CAACC,aAAa,CAAC,CAAC;AAC9C;AACA,SAASC,cAAcA,CAACC,MAAM,EAAEC,IAAI,EAAE;EACpCR,SAAS,CAAC,YAAY;IACpB,IAAIS,CAAC,GAAGF,MAAM,CAAC,CAAC;IAChB,IAAIG,SAAS,GAAG,KAAK;IACrB,SAASC,OAAOA,CAAA,EAAG;MACjB,OAAOb,SAAS,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;QACjD,IAAIc,MAAM;QACV,OAAOb,WAAW,CAAC,IAAI,EAAE,UAAUc,EAAE,EAAE;UACrC,QAAQA,EAAE,CAACC,KAAK;YACd,KAAK,CAAC;cACJ,IAAI,CAACZ,gBAAgB,CAACO,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;cACjDI,EAAE,CAACC,KAAK,GAAG,CAAC;YACd,KAAK,CAAC;cACJ,IAAI,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;cAClC,OAAO,CAAC,CAAC,CAAC,WAAWL,CAAC,CAACM,IAAI,CAAC,CAAC,CAAC;YAChC,KAAK,CAAC;cACJH,MAAM,GAAGC,EAAE,CAACG,IAAI,CAAC,CAAC;cAClB,IAAIJ,MAAM,CAACK,IAAI,IAAIP,SAAS,EAAE;gBAC5B,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;cACzB;cACA,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;YACzB,KAAK,CAAC;cACJ,OAAO,CAAC,CAAC,CAAC,WAAW,CAAC,CAAC;YACzB,KAAK,CAAC;cACJ,OAAO,CAAC,CAAC,CAAC,WAAWD,CAAC,CAAC;YACzB,KAAK,CAAC;cACJI,EAAE,CAACG,IAAI,CAAC,CAAC;cACTH,EAAE,CAACC,KAAK,GAAG,CAAC;YACd,KAAK,CAAC;cACJ,OAAO,CAAC,CAAC,CAAC,WAAW;UACzB;QACF,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACAH,OAAO,CAAC,CAAC;IACT,OAAO,YAAY;MACjBD,SAAS,GAAG,IAAI;IAClB,CAAC;EACH,CAAC,EAAEF,IAAI,CAAC;AACV;AACA,eAAeF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}