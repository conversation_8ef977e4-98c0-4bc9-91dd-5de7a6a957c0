/**
 * 名片詳情查看頁面
 */

import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Card, Button, Space, Divider } from 'antd-mobile';
import { 
  EditSOutline, 
  DeleteOutline,
  PhoneFill,
  MailOutline,
  EnvironmentOutline,
  UserContactOutline
} from 'antd-mobile-icons';
import { PageContainer } from '../components/Layout';
import { LoadingSpinner, ErrorMessage } from '../components/UI';
import { useCardData } from '../hooks';
import { formatDateTime, getFieldDisplayName } from '../utils/formatters';

const CardViewPage = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  const { getCard, deleteCard, loading } = useCardData();
  
  const [cardData, setCardData] = useState(null);
  const [loadError, setLoadError] = useState(null);
  const [initialLoading, setInitialLoading] = useState(true);

  // 載入名片數據
  useEffect(() => {
    loadCardData();
  }, [id]);

  const loadCardData = async () => {
    try {
      setInitialLoading(true);
      setLoadError(null);
      const data = await getCard(id);
      setCardData(data);
    } catch (error) {
      console.error('載入名片失敗:', error);
      setLoadError(error.message || '載入名片失敗');
    } finally {
      setInitialLoading(false);
    }
  };

  // 處理刪除
  const handleDelete = async () => {
    try {
      await deleteCard(id);
      navigate('/cards');
    } catch (error) {
      console.error('刪除失敗:', error);
    }
  };

  // 渲染欄位
  const renderField = (key, value, icon = null) => {
    if (!value || value.trim() === '') return null;

    return (
      <div key={key} style={{ marginBottom: '12px' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
          {icon}
          <div style={{ flex: 1 }}>
            <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '2px' }}>
              {getFieldDisplayName(key)}
            </div>
            <div style={{ fontSize: '14px', color: '#262626' }}>
              {value}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (initialLoading) {
    return (
      <PageContainer title="名片詳情">
        <LoadingSpinner text="載入中..." />
      </PageContainer>
    );
  }

  if (loadError) {
    return (
      <PageContainer title="名片詳情">
        <ErrorMessage 
          error={loadError}
          onRetry={loadCardData}
        />
      </PageContainer>
    );
  }

  if (!cardData) {
    return (
      <PageContainer title="名片詳情">
        <ErrorMessage error="名片不存在" />
      </PageContainer>
    );
  }

  return (
    <PageContainer
      title="名片詳情"
      onBack={() => navigate('/cards')}
      headerRight={
        <Space>
          <Button 
            size="mini" 
            color="primary"
            onClick={() => navigate(`/cards/${id}/edit`)}
          >
            <EditSOutline /> 編輯
          </Button>
        </Space>
      }
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 基本資訊 */}
        <Card 
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
              <UserContactOutline />
              基本資訊
            </div>
          }
        >
          {renderField('name', cardData.name)}
          {renderField('company_name', cardData.company_name)}
          {renderField('position', cardData.position)}
        </Card>

        {/* 聯絡方式 */}
        <Card title="聯絡方式">
          {renderField('mobile_phone', cardData.mobile_phone, 
            <PhoneFill style={{ fontSize: '16px', color: '#52c41a' }} />
          )}
          {renderField('office_phone', cardData.office_phone,
            <PhoneFill style={{ fontSize: '16px', color: '#1677ff' }} />
          )}
          {renderField('email', cardData.email,
            <MailOutline style={{ fontSize: '16px', color: '#1677ff' }} />
          )}
          {renderField('line_id', cardData.line_id)}
        </Card>

        {/* 地址資訊 */}
        {(cardData.company_address_1 || cardData.company_address_2) && (
          <Card title="地址資訊">
            {renderField('company_address_1', cardData.company_address_1,
              <EnvironmentOutline style={{ fontSize: '16px', color: '#fa8c16' }} />
            )}
            {renderField('company_address_2', cardData.company_address_2,
              <EnvironmentOutline style={{ fontSize: '16px', color: '#fa8c16' }} />
            )}
          </Card>
        )}

        {/* 備註 */}
        {cardData.notes && (
          <Card title="備註">
            <div style={{ 
              fontSize: '14px', 
              color: '#262626',
              lineHeight: '1.5',
              whiteSpace: 'pre-wrap'
            }}>
              {cardData.notes}
            </div>
          </Card>
        )}

        {/* 系統資訊 */}
        <Card title="系統資訊">
          <div style={{ fontSize: '12px', color: '#8c8c8c' }}>
            <div style={{ marginBottom: '4px' }}>
              創建時間：{formatDateTime(cardData.created_at)}
            </div>
            {cardData.updated_at && cardData.updated_at !== cardData.created_at && (
              <div>
                更新時間：{formatDateTime(cardData.updated_at)}
              </div>
            )}
          </div>
        </Card>

        {/* 操作按鈕 */}
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            color="primary"
            size="large"
            block
            onClick={() => navigate(`/cards/${id}/edit`)}
          >
            <EditSOutline /> 編輯名片
          </Button>
          
          <Button
            color="danger"
            size="large"
            block
            fill="outline"
            onClick={handleDelete}
            loading={loading}
          >
            <DeleteOutline /> 刪除名片
          </Button>
        </Space>
      </Space>
    </PageContainer>
  );
};

export default CardViewPage;
