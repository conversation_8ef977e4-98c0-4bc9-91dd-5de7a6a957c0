/**
 * 名片數據管理Hook
 * 提供名片的CRUD操作和狀態管理
 */

import { useState, useCallback, useEffect } from 'react';
import { Toast } from 'antd-mobile';
import { cardService } from '../services/api/cardService';
import { ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';

export const useCardData = () => {
  const [cards, setCards] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [filteredCards, setFilteredCards] = useState([]);

  /**
   * 載入名片列表
   */
  const loadCards = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await cardService.getCards();
      setCards(data || []);
      console.log('✅ 名片列表載入成功:', data?.length || 0, '張名片');
    } catch (err) {
      console.error('❌ 載入名片列表失敗:', err);
      setError(err.message || ERROR_MESSAGES.LOAD_FAILED);
      Toast.show({
        content: err.message || ERROR_MESSAGES.LOAD_FAILED,
        position: 'center',
      });
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 獲取單張名片
   */
  const getCard = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    
    try {
      const data = await cardService.getCard(id);
      console.log('✅ 名片詳情載入成功:', data);
      return data;
    } catch (err) {
      console.error('❌ 載入名片詳情失敗:', err);
      setError(err.message || ERROR_MESSAGES.LOAD_FAILED);
      Toast.show({
        content: err.message || ERROR_MESSAGES.LOAD_FAILED,
        position: 'center',
      });
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 創建名片
   */
  const createCard = useCallback(async (cardData, images = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const newCard = await cardService.createCard(cardData, images);
      setCards(prevCards => [newCard, ...prevCards]);
      
      Toast.show({
        content: SUCCESS_MESSAGES.CARD_SAVED,
        position: 'center',
      });
      
      console.log('✅ 名片創建成功:', newCard);
      return newCard;
    } catch (err) {
      console.error('❌ 創建名片失敗:', err);
      setError(err.message || ERROR_MESSAGES.SAVE_FAILED);
      Toast.show({
        content: err.message || ERROR_MESSAGES.SAVE_FAILED,
        position: 'center',
      });
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 更新名片
   */
  const updateCard = useCallback(async (id, cardData, images = {}) => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedCard = await cardService.updateCard(id, cardData, images);
      setCards(prevCards => 
        prevCards.map(card => 
          card.id === id ? updatedCard : card
        )
      );
      
      Toast.show({
        content: SUCCESS_MESSAGES.CARD_UPDATED,
        position: 'center',
      });
      
      console.log('✅ 名片更新成功:', updatedCard);
      return updatedCard;
    } catch (err) {
      console.error('❌ 更新名片失敗:', err);
      setError(err.message || ERROR_MESSAGES.SAVE_FAILED);
      Toast.show({
        content: err.message || ERROR_MESSAGES.SAVE_FAILED,
        position: 'center',
      });
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 刪除名片
   */
  const deleteCard = useCallback(async (id) => {
    setLoading(true);
    setError(null);
    
    try {
      await cardService.deleteCard(id);
      setCards(prevCards => prevCards.filter(card => card.id !== id));
      
      Toast.show({
        content: SUCCESS_MESSAGES.CARD_DELETED,
        position: 'center',
      });
      
      console.log('✅ 名片刪除成功:', id);
    } catch (err) {
      console.error('❌ 刪除名片失敗:', err);
      setError(err.message || ERROR_MESSAGES.DELETE_FAILED);
      Toast.show({
        content: err.message || ERROR_MESSAGES.DELETE_FAILED,
        position: 'center',
      });
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 導出名片
   */
  const exportCards = useCallback(async (format = 'csv') => {
    setLoading(true);
    setError(null);
    
    try {
      const blob = await cardService.exportCards(format);
      
      // 創建下載鏈接
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `cards_export_${new Date().toISOString().split('T')[0]}.${format}`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      Toast.show({
        content: SUCCESS_MESSAGES.EXPORT_SUCCESS,
        position: 'center',
      });
      
      console.log('✅ 名片導出成功');
    } catch (err) {
      console.error('❌ 導出名片失敗:', err);
      setError(err.message || '導出失敗');
      Toast.show({
        content: err.message || '導出失敗',
        position: 'center',
      });
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * 搜索名片
   */
  const searchCards = useCallback((keyword) => {
    setSearchText(keyword);
    
    if (!keyword.trim()) {
      setFilteredCards(cards);
      return;
    }
    
    const filtered = cards.filter(card => {
      const searchFields = [
        card.name,
        card.company_name,
        card.position,
        card.mobile_phone,
        card.office_phone,
        card.email,
        card.line_id,
        card.notes,
        card.company_address_1,
        card.company_address_2
      ];
      
      return searchFields.some(field => 
        field && field.toLowerCase().includes(keyword.toLowerCase())
      );
    });
    
    setFilteredCards(filtered);
  }, [cards]);

  /**
   * 清空搜索
   */
  const clearSearch = useCallback(() => {
    setSearchText('');
    setFilteredCards(cards);
  }, [cards]);

  // 當cards變化時，更新filteredCards
  useEffect(() => {
    if (searchText.trim()) {
      searchCards(searchText);
    } else {
      setFilteredCards(cards);
    }
  }, [cards, searchText, searchCards]);

  // 初始化時載入名片列表
  useEffect(() => {
    loadCards();
  }, [loadCards]);

  return {
    // 數據狀態
    cards,
    filteredCards,
    loading,
    error,
    searchText,
    
    // 操作方法
    loadCards,
    getCard,
    createCard,
    updateCard,
    deleteCard,
    exportCards,
    searchCards,
    clearSearch,
    
    // 統計信息
    totalCards: cards.length,
    filteredCount: filteredCards.length
  };
};

export default useCardData;
