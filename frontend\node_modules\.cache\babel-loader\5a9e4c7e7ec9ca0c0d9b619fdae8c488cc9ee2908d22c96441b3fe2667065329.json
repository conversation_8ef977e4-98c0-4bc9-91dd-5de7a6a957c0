{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\UI\\\\ErrorMessage.js\";\n/**\n * 錯誤消息組件\n */\n\nimport React from 'react';\nimport { Card, Button } from 'antd-mobile';\nimport { ExclamationCircleOutline, ReloadOutline } from 'antd-mobile-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ErrorMessage = ({\n  error,\n  onRetry,\n  title = '發生錯誤',\n  showRetry = true,\n  style = {},\n  className = ''\n}) => {\n  const errorMessage = typeof error === 'string' ? error : (error === null || error === void 0 ? void 0 : error.message) || '未知錯誤';\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: `error-message ${className}`,\n    style: {\n      margin: '16px',\n      textAlign: 'center',\n      ...style\n    },\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        padding: '20px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(ExclamationCircleOutline, {\n        style: {\n          fontSize: '48px',\n          color: '#ff4d4f',\n          marginBottom: '16px'\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '16px',\n          fontWeight: 'bold',\n          color: '#262626',\n          marginBottom: '8px'\n        },\n        children: title\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          fontSize: '14px',\n          color: '#8c8c8c',\n          marginBottom: showRetry ? '20px' : '0',\n          lineHeight: '1.5'\n        },\n        children: errorMessage\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 9\n      }, this), showRetry && onRetry && /*#__PURE__*/_jsxDEV(Button, {\n        color: \"primary\",\n        fill: \"outline\",\n        onClick: onRetry,\n        style: {\n          minWidth: '100px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ReloadOutline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 13\n        }, this), \" \\u91CD\\u8A66\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_c = ErrorMessage;\nexport default ErrorMessage;\nvar _c;\n$RefreshReg$(_c, \"ErrorMessage\");", "map": {"version": 3, "names": ["React", "Card", "<PERSON><PERSON>", "ExclamationCircleOutline", "ReloadOutline", "jsxDEV", "_jsxDEV", "ErrorMessage", "error", "onRetry", "title", "showRetry", "style", "className", "errorMessage", "message", "margin", "textAlign", "children", "padding", "fontSize", "color", "marginBottom", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fontWeight", "lineHeight", "fill", "onClick", "min<PERSON><PERSON><PERSON>", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/UI/ErrorMessage.js"], "sourcesContent": ["/**\n * 錯誤消息組件\n */\n\nimport React from 'react';\nimport { Card, Button } from 'antd-mobile';\nimport { ExclamationCircleOutline, ReloadOutline } from 'antd-mobile-icons';\n\nconst ErrorMessage = ({ \n  error, \n  onRetry, \n  title = '發生錯誤',\n  showRetry = true,\n  style = {},\n  className = '' \n}) => {\n  const errorMessage = typeof error === 'string' ? error : error?.message || '未知錯誤';\n\n  return (\n    <Card \n      className={`error-message ${className}`}\n      style={{\n        margin: '16px',\n        textAlign: 'center',\n        ...style\n      }}\n    >\n      <div style={{ padding: '20px' }}>\n        <ExclamationCircleOutline \n          style={{ \n            fontSize: '48px', \n            color: '#ff4d4f',\n            marginBottom: '16px'\n          }} \n        />\n        \n        <div \n          style={{\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: '#262626',\n            marginBottom: '8px'\n          }}\n        >\n          {title}\n        </div>\n        \n        <div \n          style={{\n            fontSize: '14px',\n            color: '#8c8c8c',\n            marginBottom: showRetry ? '20px' : '0',\n            lineHeight: '1.5'\n          }}\n        >\n          {errorMessage}\n        </div>\n        \n        {showRetry && onRetry && (\n          <Button \n            color=\"primary\" \n            fill=\"outline\"\n            onClick={onRetry}\n            style={{ minWidth: '100px' }}\n          >\n            <ReloadOutline /> 重試\n          </Button>\n        )}\n      </div>\n    </Card>\n  );\n};\n\nexport default ErrorMessage;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,MAAM,QAAQ,aAAa;AAC1C,SAASC,wBAAwB,EAAEC,aAAa,QAAQ,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5E,MAAMC,YAAY,GAAGA,CAAC;EACpBC,KAAK;EACLC,OAAO;EACPC,KAAK,GAAG,MAAM;EACdC,SAAS,GAAG,IAAI;EAChBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ,MAAMC,YAAY,GAAG,OAAON,KAAK,KAAK,QAAQ,GAAGA,KAAK,GAAG,CAAAA,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEO,OAAO,KAAI,MAAM;EAEjF,oBACET,OAAA,CAACL,IAAI;IACHY,SAAS,EAAE,iBAAiBA,SAAS,EAAG;IACxCD,KAAK,EAAE;MACLI,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,QAAQ;MACnB,GAAGL;IACL,CAAE;IAAAM,QAAA,eAEFZ,OAAA;MAAKM,KAAK,EAAE;QAAEO,OAAO,EAAE;MAAO,CAAE;MAAAD,QAAA,gBAC9BZ,OAAA,CAACH,wBAAwB;QACvBS,KAAK,EAAE;UACLQ,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,SAAS;UAChBC,YAAY,EAAE;QAChB;MAAE;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEFpB,OAAA;QACEM,KAAK,EAAE;UACLQ,QAAQ,EAAE,MAAM;UAChBO,UAAU,EAAE,MAAM;UAClBN,KAAK,EAAE,SAAS;UAChBC,YAAY,EAAE;QAChB,CAAE;QAAAJ,QAAA,EAEDR;MAAK;QAAAa,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAENpB,OAAA;QACEM,KAAK,EAAE;UACLQ,QAAQ,EAAE,MAAM;UAChBC,KAAK,EAAE,SAAS;UAChBC,YAAY,EAAEX,SAAS,GAAG,MAAM,GAAG,GAAG;UACtCiB,UAAU,EAAE;QACd,CAAE;QAAAV,QAAA,EAEDJ;MAAY;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACV,CAAC,EAELf,SAAS,IAAIF,OAAO,iBACnBH,OAAA,CAACJ,MAAM;QACLmB,KAAK,EAAC,SAAS;QACfQ,IAAI,EAAC,SAAS;QACdC,OAAO,EAAErB,OAAQ;QACjBG,KAAK,EAAE;UAAEmB,QAAQ,EAAE;QAAQ,CAAE;QAAAb,QAAA,gBAE7BZ,OAAA,CAACF,aAAa;UAAAmB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,iBACnB;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACM,EAAA,GA/DIzB,YAAY;AAiElB,eAAeA,YAAY;AAAC,IAAAyB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}