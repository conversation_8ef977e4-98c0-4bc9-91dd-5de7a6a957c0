{"ast": null, "code": "import isBrowser from './isBrowser';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport useLayoutEffectWithTarget from './useLayoutEffectWithTarget';\nvar useIsomorphicLayoutEffectWithTarget = isBrowser ? useLayoutEffectWithTarget : useEffectWithTarget;\nexport default useIsomorphicLayoutEffectWithTarget;", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "useEffectWithTarget", "useLayoutEffectWithTarget", "useIsomorphicLayoutEffectWithTarget"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js"], "sourcesContent": ["import isBrowser from './isBrowser';\nimport useEffectWithTarget from './useEffectWithTarget';\nimport useLayoutEffectWithTarget from './useLayoutEffectWithTarget';\nvar useIsomorphicLayoutEffectWithTarget = isBrowser ? useLayoutEffectWithTarget : useEffectWithTarget;\nexport default useIsomorphicLayoutEffectWithTarget;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,aAAa;AACnC,OAAOC,mBAAmB,MAAM,uBAAuB;AACvD,OAAOC,yBAAyB,MAAM,6BAA6B;AACnE,IAAIC,mCAAmC,GAAGH,SAAS,GAAGE,yBAAyB,GAAGD,mBAAmB;AACrG,eAAeE,mCAAmC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}