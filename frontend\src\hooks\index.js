/**
 * 自定義Hooks統一導出
 */

export { useCardData } from './useCardData';
export { useOCRState } from './useOCRState';
export { useCameraState } from './useCameraState';

// 便捷的組合Hook
export const useOCRApp = () => {
  const cardData = useCardData();
  const ocrState = useOCRState();
  const cameraState = useCameraState();
  
  return {
    cardData,
    ocrState,
    cameraState
  };
};

export default {
  useCardData,
  useOCRState,
  useCameraState,
  useOCRApp
};
