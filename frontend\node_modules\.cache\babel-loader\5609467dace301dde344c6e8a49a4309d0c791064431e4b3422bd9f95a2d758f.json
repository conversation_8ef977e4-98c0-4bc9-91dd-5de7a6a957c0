{"ast": null, "code": "/**\n * OCR相關API服務\n * 處理圖片識別和智能解析功能\n */\n\nimport apiClient from './apiClient';\nexport const ocrService = {\n  /**\n   * OCR圖片識別\n   * @param {File} imageFile - 圖片文件\n   * @returns {Promise<Object>} OCR識別結果\n   */\n  processImage: async imageFile => {\n    try {\n      const formData = new FormData();\n      formData.append('file', imageFile);\n      const response = await apiClient.post('/ocr/image', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data'\n        }\n      });\n      return response.data;\n    } catch (error) {\n      console.error('OCR圖片識別失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 智能解析OCR文字到標準化欄位\n   * @param {string} ocrText - OCR識別的文字\n   * @param {string} side - 名片面（'front' 或 'back'）\n   * @returns {Promise<Object>} 解析後的欄位數據\n   */\n  parseFields: async (ocrText, side = 'front') => {\n    try {\n      const response = await apiClient.post('/ocr/parse-fields', {\n        ocr_text: ocrText,\n        side: side\n      });\n      return response.data;\n    } catch (error) {\n      console.error('OCR文字解析失敗:', error);\n      throw error;\n    }\n  },\n  /**\n   * 批量處理圖片（如果需要）\n   * @param {File[]} imageFiles - 圖片文件數組\n   * @returns {Promise<Object[]>} 批量處理結果\n   */\n  processBatchImages: async imageFiles => {\n    try {\n      const promises = imageFiles.map(file => ocrService.processImage(file));\n      const results = await Promise.all(promises);\n      return results;\n    } catch (error) {\n      console.error('批量OCR處理失敗:', error);\n      throw error;\n    }\n  }\n};\nexport default ocrService;", "map": {"version": 3, "names": ["apiClient", "ocrService", "processImage", "imageFile", "formData", "FormData", "append", "response", "post", "headers", "data", "error", "console", "parseFields", "ocrText", "side", "ocr_text", "processBatchImages", "imageFiles", "promises", "map", "file", "results", "Promise", "all"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/services/api/ocrService.js"], "sourcesContent": ["/**\n * OCR相關API服務\n * 處理圖片識別和智能解析功能\n */\n\nimport apiClient from './apiClient';\n\nexport const ocrService = {\n  /**\n   * OCR圖片識別\n   * @param {File} imageFile - 圖片文件\n   * @returns {Promise<Object>} OCR識別結果\n   */\n  processImage: async (imageFile) => {\n    try {\n      const formData = new FormData();\n      formData.append('file', imageFile);\n      \n      const response = await apiClient.post('/ocr/image', formData, {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      });\n      \n      return response.data;\n    } catch (error) {\n      console.error('OCR圖片識別失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 智能解析OCR文字到標準化欄位\n   * @param {string} ocrText - OCR識別的文字\n   * @param {string} side - 名片面（'front' 或 'back'）\n   * @returns {Promise<Object>} 解析後的欄位數據\n   */\n  parseFields: async (ocrText, side = 'front') => {\n    try {\n      const response = await apiClient.post('/ocr/parse-fields', {\n        ocr_text: ocrText,\n        side: side,\n      });\n      \n      return response.data;\n    } catch (error) {\n      console.error('OCR文字解析失敗:', error);\n      throw error;\n    }\n  },\n\n  /**\n   * 批量處理圖片（如果需要）\n   * @param {File[]} imageFiles - 圖片文件數組\n   * @returns {Promise<Object[]>} 批量處理結果\n   */\n  processBatchImages: async (imageFiles) => {\n    try {\n      const promises = imageFiles.map(file => ocrService.processImage(file));\n      const results = await Promise.all(promises);\n      return results;\n    } catch (error) {\n      console.error('批量OCR處理失敗:', error);\n      throw error;\n    }\n  }\n};\n\nexport default ocrService;\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,OAAOA,SAAS,MAAM,aAAa;AAEnC,OAAO,MAAMC,UAAU,GAAG;EACxB;AACF;AACA;AACA;AACA;EACEC,YAAY,EAAE,MAAOC,SAAS,IAAK;IACjC,IAAI;MACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;MAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEH,SAAS,CAAC;MAElC,MAAMI,QAAQ,GAAG,MAAMP,SAAS,CAACQ,IAAI,CAAC,YAAY,EAAEJ,QAAQ,EAAE;QAC5DK,OAAO,EAAE;UACP,cAAc,EAAE;QAClB;MACF,CAAC,CAAC;MAEF,OAAOF,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;AACA;EACEE,WAAW,EAAE,MAAAA,CAAOC,OAAO,EAAEC,IAAI,GAAG,OAAO,KAAK;IAC9C,IAAI;MACF,MAAMR,QAAQ,GAAG,MAAMP,SAAS,CAACQ,IAAI,CAAC,mBAAmB,EAAE;QACzDQ,QAAQ,EAAEF,OAAO;QACjBC,IAAI,EAAEA;MACR,CAAC,CAAC;MAEF,OAAOR,QAAQ,CAACG,IAAI;IACtB,CAAC,CAAC,OAAOC,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMA,KAAK;IACb;EACF,CAAC;EAED;AACF;AACA;AACA;AACA;EACEM,kBAAkB,EAAE,MAAOC,UAAU,IAAK;IACxC,IAAI;MACF,MAAMC,QAAQ,GAAGD,UAAU,CAACE,GAAG,CAACC,IAAI,IAAIpB,UAAU,CAACC,YAAY,CAACmB,IAAI,CAAC,CAAC;MACtE,MAAMC,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAAC;MAC3C,OAAOG,OAAO;IAChB,CAAC,CAAC,OAAOX,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;MAClC,MAAMA,KAAK;IACb;EACF;AACF,CAAC;AAED,eAAeV,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}