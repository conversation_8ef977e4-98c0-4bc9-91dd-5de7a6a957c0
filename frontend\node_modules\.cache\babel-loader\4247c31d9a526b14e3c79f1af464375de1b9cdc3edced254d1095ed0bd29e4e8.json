{"ast": null, "code": "import { __read } from \"tslib\";\nimport ResizeObserver from 'resize-observer-polyfill';\nimport useRafState from '../useRafState';\nimport { getTargetElement } from '../utils/domTarget';\nimport useIsomorphicLayoutEffectWithTarget from '../utils/useIsomorphicLayoutEffectWithTarget';\nfunction useSize(target) {\n  var _a = __read(useRafState(function () {\n      var el = getTargetElement(target);\n      return el ? {\n        width: el.clientWidth,\n        height: el.clientHeight\n      } : undefined;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useIsomorphicLayoutEffectWithTarget(function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var resizeObserver = new ResizeObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n          clientWidth = _a.clientWidth,\n          clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\nexport default useSize;", "map": {"version": 3, "names": ["__read", "ResizeObserver", "useRafState", "getTargetElement", "useIsomorphicLayoutEffectWithTarget", "useSize", "target", "_a", "el", "width", "clientWidth", "height", "clientHeight", "undefined", "state", "setState", "resizeObserver", "entries", "for<PERSON>ach", "entry", "observe", "disconnect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useSize/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport ResizeObserver from 'resize-observer-polyfill';\nimport useRafState from '../useRafState';\nimport { getTargetElement } from '../utils/domTarget';\nimport useIsomorphicLayoutEffectWithTarget from '../utils/useIsomorphicLayoutEffectWithTarget';\nfunction useSize(target) {\n  var _a = __read(useRafState(function () {\n      var el = getTargetElement(target);\n      return el ? {\n        width: el.clientWidth,\n        height: el.clientHeight\n      } : undefined;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  useIsomorphicLayoutEffectWithTarget(function () {\n    var el = getTargetElement(target);\n    if (!el) {\n      return;\n    }\n    var resizeObserver = new ResizeObserver(function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n          clientWidth = _a.clientWidth,\n          clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\nexport default useSize;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,cAAc,MAAM,0BAA0B;AACrD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mCAAmC,MAAM,8CAA8C;AAC9F,SAASC,OAAOA,CAACC,MAAM,EAAE;EACvB,IAAIC,EAAE,GAAGP,MAAM,CAACE,WAAW,CAAC,YAAY;MACpC,IAAIM,EAAE,GAAGL,gBAAgB,CAACG,MAAM,CAAC;MACjC,OAAOE,EAAE,GAAG;QACVC,KAAK,EAAED,EAAE,CAACE,WAAW;QACrBC,MAAM,EAAEH,EAAE,CAACI;MACb,CAAC,GAAGC,SAAS;IACf,CAAC,CAAC,EAAE,CAAC,CAAC;IACNC,KAAK,GAAGP,EAAE,CAAC,CAAC,CAAC;IACbQ,QAAQ,GAAGR,EAAE,CAAC,CAAC,CAAC;EAClBH,mCAAmC,CAAC,YAAY;IAC9C,IAAII,EAAE,GAAGL,gBAAgB,CAACG,MAAM,CAAC;IACjC,IAAI,CAACE,EAAE,EAAE;MACP;IACF;IACA,IAAIQ,cAAc,GAAG,IAAIf,cAAc,CAAC,UAAUgB,OAAO,EAAE;MACzDA,OAAO,CAACC,OAAO,CAAC,UAAUC,KAAK,EAAE;QAC/B,IAAIZ,EAAE,GAAGY,KAAK,CAACb,MAAM;UACnBI,WAAW,GAAGH,EAAE,CAACG,WAAW;UAC5BE,YAAY,GAAGL,EAAE,CAACK,YAAY;QAChCG,QAAQ,CAAC;UACPN,KAAK,EAAEC,WAAW;UAClBC,MAAM,EAAEC;QACV,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,CAAC;IACFI,cAAc,CAACI,OAAO,CAACZ,EAAE,CAAC;IAC1B,OAAO,YAAY;MACjBQ,cAAc,CAACK,UAAU,CAAC,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,EAAEf,MAAM,CAAC;EACd,OAAOQ,KAAK;AACd;AACA,eAAeT,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}