{"ast": null, "code": "import { __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useRef, useCallback } from 'react';\nfunction useLockFn(fn) {\n  var _this = this;\n  var lockRef = useRef(false);\n  return useCallback(function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return __awaiter(_this, void 0, void 0, function () {\n      var ret, e_1;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (lockRef.current) return [2 /*return*/];\n            lockRef.current = true;\n            _a.label = 1;\n          case 1:\n            _a.trys.push([1, 3, 4, 5]);\n            return [4 /*yield*/, fn.apply(void 0, __spreadArray([], __read(args), false))];\n          case 2:\n            ret = _a.sent();\n            return [2 /*return*/, ret];\n          case 3:\n            e_1 = _a.sent();\n            throw e_1;\n          case 4:\n            lockRef.current = false;\n            return [7 /*endfinally*/];\n          case 5:\n            return [2 /*return*/];\n        }\n      });\n    });\n  }, [fn]);\n}\nexport default useLockFn;", "map": {"version": 3, "names": ["__awaiter", "__generator", "__read", "__spread<PERSON><PERSON>y", "useRef", "useCallback", "useLockFn", "fn", "_this", "lockRef", "args", "_i", "arguments", "length", "ret", "e_1", "_a", "label", "current", "trys", "push", "apply", "sent"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useLockFn/index.js"], "sourcesContent": ["import { __awaiter, __generator, __read, __spreadArray } from \"tslib\";\nimport { useRef, useCallback } from 'react';\nfunction useLockFn(fn) {\n  var _this = this;\n  var lockRef = useRef(false);\n  return useCallback(function () {\n    var args = [];\n    for (var _i = 0; _i < arguments.length; _i++) {\n      args[_i] = arguments[_i];\n    }\n    return __awaiter(_this, void 0, void 0, function () {\n      var ret, e_1;\n      return __generator(this, function (_a) {\n        switch (_a.label) {\n          case 0:\n            if (lockRef.current) return [2 /*return*/];\n            lockRef.current = true;\n            _a.label = 1;\n          case 1:\n            _a.trys.push([1, 3, 4, 5]);\n            return [4 /*yield*/, fn.apply(void 0, __spreadArray([], __read(args), false))];\n          case 2:\n            ret = _a.sent();\n            return [2 /*return*/, ret];\n          case 3:\n            e_1 = _a.sent();\n            throw e_1;\n          case 4:\n            lockRef.current = false;\n            return [7 /*endfinally*/];\n          case 5:\n            return [2 /*return*/];\n        }\n      });\n    });\n  }, [fn]);\n}\nexport default useLockFn;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,WAAW,EAAEC,MAAM,EAAEC,aAAa,QAAQ,OAAO;AACrE,SAASC,MAAM,EAAEC,WAAW,QAAQ,OAAO;AAC3C,SAASC,SAASA,CAACC,EAAE,EAAE;EACrB,IAAIC,KAAK,GAAG,IAAI;EAChB,IAAIC,OAAO,GAAGL,MAAM,CAAC,KAAK,CAAC;EAC3B,OAAOC,WAAW,CAAC,YAAY;IAC7B,IAAIK,IAAI,GAAG,EAAE;IACb,KAAK,IAAIC,EAAE,GAAG,CAAC,EAAEA,EAAE,GAAGC,SAAS,CAACC,MAAM,EAAEF,EAAE,EAAE,EAAE;MAC5CD,IAAI,CAACC,EAAE,CAAC,GAAGC,SAAS,CAACD,EAAE,CAAC;IAC1B;IACA,OAAOX,SAAS,CAACQ,KAAK,EAAE,KAAK,CAAC,EAAE,KAAK,CAAC,EAAE,YAAY;MAClD,IAAIM,GAAG,EAAEC,GAAG;MACZ,OAAOd,WAAW,CAAC,IAAI,EAAE,UAAUe,EAAE,EAAE;QACrC,QAAQA,EAAE,CAACC,KAAK;UACd,KAAK,CAAC;YACJ,IAAIR,OAAO,CAACS,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,WAAW;YAC1CT,OAAO,CAACS,OAAO,GAAG,IAAI;YACtBF,EAAE,CAACC,KAAK,GAAG,CAAC;UACd,KAAK,CAAC;YACJD,EAAE,CAACG,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAC1B,OAAO,CAAC,CAAC,CAAC,WAAWb,EAAE,CAACc,KAAK,CAAC,KAAK,CAAC,EAAElB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACQ,IAAI,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;UAChF,KAAK,CAAC;YACJI,GAAG,GAAGE,EAAE,CAACM,IAAI,CAAC,CAAC;YACf,OAAO,CAAC,CAAC,CAAC,YAAYR,GAAG,CAAC;UAC5B,KAAK,CAAC;YACJC,GAAG,GAAGC,EAAE,CAACM,IAAI,CAAC,CAAC;YACf,MAAMP,GAAG;UACX,KAAK,CAAC;YACJN,OAAO,CAACS,OAAO,GAAG,KAAK;YACvB,OAAO,CAAC,CAAC,CAAC,eAAe;UAC3B,KAAK,CAAC;YACJ,OAAO,CAAC,CAAC,CAAC,WAAW;QACzB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACX,EAAE,CAAC,CAAC;AACV;AACA,eAAeD,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}