{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { isFunction, isUndef } from '../utils';\nexport var SYNC_STORAGE_EVENT_NAME = 'AHOOKS_SYNC_STORAGE_EVENT_NAME';\nexport function createUseStorageState(getStorage) {\n  function useStorageState(key, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var storage;\n    var _a = options.listenStorageChange,\n      listenStorageChange = _a === void 0 ? false : _a,\n      _b = options.onError,\n      onError = _b === void 0 ? function (e) {\n        console.error(e);\n      } : _b;\n    // https://github.com/alibaba/hooks/issues/800\n    try {\n      storage = getStorage();\n    } catch (err) {\n      onError(err);\n    }\n    var serializer = function (value) {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    };\n    var deserializer = function (value) {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      return JSON.parse(value);\n    };\n    function getStoredValue() {\n      try {\n        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (raw) {\n          return deserializer(raw);\n        }\n      } catch (e) {\n        onError(e);\n      }\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }\n    var _c = __read(useState(getStoredValue), 2),\n      state = _c[0],\n      setState = _c[1];\n    useUpdateEffect(function () {\n      setState(getStoredValue());\n    }, [key]);\n    var updateState = function (value) {\n      var currentState = isFunction(value) ? value(state) : value;\n      if (!listenStorageChange) {\n        setState(currentState);\n      }\n      try {\n        var newValue = void 0;\n        var oldValue = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (isUndef(currentState)) {\n          newValue = null;\n          storage === null || storage === void 0 ? void 0 : storage.removeItem(key);\n        } else {\n          newValue = serializer(currentState);\n          storage === null || storage === void 0 ? void 0 : storage.setItem(key, newValue);\n        }\n        dispatchEvent(\n        // send custom event to communicate within same page\n        // importantly this should not be a StorageEvent since those cannot\n        // be constructed with a non-built-in storage area\n        new CustomEvent(SYNC_STORAGE_EVENT_NAME, {\n          detail: {\n            key: key,\n            newValue: newValue,\n            oldValue: oldValue,\n            storageArea: storage\n          }\n        }));\n      } catch (e) {\n        onError(e);\n      }\n    };\n    var syncState = function (event) {\n      if (event.key !== key || event.storageArea !== storage) {\n        return;\n      }\n      setState(getStoredValue());\n    };\n    var syncStateFromCustomEvent = function (event) {\n      syncState(event.detail);\n    };\n    // from another document\n    useEventListener('storage', syncState, {\n      enable: listenStorageChange\n    });\n    // from the same document but different hooks\n    useEventListener(SYNC_STORAGE_EVENT_NAME, syncStateFromCustomEvent, {\n      enable: listenStorageChange\n    });\n    return [state, useMemoizedFn(updateState)];\n  }\n  return useStorageState;\n}", "map": {"version": 3, "names": ["__read", "useState", "useEventListener", "useMemoizedFn", "useUpdateEffect", "isFunction", "isUndef", "SYNC_STORAGE_EVENT_NAME", "createUseStorageState", "getStorage", "useStorageState", "key", "options", "storage", "_a", "listenStorageChange", "_b", "onError", "e", "console", "error", "err", "serializer", "value", "JSON", "stringify", "deserializer", "parse", "getStoredValue", "raw", "getItem", "defaultValue", "_c", "state", "setState", "updateState", "currentState", "newValue", "oldValue", "removeItem", "setItem", "dispatchEvent", "CustomEvent", "detail", "storageArea", "syncState", "event", "syncStateFromCustomEvent", "enable"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/createUseStorageState/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUpdateEffect from '../useUpdateEffect';\nimport { isFunction, isUndef } from '../utils';\nexport var SYNC_STORAGE_EVENT_NAME = 'AHOOKS_SYNC_STORAGE_EVENT_NAME';\nexport function createUseStorageState(getStorage) {\n  function useStorageState(key, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    var storage;\n    var _a = options.listenStorageChange,\n      listenStorageChange = _a === void 0 ? false : _a,\n      _b = options.onError,\n      onError = _b === void 0 ? function (e) {\n        console.error(e);\n      } : _b;\n    // https://github.com/alibaba/hooks/issues/800\n    try {\n      storage = getStorage();\n    } catch (err) {\n      onError(err);\n    }\n    var serializer = function (value) {\n      if (options.serializer) {\n        return options.serializer(value);\n      }\n      return JSON.stringify(value);\n    };\n    var deserializer = function (value) {\n      if (options.deserializer) {\n        return options.deserializer(value);\n      }\n      return JSON.parse(value);\n    };\n    function getStoredValue() {\n      try {\n        var raw = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (raw) {\n          return deserializer(raw);\n        }\n      } catch (e) {\n        onError(e);\n      }\n      if (isFunction(options.defaultValue)) {\n        return options.defaultValue();\n      }\n      return options.defaultValue;\n    }\n    var _c = __read(useState(getStoredValue), 2),\n      state = _c[0],\n      setState = _c[1];\n    useUpdateEffect(function () {\n      setState(getStoredValue());\n    }, [key]);\n    var updateState = function (value) {\n      var currentState = isFunction(value) ? value(state) : value;\n      if (!listenStorageChange) {\n        setState(currentState);\n      }\n      try {\n        var newValue = void 0;\n        var oldValue = storage === null || storage === void 0 ? void 0 : storage.getItem(key);\n        if (isUndef(currentState)) {\n          newValue = null;\n          storage === null || storage === void 0 ? void 0 : storage.removeItem(key);\n        } else {\n          newValue = serializer(currentState);\n          storage === null || storage === void 0 ? void 0 : storage.setItem(key, newValue);\n        }\n        dispatchEvent(\n        // send custom event to communicate within same page\n        // importantly this should not be a StorageEvent since those cannot\n        // be constructed with a non-built-in storage area\n        new CustomEvent(SYNC_STORAGE_EVENT_NAME, {\n          detail: {\n            key: key,\n            newValue: newValue,\n            oldValue: oldValue,\n            storageArea: storage\n          }\n        }));\n      } catch (e) {\n        onError(e);\n      }\n    };\n    var syncState = function (event) {\n      if (event.key !== key || event.storageArea !== storage) {\n        return;\n      }\n      setState(getStoredValue());\n    };\n    var syncStateFromCustomEvent = function (event) {\n      syncState(event.detail);\n    };\n    // from another document\n    useEventListener('storage', syncState, {\n      enable: listenStorageChange\n    });\n    // from the same document but different hooks\n    useEventListener(SYNC_STORAGE_EVENT_NAME, syncStateFromCustomEvent, {\n      enable: listenStorageChange\n    });\n    return [state, useMemoizedFn(updateState)];\n  }\n  return useStorageState;\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,eAAe,MAAM,oBAAoB;AAChD,SAASC,UAAU,EAAEC,OAAO,QAAQ,UAAU;AAC9C,OAAO,IAAIC,uBAAuB,GAAG,gCAAgC;AACrE,OAAO,SAASC,qBAAqBA,CAACC,UAAU,EAAE;EAChD,SAASC,eAAeA,CAACC,GAAG,EAAEC,OAAO,EAAE;IACrC,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;MACtBA,OAAO,GAAG,CAAC,CAAC;IACd;IACA,IAAIC,OAAO;IACX,IAAIC,EAAE,GAAGF,OAAO,CAACG,mBAAmB;MAClCA,mBAAmB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;MAChDE,EAAE,GAAGJ,OAAO,CAACK,OAAO;MACpBA,OAAO,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,UAAUE,CAAC,EAAE;QACrCC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MAClB,CAAC,GAAGF,EAAE;IACR;IACA,IAAI;MACFH,OAAO,GAAGJ,UAAU,CAAC,CAAC;IACxB,CAAC,CAAC,OAAOY,GAAG,EAAE;MACZJ,OAAO,CAACI,GAAG,CAAC;IACd;IACA,IAAIC,UAAU,GAAG,SAAAA,CAAUC,KAAK,EAAE;MAChC,IAAIX,OAAO,CAACU,UAAU,EAAE;QACtB,OAAOV,OAAO,CAACU,UAAU,CAACC,KAAK,CAAC;MAClC;MACA,OAAOC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC;IAC9B,CAAC;IACD,IAAIG,YAAY,GAAG,SAAAA,CAAUH,KAAK,EAAE;MAClC,IAAIX,OAAO,CAACc,YAAY,EAAE;QACxB,OAAOd,OAAO,CAACc,YAAY,CAACH,KAAK,CAAC;MACpC;MACA,OAAOC,IAAI,CAACG,KAAK,CAACJ,KAAK,CAAC;IAC1B,CAAC;IACD,SAASK,cAAcA,CAAA,EAAG;MACxB,IAAI;QACF,IAAIC,GAAG,GAAGhB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,OAAO,CAACnB,GAAG,CAAC;QAChF,IAAIkB,GAAG,EAAE;UACP,OAAOH,YAAY,CAACG,GAAG,CAAC;QAC1B;MACF,CAAC,CAAC,OAAOX,CAAC,EAAE;QACVD,OAAO,CAACC,CAAC,CAAC;MACZ;MACA,IAAIb,UAAU,CAACO,OAAO,CAACmB,YAAY,CAAC,EAAE;QACpC,OAAOnB,OAAO,CAACmB,YAAY,CAAC,CAAC;MAC/B;MACA,OAAOnB,OAAO,CAACmB,YAAY;IAC7B;IACA,IAAIC,EAAE,GAAGhC,MAAM,CAACC,QAAQ,CAAC2B,cAAc,CAAC,EAAE,CAAC,CAAC;MAC1CK,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;MACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;IAClB5B,eAAe,CAAC,YAAY;MAC1B8B,QAAQ,CAACN,cAAc,CAAC,CAAC,CAAC;IAC5B,CAAC,EAAE,CAACjB,GAAG,CAAC,CAAC;IACT,IAAIwB,WAAW,GAAG,SAAAA,CAAUZ,KAAK,EAAE;MACjC,IAAIa,YAAY,GAAG/B,UAAU,CAACkB,KAAK,CAAC,GAAGA,KAAK,CAACU,KAAK,CAAC,GAAGV,KAAK;MAC3D,IAAI,CAACR,mBAAmB,EAAE;QACxBmB,QAAQ,CAACE,YAAY,CAAC;MACxB;MACA,IAAI;QACF,IAAIC,QAAQ,GAAG,KAAK,CAAC;QACrB,IAAIC,QAAQ,GAAGzB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACiB,OAAO,CAACnB,GAAG,CAAC;QACrF,IAAIL,OAAO,CAAC8B,YAAY,CAAC,EAAE;UACzBC,QAAQ,GAAG,IAAI;UACfxB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC0B,UAAU,CAAC5B,GAAG,CAAC;QAC3E,CAAC,MAAM;UACL0B,QAAQ,GAAGf,UAAU,CAACc,YAAY,CAAC;UACnCvB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC2B,OAAO,CAAC7B,GAAG,EAAE0B,QAAQ,CAAC;QAClF;QACAI,aAAa;QACb;QACA;QACA;QACA,IAAIC,WAAW,CAACnC,uBAAuB,EAAE;UACvCoC,MAAM,EAAE;YACNhC,GAAG,EAAEA,GAAG;YACR0B,QAAQ,EAAEA,QAAQ;YAClBC,QAAQ,EAAEA,QAAQ;YAClBM,WAAW,EAAE/B;UACf;QACF,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,OAAOK,CAAC,EAAE;QACVD,OAAO,CAACC,CAAC,CAAC;MACZ;IACF,CAAC;IACD,IAAI2B,SAAS,GAAG,SAAAA,CAAUC,KAAK,EAAE;MAC/B,IAAIA,KAAK,CAACnC,GAAG,KAAKA,GAAG,IAAImC,KAAK,CAACF,WAAW,KAAK/B,OAAO,EAAE;QACtD;MACF;MACAqB,QAAQ,CAACN,cAAc,CAAC,CAAC,CAAC;IAC5B,CAAC;IACD,IAAImB,wBAAwB,GAAG,SAAAA,CAAUD,KAAK,EAAE;MAC9CD,SAAS,CAACC,KAAK,CAACH,MAAM,CAAC;IACzB,CAAC;IACD;IACAzC,gBAAgB,CAAC,SAAS,EAAE2C,SAAS,EAAE;MACrCG,MAAM,EAAEjC;IACV,CAAC,CAAC;IACF;IACAb,gBAAgB,CAACK,uBAAuB,EAAEwC,wBAAwB,EAAE;MAClEC,MAAM,EAAEjC;IACV,CAAC,CAAC;IACF,OAAO,CAACkB,KAAK,EAAE9B,aAAa,CAACgC,WAAW,CAAC,CAAC;EAC5C;EACA,OAAOzB,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}