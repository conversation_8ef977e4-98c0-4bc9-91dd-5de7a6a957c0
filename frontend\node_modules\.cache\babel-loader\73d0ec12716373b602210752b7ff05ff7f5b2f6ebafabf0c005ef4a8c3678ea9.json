{"ast": null, "code": "import { useEffect, useRef } from 'react';\nvar useUnmountedRef = function () {\n  var unmountedRef = useRef(false);\n  useEffect(function () {\n    unmountedRef.current = false;\n    return function () {\n      unmountedRef.current = true;\n    };\n  }, []);\n  return unmountedRef;\n};\nexport default useUnmountedRef;", "map": {"version": 3, "names": ["useEffect", "useRef", "useUnmountedRef", "unmountedRef", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useUnmountedRef/index.js"], "sourcesContent": ["import { useEffect, useRef } from 'react';\nvar useUnmountedRef = function () {\n  var unmountedRef = useRef(false);\n  useEffect(function () {\n    unmountedRef.current = false;\n    return function () {\n      unmountedRef.current = true;\n    };\n  }, []);\n  return unmountedRef;\n};\nexport default useUnmountedRef;"], "mappings": "AAAA,SAASA,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACzC,IAAIC,eAAe,GAAG,SAAAA,CAAA,EAAY;EAChC,IAAIC,YAAY,GAAGF,MAAM,CAAC,KAAK,CAAC;EAChCD,SAAS,CAAC,YAAY;IACpBG,YAAY,CAACC,OAAO,GAAG,KAAK;IAC5B,OAAO,YAAY;MACjBD,YAAY,CAACC,OAAO,GAAG,IAAI;IAC7B,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,OAAOD,YAAY;AACrB,CAAC;AACD,eAAeD,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}