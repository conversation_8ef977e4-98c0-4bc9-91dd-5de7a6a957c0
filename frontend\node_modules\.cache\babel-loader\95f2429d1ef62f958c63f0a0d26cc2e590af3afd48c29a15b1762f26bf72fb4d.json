{"ast": null, "code": "import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafTimeout = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setTimeout(callback, delay)\n    };\n  }\n  var handle = {\n    id: 0\n  };\n  var startTime = new Date().getTime();\n  var loop = function () {\n    var current = new Date().getTime();\n    if (current - startTime >= delay) {\n      callback();\n    } else {\n      handle.id = requestAnimationFrame(loop);\n    }\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafTimeout = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearTimeout(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n    timerRef.current = setRafTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafTimeout;", "map": {"version": 3, "names": ["useCallback", "useEffect", "useRef", "useLatest", "isNumber", "setRafTimeout", "callback", "delay", "requestAnimationFrame", "undefined", "id", "setTimeout", "handle", "startTime", "Date", "getTime", "loop", "current", "cancelAnimationFrameIsNotDefined", "t", "cancelAnimationFrame", "clearRafTimeout", "clearTimeout", "useRafTimeout", "fn", "fnRef", "timerRef", "clear"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRafTimeout/index.js"], "sourcesContent": ["import { useCallback, useEffect, useRef } from 'react';\nimport useLatest from '../useLatest';\nimport { isNumber } from '../utils';\nvar setRafTimeout = function (callback, delay) {\n  if (delay === void 0) {\n    delay = 0;\n  }\n  if (typeof requestAnimationFrame === typeof undefined) {\n    return {\n      id: setTimeout(callback, delay)\n    };\n  }\n  var handle = {\n    id: 0\n  };\n  var startTime = new Date().getTime();\n  var loop = function () {\n    var current = new Date().getTime();\n    if (current - startTime >= delay) {\n      callback();\n    } else {\n      handle.id = requestAnimationFrame(loop);\n    }\n  };\n  handle.id = requestAnimationFrame(loop);\n  return handle;\n};\nfunction cancelAnimationFrameIsNotDefined(t) {\n  return typeof cancelAnimationFrame === typeof undefined;\n}\nvar clearRafTimeout = function (handle) {\n  if (cancelAnimationFrameIsNotDefined(handle.id)) {\n    return clearTimeout(handle.id);\n  }\n  cancelAnimationFrame(handle.id);\n};\nfunction useRafTimeout(fn, delay) {\n  var fnRef = useLatest(fn);\n  var timerRef = useRef();\n  var clear = useCallback(function () {\n    if (timerRef.current) {\n      clearRafTimeout(timerRef.current);\n    }\n  }, []);\n  useEffect(function () {\n    if (!isNumber(delay) || delay < 0) return;\n    timerRef.current = setRafTimeout(function () {\n      fnRef.current();\n    }, delay);\n    return clear;\n  }, [delay]);\n  return clear;\n}\nexport default useRafTimeout;"], "mappings": "AAAA,SAASA,WAAW,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AACtD,OAAOC,SAAS,MAAM,cAAc;AACpC,SAASC,QAAQ,QAAQ,UAAU;AACnC,IAAIC,aAAa,GAAG,SAAAA,CAAUC,QAAQ,EAAEC,KAAK,EAAE;EAC7C,IAAIA,KAAK,KAAK,KAAK,CAAC,EAAE;IACpBA,KAAK,GAAG,CAAC;EACX;EACA,IAAI,OAAOC,qBAAqB,KAAK,OAAOC,SAAS,EAAE;IACrD,OAAO;MACLC,EAAE,EAAEC,UAAU,CAACL,QAAQ,EAAEC,KAAK;IAChC,CAAC;EACH;EACA,IAAIK,MAAM,GAAG;IACXF,EAAE,EAAE;EACN,CAAC;EACD,IAAIG,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;EACpC,IAAIC,IAAI,GAAG,SAAAA,CAAA,EAAY;IACrB,IAAIC,OAAO,GAAG,IAAIH,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IAClC,IAAIE,OAAO,GAAGJ,SAAS,IAAIN,KAAK,EAAE;MAChCD,QAAQ,CAAC,CAAC;IACZ,CAAC,MAAM;MACLM,MAAM,CAACF,EAAE,GAAGF,qBAAqB,CAACQ,IAAI,CAAC;IACzC;EACF,CAAC;EACDJ,MAAM,CAACF,EAAE,GAAGF,qBAAqB,CAACQ,IAAI,CAAC;EACvC,OAAOJ,MAAM;AACf,CAAC;AACD,SAASM,gCAAgCA,CAACC,CAAC,EAAE;EAC3C,OAAO,OAAOC,oBAAoB,KAAK,OAAOX,SAAS;AACzD;AACA,IAAIY,eAAe,GAAG,SAAAA,CAAUT,MAAM,EAAE;EACtC,IAAIM,gCAAgC,CAACN,MAAM,CAACF,EAAE,CAAC,EAAE;IAC/C,OAAOY,YAAY,CAACV,MAAM,CAACF,EAAE,CAAC;EAChC;EACAU,oBAAoB,CAACR,MAAM,CAACF,EAAE,CAAC;AACjC,CAAC;AACD,SAASa,aAAaA,CAACC,EAAE,EAAEjB,KAAK,EAAE;EAChC,IAAIkB,KAAK,GAAGtB,SAAS,CAACqB,EAAE,CAAC;EACzB,IAAIE,QAAQ,GAAGxB,MAAM,CAAC,CAAC;EACvB,IAAIyB,KAAK,GAAG3B,WAAW,CAAC,YAAY;IAClC,IAAI0B,QAAQ,CAACT,OAAO,EAAE;MACpBI,eAAe,CAACK,QAAQ,CAACT,OAAO,CAAC;IACnC;EACF,CAAC,EAAE,EAAE,CAAC;EACNhB,SAAS,CAAC,YAAY;IACpB,IAAI,CAACG,QAAQ,CAACG,KAAK,CAAC,IAAIA,KAAK,GAAG,CAAC,EAAE;IACnCmB,QAAQ,CAACT,OAAO,GAAGZ,aAAa,CAAC,YAAY;MAC3CoB,KAAK,CAACR,OAAO,CAAC,CAAC;IACjB,CAAC,EAAEV,KAAK,CAAC;IACT,OAAOoB,KAAK;EACd,CAAC,EAAE,CAACpB,KAAK,CAAC,CAAC;EACX,OAAOoB,KAAK;AACd;AACA,eAAeJ,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}