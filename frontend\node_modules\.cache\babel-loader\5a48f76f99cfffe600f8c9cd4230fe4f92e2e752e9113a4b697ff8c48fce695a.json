{"ast": null, "code": "import { useEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useEffect);", "map": {"version": 3, "names": ["useEffect", "createDeepCompareEffect"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDeepCompareEffect/index.js"], "sourcesContent": ["import { useEffect } from 'react';\nimport { createDeepCompareEffect } from '../createDeepCompareEffect';\nexport default createDeepCompareEffect(useEffect);"], "mappings": "AAAA,SAASA,SAAS,QAAQ,OAAO;AACjC,SAASC,uBAAuB,QAAQ,4BAA4B;AACpE,eAAeA,uBAAuB,CAACD,SAAS,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}