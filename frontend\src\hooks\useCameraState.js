/**
 * 相機狀態管理Hook
 * 管理相機拍攝、圖片選擇和預覽功能
 */

import { useState, useCallback, useRef, useEffect } from 'react';
import { Toast } from 'antd-mobile';
import { getCameraManager } from '../utils/cameraManager';
import { getDeviceType } from '../utils/deviceDetector';
import { validateImageFile, getImagePreviewUrl, revokeImagePreviewUrl } from '../utils/imageUtils';
import { CAMERA_TARGET, CAMERA_STATUS, ERROR_MESSAGES } from '../utils/constants';

export const useCameraState = () => {
  const [images, setImages] = useState({
    front: { file: null, preview: null, ocrText: '', parseStatus: null },
    back: { file: null, preview: null, ocrText: '', parseStatus: null }
  });
  
  const [currentTarget, setCurrentTarget] = useState(CAMERA_TARGET.FRONT);
  const [cameraStatus, setCameraStatus] = useState(CAMERA_STATUS.INACTIVE);
  const [cameraModalVisible, setCameraModalVisible] = useState(false);
  const [deviceType, setDeviceType] = useState('desktop');
  
  const cameraManagerRef = useRef(null);
  const fileInputRef = useRef(null);

  /**
   * 初始化相機管理器
   */
  const initializeCameraManager = useCallback(async () => {
    try {
      if (!cameraManagerRef.current) {
        cameraManagerRef.current = getCameraManager();
        await cameraManagerRef.current.initialize();
      }
      
      const type = getDeviceType();
      setDeviceType(type);
      
      console.log('📱 設備類型:', type);
      console.log('📷 相機管理器初始化完成');
    } catch (error) {
      console.error('❌ 相機管理器初始化失敗:', error);
      Toast.show({
        content: '相機初始化失敗',
        position: 'center',
      });
    }
  }, []);

  /**
   * 更新圖片狀態
   */
  const updateImageState = useCallback((target, updates) => {
    setImages(prev => ({
      ...prev,
      [target]: { ...prev[target], ...updates }
    }));
  }, []);

  /**
   * 處理圖片文件
   */
  const handleImageFile = useCallback((file, target) => {
    // 驗證圖片文件
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      Toast.show({
        content: validation.error,
        position: 'center',
      });
      return null;
    }

    // 釋放舊的預覽URL
    const oldPreview = images[target].preview;
    if (oldPreview) {
      revokeImagePreviewUrl(oldPreview);
    }

    // 創建新的預覽URL
    const preview = getImagePreviewUrl(file);
    
    // 更新狀態
    updateImageState(target, {
      file,
      preview,
      ocrText: '',
      parseStatus: null
    });

    console.log(`📸 圖片已設置 - ${target}面:`, file.name);
    return { file, preview };
  }, [images, updateImageState]);

  /**
   * 啟動相機
   */
  const startCamera = useCallback(async (target = currentTarget) => {
    if (!cameraManagerRef.current) {
      await initializeCameraManager();
    }

    setCurrentTarget(target);
    setCameraStatus(CAMERA_STATUS.STARTING);
    setCameraModalVisible(true);

    try {
      console.log(`📷 啟動相機 - ${target}面`);
      setCameraStatus(CAMERA_STATUS.ACTIVE);
    } catch (error) {
      console.error('❌ 啟動相機失敗:', error);
      setCameraStatus(CAMERA_STATUS.ERROR);
      setCameraModalVisible(false);
      
      let message = ERROR_MESSAGES.CAMERA_PERMISSION_DENIED;
      if (error.name === 'NotFoundError') {
        message = ERROR_MESSAGES.CAMERA_NOT_FOUND;
      } else if (error.name === 'NotReadableError') {
        message = ERROR_MESSAGES.CAMERA_OCCUPIED;
      }
      
      Toast.show({
        content: message,
        position: 'center',
      });
    }
  }, [currentTarget, initializeCameraManager]);

  /**
   * 停止相機
   */
  const stopCamera = useCallback(() => {
    if (cameraManagerRef.current) {
      cameraManagerRef.current.stopCamera();
    }
    setCameraStatus(CAMERA_STATUS.INACTIVE);
    setCameraModalVisible(false);
    console.log('📷 相機已停止');
  }, []);

  /**
   * 拍照
   */
  const capturePhoto = useCallback(async (target = currentTarget) => {
    if (!cameraManagerRef.current) {
      Toast.show({
        content: '相機未初始化',
        position: 'center',
      });
      return null;
    }

    try {
      const photoData = await cameraManagerRef.current.takePhoto();
      if (photoData && photoData.file) {
        const result = handleImageFile(photoData.file, target);
        stopCamera();
        
        Toast.show({
          content: `${target === 'front' ? '正面' : '反面'}拍照完成`,
          position: 'center',
        });
        
        return result;
      }
    } catch (error) {
      console.error('❌ 拍照失敗:', error);
      Toast.show({
        content: '拍照失敗，請重試',
        position: 'center',
      });
    }
    
    return null;
  }, [currentTarget, handleImageFile, stopCamera]);

  /**
   * 從相冊選擇圖片
   */
  const selectFromGallery = useCallback((target = currentTarget) => {
    setCurrentTarget(target);
    
    // 創建文件輸入元素
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.capture = 'environment'; // 優先使用後置攝像頭
    
    input.onchange = (event) => {
      const file = event.target.files[0];
      if (file) {
        const result = handleImageFile(file, target);
        if (result) {
          Toast.show({
            content: `${target === 'front' ? '正面' : '反面'}圖片已選擇`,
            position: 'center',
          });
        }
      }
    };
    
    input.click();
  }, [currentTarget, handleImageFile]);

  /**
   * 移除圖片
   */
  const removeImage = useCallback((target) => {
    const oldPreview = images[target].preview;
    if (oldPreview) {
      revokeImagePreviewUrl(oldPreview);
    }
    
    updateImageState(target, {
      file: null,
      preview: null,
      ocrText: '',
      parseStatus: null
    });
    
    console.log(`🗑️ 移除圖片 - ${target}面`);
  }, [images, updateImageState]);

  /**
   * 切換拍攝目標
   */
  const switchTarget = useCallback((target) => {
    setCurrentTarget(target);
    console.log(`🔄 切換拍攝目標: ${target}面`);
  }, []);

  /**
   * 更新OCR結果
   */
  const updateOCRResult = useCallback((target, ocrText, parseStatus = null) => {
    updateImageState(target, {
      ocrText,
      parseStatus
    });
  }, [updateImageState]);

  /**
   * 清空所有圖片
   */
  const clearAllImages = useCallback(() => {
    // 釋放預覽URL
    Object.keys(images).forEach(target => {
      const preview = images[target].preview;
      if (preview) {
        revokeImagePreviewUrl(preview);
      }
    });
    
    setImages({
      front: { file: null, preview: null, ocrText: '', parseStatus: null },
      back: { file: null, preview: null, ocrText: '', parseStatus: null }
    });
    
    console.log('🧹 清空所有圖片');
  }, [images]);

  /**
   * 檢查是否有圖片
   */
  const hasImages = useCallback((target = 'any') => {
    if (target === 'any') {
      return !!(images.front.file || images.back.file);
    }
    return !!images[target].file;
  }, [images]);

  /**
   * 獲取圖片數據用於提交
   */
  const getImagesForSubmit = useCallback(() => {
    const result = {};
    
    if (images.front.file) {
      result.front = {
        file: images.front.file,
        ocrText: images.front.ocrText
      };
    }
    
    if (images.back.file) {
      result.back = {
        file: images.back.file,
        ocrText: images.back.ocrText
      };
    }
    
    return result;
  }, [images]);

  // 初始化相機管理器
  useEffect(() => {
    initializeCameraManager();
  }, [initializeCameraManager]);

  // 清理預覽URL
  useEffect(() => {
    return () => {
      Object.keys(images).forEach(target => {
        const preview = images[target].preview;
        if (preview) {
          revokeImagePreviewUrl(preview);
        }
      });
    };
  }, []);

  return {
    // 狀態
    images,
    currentTarget,
    cameraStatus,
    cameraModalVisible,
    deviceType,
    cameraManager: cameraManagerRef.current,
    
    // 操作方法
    startCamera,
    stopCamera,
    capturePhoto,
    selectFromGallery,
    removeImage,
    switchTarget,
    updateOCRResult,
    clearAllImages,
    
    // 工具方法
    hasImages,
    getImagesForSubmit,
    
    // 便捷屬性
    isMobile: deviceType === 'mobile',
    isTablet: deviceType === 'tablet',
    isCameraActive: cameraStatus === CAMERA_STATUS.ACTIVE,
    isCameraStarting: cameraStatus === CAMERA_STATUS.STARTING,
    hasFrontImage: !!images.front.file,
    hasBackImage: !!images.back.file,
    hasAnyImage: hasImages('any')
  };
};

export default useCameraState;
