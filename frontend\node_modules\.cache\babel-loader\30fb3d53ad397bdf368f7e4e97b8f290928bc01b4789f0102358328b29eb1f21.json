{"ast": null, "code": "import { __assign } from \"tslib\";\nimport useAntdTable from '../useAntdTable';\nimport { fieldAdapter, resultAdapter } from './fusionAdapter';\nvar useFusionTable = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var ret = useAntdTable(service, __assign(__assign({}, options), {\n    form: options.field ? fieldAdapter(options.field) : undefined\n  }));\n  return resultAdapter(ret);\n};\nexport default useFusionTable;", "map": {"version": 3, "names": ["__assign", "useAntdTable", "fieldAdapter", "resultAdapter", "useFusionTable", "service", "options", "ret", "form", "field", "undefined"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useFusionTable/index.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport useAntdTable from '../useAntdTable';\nimport { fieldAdapter, resultAdapter } from './fusionAdapter';\nvar useFusionTable = function (service, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var ret = useAntdTable(service, __assign(__assign({}, options), {\n    form: options.field ? fieldAdapter(options.field) : undefined\n  }));\n  return resultAdapter(ret);\n};\nexport default useFusionTable;"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,SAASC,YAAY,EAAEC,aAAa,QAAQ,iBAAiB;AAC7D,IAAIC,cAAc,GAAG,SAAAA,CAAUC,OAAO,EAAEC,OAAO,EAAE;EAC/C,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,GAAG,GAAGN,YAAY,CAACI,OAAO,EAAEL,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEM,OAAO,CAAC,EAAE;IAC9DE,IAAI,EAAEF,OAAO,CAACG,KAAK,GAAGP,YAAY,CAACI,OAAO,CAACG,KAAK,CAAC,GAAGC;EACtD,CAAC,CAAC,CAAC;EACH,OAAOP,aAAa,CAACI,GAAG,CAAC;AAC3B,CAAC;AACD,eAAeH,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}