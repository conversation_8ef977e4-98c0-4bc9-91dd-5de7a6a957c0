{"ast": null, "code": "import { useRef } from 'react';\nfunction useLatest(value) {\n  var ref = useRef(value);\n  ref.current = value;\n  return ref;\n}\nexport default useLatest;", "map": {"version": 3, "names": ["useRef", "useLatest", "value", "ref", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useLatest/index.js"], "sourcesContent": ["import { useRef } from 'react';\nfunction useLatest(value) {\n  var ref = useRef(value);\n  ref.current = value;\n  return ref;\n}\nexport default useLatest;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAASA,CAACC,KAAK,EAAE;EACxB,IAAIC,GAAG,GAAGH,MAAM,CAACE,KAAK,CAAC;EACvBC,GAAG,CAACC,OAAO,GAAGF,KAAK;EACnB,OAAOC,GAAG;AACZ;AACA,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}