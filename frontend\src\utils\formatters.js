/**
 * 數據格式化工具函數
 */

/**
 * 格式化電話號碼
 * @param {string} phone - 原始電話號碼
 * @returns {string} 格式化後的電話號碼
 */
export const formatPhone = (phone) => {
  if (!phone) return '';
  
  // 移除所有非數字字符
  const cleaned = phone.replace(/\D/g, '');
  
  // 台灣手機號碼格式化 (09xx-xxx-xxx)
  if (cleaned.length === 10 && cleaned.startsWith('09')) {
    return `${cleaned.slice(0, 4)}-${cleaned.slice(4, 7)}-${cleaned.slice(7)}`;
  }
  
  // 台灣市話格式化 (0x-xxxx-xxxx)
  if (cleaned.length >= 8 && cleaned.startsWith('0')) {
    if (cleaned.length === 8) {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 9) {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;
    } else if (cleaned.length === 10) {
      return `${cleaned.slice(0, 2)}-${cleaned.slice(2, 6)}-${cleaned.slice(6)}`;
    }
  }
  
  // 其他格式保持原樣
  return phone;
};

/**
 * 格式化電子郵件（轉為小寫）
 * @param {string} email - 原始電子郵件
 * @returns {string} 格式化後的電子郵件
 */
export const formatEmail = (email) => {
  if (!email) return '';
  return email.toLowerCase().trim();
};

/**
 * 格式化姓名（移除多餘空格）
 * @param {string} name - 原始姓名
 * @returns {string} 格式化後的姓名
 */
export const formatName = (name) => {
  if (!name) return '';
  return name.trim().replace(/\s+/g, ' ');
};

/**
 * 格式化公司名稱
 * @param {string} company - 原始公司名稱
 * @returns {string} 格式化後的公司名稱
 */
export const formatCompany = (company) => {
  if (!company) return '';
  return company.trim();
};

/**
 * 格式化地址
 * @param {string} address - 原始地址
 * @returns {string} 格式化後的地址
 */
export const formatAddress = (address) => {
  if (!address) return '';
  return address.trim().replace(/\s+/g, ' ');
};

/**
 * 格式化Line ID
 * @param {string} lineId - 原始Line ID
 * @returns {string} 格式化後的Line ID
 */
export const formatLineId = (lineId) => {
  if (!lineId) return '';
  // 移除@符號（如果存在）
  return lineId.replace(/^@/, '').trim();
};

/**
 * 格式化日期時間
 * @param {string|Date} date - 日期
 * @param {string} format - 格式類型 ('date', 'datetime', 'time')
 * @returns {string} 格式化後的日期時間
 */
export const formatDateTime = (date, format = 'datetime') => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    timeZone: 'Asia/Taipei'
  };
  
  switch (format) {
    case 'date':
      return dateObj.toLocaleDateString('zh-TW', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
      });
    case 'time':
      return dateObj.toLocaleTimeString('zh-TW', {
        hour: '2-digit',
        minute: '2-digit'
      });
    case 'datetime':
    default:
      return dateObj.toLocaleString('zh-TW', options);
  }
};

/**
 * 格式化相對時間（多久之前）
 * @param {string|Date} date - 日期
 * @returns {string} 相對時間描述
 */
export const formatRelativeTime = (date) => {
  if (!date) return '';
  
  const dateObj = new Date(date);
  if (isNaN(dateObj.getTime())) return '';
  
  const now = new Date();
  const diffMs = now.getTime() - dateObj.getTime();
  const diffSeconds = Math.floor(diffMs / 1000);
  const diffMinutes = Math.floor(diffSeconds / 60);
  const diffHours = Math.floor(diffMinutes / 60);
  const diffDays = Math.floor(diffHours / 24);
  
  if (diffSeconds < 60) {
    return '剛剛';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分鐘前`;
  } else if (diffHours < 24) {
    return `${diffHours}小時前`;
  } else if (diffDays < 7) {
    return `${diffDays}天前`;
  } else {
    return formatDateTime(date, 'date');
  }
};

/**
 * 截斷文字並添加省略號
 * @param {string} text - 原始文字
 * @param {number} maxLength - 最大長度
 * @returns {string} 截斷後的文字
 */
export const truncateText = (text, maxLength = 50) => {
  if (!text) return '';
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

/**
 * 高亮搜索關鍵字
 * @param {string} text - 原始文字
 * @param {string} keyword - 搜索關鍵字
 * @returns {string} 包含高亮標記的HTML字符串
 */
export const highlightKeyword = (text, keyword) => {
  if (!text || !keyword) return text;
  
  const regex = new RegExp(`(${keyword})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

/**
 * 格式化文件大小
 * @param {number} bytes - 字節數
 * @returns {string} 格式化後的大小
 */
export const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * 格式化百分比
 * @param {number} value - 數值
 * @param {number} total - 總數
 * @param {number} decimals - 小數位數
 * @returns {string} 百分比字符串
 */
export const formatPercentage = (value, total, decimals = 1) => {
  if (!total || total === 0) return '0%';
  const percentage = (value / total) * 100;
  return `${percentage.toFixed(decimals)}%`;
};

/**
 * 清理和格式化表單數據
 * @param {Object} formData - 原始表單數據
 * @returns {Object} 格式化後的表單數據
 */
export const formatFormData = (formData) => {
  const formatted = {};
  
  Object.keys(formData).forEach(key => {
    const value = formData[key];
    if (value !== null && value !== undefined && value !== '') {
      switch (key) {
        case 'name':
          formatted[key] = formatName(value);
          break;
        case 'company_name':
          formatted[key] = formatCompany(value);
          break;
        case 'mobile_phone':
        case 'office_phone':
          formatted[key] = formatPhone(value);
          break;
        case 'email':
          formatted[key] = formatEmail(value);
          break;
        case 'line_id':
          formatted[key] = formatLineId(value);
          break;
        case 'company_address_1':
        case 'company_address_2':
          formatted[key] = formatAddress(value);
          break;
        default:
          formatted[key] = typeof value === 'string' ? value.trim() : value;
      }
    }
  });
  
  return formatted;
};

export default {
  formatPhone,
  formatEmail,
  formatName,
  formatCompany,
  formatAddress,
  formatLineId,
  formatDateTime,
  formatRelativeTime,
  truncateText,
  highlightKeyword,
  formatFileSize,
  formatPercentage,
  formatFormData
};
