{"ast": null, "code": "import { __assign, __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar initRect = {\n  top: NaN,\n  left: NaN,\n  bottom: NaN,\n  right: NaN,\n  height: NaN,\n  width: NaN\n};\nvar initState = __assign({\n  text: ''\n}, initRect);\nfunction getRectFromSelection(selection) {\n  if (!selection) {\n    return initRect;\n  }\n  if (selection.rangeCount < 1) {\n    return initRect;\n  }\n  var range = selection.getRangeAt(0);\n  var _a = range.getBoundingClientRect(),\n    height = _a.height,\n    width = _a.width,\n    top = _a.top,\n    left = _a.left,\n    right = _a.right,\n    bottom = _a.bottom;\n  return {\n    height: height,\n    width: width,\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  };\n}\nfunction useTextSelection(target) {\n  var _a = __read(useState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useRef(state);\n  var isInRangeRef = useRef(false);\n  stateRef.current = state;\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var mouseupHandler = function () {\n      var selObj = null;\n      var text = '';\n      var rect = initRect;\n      if (!window.getSelection) return;\n      selObj = window.getSelection();\n      text = selObj ? selObj.toString() : '';\n      if (text && isInRangeRef.current) {\n        rect = getRectFromSelection(selObj);\n        setState(__assign(__assign(__assign({}, state), {\n          text: text\n        }), rect));\n      }\n    };\n    // 任意点击都需要清空之前的 range\n    var mousedownHandler = function (e) {\n      // 如果是鼠标右键需要跳过 这样选中的数据就不会被清空\n      if (e.button === 2) return;\n      if (!window.getSelection) return;\n      if (stateRef.current.text) {\n        setState(__assign({}, initState));\n      }\n      isInRangeRef.current = false;\n      var selObj = window.getSelection();\n      if (!selObj) return;\n      selObj.removeAllRanges();\n      isInRangeRef.current = el.contains(e.target);\n    };\n    el.addEventListener('mouseup', mouseupHandler);\n    document.addEventListener('mousedown', mousedownHandler);\n    return function () {\n      el.removeEventListener('mouseup', mouseupHandler);\n      document.removeEventListener('mousedown', mousedownHandler);\n    };\n  }, [], target);\n  return state;\n}\nexport default useTextSelection;", "map": {"version": 3, "names": ["__assign", "__read", "useRef", "useState", "getTargetElement", "useEffectWithTarget", "initRect", "top", "NaN", "left", "bottom", "right", "height", "width", "initState", "text", "getRectFromSelection", "selection", "rangeCount", "range", "getRangeAt", "_a", "getBoundingClientRect", "useTextSelection", "target", "state", "setState", "stateRef", "isInRangeRef", "current", "el", "document", "mouseup<PERSON><PERSON><PERSON>", "sel<PERSON>bj", "rect", "window", "getSelection", "toString", "mousedownHandler", "e", "button", "removeAllRanges", "contains", "addEventListener", "removeEventListener"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useTextSelection/index.js"], "sourcesContent": ["import { __assign, __read } from \"tslib\";\nimport { useRef, useState } from 'react';\nimport { getTargetElement } from '../utils/domTarget';\nimport useEffectWithTarget from '../utils/useEffectWithTarget';\nvar initRect = {\n  top: NaN,\n  left: NaN,\n  bottom: NaN,\n  right: NaN,\n  height: NaN,\n  width: NaN\n};\nvar initState = __assign({\n  text: ''\n}, initRect);\nfunction getRectFromSelection(selection) {\n  if (!selection) {\n    return initRect;\n  }\n  if (selection.rangeCount < 1) {\n    return initRect;\n  }\n  var range = selection.getRangeAt(0);\n  var _a = range.getBoundingClientRect(),\n    height = _a.height,\n    width = _a.width,\n    top = _a.top,\n    left = _a.left,\n    right = _a.right,\n    bottom = _a.bottom;\n  return {\n    height: height,\n    width: width,\n    top: top,\n    left: left,\n    right: right,\n    bottom: bottom\n  };\n}\nfunction useTextSelection(target) {\n  var _a = __read(useState(initState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var stateRef = useRef(state);\n  var isInRangeRef = useRef(false);\n  stateRef.current = state;\n  useEffectWithTarget(function () {\n    var el = getTargetElement(target, document);\n    if (!el) {\n      return;\n    }\n    var mouseupHandler = function () {\n      var selObj = null;\n      var text = '';\n      var rect = initRect;\n      if (!window.getSelection) return;\n      selObj = window.getSelection();\n      text = selObj ? selObj.toString() : '';\n      if (text && isInRangeRef.current) {\n        rect = getRectFromSelection(selObj);\n        setState(__assign(__assign(__assign({}, state), {\n          text: text\n        }), rect));\n      }\n    };\n    // 任意点击都需要清空之前的 range\n    var mousedownHandler = function (e) {\n      // 如果是鼠标右键需要跳过 这样选中的数据就不会被清空\n      if (e.button === 2) return;\n      if (!window.getSelection) return;\n      if (stateRef.current.text) {\n        setState(__assign({}, initState));\n      }\n      isInRangeRef.current = false;\n      var selObj = window.getSelection();\n      if (!selObj) return;\n      selObj.removeAllRanges();\n      isInRangeRef.current = el.contains(e.target);\n    };\n    el.addEventListener('mouseup', mouseupHandler);\n    document.addEventListener('mousedown', mousedownHandler);\n    return function () {\n      el.removeEventListener('mouseup', mouseupHandler);\n      document.removeEventListener('mousedown', mousedownHandler);\n    };\n  }, [], target);\n  return state;\n}\nexport default useTextSelection;"], "mappings": "AAAA,SAASA,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AACxC,SAASC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACxC,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,mBAAmB,MAAM,8BAA8B;AAC9D,IAAIC,QAAQ,GAAG;EACbC,GAAG,EAAEC,GAAG;EACRC,IAAI,EAAED,GAAG;EACTE,MAAM,EAAEF,GAAG;EACXG,KAAK,EAAEH,GAAG;EACVI,MAAM,EAAEJ,GAAG;EACXK,KAAK,EAAEL;AACT,CAAC;AACD,IAAIM,SAAS,GAAGd,QAAQ,CAAC;EACvBe,IAAI,EAAE;AACR,CAAC,EAAET,QAAQ,CAAC;AACZ,SAASU,oBAAoBA,CAACC,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,EAAE;IACd,OAAOX,QAAQ;EACjB;EACA,IAAIW,SAAS,CAACC,UAAU,GAAG,CAAC,EAAE;IAC5B,OAAOZ,QAAQ;EACjB;EACA,IAAIa,KAAK,GAAGF,SAAS,CAACG,UAAU,CAAC,CAAC,CAAC;EACnC,IAAIC,EAAE,GAAGF,KAAK,CAACG,qBAAqB,CAAC,CAAC;IACpCV,MAAM,GAAGS,EAAE,CAACT,MAAM;IAClBC,KAAK,GAAGQ,EAAE,CAACR,KAAK;IAChBN,GAAG,GAAGc,EAAE,CAACd,GAAG;IACZE,IAAI,GAAGY,EAAE,CAACZ,IAAI;IACdE,KAAK,GAAGU,EAAE,CAACV,KAAK;IAChBD,MAAM,GAAGW,EAAE,CAACX,MAAM;EACpB,OAAO;IACLE,MAAM,EAAEA,MAAM;IACdC,KAAK,EAAEA,KAAK;IACZN,GAAG,EAAEA,GAAG;IACRE,IAAI,EAAEA,IAAI;IACVE,KAAK,EAAEA,KAAK;IACZD,MAAM,EAAEA;EACV,CAAC;AACH;AACA,SAASa,gBAAgBA,CAACC,MAAM,EAAE;EAChC,IAAIH,EAAE,GAAGpB,MAAM,CAACE,QAAQ,CAACW,SAAS,CAAC,EAAE,CAAC,CAAC;IACrCW,KAAK,GAAGJ,EAAE,CAAC,CAAC,CAAC;IACbK,QAAQ,GAAGL,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIM,QAAQ,GAAGzB,MAAM,CAACuB,KAAK,CAAC;EAC5B,IAAIG,YAAY,GAAG1B,MAAM,CAAC,KAAK,CAAC;EAChCyB,QAAQ,CAACE,OAAO,GAAGJ,KAAK;EACxBpB,mBAAmB,CAAC,YAAY;IAC9B,IAAIyB,EAAE,GAAG1B,gBAAgB,CAACoB,MAAM,EAAEO,QAAQ,CAAC;IAC3C,IAAI,CAACD,EAAE,EAAE;MACP;IACF;IACA,IAAIE,cAAc,GAAG,SAAAA,CAAA,EAAY;MAC/B,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAIlB,IAAI,GAAG,EAAE;MACb,IAAImB,IAAI,GAAG5B,QAAQ;MACnB,IAAI,CAAC6B,MAAM,CAACC,YAAY,EAAE;MAC1BH,MAAM,GAAGE,MAAM,CAACC,YAAY,CAAC,CAAC;MAC9BrB,IAAI,GAAGkB,MAAM,GAAGA,MAAM,CAACI,QAAQ,CAAC,CAAC,GAAG,EAAE;MACtC,IAAItB,IAAI,IAAIa,YAAY,CAACC,OAAO,EAAE;QAChCK,IAAI,GAAGlB,oBAAoB,CAACiB,MAAM,CAAC;QACnCP,QAAQ,CAAC1B,QAAQ,CAACA,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEyB,KAAK,CAAC,EAAE;UAC9CV,IAAI,EAAEA;QACR,CAAC,CAAC,EAAEmB,IAAI,CAAC,CAAC;MACZ;IACF,CAAC;IACD;IACA,IAAII,gBAAgB,GAAG,SAAAA,CAAUC,CAAC,EAAE;MAClC;MACA,IAAIA,CAAC,CAACC,MAAM,KAAK,CAAC,EAAE;MACpB,IAAI,CAACL,MAAM,CAACC,YAAY,EAAE;MAC1B,IAAIT,QAAQ,CAACE,OAAO,CAACd,IAAI,EAAE;QACzBW,QAAQ,CAAC1B,QAAQ,CAAC,CAAC,CAAC,EAAEc,SAAS,CAAC,CAAC;MACnC;MACAc,YAAY,CAACC,OAAO,GAAG,KAAK;MAC5B,IAAII,MAAM,GAAGE,MAAM,CAACC,YAAY,CAAC,CAAC;MAClC,IAAI,CAACH,MAAM,EAAE;MACbA,MAAM,CAACQ,eAAe,CAAC,CAAC;MACxBb,YAAY,CAACC,OAAO,GAAGC,EAAE,CAACY,QAAQ,CAACH,CAAC,CAACf,MAAM,CAAC;IAC9C,CAAC;IACDM,EAAE,CAACa,gBAAgB,CAAC,SAAS,EAAEX,cAAc,CAAC;IAC9CD,QAAQ,CAACY,gBAAgB,CAAC,WAAW,EAAEL,gBAAgB,CAAC;IACxD,OAAO,YAAY;MACjBR,EAAE,CAACc,mBAAmB,CAAC,SAAS,EAAEZ,cAAc,CAAC;MACjDD,QAAQ,CAACa,mBAAmB,CAAC,WAAW,EAAEN,gBAAgB,CAAC;IAC7D,CAAC;EACH,CAAC,EAAE,EAAE,EAAEd,MAAM,CAAC;EACd,OAAOC,KAAK;AACd;AACA,eAAeF,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}