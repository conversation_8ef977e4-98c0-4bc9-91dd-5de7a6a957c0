{"ast": null, "code": "import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const ThumbIcon = props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 24 24',\n    xmlns: 'http://www.w3.org/2000/svg'\n  }, React.createElement(\"g\", {\n    fill: 'currentColor',\n    fillRule: 'evenodd'\n  }, React.createElement(\"rect\", {\n    x: 10,\n    width: 4,\n    height: 24,\n    rx: 2\n  }), React.createElement(\"rect\", {\n    y: 4,\n    width: 4,\n    height: 16,\n    rx: 2\n  }), React.createElement(\"rect\", {\n    x: 20,\n    y: 4,\n    width: 4,\n    height: 16,\n    rx: 2\n  }))));\n};", "map": {"version": 3, "names": ["React", "withNativeProps", "ThumbIcon", "props", "createElement", "viewBox", "xmlns", "fill", "fillRule", "x", "width", "height", "rx", "y"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/antd-mobile/es/components/slider/thumb-icon.js"], "sourcesContent": ["import React from 'react';\nimport { withNativeProps } from '../../utils/native-props';\nexport const ThumbIcon = props => {\n  return withNativeProps(props, React.createElement(\"svg\", {\n    viewBox: '0 0 24 24',\n    xmlns: 'http://www.w3.org/2000/svg'\n  }, React.createElement(\"g\", {\n    fill: 'currentColor',\n    fillRule: 'evenodd'\n  }, React.createElement(\"rect\", {\n    x: 10,\n    width: 4,\n    height: 24,\n    rx: 2\n  }), React.createElement(\"rect\", {\n    y: 4,\n    width: 4,\n    height: 16,\n    rx: 2\n  }), React.createElement(\"rect\", {\n    x: 20,\n    y: 4,\n    width: 4,\n    height: 16,\n    rx: 2\n  }))));\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,OAAO,MAAMC,SAAS,GAAGC,KAAK,IAAI;EAChC,OAAOF,eAAe,CAACE,KAAK,EAAEH,KAAK,CAACI,aAAa,CAAC,KAAK,EAAE;IACvDC,OAAO,EAAE,WAAW;IACpBC,KAAK,EAAE;EACT,CAAC,EAAEN,KAAK,CAACI,aAAa,CAAC,GAAG,EAAE;IAC1BG,IAAI,EAAE,cAAc;IACpBC,QAAQ,EAAE;EACZ,CAAC,EAAER,KAAK,CAACI,aAAa,CAAC,MAAM,EAAE;IAC7BK,CAAC,EAAE,EAAE;IACLC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,EAAE;IACVC,EAAE,EAAE;EACN,CAAC,CAAC,EAAEZ,KAAK,CAACI,aAAa,CAAC,MAAM,EAAE;IAC9BS,CAAC,EAAE,CAAC;IACJH,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,EAAE;IACVC,EAAE,EAAE;EACN,CAAC,CAAC,EAAEZ,KAAK,CAACI,aAAa,CAAC,MAAM,EAAE;IAC9BK,CAAC,EAAE,EAAE;IACLI,CAAC,EAAE,CAAC;IACJH,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,EAAE;IACVC,EAAE,EAAE;EACN,CAAC,CAAC,CAAC,CAAC,CAAC;AACP,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}