/**
 * 主頁 - 重構版本
 */

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button, Space, Card } from 'antd-mobile';
import { 
  ScanningOutline, 
  ContactsOutline, 
  CameraOutline 
} from 'antd-mobile-icons';

const HomePage = () => {
  const navigate = useNavigate();

  return (
    <div 
      style={{ 
        minHeight: '100vh', 
        background: '#f5f5f5', 
        display: 'flex', 
        flexDirection: 'column', 
        justifyContent: 'center', 
        alignItems: 'center',
        padding: '20px'
      }}
    >
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 400, 
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)' 
        }}
      >
        <div style={{ textAlign: 'center', marginBottom: '32px' }}>
          <ScanningOutline 
            style={{ 
              fontSize: '64px', 
              color: '#1677ff',
              marginBottom: '16px'
            }} 
          />
          <h2 style={{ margin: 0, color: '#262626' }}>名片 OCR 應用</h2>
          <p style={{ color: '#8c8c8c', margin: '8px 0 0 0' }}>
            智能識別名片資訊，輕鬆管理聯絡人
          </p>
        </div>
        
        <Space direction="vertical" block style={{ width: '100%' }}>
          <Button 
            color="primary" 
            size="large" 
            block 
            style={{ fontSize: '18px', height: '56px' }}
            onClick={() => navigate('/scan')}
          >
            <ScanningOutline /> 開始掃描
          </Button>
          
          <Button 
            color="default" 
            size="large" 
            block 
            style={{ fontSize: '18px', height: '56px' }}
            onClick={() => navigate('/cards')}
          >
            <ContactsOutline /> 名片管理
          </Button>
          
          <Button 
            color="warning" 
            size="large" 
            block 
            style={{ fontSize: '18px', height: '56px' }}
            onClick={() => navigate('/cards/new')}
          >
            <CameraOutline /> 手動新增
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default HomePage;
