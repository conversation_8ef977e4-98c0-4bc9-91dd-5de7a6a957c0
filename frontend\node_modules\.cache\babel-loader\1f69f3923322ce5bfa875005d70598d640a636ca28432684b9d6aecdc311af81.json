{"ast": null, "code": "import { __read, __spreadArray } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport isDev from '../utils/isDev';\nvar useDynamicList = function (initialList) {\n  if (initialList === void 0) {\n    initialList = [];\n  }\n  var counterRef = useRef(-1);\n  var keyList = useRef([]);\n  var setKey = useCallback(function (index) {\n    counterRef.current += 1;\n    keyList.current.splice(index, 0, counterRef.current);\n  }, []);\n  var _a = __read(useState(function () {\n      initialList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return initialList;\n    }), 2),\n    list = _a[0],\n    setList = _a[1];\n  var resetList = useCallback(function (newList) {\n    keyList.current = [];\n    setList(function () {\n      newList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return newList;\n    });\n  }, []);\n  var insert = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 0, item);\n      setKey(index);\n      return temp;\n    });\n  }, []);\n  var getKey = useCallback(function (index) {\n    return keyList.current[index];\n  }, []);\n  var getIndex = useCallback(function (key) {\n    return keyList.current.findIndex(function (ele) {\n      return ele === key;\n    });\n  }, []);\n  var merge = useCallback(function (index, items) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      items.forEach(function (_, i) {\n        setKey(index + i);\n      });\n      temp.splice.apply(temp, __spreadArray([index, 0], __read(items), false));\n      return temp;\n    });\n  }, []);\n  var replace = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp[index] = item;\n      return temp;\n    });\n  }, []);\n  var remove = useCallback(function (index) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 1);\n      // remove keys if necessary\n      try {\n        keyList.current.splice(index, 1);\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var batchRemove = useCallback(function (indexes) {\n    if (!Array.isArray(indexes)) {\n      if (isDev) {\n        console.error(\"`indexes` parameter of `batchRemove` function expected to be an array, but got \\\"\".concat(typeof indexes, \"\\\".\"));\n      }\n      return;\n    }\n    if (!indexes.length) {\n      return;\n    }\n    setList(function (prevList) {\n      var newKeyList = [];\n      var newList = prevList.filter(function (item, index) {\n        var shouldKeep = !indexes.includes(index);\n        if (shouldKeep) {\n          newKeyList.push(getKey(index));\n        }\n        return shouldKeep;\n      });\n      keyList.current = newKeyList;\n      return newList;\n    });\n  }, []);\n  var move = useCallback(function (oldIndex, newIndex) {\n    if (oldIndex === newIndex) {\n      return;\n    }\n    setList(function (l) {\n      var newList = __spreadArray([], __read(l), false);\n      var temp = newList.filter(function (_, index) {\n        return index !== oldIndex;\n      });\n      temp.splice(newIndex, 0, newList[oldIndex]);\n      // move keys if necessary\n      try {\n        var keyTemp = keyList.current.filter(function (_, index) {\n          return index !== oldIndex;\n        });\n        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);\n        keyList.current = keyTemp;\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var push = useCallback(function (item) {\n    setList(function (l) {\n      setKey(l.length);\n      return l.concat([item]);\n    });\n  }, []);\n  var pop = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(0, keyList.current.length - 1);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(0, l.length - 1);\n    });\n  }, []);\n  var unshift = useCallback(function (item) {\n    setList(function (l) {\n      setKey(0);\n      return [item].concat(l);\n    });\n  }, []);\n  var shift = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(1, keyList.current.length);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(1, l.length);\n    });\n  }, []);\n  var sortList = useCallback(function (result) {\n    return result.map(function (item, index) {\n      return {\n        key: index,\n        item: item\n      };\n    }) // add index into obj\n    .sort(function (a, b) {\n      return getIndex(a.key) - getIndex(b.key);\n    }) // sort based on the index of table\n    .filter(function (item) {\n      return !!item.item;\n    }) // remove undefined(s)\n    .map(function (item) {\n      return item.item;\n    });\n  },\n  // retrive the data\n  []);\n  return {\n    list: list,\n    insert: insert,\n    merge: merge,\n    replace: replace,\n    remove: remove,\n    batchRemove: batchRemove,\n    getKey: getKey,\n    getIndex: getIndex,\n    move: move,\n    push: push,\n    pop: pop,\n    unshift: unshift,\n    shift: shift,\n    sortList: sortList,\n    resetList: resetList\n  };\n};\nexport default useDynamicList;", "map": {"version": 3, "names": ["__read", "__spread<PERSON><PERSON>y", "useCallback", "useRef", "useState", "isDev", "useDynamicList", "initialList", "counterRef", "keyList", "<PERSON><PERSON><PERSON>", "index", "current", "splice", "_a", "for<PERSON>ach", "_", "list", "setList", "resetList", "newList", "insert", "item", "l", "temp", "<PERSON><PERSON><PERSON>", "getIndex", "key", "findIndex", "ele", "merge", "items", "i", "apply", "replace", "remove", "e", "console", "error", "batchRemove", "indexes", "Array", "isArray", "concat", "length", "prevList", "newKeyList", "filter", "<PERSON><PERSON><PERSON>", "includes", "push", "move", "oldIndex", "newIndex", "keyTemp", "pop", "slice", "unshift", "shift", "sortList", "result", "map", "sort", "a", "b"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDynamicList/index.js"], "sourcesContent": ["import { __read, __spreadArray } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport isDev from '../utils/isDev';\nvar useDynamicList = function (initialList) {\n  if (initialList === void 0) {\n    initialList = [];\n  }\n  var counterRef = useRef(-1);\n  var keyList = useRef([]);\n  var setKey = useCallback(function (index) {\n    counterRef.current += 1;\n    keyList.current.splice(index, 0, counterRef.current);\n  }, []);\n  var _a = __read(useState(function () {\n      initialList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return initialList;\n    }), 2),\n    list = _a[0],\n    setList = _a[1];\n  var resetList = useCallback(function (newList) {\n    keyList.current = [];\n    setList(function () {\n      newList.forEach(function (_, index) {\n        setKey(index);\n      });\n      return newList;\n    });\n  }, []);\n  var insert = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 0, item);\n      setKey(index);\n      return temp;\n    });\n  }, []);\n  var getKey = useCallback(function (index) {\n    return keyList.current[index];\n  }, []);\n  var getIndex = useCallback(function (key) {\n    return keyList.current.findIndex(function (ele) {\n      return ele === key;\n    });\n  }, []);\n  var merge = useCallback(function (index, items) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      items.forEach(function (_, i) {\n        setKey(index + i);\n      });\n      temp.splice.apply(temp, __spreadArray([index, 0], __read(items), false));\n      return temp;\n    });\n  }, []);\n  var replace = useCallback(function (index, item) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp[index] = item;\n      return temp;\n    });\n  }, []);\n  var remove = useCallback(function (index) {\n    setList(function (l) {\n      var temp = __spreadArray([], __read(l), false);\n      temp.splice(index, 1);\n      // remove keys if necessary\n      try {\n        keyList.current.splice(index, 1);\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var batchRemove = useCallback(function (indexes) {\n    if (!Array.isArray(indexes)) {\n      if (isDev) {\n        console.error(\"`indexes` parameter of `batchRemove` function expected to be an array, but got \\\"\".concat(typeof indexes, \"\\\".\"));\n      }\n      return;\n    }\n    if (!indexes.length) {\n      return;\n    }\n    setList(function (prevList) {\n      var newKeyList = [];\n      var newList = prevList.filter(function (item, index) {\n        var shouldKeep = !indexes.includes(index);\n        if (shouldKeep) {\n          newKeyList.push(getKey(index));\n        }\n        return shouldKeep;\n      });\n      keyList.current = newKeyList;\n      return newList;\n    });\n  }, []);\n  var move = useCallback(function (oldIndex, newIndex) {\n    if (oldIndex === newIndex) {\n      return;\n    }\n    setList(function (l) {\n      var newList = __spreadArray([], __read(l), false);\n      var temp = newList.filter(function (_, index) {\n        return index !== oldIndex;\n      });\n      temp.splice(newIndex, 0, newList[oldIndex]);\n      // move keys if necessary\n      try {\n        var keyTemp = keyList.current.filter(function (_, index) {\n          return index !== oldIndex;\n        });\n        keyTemp.splice(newIndex, 0, keyList.current[oldIndex]);\n        keyList.current = keyTemp;\n      } catch (e) {\n        console.error(e);\n      }\n      return temp;\n    });\n  }, []);\n  var push = useCallback(function (item) {\n    setList(function (l) {\n      setKey(l.length);\n      return l.concat([item]);\n    });\n  }, []);\n  var pop = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(0, keyList.current.length - 1);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(0, l.length - 1);\n    });\n  }, []);\n  var unshift = useCallback(function (item) {\n    setList(function (l) {\n      setKey(0);\n      return [item].concat(l);\n    });\n  }, []);\n  var shift = useCallback(function () {\n    // remove keys if necessary\n    try {\n      keyList.current = keyList.current.slice(1, keyList.current.length);\n    } catch (e) {\n      console.error(e);\n    }\n    setList(function (l) {\n      return l.slice(1, l.length);\n    });\n  }, []);\n  var sortList = useCallback(function (result) {\n    return result.map(function (item, index) {\n      return {\n        key: index,\n        item: item\n      };\n    }) // add index into obj\n    .sort(function (a, b) {\n      return getIndex(a.key) - getIndex(b.key);\n    }) // sort based on the index of table\n    .filter(function (item) {\n      return !!item.item;\n    }) // remove undefined(s)\n    .map(function (item) {\n      return item.item;\n    });\n  },\n  // retrive the data\n  []);\n  return {\n    list: list,\n    insert: insert,\n    merge: merge,\n    replace: replace,\n    remove: remove,\n    batchRemove: batchRemove,\n    getKey: getKey,\n    getIndex: getIndex,\n    move: move,\n    push: push,\n    pop: pop,\n    unshift: unshift,\n    shift: shift,\n    sortList: sortList,\n    resetList: resetList\n  };\n};\nexport default useDynamicList;"], "mappings": "AAAA,SAASA,MAAM,EAAEC,aAAa,QAAQ,OAAO;AAC7C,SAASC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrD,OAAOC,KAAK,MAAM,gBAAgB;AAClC,IAAIC,cAAc,GAAG,SAAAA,CAAUC,WAAW,EAAE;EAC1C,IAAIA,WAAW,KAAK,KAAK,CAAC,EAAE;IAC1BA,WAAW,GAAG,EAAE;EAClB;EACA,IAAIC,UAAU,GAAGL,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3B,IAAIM,OAAO,GAAGN,MAAM,CAAC,EAAE,CAAC;EACxB,IAAIO,MAAM,GAAGR,WAAW,CAAC,UAAUS,KAAK,EAAE;IACxCH,UAAU,CAACI,OAAO,IAAI,CAAC;IACvBH,OAAO,CAACG,OAAO,CAACC,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEH,UAAU,CAACI,OAAO,CAAC;EACtD,CAAC,EAAE,EAAE,CAAC;EACN,IAAIE,EAAE,GAAGd,MAAM,CAACI,QAAQ,CAAC,YAAY;MACjCG,WAAW,CAACQ,OAAO,CAAC,UAAUC,CAAC,EAAEL,KAAK,EAAE;QACtCD,MAAM,CAACC,KAAK,CAAC;MACf,CAAC,CAAC;MACF,OAAOJ,WAAW;IACpB,CAAC,CAAC,EAAE,CAAC,CAAC;IACNU,IAAI,GAAGH,EAAE,CAAC,CAAC,CAAC;IACZI,OAAO,GAAGJ,EAAE,CAAC,CAAC,CAAC;EACjB,IAAIK,SAAS,GAAGjB,WAAW,CAAC,UAAUkB,OAAO,EAAE;IAC7CX,OAAO,CAACG,OAAO,GAAG,EAAE;IACpBM,OAAO,CAAC,YAAY;MAClBE,OAAO,CAACL,OAAO,CAAC,UAAUC,CAAC,EAAEL,KAAK,EAAE;QAClCD,MAAM,CAACC,KAAK,CAAC;MACf,CAAC,CAAC;MACF,OAAOS,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,MAAM,GAAGnB,WAAW,CAAC,UAAUS,KAAK,EAAEW,IAAI,EAAE;IAC9CJ,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,IAAIC,IAAI,GAAGvB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACuB,CAAC,CAAC,EAAE,KAAK,CAAC;MAC9CC,IAAI,CAACX,MAAM,CAACF,KAAK,EAAE,CAAC,EAAEW,IAAI,CAAC;MAC3BZ,MAAM,CAACC,KAAK,CAAC;MACb,OAAOa,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIC,MAAM,GAAGvB,WAAW,CAAC,UAAUS,KAAK,EAAE;IACxC,OAAOF,OAAO,CAACG,OAAO,CAACD,KAAK,CAAC;EAC/B,CAAC,EAAE,EAAE,CAAC;EACN,IAAIe,QAAQ,GAAGxB,WAAW,CAAC,UAAUyB,GAAG,EAAE;IACxC,OAAOlB,OAAO,CAACG,OAAO,CAACgB,SAAS,CAAC,UAAUC,GAAG,EAAE;MAC9C,OAAOA,GAAG,KAAKF,GAAG;IACpB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIG,KAAK,GAAG5B,WAAW,CAAC,UAAUS,KAAK,EAAEoB,KAAK,EAAE;IAC9Cb,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,IAAIC,IAAI,GAAGvB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACuB,CAAC,CAAC,EAAE,KAAK,CAAC;MAC9CQ,KAAK,CAAChB,OAAO,CAAC,UAAUC,CAAC,EAAEgB,CAAC,EAAE;QAC5BtB,MAAM,CAACC,KAAK,GAAGqB,CAAC,CAAC;MACnB,CAAC,CAAC;MACFR,IAAI,CAACX,MAAM,CAACoB,KAAK,CAACT,IAAI,EAAEvB,aAAa,CAAC,CAACU,KAAK,EAAE,CAAC,CAAC,EAAEX,MAAM,CAAC+B,KAAK,CAAC,EAAE,KAAK,CAAC,CAAC;MACxE,OAAOP,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIU,OAAO,GAAGhC,WAAW,CAAC,UAAUS,KAAK,EAAEW,IAAI,EAAE;IAC/CJ,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,IAAIC,IAAI,GAAGvB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACuB,CAAC,CAAC,EAAE,KAAK,CAAC;MAC9CC,IAAI,CAACb,KAAK,CAAC,GAAGW,IAAI;MAClB,OAAOE,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIW,MAAM,GAAGjC,WAAW,CAAC,UAAUS,KAAK,EAAE;IACxCO,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,IAAIC,IAAI,GAAGvB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACuB,CAAC,CAAC,EAAE,KAAK,CAAC;MAC9CC,IAAI,CAACX,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACrB;MACA,IAAI;QACFF,OAAO,CAACG,OAAO,CAACC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAClC,CAAC,CAAC,OAAOyB,CAAC,EAAE;QACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MAClB;MACA,OAAOZ,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIe,WAAW,GAAGrC,WAAW,CAAC,UAAUsC,OAAO,EAAE;IAC/C,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,OAAO,CAAC,EAAE;MAC3B,IAAInC,KAAK,EAAE;QACTgC,OAAO,CAACC,KAAK,CAAC,mFAAmF,CAACK,MAAM,CAAC,OAAOH,OAAO,EAAE,KAAK,CAAC,CAAC;MAClI;MACA;IACF;IACA,IAAI,CAACA,OAAO,CAACI,MAAM,EAAE;MACnB;IACF;IACA1B,OAAO,CAAC,UAAU2B,QAAQ,EAAE;MAC1B,IAAIC,UAAU,GAAG,EAAE;MACnB,IAAI1B,OAAO,GAAGyB,QAAQ,CAACE,MAAM,CAAC,UAAUzB,IAAI,EAAEX,KAAK,EAAE;QACnD,IAAIqC,UAAU,GAAG,CAACR,OAAO,CAACS,QAAQ,CAACtC,KAAK,CAAC;QACzC,IAAIqC,UAAU,EAAE;UACdF,UAAU,CAACI,IAAI,CAACzB,MAAM,CAACd,KAAK,CAAC,CAAC;QAChC;QACA,OAAOqC,UAAU;MACnB,CAAC,CAAC;MACFvC,OAAO,CAACG,OAAO,GAAGkC,UAAU;MAC5B,OAAO1B,OAAO;IAChB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAI+B,IAAI,GAAGjD,WAAW,CAAC,UAAUkD,QAAQ,EAAEC,QAAQ,EAAE;IACnD,IAAID,QAAQ,KAAKC,QAAQ,EAAE;MACzB;IACF;IACAnC,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,IAAIH,OAAO,GAAGnB,aAAa,CAAC,EAAE,EAAED,MAAM,CAACuB,CAAC,CAAC,EAAE,KAAK,CAAC;MACjD,IAAIC,IAAI,GAAGJ,OAAO,CAAC2B,MAAM,CAAC,UAAU/B,CAAC,EAAEL,KAAK,EAAE;QAC5C,OAAOA,KAAK,KAAKyC,QAAQ;MAC3B,CAAC,CAAC;MACF5B,IAAI,CAACX,MAAM,CAACwC,QAAQ,EAAE,CAAC,EAAEjC,OAAO,CAACgC,QAAQ,CAAC,CAAC;MAC3C;MACA,IAAI;QACF,IAAIE,OAAO,GAAG7C,OAAO,CAACG,OAAO,CAACmC,MAAM,CAAC,UAAU/B,CAAC,EAAEL,KAAK,EAAE;UACvD,OAAOA,KAAK,KAAKyC,QAAQ;QAC3B,CAAC,CAAC;QACFE,OAAO,CAACzC,MAAM,CAACwC,QAAQ,EAAE,CAAC,EAAE5C,OAAO,CAACG,OAAO,CAACwC,QAAQ,CAAC,CAAC;QACtD3C,OAAO,CAACG,OAAO,GAAG0C,OAAO;MAC3B,CAAC,CAAC,OAAOlB,CAAC,EAAE;QACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MAClB;MACA,OAAOZ,IAAI;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAI0B,IAAI,GAAGhD,WAAW,CAAC,UAAUoB,IAAI,EAAE;IACrCJ,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnBb,MAAM,CAACa,CAAC,CAACqB,MAAM,CAAC;MAChB,OAAOrB,CAAC,CAACoB,MAAM,CAAC,CAACrB,IAAI,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIiC,GAAG,GAAGrD,WAAW,CAAC,YAAY;IAChC;IACA,IAAI;MACFO,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACG,OAAO,CAAC4C,KAAK,CAAC,CAAC,EAAE/C,OAAO,CAACG,OAAO,CAACgC,MAAM,GAAG,CAAC,CAAC;IACxE,CAAC,CAAC,OAAOR,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;IAClB;IACAlB,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,OAAOA,CAAC,CAACiC,KAAK,CAAC,CAAC,EAAEjC,CAAC,CAACqB,MAAM,GAAG,CAAC,CAAC;IACjC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIa,OAAO,GAAGvD,WAAW,CAAC,UAAUoB,IAAI,EAAE;IACxCJ,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnBb,MAAM,CAAC,CAAC,CAAC;MACT,OAAO,CAACY,IAAI,CAAC,CAACqB,MAAM,CAACpB,CAAC,CAAC;IACzB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAImC,KAAK,GAAGxD,WAAW,CAAC,YAAY;IAClC;IACA,IAAI;MACFO,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACG,OAAO,CAAC4C,KAAK,CAAC,CAAC,EAAE/C,OAAO,CAACG,OAAO,CAACgC,MAAM,CAAC;IACpE,CAAC,CAAC,OAAOR,CAAC,EAAE;MACVC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;IAClB;IACAlB,OAAO,CAAC,UAAUK,CAAC,EAAE;MACnB,OAAOA,CAAC,CAACiC,KAAK,CAAC,CAAC,EAAEjC,CAAC,CAACqB,MAAM,CAAC;IAC7B,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,IAAIe,QAAQ,GAAGzD,WAAW,CAAC,UAAU0D,MAAM,EAAE;IAC3C,OAAOA,MAAM,CAACC,GAAG,CAAC,UAAUvC,IAAI,EAAEX,KAAK,EAAE;MACvC,OAAO;QACLgB,GAAG,EAAEhB,KAAK;QACVW,IAAI,EAAEA;MACR,CAAC;IACH,CAAC,CAAC,CAAC;IAAA,CACFwC,IAAI,CAAC,UAAUC,CAAC,EAAEC,CAAC,EAAE;MACpB,OAAOtC,QAAQ,CAACqC,CAAC,CAACpC,GAAG,CAAC,GAAGD,QAAQ,CAACsC,CAAC,CAACrC,GAAG,CAAC;IAC1C,CAAC,CAAC,CAAC;IAAA,CACFoB,MAAM,CAAC,UAAUzB,IAAI,EAAE;MACtB,OAAO,CAAC,CAACA,IAAI,CAACA,IAAI;IACpB,CAAC,CAAC,CAAC;IAAA,CACFuC,GAAG,CAAC,UAAUvC,IAAI,EAAE;MACnB,OAAOA,IAAI,CAACA,IAAI;IAClB,CAAC,CAAC;EACJ,CAAC;EACD;EACA,EAAE,CAAC;EACH,OAAO;IACLL,IAAI,EAAEA,IAAI;IACVI,MAAM,EAAEA,MAAM;IACdS,KAAK,EAAEA,KAAK;IACZI,OAAO,EAAEA,OAAO;IAChBC,MAAM,EAAEA,MAAM;IACdI,WAAW,EAAEA,WAAW;IACxBd,MAAM,EAAEA,MAAM;IACdC,QAAQ,EAAEA,QAAQ;IAClByB,IAAI,EAAEA,IAAI;IACVD,IAAI,EAAEA,IAAI;IACVK,GAAG,EAAEA,GAAG;IACRE,OAAO,EAAEA,OAAO;IAChBC,KAAK,EAAEA,KAAK;IACZC,QAAQ,EAAEA,QAAQ;IAClBxC,SAAS,EAAEA;EACb,CAAC;AACH,CAAC;AACD,eAAeb,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}