{"ast": null, "code": "import { useRef } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useCreation from '../useCreation';\nimport useUpdate from '../useUpdate';\n// k:v 原对象:代理过的对象\nvar proxyMap = new WeakMap();\n// k:v 代理过的对象:原对象\nvar rawMap = new WeakMap();\nfunction observer(initialVal, cb) {\n  var existingProxy = proxyMap.get(initialVal);\n  // 添加缓存 防止重新构建proxy\n  if (existingProxy) {\n    return existingProxy;\n  }\n  // 防止代理已经代理过的对象\n  // https://github.com/alibaba/hooks/issues/839\n  if (rawMap.has(initialVal)) {\n    return initialVal;\n  }\n  var proxy = new Proxy(initialVal, {\n    get: function (target, key, receiver) {\n      var res = Reflect.get(target, key, receiver);\n      // https://github.com/alibaba/hooks/issues/1317\n      var descriptor = Reflect.getOwnPropertyDescriptor(target, key);\n      if (!(descriptor === null || descriptor === void 0 ? void 0 : descriptor.configurable) && !(descriptor === null || descriptor === void 0 ? void 0 : descriptor.writable)) {\n        return res;\n      }\n      // Only proxy plain object or array,\n      // otherwise it will cause: https://github.com/alibaba/hooks/issues/2080\n      return isPlainObject(res) || Array.isArray(res) ? observer(res, cb) : res;\n    },\n    set: function (target, key, val) {\n      var ret = Reflect.set(target, key, val);\n      cb();\n      return ret;\n    },\n    deleteProperty: function (target, key) {\n      var ret = Reflect.deleteProperty(target, key);\n      cb();\n      return ret;\n    }\n  });\n  proxyMap.set(initialVal, proxy);\n  rawMap.set(proxy, initialVal);\n  return proxy;\n}\nfunction useReactive(initialState) {\n  var update = useUpdate();\n  var stateRef = useRef(initialState);\n  var state = useCreation(function () {\n    return observer(stateRef.current, function () {\n      update();\n    });\n  }, []);\n  return state;\n}\nexport default useReactive;", "map": {"version": 3, "names": ["useRef", "isPlainObject", "useCreation", "useUpdate", "proxyMap", "WeakMap", "rawMap", "observer", "initialVal", "cb", "existingProxy", "get", "has", "proxy", "Proxy", "target", "key", "receiver", "res", "Reflect", "descriptor", "getOwnPropertyDescriptor", "configurable", "writable", "Array", "isArray", "set", "val", "ret", "deleteProperty", "useReactive", "initialState", "update", "stateRef", "state", "current"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useReactive/index.js"], "sourcesContent": ["import { useRef } from 'react';\nimport isPlainObject from 'lodash/isPlainObject';\nimport useCreation from '../useCreation';\nimport useUpdate from '../useUpdate';\n// k:v 原对象:代理过的对象\nvar proxyMap = new WeakMap();\n// k:v 代理过的对象:原对象\nvar rawMap = new WeakMap();\nfunction observer(initialVal, cb) {\n  var existingProxy = proxyMap.get(initialVal);\n  // 添加缓存 防止重新构建proxy\n  if (existingProxy) {\n    return existingProxy;\n  }\n  // 防止代理已经代理过的对象\n  // https://github.com/alibaba/hooks/issues/839\n  if (rawMap.has(initialVal)) {\n    return initialVal;\n  }\n  var proxy = new Proxy(initialVal, {\n    get: function (target, key, receiver) {\n      var res = Reflect.get(target, key, receiver);\n      // https://github.com/alibaba/hooks/issues/1317\n      var descriptor = Reflect.getOwnPropertyDescriptor(target, key);\n      if (!(descriptor === null || descriptor === void 0 ? void 0 : descriptor.configurable) && !(descriptor === null || descriptor === void 0 ? void 0 : descriptor.writable)) {\n        return res;\n      }\n      // Only proxy plain object or array,\n      // otherwise it will cause: https://github.com/alibaba/hooks/issues/2080\n      return isPlainObject(res) || Array.isArray(res) ? observer(res, cb) : res;\n    },\n    set: function (target, key, val) {\n      var ret = Reflect.set(target, key, val);\n      cb();\n      return ret;\n    },\n    deleteProperty: function (target, key) {\n      var ret = Reflect.deleteProperty(target, key);\n      cb();\n      return ret;\n    }\n  });\n  proxyMap.set(initialVal, proxy);\n  rawMap.set(proxy, initialVal);\n  return proxy;\n}\nfunction useReactive(initialState) {\n  var update = useUpdate();\n  var stateRef = useRef(initialState);\n  var state = useCreation(function () {\n    return observer(stateRef.current, function () {\n      update();\n    });\n  }, []);\n  return state;\n}\nexport default useReactive;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,OAAOC,aAAa,MAAM,sBAAsB;AAChD,OAAOC,WAAW,MAAM,gBAAgB;AACxC,OAAOC,SAAS,MAAM,cAAc;AACpC;AACA,IAAIC,QAAQ,GAAG,IAAIC,OAAO,CAAC,CAAC;AAC5B;AACA,IAAIC,MAAM,GAAG,IAAID,OAAO,CAAC,CAAC;AAC1B,SAASE,QAAQA,CAACC,UAAU,EAAEC,EAAE,EAAE;EAChC,IAAIC,aAAa,GAAGN,QAAQ,CAACO,GAAG,CAACH,UAAU,CAAC;EAC5C;EACA,IAAIE,aAAa,EAAE;IACjB,OAAOA,aAAa;EACtB;EACA;EACA;EACA,IAAIJ,MAAM,CAACM,GAAG,CAACJ,UAAU,CAAC,EAAE;IAC1B,OAAOA,UAAU;EACnB;EACA,IAAIK,KAAK,GAAG,IAAIC,KAAK,CAACN,UAAU,EAAE;IAChCG,GAAG,EAAE,SAAAA,CAAUI,MAAM,EAAEC,GAAG,EAAEC,QAAQ,EAAE;MACpC,IAAIC,GAAG,GAAGC,OAAO,CAACR,GAAG,CAACI,MAAM,EAAEC,GAAG,EAAEC,QAAQ,CAAC;MAC5C;MACA,IAAIG,UAAU,GAAGD,OAAO,CAACE,wBAAwB,CAACN,MAAM,EAAEC,GAAG,CAAC;MAC9D,IAAI,EAAEI,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACE,YAAY,CAAC,IAAI,EAAEF,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACG,QAAQ,CAAC,EAAE;QACxK,OAAOL,GAAG;MACZ;MACA;MACA;MACA,OAAOjB,aAAa,CAACiB,GAAG,CAAC,IAAIM,KAAK,CAACC,OAAO,CAACP,GAAG,CAAC,GAAGX,QAAQ,CAACW,GAAG,EAAET,EAAE,CAAC,GAAGS,GAAG;IAC3E,CAAC;IACDQ,GAAG,EAAE,SAAAA,CAAUX,MAAM,EAAEC,GAAG,EAAEW,GAAG,EAAE;MAC/B,IAAIC,GAAG,GAAGT,OAAO,CAACO,GAAG,CAACX,MAAM,EAAEC,GAAG,EAAEW,GAAG,CAAC;MACvClB,EAAE,CAAC,CAAC;MACJ,OAAOmB,GAAG;IACZ,CAAC;IACDC,cAAc,EAAE,SAAAA,CAAUd,MAAM,EAAEC,GAAG,EAAE;MACrC,IAAIY,GAAG,GAAGT,OAAO,CAACU,cAAc,CAACd,MAAM,EAAEC,GAAG,CAAC;MAC7CP,EAAE,CAAC,CAAC;MACJ,OAAOmB,GAAG;IACZ;EACF,CAAC,CAAC;EACFxB,QAAQ,CAACsB,GAAG,CAAClB,UAAU,EAAEK,KAAK,CAAC;EAC/BP,MAAM,CAACoB,GAAG,CAACb,KAAK,EAAEL,UAAU,CAAC;EAC7B,OAAOK,KAAK;AACd;AACA,SAASiB,WAAWA,CAACC,YAAY,EAAE;EACjC,IAAIC,MAAM,GAAG7B,SAAS,CAAC,CAAC;EACxB,IAAI8B,QAAQ,GAAGjC,MAAM,CAAC+B,YAAY,CAAC;EACnC,IAAIG,KAAK,GAAGhC,WAAW,CAAC,YAAY;IAClC,OAAOK,QAAQ,CAAC0B,QAAQ,CAACE,OAAO,EAAE,YAAY;MAC5CH,MAAM,CAAC,CAAC;IACV,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACN,OAAOE,KAAK;AACd;AACA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}