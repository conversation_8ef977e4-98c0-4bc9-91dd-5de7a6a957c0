{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { isValidElement, version } from 'react';\nimport { ForwardRef, isMemo } from 'react-is';\nimport useMemo from \"./hooks/useMemo\";\nimport isFragment from \"./React/isFragment\";\nvar ReactMajorVersion = Number(version.split('.')[0]);\nexport var fillRef = function fillRef(ref, node) {\n  if (typeof ref === 'function') {\n    ref(node);\n  } else if (_typeof(ref) === 'object' && ref && 'current' in ref) {\n    ref.current = node;\n  }\n};\n\n/**\n * Merge refs into one ref function to support ref passing.\n */\nexport var composeRef = function composeRef() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  var refList = refs.filter(Boolean);\n  if (refList.length <= 1) {\n    return refList[0];\n  }\n  return function (node) {\n    refs.forEach(function (ref) {\n      fillRef(ref, node);\n    });\n  };\n};\nexport var useComposeRef = function useComposeRef() {\n  for (var _len2 = arguments.length, refs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    refs[_key2] = arguments[_key2];\n  }\n  return useMemo(function () {\n    return composeRef.apply(void 0, refs);\n  }, refs, function (prev, next) {\n    return prev.length !== next.length || prev.every(function (ref, i) {\n      return ref !== next[i];\n    });\n  });\n};\nexport var supportRef = function supportRef(nodeOrComponent) {\n  var _type$prototype, _nodeOrComponent$prot;\n  if (!nodeOrComponent) {\n    return false;\n  }\n\n  // React 19 no need `forwardRef` anymore. So just pass if is a React element.\n  if (isReactElement(nodeOrComponent) && ReactMajorVersion >= 19) {\n    return true;\n  }\n  var type = isMemo(nodeOrComponent) ? nodeOrComponent.type.type : nodeOrComponent.type;\n\n  // Function component node\n  if (typeof type === 'function' && !((_type$prototype = type.prototype) !== null && _type$prototype !== void 0 && _type$prototype.render) && type.$$typeof !== ForwardRef) {\n    return false;\n  }\n\n  // Class component\n  if (typeof nodeOrComponent === 'function' && !((_nodeOrComponent$prot = nodeOrComponent.prototype) !== null && _nodeOrComponent$prot !== void 0 && _nodeOrComponent$prot.render) && nodeOrComponent.$$typeof !== ForwardRef) {\n    return false;\n  }\n  return true;\n};\nfunction isReactElement(node) {\n  return /*#__PURE__*/isValidElement(node) && !isFragment(node);\n}\nexport var supportNodeRef = function supportNodeRef(node) {\n  return isReactElement(node) && supportRef(node);\n};\n\n/**\n * In React 19. `ref` is not a property from node.\n * But a property from `props.ref`.\n * To check if `props.ref` exist or fallback to `ref`.\n */\nexport var getNodeRef = function getNodeRef(node) {\n  if (node && isReactElement(node)) {\n    var ele = node;\n\n    // Source from:\n    // https://github.com/mui/material-ui/blob/master/packages/mui-utils/src/getReactNodeRef/getReactNodeRef.ts\n    return ele.props.propertyIsEnumerable('ref') ? ele.props.ref : ele.ref;\n  }\n  return null;\n};", "map": {"version": 3, "names": ["_typeof", "isValidElement", "version", "ForwardRef", "isMemo", "useMemo", "isFragment", "ReactMajorVersion", "Number", "split", "fillRef", "ref", "node", "current", "composeRef", "_len", "arguments", "length", "refs", "Array", "_key", "refList", "filter", "Boolean", "for<PERSON>ach", "useComposeRef", "_len2", "_key2", "apply", "prev", "next", "every", "i", "supportRef", "nodeOrComponent", "_type$prototype", "_nodeOrComponent$prot", "isReactElement", "type", "prototype", "render", "$$typeof", "supportNodeRef", "getNodeRef", "ele", "props", "propertyIsEnumerable"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-util/es/ref.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport { isValidElement, version } from 'react';\nimport { ForwardRef, isMemo } from 'react-is';\nimport useMemo from \"./hooks/useMemo\";\nimport isFragment from \"./React/isFragment\";\nvar ReactMajorVersion = Number(version.split('.')[0]);\nexport var fillRef = function fillRef(ref, node) {\n  if (typeof ref === 'function') {\n    ref(node);\n  } else if (_typeof(ref) === 'object' && ref && 'current' in ref) {\n    ref.current = node;\n  }\n};\n\n/**\n * Merge refs into one ref function to support ref passing.\n */\nexport var composeRef = function composeRef() {\n  for (var _len = arguments.length, refs = new Array(_len), _key = 0; _key < _len; _key++) {\n    refs[_key] = arguments[_key];\n  }\n  var refList = refs.filter(Boolean);\n  if (refList.length <= 1) {\n    return refList[0];\n  }\n  return function (node) {\n    refs.forEach(function (ref) {\n      fillRef(ref, node);\n    });\n  };\n};\nexport var useComposeRef = function useComposeRef() {\n  for (var _len2 = arguments.length, refs = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    refs[_key2] = arguments[_key2];\n  }\n  return useMemo(function () {\n    return composeRef.apply(void 0, refs);\n  }, refs, function (prev, next) {\n    return prev.length !== next.length || prev.every(function (ref, i) {\n      return ref !== next[i];\n    });\n  });\n};\nexport var supportRef = function supportRef(nodeOrComponent) {\n  var _type$prototype, _nodeOrComponent$prot;\n  if (!nodeOrComponent) {\n    return false;\n  }\n\n  // React 19 no need `forwardRef` anymore. So just pass if is a React element.\n  if (isReactElement(nodeOrComponent) && ReactMajorVersion >= 19) {\n    return true;\n  }\n  var type = isMemo(nodeOrComponent) ? nodeOrComponent.type.type : nodeOrComponent.type;\n\n  // Function component node\n  if (typeof type === 'function' && !((_type$prototype = type.prototype) !== null && _type$prototype !== void 0 && _type$prototype.render) && type.$$typeof !== ForwardRef) {\n    return false;\n  }\n\n  // Class component\n  if (typeof nodeOrComponent === 'function' && !((_nodeOrComponent$prot = nodeOrComponent.prototype) !== null && _nodeOrComponent$prot !== void 0 && _nodeOrComponent$prot.render) && nodeOrComponent.$$typeof !== ForwardRef) {\n    return false;\n  }\n  return true;\n};\nfunction isReactElement(node) {\n  return /*#__PURE__*/isValidElement(node) && !isFragment(node);\n}\nexport var supportNodeRef = function supportNodeRef(node) {\n  return isReactElement(node) && supportRef(node);\n};\n\n/**\n * In React 19. `ref` is not a property from node.\n * But a property from `props.ref`.\n * To check if `props.ref` exist or fallback to `ref`.\n */\nexport var getNodeRef = function getNodeRef(node) {\n  if (node && isReactElement(node)) {\n    var ele = node;\n\n    // Source from:\n    // https://github.com/mui/material-ui/blob/master/packages/mui-utils/src/getReactNodeRef/getReactNodeRef.ts\n    return ele.props.propertyIsEnumerable('ref') ? ele.props.ref : ele.ref;\n  }\n  return null;\n};"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,SAASC,cAAc,EAAEC,OAAO,QAAQ,OAAO;AAC/C,SAASC,UAAU,EAAEC,MAAM,QAAQ,UAAU;AAC7C,OAAOC,OAAO,MAAM,iBAAiB;AACrC,OAAOC,UAAU,MAAM,oBAAoB;AAC3C,IAAIC,iBAAiB,GAAGC,MAAM,CAACN,OAAO,CAACO,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD,OAAO,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAEC,IAAI,EAAE;EAC/C,IAAI,OAAOD,GAAG,KAAK,UAAU,EAAE;IAC7BA,GAAG,CAACC,IAAI,CAAC;EACX,CAAC,MAAM,IAAIZ,OAAO,CAACW,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,IAAI,SAAS,IAAIA,GAAG,EAAE;IAC/DA,GAAG,CAACE,OAAO,GAAGD,IAAI;EACpB;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,IAAIE,UAAU,GAAG,SAASA,UAAUA,CAAA,EAAG;EAC5C,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;IACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;EAC9B;EACA,IAAIC,OAAO,GAAGH,IAAI,CAACI,MAAM,CAACC,OAAO,CAAC;EAClC,IAAIF,OAAO,CAACJ,MAAM,IAAI,CAAC,EAAE;IACvB,OAAOI,OAAO,CAAC,CAAC,CAAC;EACnB;EACA,OAAO,UAAUT,IAAI,EAAE;IACrBM,IAAI,CAACM,OAAO,CAAC,UAAUb,GAAG,EAAE;MAC1BD,OAAO,CAACC,GAAG,EAAEC,IAAI,CAAC;IACpB,CAAC,CAAC;EACJ,CAAC;AACH,CAAC;AACD,OAAO,IAAIa,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;EAClD,KAAK,IAAIC,KAAK,GAAGV,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACO,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC7FT,IAAI,CAACS,KAAK,CAAC,GAAGX,SAAS,CAACW,KAAK,CAAC;EAChC;EACA,OAAOtB,OAAO,CAAC,YAAY;IACzB,OAAOS,UAAU,CAACc,KAAK,CAAC,KAAK,CAAC,EAAEV,IAAI,CAAC;EACvC,CAAC,EAAEA,IAAI,EAAE,UAAUW,IAAI,EAAEC,IAAI,EAAE;IAC7B,OAAOD,IAAI,CAACZ,MAAM,KAAKa,IAAI,CAACb,MAAM,IAAIY,IAAI,CAACE,KAAK,CAAC,UAAUpB,GAAG,EAAEqB,CAAC,EAAE;MACjE,OAAOrB,GAAG,KAAKmB,IAAI,CAACE,CAAC,CAAC;IACxB,CAAC,CAAC;EACJ,CAAC,CAAC;AACJ,CAAC;AACD,OAAO,IAAIC,UAAU,GAAG,SAASA,UAAUA,CAACC,eAAe,EAAE;EAC3D,IAAIC,eAAe,EAAEC,qBAAqB;EAC1C,IAAI,CAACF,eAAe,EAAE;IACpB,OAAO,KAAK;EACd;;EAEA;EACA,IAAIG,cAAc,CAACH,eAAe,CAAC,IAAI3B,iBAAiB,IAAI,EAAE,EAAE;IAC9D,OAAO,IAAI;EACb;EACA,IAAI+B,IAAI,GAAGlC,MAAM,CAAC8B,eAAe,CAAC,GAAGA,eAAe,CAACI,IAAI,CAACA,IAAI,GAAGJ,eAAe,CAACI,IAAI;;EAErF;EACA,IAAI,OAAOA,IAAI,KAAK,UAAU,IAAI,EAAE,CAACH,eAAe,GAAGG,IAAI,CAACC,SAAS,MAAM,IAAI,IAAIJ,eAAe,KAAK,KAAK,CAAC,IAAIA,eAAe,CAACK,MAAM,CAAC,IAAIF,IAAI,CAACG,QAAQ,KAAKtC,UAAU,EAAE;IACxK,OAAO,KAAK;EACd;;EAEA;EACA,IAAI,OAAO+B,eAAe,KAAK,UAAU,IAAI,EAAE,CAACE,qBAAqB,GAAGF,eAAe,CAACK,SAAS,MAAM,IAAI,IAAIH,qBAAqB,KAAK,KAAK,CAAC,IAAIA,qBAAqB,CAACI,MAAM,CAAC,IAAIN,eAAe,CAACO,QAAQ,KAAKtC,UAAU,EAAE;IAC3N,OAAO,KAAK;EACd;EACA,OAAO,IAAI;AACb,CAAC;AACD,SAASkC,cAAcA,CAACzB,IAAI,EAAE;EAC5B,OAAO,aAAaX,cAAc,CAACW,IAAI,CAAC,IAAI,CAACN,UAAU,CAACM,IAAI,CAAC;AAC/D;AACA,OAAO,IAAI8B,cAAc,GAAG,SAASA,cAAcA,CAAC9B,IAAI,EAAE;EACxD,OAAOyB,cAAc,CAACzB,IAAI,CAAC,IAAIqB,UAAU,CAACrB,IAAI,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,IAAI+B,UAAU,GAAG,SAASA,UAAUA,CAAC/B,IAAI,EAAE;EAChD,IAAIA,IAAI,IAAIyB,cAAc,CAACzB,IAAI,CAAC,EAAE;IAChC,IAAIgC,GAAG,GAAGhC,IAAI;;IAEd;IACA;IACA,OAAOgC,GAAG,CAACC,KAAK,CAACC,oBAAoB,CAAC,KAAK,CAAC,GAAGF,GAAG,CAACC,KAAK,CAAClC,GAAG,GAAGiC,GAAG,CAACjC,GAAG;EACxE;EACA,OAAO,IAAI;AACb,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}