{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport isBrowser from '../utils/isBrowser';\nvar getVisibility = function () {\n  if (!isBrowser) {\n    return 'visible';\n  }\n  return document.visibilityState;\n};\nfunction useDocumentVisibility() {\n  var _a = __read(useState(getVisibility), 2),\n    documentVisibility = _a[0],\n    setDocumentVisibility = _a[1];\n  useEventListener('visibilitychange', function () {\n    setDocumentVisibility(getVisibility());\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return documentVisibility;\n}\nexport default useDocumentVisibility;", "map": {"version": 3, "names": ["__read", "useState", "useEventListener", "<PERSON><PERSON><PERSON><PERSON>", "getVisibility", "document", "visibilityState", "useDocumentVisibility", "_a", "documentVisibility", "setDocumentVisibility", "target"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDocumentVisibility/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useState } from 'react';\nimport useEventListener from '../useEventListener';\nimport isBrowser from '../utils/isBrowser';\nvar getVisibility = function () {\n  if (!isBrowser) {\n    return 'visible';\n  }\n  return document.visibilityState;\n};\nfunction useDocumentVisibility() {\n  var _a = __read(useState(getVisibility), 2),\n    documentVisibility = _a[0],\n    setDocumentVisibility = _a[1];\n  useEventListener('visibilitychange', function () {\n    setDocumentVisibility(getVisibility());\n  }, {\n    target: function () {\n      return document;\n    }\n  });\n  return documentVisibility;\n}\nexport default useDocumentVisibility;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,QAAQ,QAAQ,OAAO;AAChC,OAAOC,gBAAgB,MAAM,qBAAqB;AAClD,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,IAAIC,aAAa,GAAG,SAAAA,CAAA,EAAY;EAC9B,IAAI,CAACD,SAAS,EAAE;IACd,OAAO,SAAS;EAClB;EACA,OAAOE,QAAQ,CAACC,eAAe;AACjC,CAAC;AACD,SAASC,qBAAqBA,CAAA,EAAG;EAC/B,IAAIC,EAAE,GAAGR,MAAM,CAACC,QAAQ,CAACG,aAAa,CAAC,EAAE,CAAC,CAAC;IACzCK,kBAAkB,GAAGD,EAAE,CAAC,CAAC,CAAC;IAC1BE,qBAAqB,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC/BN,gBAAgB,CAAC,kBAAkB,EAAE,YAAY;IAC/CQ,qBAAqB,CAACN,aAAa,CAAC,CAAC,CAAC;EACxC,CAAC,EAAE;IACDO,MAAM,EAAE,SAAAA,CAAA,EAAY;MAClB,OAAON,QAAQ;IACjB;EACF,CAAC,CAAC;EACF,OAAOI,kBAAkB;AAC3B;AACA,eAAeF,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}