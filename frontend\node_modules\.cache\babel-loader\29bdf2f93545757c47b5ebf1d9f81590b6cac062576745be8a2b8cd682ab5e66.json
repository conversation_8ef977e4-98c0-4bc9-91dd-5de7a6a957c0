{"ast": null, "code": "'use strict';\n\nimport AxiosURLSearchParams from '../../../helpers/AxiosURLSearchParams.js';\nexport default typeof URLSearchParams !== 'undefined' ? URLSearchParams : AxiosURLSearchParams;", "map": {"version": 3, "names": ["AxiosURLSearchParams", "URLSearchParams"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/axios/lib/platform/browser/classes/URLSearchParams.js"], "sourcesContent": ["'use strict';\n\nimport AxiosURLSearchParams from '../../../helpers/AxiosURLSearchParams.js';\nexport default typeof URLSearchParams !== 'undefined' ? URLSearchParams : AxiosURLSearchParams;\n"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,oBAAoB,MAAM,0CAA0C;AAC3E,eAAe,OAAOC,eAAe,KAAK,WAAW,GAAGA,eAAe,GAAGD,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}