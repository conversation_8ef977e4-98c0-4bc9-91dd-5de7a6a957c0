{"ast": null, "code": "var cachePromise = new Map();\nvar getCachePromise = function (cacheKey) {\n  return cachePromise.get(cacheKey);\n};\nvar setCachePromise = function (cacheKey, promise) {\n  // Should cache the same promise, cannot be promise.finally\n  // Because the promise.finally will change the reference of the promise\n  cachePromise.set(cacheKey, promise);\n  // no use promise.finally for compatibility\n  promise.then(function (res) {\n    cachePromise.delete(cacheKey);\n    return res;\n  }).catch(function () {\n    cachePromise.delete(cacheKey);\n  });\n};\nexport { getCachePromise, setCachePromise };", "map": {"version": 3, "names": ["cachePromise", "Map", "getCachePromise", "cache<PERSON>ey", "get", "setCachePromise", "promise", "set", "then", "res", "delete", "catch"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/src/utils/cachePromise.js"], "sourcesContent": ["var cachePromise = new Map();\nvar getCachePromise = function (cacheKey) {\n  return cachePromise.get(cacheKey);\n};\nvar setCachePromise = function (cacheKey, promise) {\n  // Should cache the same promise, cannot be promise.finally\n  // Because the promise.finally will change the reference of the promise\n  cachePromise.set(cacheKey, promise);\n  // no use promise.finally for compatibility\n  promise.then(function (res) {\n    cachePromise.delete(cacheKey);\n    return res;\n  }).catch(function () {\n    cachePromise.delete(cacheKey);\n  });\n};\nexport { getCachePromise, setCachePromise };"], "mappings": "AAAA,IAAIA,YAAY,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC5B,IAAIC,eAAe,GAAG,SAAAA,CAAUC,QAAQ,EAAE;EACxC,OAAOH,YAAY,CAACI,GAAG,CAACD,QAAQ,CAAC;AACnC,CAAC;AACD,IAAIE,eAAe,GAAG,SAAAA,CAAUF,QAAQ,EAAEG,OAAO,EAAE;EACjD;EACA;EACAN,YAAY,CAACO,GAAG,CAACJ,QAAQ,EAAEG,OAAO,CAAC;EACnC;EACAA,OAAO,CAACE,IAAI,CAAC,UAAUC,GAAG,EAAE;IAC1BT,YAAY,CAACU,MAAM,CAACP,QAAQ,CAAC;IAC7B,OAAOM,GAAG;EACZ,CAAC,CAAC,CAACE,KAAK,CAAC,YAAY;IACnBX,YAAY,CAACU,MAAM,CAACP,QAAQ,CAAC;EAC/B,CAAC,CAAC;AACJ,CAAC;AACD,SAASD,eAAe,EAAEG,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}