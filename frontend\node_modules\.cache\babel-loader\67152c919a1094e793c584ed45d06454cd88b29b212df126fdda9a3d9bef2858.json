{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nexport var ReadyState;\n(function (ReadyState) {\n  ReadyState[ReadyState[\"Connecting\"] = 0] = \"Connecting\";\n  ReadyState[ReadyState[\"Open\"] = 1] = \"Open\";\n  ReadyState[ReadyState[\"Closing\"] = 2] = \"Closing\";\n  ReadyState[ReadyState[\"Closed\"] = 3] = \"Closed\";\n})(ReadyState || (ReadyState = {}));\nexport default function useWebSocket(socketUrl, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.reconnectLimit,\n    reconnectLimit = _a === void 0 ? 3 : _a,\n    _b = options.reconnectInterval,\n    reconnectInterval = _b === void 0 ? 3 * 1000 : _b,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    onOpen = options.onOpen,\n    onClose = options.onClose,\n    onMessage = options.onMessage,\n    onError = options.onError,\n    protocols = options.protocols;\n  var onOpenRef = useLatest(onOpen);\n  var onCloseRef = useLatest(onClose);\n  var onMessageRef = useLatest(onMessage);\n  var onErrorRef = useLatest(onError);\n  var reconnectTimesRef = useRef(0);\n  var reconnectTimerRef = useRef();\n  var websocketRef = useRef();\n  var _d = __read(useState(), 2),\n    latestMessage = _d[0],\n    setLatestMessage = _d[1];\n  var _e = __read(useState(ReadyState.Closed), 2),\n    readyState = _e[0],\n    setReadyState = _e[1];\n  var reconnect = function () {\n    var _a;\n    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {\n      if (reconnectTimerRef.current) {\n        clearTimeout(reconnectTimerRef.current);\n      }\n      reconnectTimerRef.current = setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        connectWs();\n        reconnectTimesRef.current++;\n      }, reconnectInterval);\n    }\n  };\n  var connectWs = function () {\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    if (websocketRef.current) {\n      websocketRef.current.close();\n    }\n    var ws = new WebSocket(socketUrl, protocols);\n    setReadyState(ReadyState.Connecting);\n    ws.onerror = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      reconnect();\n      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n    ws.onopen = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);\n      reconnectTimesRef.current = 0;\n      setReadyState(ws.readyState || ReadyState.Open);\n    };\n    ws.onmessage = function (message) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);\n      setLatestMessage(message);\n    };\n    ws.onclose = function (event) {\n      var _a;\n      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);\n      // closed by server\n      if (websocketRef.current === ws) {\n        reconnect();\n      }\n      // closed by disconnect or closed by server\n      if (!websocketRef.current || websocketRef.current === ws) {\n        setReadyState(ws.readyState || ReadyState.Closed);\n      }\n    };\n    websocketRef.current = ws;\n  };\n  var sendMessage = function (message) {\n    var _a;\n    if (readyState === ReadyState.Open) {\n      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);\n    } else {\n      throw new Error('WebSocket disconnected');\n    }\n  };\n  var connect = function () {\n    reconnectTimesRef.current = 0;\n    connectWs();\n  };\n  var disconnect = function () {\n    var _a;\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    reconnectTimesRef.current = reconnectLimit;\n    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();\n    websocketRef.current = undefined;\n  };\n  useEffect(function () {\n    if (!manual && socketUrl) {\n      connect();\n    }\n  }, [socketUrl, manual]);\n  useUnmount(function () {\n    disconnect();\n  });\n  return {\n    latestMessage: latestMessage,\n    sendMessage: useMemoizedFn(sendMessage),\n    connect: useMemoizedFn(connect),\n    disconnect: useMemoizedFn(disconnect),\n    readyState: readyState,\n    webSocketIns: websocketRef.current\n  };\n}", "map": {"version": 3, "names": ["__read", "useEffect", "useRef", "useState", "useLatest", "useMemoizedFn", "useUnmount", "ReadyState", "useWebSocket", "socketUrl", "options", "_a", "reconnectLimit", "_b", "reconnectInterval", "_c", "manual", "onOpen", "onClose", "onMessage", "onError", "protocols", "onOpenRef", "onCloseRef", "onMessageRef", "onErrorRef", "reconnectTimesRef", "reconnectTimerRef", "websocketRef", "_d", "latestMessage", "setLatestMessage", "_e", "Closed", "readyState", "setReadyState", "reconnect", "current", "Open", "clearTimeout", "setTimeout", "connectWs", "close", "ws", "WebSocket", "Connecting", "onerror", "event", "call", "onopen", "onmessage", "message", "onclose", "sendMessage", "send", "Error", "connect", "disconnect", "undefined", "webSocketIns"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useWebSocket/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useRef, useState } from 'react';\nimport useLatest from '../useLatest';\nimport useMemoizedFn from '../useMemoizedFn';\nimport useUnmount from '../useUnmount';\nexport var ReadyState;\n(function (ReadyState) {\n  ReadyState[ReadyState[\"Connecting\"] = 0] = \"Connecting\";\n  ReadyState[ReadyState[\"Open\"] = 1] = \"Open\";\n  ReadyState[ReadyState[\"Closing\"] = 2] = \"Closing\";\n  ReadyState[ReadyState[\"Closed\"] = 3] = \"Closed\";\n})(ReadyState || (ReadyState = {}));\nexport default function useWebSocket(socketUrl, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _a = options.reconnectLimit,\n    reconnectLimit = _a === void 0 ? 3 : _a,\n    _b = options.reconnectInterval,\n    reconnectInterval = _b === void 0 ? 3 * 1000 : _b,\n    _c = options.manual,\n    manual = _c === void 0 ? false : _c,\n    onOpen = options.onOpen,\n    onClose = options.onClose,\n    onMessage = options.onMessage,\n    onError = options.onError,\n    protocols = options.protocols;\n  var onOpenRef = useLatest(onOpen);\n  var onCloseRef = useLatest(onClose);\n  var onMessageRef = useLatest(onMessage);\n  var onErrorRef = useLatest(onError);\n  var reconnectTimesRef = useRef(0);\n  var reconnectTimerRef = useRef();\n  var websocketRef = useRef();\n  var _d = __read(useState(), 2),\n    latestMessage = _d[0],\n    setLatestMessage = _d[1];\n  var _e = __read(useState(ReadyState.Closed), 2),\n    readyState = _e[0],\n    setReadyState = _e[1];\n  var reconnect = function () {\n    var _a;\n    if (reconnectTimesRef.current < reconnectLimit && ((_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.readyState) !== ReadyState.Open) {\n      if (reconnectTimerRef.current) {\n        clearTimeout(reconnectTimerRef.current);\n      }\n      reconnectTimerRef.current = setTimeout(function () {\n        // eslint-disable-next-line @typescript-eslint/no-use-before-define\n        connectWs();\n        reconnectTimesRef.current++;\n      }, reconnectInterval);\n    }\n  };\n  var connectWs = function () {\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    if (websocketRef.current) {\n      websocketRef.current.close();\n    }\n    var ws = new WebSocket(socketUrl, protocols);\n    setReadyState(ReadyState.Connecting);\n    ws.onerror = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      reconnect();\n      (_a = onErrorRef.current) === null || _a === void 0 ? void 0 : _a.call(onErrorRef, event, ws);\n      setReadyState(ws.readyState || ReadyState.Closed);\n    };\n    ws.onopen = function (event) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onOpenRef.current) === null || _a === void 0 ? void 0 : _a.call(onOpenRef, event, ws);\n      reconnectTimesRef.current = 0;\n      setReadyState(ws.readyState || ReadyState.Open);\n    };\n    ws.onmessage = function (message) {\n      var _a;\n      if (websocketRef.current !== ws) {\n        return;\n      }\n      (_a = onMessageRef.current) === null || _a === void 0 ? void 0 : _a.call(onMessageRef, message, ws);\n      setLatestMessage(message);\n    };\n    ws.onclose = function (event) {\n      var _a;\n      (_a = onCloseRef.current) === null || _a === void 0 ? void 0 : _a.call(onCloseRef, event, ws);\n      // closed by server\n      if (websocketRef.current === ws) {\n        reconnect();\n      }\n      // closed by disconnect or closed by server\n      if (!websocketRef.current || websocketRef.current === ws) {\n        setReadyState(ws.readyState || ReadyState.Closed);\n      }\n    };\n    websocketRef.current = ws;\n  };\n  var sendMessage = function (message) {\n    var _a;\n    if (readyState === ReadyState.Open) {\n      (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.send(message);\n    } else {\n      throw new Error('WebSocket disconnected');\n    }\n  };\n  var connect = function () {\n    reconnectTimesRef.current = 0;\n    connectWs();\n  };\n  var disconnect = function () {\n    var _a;\n    if (reconnectTimerRef.current) {\n      clearTimeout(reconnectTimerRef.current);\n    }\n    reconnectTimesRef.current = reconnectLimit;\n    (_a = websocketRef.current) === null || _a === void 0 ? void 0 : _a.close();\n    websocketRef.current = undefined;\n  };\n  useEffect(function () {\n    if (!manual && socketUrl) {\n      connect();\n    }\n  }, [socketUrl, manual]);\n  useUnmount(function () {\n    disconnect();\n  });\n  return {\n    latestMessage: latestMessage,\n    sendMessage: useMemoizedFn(sendMessage),\n    connect: useMemoizedFn(connect),\n    disconnect: useMemoizedFn(disconnect),\n    readyState: readyState,\n    webSocketIns: websocketRef.current\n  };\n}"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACnD,OAAOC,SAAS,MAAM,cAAc;AACpC,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,OAAOC,UAAU,MAAM,eAAe;AACtC,OAAO,IAAIC,UAAU;AACrB,CAAC,UAAUA,UAAU,EAAE;EACrBA,UAAU,CAACA,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,GAAG,YAAY;EACvDA,UAAU,CAACA,UAAU,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC3CA,UAAU,CAACA,UAAU,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACjDA,UAAU,CAACA,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,GAAG,QAAQ;AACjD,CAAC,EAAEA,UAAU,KAAKA,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC;AACnC,eAAe,SAASC,YAAYA,CAACC,SAAS,EAAEC,OAAO,EAAE;EACvD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EACA,IAAIC,EAAE,GAAGD,OAAO,CAACE,cAAc;IAC7BA,cAAc,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,EAAE;IACvCE,EAAE,GAAGH,OAAO,CAACI,iBAAiB;IAC9BA,iBAAiB,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI,GAAGA,EAAE;IACjDE,EAAE,GAAGL,OAAO,CAACM,MAAM;IACnBA,MAAM,GAAGD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,EAAE;IACnCE,MAAM,GAAGP,OAAO,CAACO,MAAM;IACvBC,OAAO,GAAGR,OAAO,CAACQ,OAAO;IACzBC,SAAS,GAAGT,OAAO,CAACS,SAAS;IAC7BC,OAAO,GAAGV,OAAO,CAACU,OAAO;IACzBC,SAAS,GAAGX,OAAO,CAACW,SAAS;EAC/B,IAAIC,SAAS,GAAGlB,SAAS,CAACa,MAAM,CAAC;EACjC,IAAIM,UAAU,GAAGnB,SAAS,CAACc,OAAO,CAAC;EACnC,IAAIM,YAAY,GAAGpB,SAAS,CAACe,SAAS,CAAC;EACvC,IAAIM,UAAU,GAAGrB,SAAS,CAACgB,OAAO,CAAC;EACnC,IAAIM,iBAAiB,GAAGxB,MAAM,CAAC,CAAC,CAAC;EACjC,IAAIyB,iBAAiB,GAAGzB,MAAM,CAAC,CAAC;EAChC,IAAI0B,YAAY,GAAG1B,MAAM,CAAC,CAAC;EAC3B,IAAI2B,EAAE,GAAG7B,MAAM,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5B2B,aAAa,GAAGD,EAAE,CAAC,CAAC,CAAC;IACrBE,gBAAgB,GAAGF,EAAE,CAAC,CAAC,CAAC;EAC1B,IAAIG,EAAE,GAAGhC,MAAM,CAACG,QAAQ,CAACI,UAAU,CAAC0B,MAAM,CAAC,EAAE,CAAC,CAAC;IAC7CC,UAAU,GAAGF,EAAE,CAAC,CAAC,CAAC;IAClBG,aAAa,GAAGH,EAAE,CAAC,CAAC,CAAC;EACvB,IAAII,SAAS,GAAG,SAAAA,CAAA,EAAY;IAC1B,IAAIzB,EAAE;IACN,IAAIe,iBAAiB,CAACW,OAAO,GAAGzB,cAAc,IAAI,CAAC,CAACD,EAAE,GAAGiB,YAAY,CAACS,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuB,UAAU,MAAM3B,UAAU,CAAC+B,IAAI,EAAE;MACtJ,IAAIX,iBAAiB,CAACU,OAAO,EAAE;QAC7BE,YAAY,CAACZ,iBAAiB,CAACU,OAAO,CAAC;MACzC;MACAV,iBAAiB,CAACU,OAAO,GAAGG,UAAU,CAAC,YAAY;QACjD;QACAC,SAAS,CAAC,CAAC;QACXf,iBAAiB,CAACW,OAAO,EAAE;MAC7B,CAAC,EAAEvB,iBAAiB,CAAC;IACvB;EACF,CAAC;EACD,IAAI2B,SAAS,GAAG,SAAAA,CAAA,EAAY;IAC1B,IAAId,iBAAiB,CAACU,OAAO,EAAE;MAC7BE,YAAY,CAACZ,iBAAiB,CAACU,OAAO,CAAC;IACzC;IACA,IAAIT,YAAY,CAACS,OAAO,EAAE;MACxBT,YAAY,CAACS,OAAO,CAACK,KAAK,CAAC,CAAC;IAC9B;IACA,IAAIC,EAAE,GAAG,IAAIC,SAAS,CAACnC,SAAS,EAAEY,SAAS,CAAC;IAC5Cc,aAAa,CAAC5B,UAAU,CAACsC,UAAU,CAAC;IACpCF,EAAE,CAACG,OAAO,GAAG,UAAUC,KAAK,EAAE;MAC5B,IAAIpC,EAAE;MACN,IAAIiB,YAAY,CAACS,OAAO,KAAKM,EAAE,EAAE;QAC/B;MACF;MACAP,SAAS,CAAC,CAAC;MACX,CAACzB,EAAE,GAAGc,UAAU,CAACY,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAACvB,UAAU,EAAEsB,KAAK,EAAEJ,EAAE,CAAC;MAC7FR,aAAa,CAACQ,EAAE,CAACT,UAAU,IAAI3B,UAAU,CAAC0B,MAAM,CAAC;IACnD,CAAC;IACDU,EAAE,CAACM,MAAM,GAAG,UAAUF,KAAK,EAAE;MAC3B,IAAIpC,EAAE;MACN,IAAIiB,YAAY,CAACS,OAAO,KAAKM,EAAE,EAAE;QAC/B;MACF;MACA,CAAChC,EAAE,GAAGW,SAAS,CAACe,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAAC1B,SAAS,EAAEyB,KAAK,EAAEJ,EAAE,CAAC;MAC3FjB,iBAAiB,CAACW,OAAO,GAAG,CAAC;MAC7BF,aAAa,CAACQ,EAAE,CAACT,UAAU,IAAI3B,UAAU,CAAC+B,IAAI,CAAC;IACjD,CAAC;IACDK,EAAE,CAACO,SAAS,GAAG,UAAUC,OAAO,EAAE;MAChC,IAAIxC,EAAE;MACN,IAAIiB,YAAY,CAACS,OAAO,KAAKM,EAAE,EAAE;QAC/B;MACF;MACA,CAAChC,EAAE,GAAGa,YAAY,CAACa,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAACxB,YAAY,EAAE2B,OAAO,EAAER,EAAE,CAAC;MACnGZ,gBAAgB,CAACoB,OAAO,CAAC;IAC3B,CAAC;IACDR,EAAE,CAACS,OAAO,GAAG,UAAUL,KAAK,EAAE;MAC5B,IAAIpC,EAAE;MACN,CAACA,EAAE,GAAGY,UAAU,CAACc,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqC,IAAI,CAACzB,UAAU,EAAEwB,KAAK,EAAEJ,EAAE,CAAC;MAC7F;MACA,IAAIf,YAAY,CAACS,OAAO,KAAKM,EAAE,EAAE;QAC/BP,SAAS,CAAC,CAAC;MACb;MACA;MACA,IAAI,CAACR,YAAY,CAACS,OAAO,IAAIT,YAAY,CAACS,OAAO,KAAKM,EAAE,EAAE;QACxDR,aAAa,CAACQ,EAAE,CAACT,UAAU,IAAI3B,UAAU,CAAC0B,MAAM,CAAC;MACnD;IACF,CAAC;IACDL,YAAY,CAACS,OAAO,GAAGM,EAAE;EAC3B,CAAC;EACD,IAAIU,WAAW,GAAG,SAAAA,CAAUF,OAAO,EAAE;IACnC,IAAIxC,EAAE;IACN,IAAIuB,UAAU,KAAK3B,UAAU,CAAC+B,IAAI,EAAE;MAClC,CAAC3B,EAAE,GAAGiB,YAAY,CAACS,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC2C,IAAI,CAACH,OAAO,CAAC;IACnF,CAAC,MAAM;MACL,MAAM,IAAII,KAAK,CAAC,wBAAwB,CAAC;IAC3C;EACF,CAAC;EACD,IAAIC,OAAO,GAAG,SAAAA,CAAA,EAAY;IACxB9B,iBAAiB,CAACW,OAAO,GAAG,CAAC;IAC7BI,SAAS,CAAC,CAAC;EACb,CAAC;EACD,IAAIgB,UAAU,GAAG,SAAAA,CAAA,EAAY;IAC3B,IAAI9C,EAAE;IACN,IAAIgB,iBAAiB,CAACU,OAAO,EAAE;MAC7BE,YAAY,CAACZ,iBAAiB,CAACU,OAAO,CAAC;IACzC;IACAX,iBAAiB,CAACW,OAAO,GAAGzB,cAAc;IAC1C,CAACD,EAAE,GAAGiB,YAAY,CAACS,OAAO,MAAM,IAAI,IAAI1B,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC+B,KAAK,CAAC,CAAC;IAC3Ed,YAAY,CAACS,OAAO,GAAGqB,SAAS;EAClC,CAAC;EACDzD,SAAS,CAAC,YAAY;IACpB,IAAI,CAACe,MAAM,IAAIP,SAAS,EAAE;MACxB+C,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAAC/C,SAAS,EAAEO,MAAM,CAAC,CAAC;EACvBV,UAAU,CAAC,YAAY;IACrBmD,UAAU,CAAC,CAAC;EACd,CAAC,CAAC;EACF,OAAO;IACL3B,aAAa,EAAEA,aAAa;IAC5BuB,WAAW,EAAEhD,aAAa,CAACgD,WAAW,CAAC;IACvCG,OAAO,EAAEnD,aAAa,CAACmD,OAAO,CAAC;IAC/BC,UAAU,EAAEpD,aAAa,CAACoD,UAAU,CAAC;IACrCvB,UAAU,EAAEA,UAAU;IACtByB,YAAY,EAAE/B,YAAY,CAACS;EAC7B,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}