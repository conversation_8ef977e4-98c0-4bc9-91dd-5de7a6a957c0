{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nfunction useDebounce(value, options) {\n  var _a = __read(useState(value), 2),\n    debounced = _a[0],\n    setDebounced = _a[1];\n  var run = useDebounceFn(function () {\n    setDebounced(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return debounced;\n}\nexport default useDebounce;", "map": {"version": 3, "names": ["__read", "useEffect", "useState", "useDebounceFn", "useDebounce", "value", "options", "_a", "debounced", "setDeb<PERSON>", "run"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useDebounce/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useEffect, useState } from 'react';\nimport useDebounceFn from '../useDebounceFn';\nfunction useDebounce(value, options) {\n  var _a = __read(useState(value), 2),\n    debounced = _a[0],\n    setDebounced = _a[1];\n  var run = useDebounceFn(function () {\n    setDebounced(value);\n  }, options).run;\n  useEffect(function () {\n    run();\n  }, [value]);\n  return debounced;\n}\nexport default useDebounce;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAC3C,OAAOC,aAAa,MAAM,kBAAkB;AAC5C,SAASC,WAAWA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACnC,IAAIC,EAAE,GAAGP,MAAM,CAACE,QAAQ,CAACG,KAAK,CAAC,EAAE,CAAC,CAAC;IACjCG,SAAS,GAAGD,EAAE,CAAC,CAAC,CAAC;IACjBE,YAAY,GAAGF,EAAE,CAAC,CAAC,CAAC;EACtB,IAAIG,GAAG,GAAGP,aAAa,CAAC,YAAY;IAClCM,YAAY,CAACJ,KAAK,CAAC;EACrB,CAAC,EAAEC,OAAO,CAAC,CAACI,GAAG;EACfT,SAAS,CAAC,YAAY;IACpBS,GAAG,CAAC,CAAC;EACP,CAAC,EAAE,CAACL,KAAK,CAAC,CAAC;EACX,OAAOG,SAAS;AAClB;AACA,eAAeJ,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}