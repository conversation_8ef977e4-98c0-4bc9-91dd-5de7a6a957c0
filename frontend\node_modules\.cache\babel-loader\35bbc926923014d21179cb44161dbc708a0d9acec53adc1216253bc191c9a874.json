{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\components\\\\OCR\\\\OCRStatus.js\";\n/**\n * OCR狀態顯示組件\n */\n\nimport React from 'react';\nimport { Card, ProgressBar } from 'antd-mobile';\nimport { CheckCircleOutline, ExclamationCircleOutline, ScanningOutline } from 'antd-mobile-icons';\nimport { OCR_STATUS } from '../../utils/constants';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst OCRStatus = ({\n  status = OCR_STATUS.IDLE,\n  error = null,\n  frontStatus = OCR_STATUS.IDLE,\n  backStatus = OCR_STATUS.IDLE,\n  showDetails = false,\n  style = {},\n  className = ''\n}) => {\n  // 獲取狀態圖標和顏色\n  const getStatusInfo = currentStatus => {\n    switch (currentStatus) {\n      case OCR_STATUS.PROCESSING:\n        return {\n          icon: /*#__PURE__*/_jsxDEV(ScanningOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 17\n          }, this),\n          color: '#1677ff',\n          text: '識別中...'\n        };\n      case OCR_STATUS.SUCCESS:\n        return {\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 34,\n            columnNumber: 17\n          }, this),\n          color: '#52c41a',\n          text: '識別完成'\n        };\n      case OCR_STATUS.ERROR:\n        return {\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 17\n          }, this),\n          color: '#ff4d4f',\n          text: '識別失敗'\n        };\n      default:\n        return {\n          icon: /*#__PURE__*/_jsxDEV(ScanningOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 17\n          }, this),\n          color: '#8c8c8c',\n          text: '待識別'\n        };\n    }\n  };\n  const statusInfo = getStatusInfo(status);\n  return /*#__PURE__*/_jsxDEV(Card, {\n    className: `ocr-status ${className}`,\n    style: style,\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        textAlign: 'center',\n        padding: '16px'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          marginBottom: '16px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '48px',\n            color: statusInfo.color,\n            marginBottom: '8px'\n          },\n          children: statusInfo.icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 63,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '16px',\n            fontWeight: 'bold',\n            color: statusInfo.color,\n            marginBottom: '4px'\n          },\n          children: statusInfo.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this), status === OCR_STATUS.PROCESSING && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            margin: '16px 0'\n          },\n          children: [/*#__PURE__*/_jsxDEV(ProgressBar, {\n            percent: 50,\n            style: {\n              '--track-width': '4px',\n              '--fill-color': statusInfo.color\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              fontSize: '12px',\n              color: '#8c8c8c',\n              marginTop: '8px'\n            },\n            children: \"\\u6B63\\u5728\\u5206\\u6790\\u5716\\u7247\\u5167\\u5BB9...\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 94,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 13\n        }, this), status === OCR_STATUS.ERROR && error && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: '14px',\n            color: '#ff4d4f',\n            backgroundColor: '#fff2f0',\n            padding: '8px 12px',\n            borderRadius: '6px',\n            border: '1px solid #ffccc7',\n            marginTop: '8px'\n          },\n          children: error\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 62,\n        columnNumber: 9\n      }, this), showDetails && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          borderTop: '1px solid #f0f0f0',\n          paddingTop: '16px'\n        },\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            display: 'flex',\n            justifyContent: 'space-around'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#8c8c8c',\n                marginBottom: '4px'\n              },\n              children: \"\\u6B63\\u9762\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 124,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '24px',\n                color: getStatusInfo(frontStatus).color\n              },\n              children: getStatusInfo(frontStatus).icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 127,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '10px',\n                color: '#8c8c8c'\n              },\n              children: getStatusInfo(frontStatus).text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 123,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              textAlign: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '12px',\n                color: '#8c8c8c',\n                marginBottom: '4px'\n              },\n              children: \"\\u53CD\\u9762\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 142,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '24px',\n                color: getStatusInfo(backStatus).color\n              },\n              children: getStatusInfo(backStatus).icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 145,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '10px',\n                color: '#8c8c8c'\n              },\n              children: getStatusInfo(backStatus).text\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 141,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 121,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 120,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 5\n  }, this);\n};\n_c = OCRStatus;\nexport default OCRStatus;\nvar _c;\n$RefreshReg$(_c, \"OCRStatus\");", "map": {"version": 3, "names": ["React", "Card", "ProgressBar", "CheckCircleOutline", "ExclamationCircleOutline", "ScanningOutline", "OCR_STATUS", "jsxDEV", "_jsxDEV", "OCRStatus", "status", "IDLE", "error", "frontStatus", "backStatus", "showDetails", "style", "className", "getStatusInfo", "currentStatus", "PROCESSING", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "color", "text", "SUCCESS", "ERROR", "statusInfo", "children", "textAlign", "padding", "marginBottom", "fontSize", "fontWeight", "margin", "percent", "marginTop", "backgroundColor", "borderRadius", "border", "borderTop", "paddingTop", "display", "justifyContent", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/components/OCR/OCRStatus.js"], "sourcesContent": ["/**\n * OCR狀態顯示組件\n */\n\nimport React from 'react';\nimport { Card, ProgressBar } from 'antd-mobile';\nimport { \n  CheckCircleOutline, \n  ExclamationCircleOutline, \n  ScanningOutline \n} from 'antd-mobile-icons';\nimport { OCR_STATUS } from '../../utils/constants';\n\nconst OCRStatus = ({\n  status = OCR_STATUS.IDLE,\n  error = null,\n  frontStatus = OCR_STATUS.IDLE,\n  backStatus = OCR_STATUS.IDLE,\n  showDetails = false,\n  style = {},\n  className = ''\n}) => {\n  // 獲取狀態圖標和顏色\n  const getStatusInfo = (currentStatus) => {\n    switch (currentStatus) {\n      case OCR_STATUS.PROCESSING:\n        return {\n          icon: <ScanningOutline />,\n          color: '#1677ff',\n          text: '識別中...'\n        };\n      case OCR_STATUS.SUCCESS:\n        return {\n          icon: <CheckCircleOutline />,\n          color: '#52c41a',\n          text: '識別完成'\n        };\n      case OCR_STATUS.ERROR:\n        return {\n          icon: <ExclamationCircleOutline />,\n          color: '#ff4d4f',\n          text: '識別失敗'\n        };\n      default:\n        return {\n          icon: <ScanningOutline />,\n          color: '#8c8c8c',\n          text: '待識別'\n        };\n    }\n  };\n\n  const statusInfo = getStatusInfo(status);\n\n  return (\n    <Card \n      className={`ocr-status ${className}`}\n      style={style}\n    >\n      <div style={{ textAlign: 'center', padding: '16px' }}>\n        {/* 主狀態顯示 */}\n        <div style={{ marginBottom: '16px' }}>\n          <div \n            style={{ \n              fontSize: '48px', \n              color: statusInfo.color,\n              marginBottom: '8px'\n            }}\n          >\n            {statusInfo.icon}\n          </div>\n          \n          <div \n            style={{ \n              fontSize: '16px', \n              fontWeight: 'bold',\n              color: statusInfo.color,\n              marginBottom: '4px'\n            }}\n          >\n            {statusInfo.text}\n          </div>\n\n          {/* 處理中的進度條 */}\n          {status === OCR_STATUS.PROCESSING && (\n            <div style={{ margin: '16px 0' }}>\n              <ProgressBar \n                percent={50} \n                style={{ \n                  '--track-width': '4px',\n                  '--fill-color': statusInfo.color\n                }}\n              />\n              <div style={{ fontSize: '12px', color: '#8c8c8c', marginTop: '8px' }}>\n                正在分析圖片內容...\n              </div>\n            </div>\n          )}\n\n          {/* 錯誤信息 */}\n          {status === OCR_STATUS.ERROR && error && (\n            <div \n              style={{ \n                fontSize: '14px', \n                color: '#ff4d4f',\n                backgroundColor: '#fff2f0',\n                padding: '8px 12px',\n                borderRadius: '6px',\n                border: '1px solid #ffccc7',\n                marginTop: '8px'\n              }}\n            >\n              {error}\n            </div>\n          )}\n        </div>\n\n        {/* 詳細狀態顯示 */}\n        {showDetails && (\n          <div style={{ borderTop: '1px solid #f0f0f0', paddingTop: '16px' }}>\n            <div style={{ display: 'flex', justifyContent: 'space-around' }}>\n              {/* 正面狀態 */}\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>\n                  正面\n                </div>\n                <div \n                  style={{ \n                    fontSize: '24px', \n                    color: getStatusInfo(frontStatus).color \n                  }}\n                >\n                  {getStatusInfo(frontStatus).icon}\n                </div>\n                <div style={{ fontSize: '10px', color: '#8c8c8c' }}>\n                  {getStatusInfo(frontStatus).text}\n                </div>\n              </div>\n\n              {/* 反面狀態 */}\n              <div style={{ textAlign: 'center' }}>\n                <div style={{ fontSize: '12px', color: '#8c8c8c', marginBottom: '4px' }}>\n                  反面\n                </div>\n                <div \n                  style={{ \n                    fontSize: '24px', \n                    color: getStatusInfo(backStatus).color \n                  }}\n                >\n                  {getStatusInfo(backStatus).icon}\n                </div>\n                <div style={{ fontSize: '10px', color: '#8c8c8c' }}>\n                  {getStatusInfo(backStatus).text}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n};\n\nexport default OCRStatus;\n"], "mappings": ";AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,WAAW,QAAQ,aAAa;AAC/C,SACEC,kBAAkB,EAClBC,wBAAwB,EACxBC,eAAe,QACV,mBAAmB;AAC1B,SAASC,UAAU,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,SAAS,GAAGA,CAAC;EACjBC,MAAM,GAAGJ,UAAU,CAACK,IAAI;EACxBC,KAAK,GAAG,IAAI;EACZC,WAAW,GAAGP,UAAU,CAACK,IAAI;EAC7BG,UAAU,GAAGR,UAAU,CAACK,IAAI;EAC5BI,WAAW,GAAG,KAAK;EACnBC,KAAK,GAAG,CAAC,CAAC;EACVC,SAAS,GAAG;AACd,CAAC,KAAK;EACJ;EACA,MAAMC,aAAa,GAAIC,aAAa,IAAK;IACvC,QAAQA,aAAa;MACnB,KAAKb,UAAU,CAACc,UAAU;QACxB,OAAO;UACLC,IAAI,eAAEb,OAAA,CAACH,eAAe;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACzBC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE;QACR,CAAC;MACH,KAAKrB,UAAU,CAACsB,OAAO;QACrB,OAAO;UACLP,IAAI,eAAEb,OAAA,CAACL,kBAAkB;YAAAmB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAC5BC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE;QACR,CAAC;MACH,KAAKrB,UAAU,CAACuB,KAAK;QACnB,OAAO;UACLR,IAAI,eAAEb,OAAA,CAACJ,wBAAwB;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UAClCC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE;QACR,CAAC;MACH;QACE,OAAO;UACLN,IAAI,eAAEb,OAAA,CAACH,eAAe;YAAAiB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;UACzBC,KAAK,EAAE,SAAS;UAChBC,IAAI,EAAE;QACR,CAAC;IACL;EACF,CAAC;EAED,MAAMG,UAAU,GAAGZ,aAAa,CAACR,MAAM,CAAC;EAExC,oBACEF,OAAA,CAACP,IAAI;IACHgB,SAAS,EAAE,cAAcA,SAAS,EAAG;IACrCD,KAAK,EAAEA,KAAM;IAAAe,QAAA,eAEbvB,OAAA;MAAKQ,KAAK,EAAE;QAAEgB,SAAS,EAAE,QAAQ;QAAEC,OAAO,EAAE;MAAO,CAAE;MAAAF,QAAA,gBAEnDvB,OAAA;QAAKQ,KAAK,EAAE;UAAEkB,YAAY,EAAE;QAAO,CAAE;QAAAH,QAAA,gBACnCvB,OAAA;UACEQ,KAAK,EAAE;YACLmB,QAAQ,EAAE,MAAM;YAChBT,KAAK,EAAEI,UAAU,CAACJ,KAAK;YACvBQ,YAAY,EAAE;UAChB,CAAE;UAAAH,QAAA,EAEDD,UAAU,CAACT;QAAI;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,eAENjB,OAAA;UACEQ,KAAK,EAAE;YACLmB,QAAQ,EAAE,MAAM;YAChBC,UAAU,EAAE,MAAM;YAClBV,KAAK,EAAEI,UAAU,CAACJ,KAAK;YACvBQ,YAAY,EAAE;UAChB,CAAE;UAAAH,QAAA,EAEDD,UAAU,CAACH;QAAI;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACb,CAAC,EAGLf,MAAM,KAAKJ,UAAU,CAACc,UAAU,iBAC/BZ,OAAA;UAAKQ,KAAK,EAAE;YAAEqB,MAAM,EAAE;UAAS,CAAE;UAAAN,QAAA,gBAC/BvB,OAAA,CAACN,WAAW;YACVoC,OAAO,EAAE,EAAG;YACZtB,KAAK,EAAE;cACL,eAAe,EAAE,KAAK;cACtB,cAAc,EAAEc,UAAU,CAACJ;YAC7B;UAAE;YAAAJ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACFjB,OAAA;YAAKQ,KAAK,EAAE;cAAEmB,QAAQ,EAAE,MAAM;cAAET,KAAK,EAAE,SAAS;cAAEa,SAAS,EAAE;YAAM,CAAE;YAAAR,QAAA,EAAC;UAEtE;YAAAT,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN,EAGAf,MAAM,KAAKJ,UAAU,CAACuB,KAAK,IAAIjB,KAAK,iBACnCJ,OAAA;UACEQ,KAAK,EAAE;YACLmB,QAAQ,EAAE,MAAM;YAChBT,KAAK,EAAE,SAAS;YAChBc,eAAe,EAAE,SAAS;YAC1BP,OAAO,EAAE,UAAU;YACnBQ,YAAY,EAAE,KAAK;YACnBC,MAAM,EAAE,mBAAmB;YAC3BH,SAAS,EAAE;UACb,CAAE;UAAAR,QAAA,EAEDnB;QAAK;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,EAGLV,WAAW,iBACVP,OAAA;QAAKQ,KAAK,EAAE;UAAE2B,SAAS,EAAE,mBAAmB;UAAEC,UAAU,EAAE;QAAO,CAAE;QAAAb,QAAA,eACjEvB,OAAA;UAAKQ,KAAK,EAAE;YAAE6B,OAAO,EAAE,MAAM;YAAEC,cAAc,EAAE;UAAe,CAAE;UAAAf,QAAA,gBAE9DvB,OAAA;YAAKQ,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAAD,QAAA,gBAClCvB,OAAA;cAAKQ,KAAK,EAAE;gBAAEmB,QAAQ,EAAE,MAAM;gBAAET,KAAK,EAAE,SAAS;gBAAEQ,YAAY,EAAE;cAAM,CAAE;cAAAH,QAAA,EAAC;YAEzE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNjB,OAAA;cACEQ,KAAK,EAAE;gBACLmB,QAAQ,EAAE,MAAM;gBAChBT,KAAK,EAAER,aAAa,CAACL,WAAW,CAAC,CAACa;cACpC,CAAE;cAAAK,QAAA,EAEDb,aAAa,CAACL,WAAW,CAAC,CAACQ;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC,eACNjB,OAAA;cAAKQ,KAAK,EAAE;gBAAEmB,QAAQ,EAAE,MAAM;gBAAET,KAAK,EAAE;cAAU,CAAE;cAAAK,QAAA,EAChDb,aAAa,CAACL,WAAW,CAAC,CAACc;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNjB,OAAA;YAAKQ,KAAK,EAAE;cAAEgB,SAAS,EAAE;YAAS,CAAE;YAAAD,QAAA,gBAClCvB,OAAA;cAAKQ,KAAK,EAAE;gBAAEmB,QAAQ,EAAE,MAAM;gBAAET,KAAK,EAAE,SAAS;gBAAEQ,YAAY,EAAE;cAAM,CAAE;cAAAH,QAAA,EAAC;YAEzE;cAAAT,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAK,CAAC,eACNjB,OAAA;cACEQ,KAAK,EAAE;gBACLmB,QAAQ,EAAE,MAAM;gBAChBT,KAAK,EAAER,aAAa,CAACJ,UAAU,CAAC,CAACY;cACnC,CAAE;cAAAK,QAAA,EAEDb,aAAa,CAACJ,UAAU,CAAC,CAACO;YAAI;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC,eACNjB,OAAA;cAAKQ,KAAK,EAAE;gBAAEmB,QAAQ,EAAE,MAAM;gBAAET,KAAK,EAAE;cAAU,CAAE;cAAAK,QAAA,EAChDb,aAAa,CAACJ,UAAU,CAAC,CAACa;YAAI;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5B,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACF,CAAC;AAEX,CAAC;AAACsB,EAAA,GArJItC,SAAS;AAuJf,eAAeA,SAAS;AAAC,IAAAsC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}