{"ast": null, "code": "import { __values } from \"tslib\";\nimport { useRef, useEffect } from 'react';\nvar EventEmitter = /** @class */function () {\n  function EventEmitter() {\n    var _this = this;\n    this.subscriptions = new Set();\n    this.emit = function (val) {\n      var e_1, _a;\n      try {\n        for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var subscription = _c.value;\n          subscription(val);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    };\n    this.useSubscription = function (callback) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      var callbackRef = useRef();\n      callbackRef.current = callback;\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useEffect(function () {\n        function subscription(val) {\n          if (callbackRef.current) {\n            callbackRef.current(val);\n          }\n        }\n        _this.subscriptions.add(subscription);\n        return function () {\n          _this.subscriptions.delete(subscription);\n        };\n      }, []);\n    };\n  }\n  return EventEmitter;\n}();\nexport { EventEmitter };\nexport default function useEventEmitter() {\n  var ref = useRef();\n  if (!ref.current) {\n    ref.current = new EventEmitter();\n  }\n  return ref.current;\n}", "map": {"version": 3, "names": ["__values", "useRef", "useEffect", "EventEmitter", "_this", "subscriptions", "Set", "emit", "val", "e_1", "_a", "_b", "_c", "next", "done", "subscription", "value", "e_1_1", "error", "return", "call", "useSubscription", "callback", "callback<PERSON><PERSON>", "current", "add", "delete", "useEventEmitter", "ref"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useEventEmitter/index.js"], "sourcesContent": ["import { __values } from \"tslib\";\nimport { useRef, useEffect } from 'react';\nvar EventEmitter = /** @class */function () {\n  function EventEmitter() {\n    var _this = this;\n    this.subscriptions = new Set();\n    this.emit = function (val) {\n      var e_1, _a;\n      try {\n        for (var _b = __values(_this.subscriptions), _c = _b.next(); !_c.done; _c = _b.next()) {\n          var subscription = _c.value;\n          subscription(val);\n        }\n      } catch (e_1_1) {\n        e_1 = {\n          error: e_1_1\n        };\n      } finally {\n        try {\n          if (_c && !_c.done && (_a = _b.return)) _a.call(_b);\n        } finally {\n          if (e_1) throw e_1.error;\n        }\n      }\n    };\n    this.useSubscription = function (callback) {\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      var callbackRef = useRef();\n      callbackRef.current = callback;\n      // eslint-disable-next-line react-hooks/rules-of-hooks\n      useEffect(function () {\n        function subscription(val) {\n          if (callbackRef.current) {\n            callbackRef.current(val);\n          }\n        }\n        _this.subscriptions.add(subscription);\n        return function () {\n          _this.subscriptions.delete(subscription);\n        };\n      }, []);\n    };\n  }\n  return EventEmitter;\n}();\nexport { EventEmitter };\nexport default function useEventEmitter() {\n  var ref = useRef();\n  if (!ref.current) {\n    ref.current = new EventEmitter();\n  }\n  return ref.current;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,SAASC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AACzC,IAAIC,YAAY,GAAG,aAAa,YAAY;EAC1C,SAASA,YAAYA,CAAA,EAAG;IACtB,IAAIC,KAAK,GAAG,IAAI;IAChB,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,IAAI,GAAG,UAAUC,GAAG,EAAE;MACzB,IAAIC,GAAG,EAAEC,EAAE;MACX,IAAI;QACF,KAAK,IAAIC,EAAE,GAAGX,QAAQ,CAACI,KAAK,CAACC,aAAa,CAAC,EAAEO,EAAE,GAAGD,EAAE,CAACE,IAAI,CAAC,CAAC,EAAE,CAACD,EAAE,CAACE,IAAI,EAAEF,EAAE,GAAGD,EAAE,CAACE,IAAI,CAAC,CAAC,EAAE;UACrF,IAAIE,YAAY,GAAGH,EAAE,CAACI,KAAK;UAC3BD,YAAY,CAACP,GAAG,CAAC;QACnB;MACF,CAAC,CAAC,OAAOS,KAAK,EAAE;QACdR,GAAG,GAAG;UACJS,KAAK,EAAED;QACT,CAAC;MACH,CAAC,SAAS;QACR,IAAI;UACF,IAAIL,EAAE,IAAI,CAACA,EAAE,CAACE,IAAI,KAAKJ,EAAE,GAAGC,EAAE,CAACQ,MAAM,CAAC,EAAET,EAAE,CAACU,IAAI,CAACT,EAAE,CAAC;QACrD,CAAC,SAAS;UACR,IAAIF,GAAG,EAAE,MAAMA,GAAG,CAACS,KAAK;QAC1B;MACF;IACF,CAAC;IACD,IAAI,CAACG,eAAe,GAAG,UAAUC,QAAQ,EAAE;MACzC;MACA,IAAIC,WAAW,GAAGtB,MAAM,CAAC,CAAC;MAC1BsB,WAAW,CAACC,OAAO,GAAGF,QAAQ;MAC9B;MACApB,SAAS,CAAC,YAAY;QACpB,SAASa,YAAYA,CAACP,GAAG,EAAE;UACzB,IAAIe,WAAW,CAACC,OAAO,EAAE;YACvBD,WAAW,CAACC,OAAO,CAAChB,GAAG,CAAC;UAC1B;QACF;QACAJ,KAAK,CAACC,aAAa,CAACoB,GAAG,CAACV,YAAY,CAAC;QACrC,OAAO,YAAY;UACjBX,KAAK,CAACC,aAAa,CAACqB,MAAM,CAACX,YAAY,CAAC;QAC1C,CAAC;MACH,CAAC,EAAE,EAAE,CAAC;IACR,CAAC;EACH;EACA,OAAOZ,YAAY;AACrB,CAAC,CAAC,CAAC;AACH,SAASA,YAAY;AACrB,eAAe,SAASwB,eAAeA,CAAA,EAAG;EACxC,IAAIC,GAAG,GAAG3B,MAAM,CAAC,CAAC;EAClB,IAAI,CAAC2B,GAAG,CAACJ,OAAO,EAAE;IAChBI,GAAG,CAACJ,OAAO,GAAG,IAAIrB,YAAY,CAAC,CAAC;EAClC;EACA,OAAOyB,GAAG,CAACJ,OAAO;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}