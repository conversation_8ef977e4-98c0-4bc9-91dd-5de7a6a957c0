{"ast": null, "code": "import { __assign } from \"tslib\";\nvar cache = new Map();\nvar setCache = function (key, cacheTime, cachedData) {\n  var currentCache = cache.get(key);\n  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {\n    clearTimeout(currentCache.timer);\n  }\n  var timer = undefined;\n  if (cacheTime > -1) {\n    // if cache out, clear it\n    timer = setTimeout(function () {\n      cache.delete(key);\n    }, cacheTime);\n  }\n  cache.set(key, __assign(__assign({}, cachedData), {\n    timer: timer\n  }));\n};\nvar getCache = function (key) {\n  return cache.get(key);\n};\nvar clearCache = function (key) {\n  if (key) {\n    var cacheKeys = Array.isArray(key) ? key : [key];\n    cacheKeys.forEach(function (cacheKey) {\n      return cache.delete(cacheKey);\n    });\n  } else {\n    cache.clear();\n  }\n};\nexport { getCache, setCache, clearCache };", "map": {"version": 3, "names": ["__assign", "cache", "Map", "setCache", "key", "cacheTime", "cachedData", "currentCache", "get", "timer", "clearTimeout", "undefined", "setTimeout", "delete", "set", "getCache", "clearCache", "cacheKeys", "Array", "isArray", "for<PERSON>ach", "cache<PERSON>ey", "clear"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRequest/src/utils/cache.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nvar cache = new Map();\nvar setCache = function (key, cacheTime, cachedData) {\n  var currentCache = cache.get(key);\n  if (currentCache === null || currentCache === void 0 ? void 0 : currentCache.timer) {\n    clearTimeout(currentCache.timer);\n  }\n  var timer = undefined;\n  if (cacheTime > -1) {\n    // if cache out, clear it\n    timer = setTimeout(function () {\n      cache.delete(key);\n    }, cacheTime);\n  }\n  cache.set(key, __assign(__assign({}, cachedData), {\n    timer: timer\n  }));\n};\nvar getCache = function (key) {\n  return cache.get(key);\n};\nvar clearCache = function (key) {\n  if (key) {\n    var cacheKeys = Array.isArray(key) ? key : [key];\n    cacheKeys.forEach(function (cacheKey) {\n      return cache.delete(cacheKey);\n    });\n  } else {\n    cache.clear();\n  }\n};\nexport { getCache, setCache, clearCache };"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,OAAO;AAChC,IAAIC,KAAK,GAAG,IAAIC,GAAG,CAAC,CAAC;AACrB,IAAIC,QAAQ,GAAG,SAAAA,CAAUC,GAAG,EAAEC,SAAS,EAAEC,UAAU,EAAE;EACnD,IAAIC,YAAY,GAAGN,KAAK,CAACO,GAAG,CAACJ,GAAG,CAAC;EACjC,IAAIG,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,KAAK,EAAE;IAClFC,YAAY,CAACH,YAAY,CAACE,KAAK,CAAC;EAClC;EACA,IAAIA,KAAK,GAAGE,SAAS;EACrB,IAAIN,SAAS,GAAG,CAAC,CAAC,EAAE;IAClB;IACAI,KAAK,GAAGG,UAAU,CAAC,YAAY;MAC7BX,KAAK,CAACY,MAAM,CAACT,GAAG,CAAC;IACnB,CAAC,EAAEC,SAAS,CAAC;EACf;EACAJ,KAAK,CAACa,GAAG,CAACV,GAAG,EAAEJ,QAAQ,CAACA,QAAQ,CAAC,CAAC,CAAC,EAAEM,UAAU,CAAC,EAAE;IAChDG,KAAK,EAAEA;EACT,CAAC,CAAC,CAAC;AACL,CAAC;AACD,IAAIM,QAAQ,GAAG,SAAAA,CAAUX,GAAG,EAAE;EAC5B,OAAOH,KAAK,CAACO,GAAG,CAACJ,GAAG,CAAC;AACvB,CAAC;AACD,IAAIY,UAAU,GAAG,SAAAA,CAAUZ,GAAG,EAAE;EAC9B,IAAIA,GAAG,EAAE;IACP,IAAIa,SAAS,GAAGC,KAAK,CAACC,OAAO,CAACf,GAAG,CAAC,GAAGA,GAAG,GAAG,CAACA,GAAG,CAAC;IAChDa,SAAS,CAACG,OAAO,CAAC,UAAUC,QAAQ,EAAE;MACpC,OAAOpB,KAAK,CAACY,MAAM,CAACQ,QAAQ,CAAC;IAC/B,CAAC,CAAC;EACJ,CAAC,MAAM;IACLpB,KAAK,CAACqB,KAAK,CAAC,CAAC;EACf;AACF,CAAC;AACD,SAASP,QAAQ,EAAEZ,QAAQ,EAAEa,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}