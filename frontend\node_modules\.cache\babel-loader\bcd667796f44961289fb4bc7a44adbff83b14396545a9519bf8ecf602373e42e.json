{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\app\\u61C9\\u7528\\\\ocr_app_v2\\\\frontend\\\\src\\\\pages\\\\HomePage.js\",\n  _s = $RefreshSig$();\n/**\n * 主頁 - 重構版本\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Button, Space, Card } from 'antd-mobile';\nimport { ScanningOutline, ContactsOutline, CameraOutline } from 'antd-mobile-icons';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst HomePage = () => {\n  _s();\n  const navigate = useNavigate();\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      minHeight: '100vh',\n      background: '#f5f5f5',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: '20px'\n    },\n    children: /*#__PURE__*/_jsxDEV(Card, {\n      style: {\n        width: '100%',\n        maxWidth: 400,\n        boxShadow: '0 2px 8px rgba(0,0,0,0.1)'\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          textAlign: 'center',\n          marginBottom: '32px'\n        },\n        children: [/*#__PURE__*/_jsxDEV(ScanningOutline, {\n          style: {\n            fontSize: '64px',\n            color: '#1677ff',\n            marginBottom: '16px'\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          style: {\n            margin: 0,\n            color: '#262626'\n          },\n          children: \"\\u540D\\u7247 OCR \\u61C9\\u7528\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          style: {\n            color: '#8c8c8c',\n            margin: '8px 0 0 0'\n          },\n          children: \"\\u667A\\u80FD\\u8B58\\u5225\\u540D\\u7247\\u8CC7\\u8A0A\\uFF0C\\u8F15\\u9B06\\u7BA1\\u7406\\u806F\\u7D61\\u4EBA\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Space, {\n        direction: \"vertical\",\n        block: true,\n        style: {\n          width: '100%'\n        },\n        children: [/*#__PURE__*/_jsxDEV(Button, {\n          color: \"primary\",\n          size: \"large\",\n          block: true,\n          style: {\n            fontSize: '18px',\n            height: '56px'\n          },\n          onClick: () => navigate('/scan'),\n          children: [/*#__PURE__*/_jsxDEV(ScanningOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this), \" \\u958B\\u59CB\\u6383\\u63CF\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"default\",\n          size: \"large\",\n          block: true,\n          style: {\n            fontSize: '18px',\n            height: '56px'\n          },\n          onClick: () => navigate('/cards'),\n          children: [/*#__PURE__*/_jsxDEV(ContactsOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 68,\n            columnNumber: 13\n          }, this), \" \\u540D\\u7247\\u7BA1\\u7406\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Button, {\n          color: \"warning\",\n          size: \"large\",\n          block: true,\n          style: {\n            fontSize: '18px',\n            height: '56px'\n          },\n          onClick: () => navigate('/cards/new'),\n          children: [/*#__PURE__*/_jsxDEV(CameraOutline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), \" \\u624B\\u52D5\\u65B0\\u589E\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this);\n};\n_s(HomePage, \"CzcTeTziyjMsSrAVmHuCCb6+Bfg=\", false, function () {\n  return [useNavigate];\n});\n_c = HomePage;\nexport default HomePage;\nvar _c;\n$RefreshReg$(_c, \"HomePage\");", "map": {"version": 3, "names": ["React", "useNavigate", "<PERSON><PERSON>", "Space", "Card", "ScanningOutline", "ContactsOutline", "CameraOutline", "jsxDEV", "_jsxDEV", "HomePage", "_s", "navigate", "style", "minHeight", "background", "display", "flexDirection", "justifyContent", "alignItems", "padding", "children", "width", "max<PERSON><PERSON><PERSON>", "boxShadow", "textAlign", "marginBottom", "fontSize", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "direction", "block", "size", "height", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/pages/HomePage.js"], "sourcesContent": ["/**\n * 主頁 - 重構版本\n */\n\nimport React from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Button, Space, Card } from 'antd-mobile';\nimport { \n  ScanningOutline, \n  ContactsOutline, \n  CameraOutline \n} from 'antd-mobile-icons';\n\nconst HomePage = () => {\n  const navigate = useNavigate();\n\n  return (\n    <div \n      style={{ \n        minHeight: '100vh', \n        background: '#f5f5f5', \n        display: 'flex', \n        flexDirection: 'column', \n        justifyContent: 'center', \n        alignItems: 'center',\n        padding: '20px'\n      }}\n    >\n      <Card \n        style={{ \n          width: '100%', \n          maxWidth: 400, \n          boxShadow: '0 2px 8px rgba(0,0,0,0.1)' \n        }}\n      >\n        <div style={{ textAlign: 'center', marginBottom: '32px' }}>\n          <ScanningOutline \n            style={{ \n              fontSize: '64px', \n              color: '#1677ff',\n              marginBottom: '16px'\n            }} \n          />\n          <h2 style={{ margin: 0, color: '#262626' }}>名片 OCR 應用</h2>\n          <p style={{ color: '#8c8c8c', margin: '8px 0 0 0' }}>\n            智能識別名片資訊，輕鬆管理聯絡人\n          </p>\n        </div>\n        \n        <Space direction=\"vertical\" block style={{ width: '100%' }}>\n          <Button \n            color=\"primary\" \n            size=\"large\" \n            block \n            style={{ fontSize: '18px', height: '56px' }}\n            onClick={() => navigate('/scan')}\n          >\n            <ScanningOutline /> 開始掃描\n          </Button>\n          \n          <Button \n            color=\"default\" \n            size=\"large\" \n            block \n            style={{ fontSize: '18px', height: '56px' }}\n            onClick={() => navigate('/cards')}\n          >\n            <ContactsOutline /> 名片管理\n          </Button>\n          \n          <Button \n            color=\"warning\" \n            size=\"large\" \n            block \n            style={{ fontSize: '18px', height: '56px' }}\n            onClick={() => navigate('/cards/new')}\n          >\n            <CameraOutline /> 手動新增\n          </Button>\n        </Space>\n      </Card>\n    </div>\n  );\n};\n\nexport default HomePage;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,WAAW,QAAQ,kBAAkB;AAC9C,SAASC,MAAM,EAAEC,KAAK,EAAEC,IAAI,QAAQ,aAAa;AACjD,SACEC,eAAe,EACfC,eAAe,EACfC,aAAa,QACR,mBAAmB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3B,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrB,MAAMC,QAAQ,GAAGX,WAAW,CAAC,CAAC;EAE9B,oBACEQ,OAAA;IACEI,KAAK,EAAE;MACLC,SAAS,EAAE,OAAO;MAClBC,UAAU,EAAE,SAAS;MACrBC,OAAO,EAAE,MAAM;MACfC,aAAa,EAAE,QAAQ;MACvBC,cAAc,EAAE,QAAQ;MACxBC,UAAU,EAAE,QAAQ;MACpBC,OAAO,EAAE;IACX,CAAE;IAAAC,QAAA,eAEFZ,OAAA,CAACL,IAAI;MACHS,KAAK,EAAE;QACLS,KAAK,EAAE,MAAM;QACbC,QAAQ,EAAE,GAAG;QACbC,SAAS,EAAE;MACb,CAAE;MAAAH,QAAA,gBAEFZ,OAAA;QAAKI,KAAK,EAAE;UAAEY,SAAS,EAAE,QAAQ;UAAEC,YAAY,EAAE;QAAO,CAAE;QAAAL,QAAA,gBACxDZ,OAAA,CAACJ,eAAe;UACdQ,KAAK,EAAE;YACLc,QAAQ,EAAE,MAAM;YAChBC,KAAK,EAAE,SAAS;YAChBF,YAAY,EAAE;UAChB;QAAE;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACFvB,OAAA;UAAII,KAAK,EAAE;YAAEoB,MAAM,EAAE,CAAC;YAAEL,KAAK,EAAE;UAAU,CAAE;UAAAP,QAAA,EAAC;QAAS;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAC1DvB,OAAA;UAAGI,KAAK,EAAE;YAAEe,KAAK,EAAE,SAAS;YAAEK,MAAM,EAAE;UAAY,CAAE;UAAAZ,QAAA,EAAC;QAErD;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAENvB,OAAA,CAACN,KAAK;QAAC+B,SAAS,EAAC,UAAU;QAACC,KAAK;QAACtB,KAAK,EAAE;UAAES,KAAK,EAAE;QAAO,CAAE;QAAAD,QAAA,gBACzDZ,OAAA,CAACP,MAAM;UACL0B,KAAK,EAAC,SAAS;UACfQ,IAAI,EAAC,OAAO;UACZD,KAAK;UACLtB,KAAK,EAAE;YAAEc,QAAQ,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAC5CC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,OAAO,CAAE;UAAAS,QAAA,gBAEjCZ,OAAA,CAACJ,eAAe;YAAAwB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BACrB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvB,OAAA,CAACP,MAAM;UACL0B,KAAK,EAAC,SAAS;UACfQ,IAAI,EAAC,OAAO;UACZD,KAAK;UACLtB,KAAK,EAAE;YAAEc,QAAQ,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAC5CC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,QAAQ,CAAE;UAAAS,QAAA,gBAElCZ,OAAA,CAACH,eAAe;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BACrB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,eAETvB,OAAA,CAACP,MAAM;UACL0B,KAAK,EAAC,SAAS;UACfQ,IAAI,EAAC,OAAO;UACZD,KAAK;UACLtB,KAAK,EAAE;YAAEc,QAAQ,EAAE,MAAM;YAAEU,MAAM,EAAE;UAAO,CAAE;UAC5CC,OAAO,EAAEA,CAAA,KAAM1B,QAAQ,CAAC,YAAY,CAAE;UAAAS,QAAA,gBAEtCZ,OAAA,CAACF,aAAa;YAAAsB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BACnB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACrB,EAAA,CAtEID,QAAQ;EAAA,QACKT,WAAW;AAAA;AAAAsC,EAAA,GADxB7B,QAAQ;AAwEd,eAAeA,QAAQ;AAAC,IAAA6B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}