{"ast": null, "code": "var typeTemplate = \"'${name}' is not a valid ${type}\";\nexport var defaultValidateMessages = {\n  default: \"Validation error on field '${name}'\",\n  required: \"'${name}' is required\",\n  enum: \"'${name}' must be one of [${enum}]\",\n  whitespace: \"'${name}' cannot be empty\",\n  date: {\n    format: \"'${name}' is invalid for format date\",\n    parse: \"'${name}' could not be parsed as date\",\n    invalid: \"'${name}' is invalid date\"\n  },\n  types: {\n    string: typeTemplate,\n    method: typeTemplate,\n    array: typeTemplate,\n    object: typeTemplate,\n    number: typeTemplate,\n    date: typeTemplate,\n    boolean: typeTemplate,\n    integer: typeTemplate,\n    float: typeTemplate,\n    regexp: typeTemplate,\n    email: typeTemplate,\n    url: typeTemplate,\n    hex: typeTemplate\n  },\n  string: {\n    len: \"'${name}' must be exactly ${len} characters\",\n    min: \"'${name}' must be at least ${min} characters\",\n    max: \"'${name}' cannot be longer than ${max} characters\",\n    range: \"'${name}' must be between ${min} and ${max} characters\"\n  },\n  number: {\n    len: \"'${name}' must equal ${len}\",\n    min: \"'${name}' cannot be less than ${min}\",\n    max: \"'${name}' cannot be greater than ${max}\",\n    range: \"'${name}' must be between ${min} and ${max}\"\n  },\n  array: {\n    len: \"'${name}' must be exactly ${len} in length\",\n    min: \"'${name}' cannot be less than ${min} in length\",\n    max: \"'${name}' cannot be greater than ${max} in length\",\n    range: \"'${name}' must be between ${min} and ${max} in length\"\n  },\n  pattern: {\n    mismatch: \"'${name}' does not match pattern ${pattern}\"\n  }\n};", "map": {"version": 3, "names": ["typeTemplate", "defaultValidateMessages", "default", "required", "enum", "whitespace", "date", "format", "parse", "invalid", "types", "string", "method", "array", "object", "number", "boolean", "integer", "float", "regexp", "email", "url", "hex", "len", "min", "max", "range", "pattern", "mismatch"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/rc-field-form/es/utils/messages.js"], "sourcesContent": ["var typeTemplate = \"'${name}' is not a valid ${type}\";\nexport var defaultValidateMessages = {\n  default: \"Validation error on field '${name}'\",\n  required: \"'${name}' is required\",\n  enum: \"'${name}' must be one of [${enum}]\",\n  whitespace: \"'${name}' cannot be empty\",\n  date: {\n    format: \"'${name}' is invalid for format date\",\n    parse: \"'${name}' could not be parsed as date\",\n    invalid: \"'${name}' is invalid date\"\n  },\n  types: {\n    string: typeTemplate,\n    method: typeTemplate,\n    array: typeTemplate,\n    object: typeTemplate,\n    number: typeTemplate,\n    date: typeTemplate,\n    boolean: typeTemplate,\n    integer: typeTemplate,\n    float: typeTemplate,\n    regexp: typeTemplate,\n    email: typeTemplate,\n    url: typeTemplate,\n    hex: typeTemplate\n  },\n  string: {\n    len: \"'${name}' must be exactly ${len} characters\",\n    min: \"'${name}' must be at least ${min} characters\",\n    max: \"'${name}' cannot be longer than ${max} characters\",\n    range: \"'${name}' must be between ${min} and ${max} characters\"\n  },\n  number: {\n    len: \"'${name}' must equal ${len}\",\n    min: \"'${name}' cannot be less than ${min}\",\n    max: \"'${name}' cannot be greater than ${max}\",\n    range: \"'${name}' must be between ${min} and ${max}\"\n  },\n  array: {\n    len: \"'${name}' must be exactly ${len} in length\",\n    min: \"'${name}' cannot be less than ${min} in length\",\n    max: \"'${name}' cannot be greater than ${max} in length\",\n    range: \"'${name}' must be between ${min} and ${max} in length\"\n  },\n  pattern: {\n    mismatch: \"'${name}' does not match pattern ${pattern}\"\n  }\n};"], "mappings": "AAAA,IAAIA,YAAY,GAAG,kCAAkC;AACrD,OAAO,IAAIC,uBAAuB,GAAG;EACnCC,OAAO,EAAE,qCAAqC;EAC9CC,QAAQ,EAAE,uBAAuB;EACjCC,IAAI,EAAE,oCAAoC;EAC1CC,UAAU,EAAE,2BAA2B;EACvCC,IAAI,EAAE;IACJC,MAAM,EAAE,sCAAsC;IAC9CC,KAAK,EAAE,uCAAuC;IAC9CC,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAE;IACLC,MAAM,EAAEX,YAAY;IACpBY,MAAM,EAAEZ,YAAY;IACpBa,KAAK,EAAEb,YAAY;IACnBc,MAAM,EAAEd,YAAY;IACpBe,MAAM,EAAEf,YAAY;IACpBM,IAAI,EAAEN,YAAY;IAClBgB,OAAO,EAAEhB,YAAY;IACrBiB,OAAO,EAAEjB,YAAY;IACrBkB,KAAK,EAAElB,YAAY;IACnBmB,MAAM,EAAEnB,YAAY;IACpBoB,KAAK,EAAEpB,YAAY;IACnBqB,GAAG,EAAErB,YAAY;IACjBsB,GAAG,EAAEtB;EACP,CAAC;EACDW,MAAM,EAAE;IACNY,GAAG,EAAE,6CAA6C;IAClDC,GAAG,EAAE,8CAA8C;IACnDC,GAAG,EAAE,mDAAmD;IACxDC,KAAK,EAAE;EACT,CAAC;EACDX,MAAM,EAAE;IACNQ,GAAG,EAAE,6BAA6B;IAClCC,GAAG,EAAE,sCAAsC;IAC3CC,GAAG,EAAE,yCAAyC;IAC9CC,KAAK,EAAE;EACT,CAAC;EACDb,KAAK,EAAE;IACLU,GAAG,EAAE,4CAA4C;IACjDC,GAAG,EAAE,gDAAgD;IACrDC,GAAG,EAAE,mDAAmD;IACxDC,KAAK,EAAE;EACT,CAAC;EACDC,OAAO,EAAE;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}