{"ast": null, "code": "import { __read } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport useUnmount from '../useUnmount';\nfunction useRafState(initialState) {\n  var ref = useRef(0);\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setRafState = useCallback(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  useUnmount(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\nexport default useRafState;", "map": {"version": 3, "names": ["__read", "useCallback", "useRef", "useState", "useUnmount", "useRafState", "initialState", "ref", "_a", "state", "setState", "setRafState", "value", "cancelAnimationFrame", "current", "requestAnimationFrame"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/node_modules/ahooks/es/useRafState/index.js"], "sourcesContent": ["import { __read } from \"tslib\";\nimport { useCallback, useRef, useState } from 'react';\nimport useUnmount from '../useUnmount';\nfunction useRafState(initialState) {\n  var ref = useRef(0);\n  var _a = __read(useState(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setRafState = useCallback(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  useUnmount(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\nexport default useRafState;"], "mappings": "AAAA,SAASA,MAAM,QAAQ,OAAO;AAC9B,SAASC,WAAW,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AACrD,OAAOC,UAAU,MAAM,eAAe;AACtC,SAASC,WAAWA,CAACC,YAAY,EAAE;EACjC,IAAIC,GAAG,GAAGL,MAAM,CAAC,CAAC,CAAC;EACnB,IAAIM,EAAE,GAAGR,MAAM,CAACG,QAAQ,CAACG,YAAY,CAAC,EAAE,CAAC,CAAC;IACxCG,KAAK,GAAGD,EAAE,CAAC,CAAC,CAAC;IACbE,QAAQ,GAAGF,EAAE,CAAC,CAAC,CAAC;EAClB,IAAIG,WAAW,GAAGV,WAAW,CAAC,UAAUW,KAAK,EAAE;IAC7CC,oBAAoB,CAACN,GAAG,CAACO,OAAO,CAAC;IACjCP,GAAG,CAACO,OAAO,GAAGC,qBAAqB,CAAC,YAAY;MAC9CL,QAAQ,CAACE,KAAK,CAAC;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EACNR,UAAU,CAAC,YAAY;IACrBS,oBAAoB,CAACN,GAAG,CAACO,OAAO,CAAC;EACnC,CAAC,CAAC;EACF,OAAO,CAACL,KAAK,EAAEE,WAAW,CAAC;AAC7B;AACA,eAAeN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}