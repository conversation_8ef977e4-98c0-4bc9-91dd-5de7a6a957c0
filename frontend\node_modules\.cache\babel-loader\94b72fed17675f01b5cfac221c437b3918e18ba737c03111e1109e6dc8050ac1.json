{"ast": null, "code": "var _s = $RefreshSig$();\n/**\n * OCR狀態管理Hook\n * 處理OCR識別和智能解析功能\n */\n\nimport { useState, useCallback } from 'react';\nimport { Toast } from 'antd-mobile';\nimport { ocrService } from '../services/api/ocrService';\nimport { OCR_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';\nexport const useOCRState = () => {\n  _s();\n  const [frontOCR, setFrontOCR] = useState({\n    text: '',\n    parsedFields: {},\n    status: OCR_STATUS.IDLE,\n    error: null\n  });\n  const [backOCR, setBackOCR] = useState({\n    text: '',\n    parsedFields: {},\n    status: OCR_STATUS.IDLE,\n    error: null\n  });\n\n  /**\n   * 更新OCR狀態\n   */\n  const updateOCRState = useCallback((side, updates) => {\n    const setter = side === 'front' ? setFrontOCR : setBackOCR;\n    setter(prev => ({\n      ...prev,\n      ...updates\n    }));\n  }, []);\n\n  /**\n   * 處理圖片OCR識別\n   */\n  const processImage = useCallback(async (imageFile, side = 'front') => {\n    console.log(`🔍 開始OCR處理 - ${side}面:`, imageFile.name);\n    updateOCRState(side, {\n      status: OCR_STATUS.PROCESSING,\n      error: null\n    });\n    try {\n      const result = await ocrService.processImage(imageFile);\n      if (result.success && result.text) {\n        updateOCRState(side, {\n          text: result.text,\n          status: OCR_STATUS.SUCCESS,\n          error: null\n        });\n        console.log(`✅ OCR識別成功 - ${side}面:`, result.text);\n\n        // 自動進行智能解析\n        await parseFields(result.text, side);\n        return result.text;\n      } else {\n        throw new Error(result.message || 'OCR識別失敗');\n      }\n    } catch (error) {\n      console.error(`❌ OCR識別失敗 - ${side}面:`, error);\n      updateOCRState(side, {\n        status: OCR_STATUS.ERROR,\n        error: error.message || ERROR_MESSAGES.OCR_FAILED\n      });\n      Toast.show({\n        content: error.message || ERROR_MESSAGES.OCR_FAILED,\n        position: 'center'\n      });\n      throw error;\n    }\n  }, [updateOCRState]);\n\n  /**\n   * 智能解析OCR文字到標準化欄位\n   */\n  const parseFields = useCallback(async (ocrText, side = 'front') => {\n    if (!ocrText || !ocrText.trim()) {\n      console.warn('⚠️ OCR文字為空，跳過解析');\n      return {};\n    }\n    console.log(`🧠 開始智能解析 - ${side}面:`, ocrText.substring(0, 100) + '...');\n    try {\n      const result = await ocrService.parseFields(ocrText, side);\n      if (result.success && result.parsed_fields) {\n        updateOCRState(side, {\n          parsedFields: result.parsed_fields\n        });\n        console.log(`✅ 智能解析成功 - ${side}面:`, result.parsed_fields);\n        Toast.show({\n          content: `${side === 'front' ? '正面' : '反面'}${SUCCESS_MESSAGES.OCR_SUCCESS}`,\n          position: 'center'\n        });\n        return result.parsed_fields;\n      } else {\n        console.warn('⚠️ 智能解析無結果');\n        return {};\n      }\n    } catch (error) {\n      console.error(`❌ 智能解析失敗 - ${side}面:`, error);\n\n      // 解析失敗不影響OCR結果，只記錄錯誤\n      updateOCRState(side, {\n        parsedFields: {}\n      });\n      return {};\n    }\n  }, [updateOCRState]);\n\n  /**\n   * 重新處理OCR\n   */\n  const retryOCR = useCallback(async (imageFile, side = 'front') => {\n    console.log(`🔄 重新處理OCR - ${side}面`);\n    return await processImage(imageFile, side);\n  }, [processImage]);\n\n  /**\n   * 清空OCR結果\n   */\n  const clearOCR = useCallback((side = 'both') => {\n    const emptyState = {\n      text: '',\n      parsedFields: {},\n      status: OCR_STATUS.IDLE,\n      error: null\n    };\n    if (side === 'both') {\n      setFrontOCR(emptyState);\n      setBackOCR(emptyState);\n    } else if (side === 'front') {\n      setFrontOCR(emptyState);\n    } else if (side === 'back') {\n      setBackOCR(emptyState);\n    }\n    console.log(`🧹 清空OCR結果 - ${side}`);\n  }, []);\n\n  /**\n   * 合併正反面解析結果\n   */\n  const getMergedFields = useCallback(() => {\n    const merged = {\n      ...frontOCR.parsedFields\n    };\n\n    // 反面的欄位可以補充或覆蓋正面的欄位\n    Object.keys(backOCR.parsedFields).forEach(key => {\n      const backValue = backOCR.parsedFields[key];\n      const frontValue = merged[key];\n\n      // 如果正面沒有該欄位，或者反面的值更完整，則使用反面的值\n      if (!frontValue || backValue && backValue.length > frontValue.length) {\n        merged[key] = backValue;\n      }\n    });\n    return merged;\n  }, [frontOCR.parsedFields, backOCR.parsedFields]);\n\n  /**\n   * 獲取OCR處理狀態\n   */\n  const getOCRStatus = useCallback((side = 'both') => {\n    if (side === 'front') {\n      return frontOCR.status;\n    } else if (side === 'back') {\n      return backOCR.status;\n    } else {\n      // 返回整體狀態\n      if (frontOCR.status === OCR_STATUS.PROCESSING || backOCR.status === OCR_STATUS.PROCESSING) {\n        return OCR_STATUS.PROCESSING;\n      } else if (frontOCR.status === OCR_STATUS.ERROR || backOCR.status === OCR_STATUS.ERROR) {\n        return OCR_STATUS.ERROR;\n      } else if (frontOCR.status === OCR_STATUS.SUCCESS || backOCR.status === OCR_STATUS.SUCCESS) {\n        return OCR_STATUS.SUCCESS;\n      } else {\n        return OCR_STATUS.IDLE;\n      }\n    }\n  }, [frontOCR.status, backOCR.status]);\n\n  /**\n   * 檢查是否有OCR結果\n   */\n  const hasOCRResults = useCallback((side = 'both') => {\n    if (side === 'front') {\n      return !!frontOCR.text;\n    } else if (side === 'back') {\n      return !!backOCR.text;\n    } else {\n      return !!(frontOCR.text || backOCR.text);\n    }\n  }, [frontOCR.text, backOCR.text]);\n\n  /**\n   * 檢查是否有解析結果\n   */\n  const hasParsedFields = useCallback((side = 'both') => {\n    if (side === 'front') {\n      return Object.keys(frontOCR.parsedFields).length > 0;\n    } else if (side === 'back') {\n      return Object.keys(backOCR.parsedFields).length > 0;\n    } else {\n      return Object.keys(frontOCR.parsedFields).length > 0 || Object.keys(backOCR.parsedFields).length > 0;\n    }\n  }, [frontOCR.parsedFields, backOCR.parsedFields]);\n  return {\n    // OCR狀態\n    frontOCR,\n    backOCR,\n    // 操作方法\n    processImage,\n    parseFields,\n    retryOCR,\n    clearOCR,\n    // 工具方法\n    getMergedFields,\n    getOCRStatus,\n    hasOCRResults,\n    hasParsedFields,\n    // 便捷屬性\n    isProcessing: getOCRStatus() === OCR_STATUS.PROCESSING,\n    hasError: getOCRStatus() === OCR_STATUS.ERROR,\n    hasResults: hasOCRResults(),\n    mergedFields: getMergedFields()\n  };\n};\n_s(useOCRState, \"f+zCrNHfY2hqY1Ye2AtIGnoxXyM=\");\nexport default useOCRState;", "map": {"version": 3, "names": ["useState", "useCallback", "Toast", "ocrService", "OCR_STATUS", "ERROR_MESSAGES", "SUCCESS_MESSAGES", "useOCRState", "_s", "frontOCR", "setFrontOCR", "text", "parsedFields", "status", "IDLE", "error", "backOCR", "setBackOCR", "updateOCRState", "side", "updates", "setter", "prev", "processImage", "imageFile", "console", "log", "name", "PROCESSING", "result", "success", "SUCCESS", "parseFields", "Error", "message", "ERROR", "OCR_FAILED", "show", "content", "position", "ocrText", "trim", "warn", "substring", "parsed_fields", "OCR_SUCCESS", "retryOCR", "clearOCR", "emptyState", "getMergedFields", "merged", "Object", "keys", "for<PERSON>ach", "key", "backValue", "frontValue", "length", "getOCRStatus", "hasOCRResults", "hasP<PERSON>ed<PERSON><PERSON>s", "isProcessing", "<PERSON><PERSON><PERSON><PERSON>", "hasResults", "mergedFields"], "sources": ["C:/Users/<USER>/Desktop/app應用/ocr_app_v2/frontend/src/hooks/useOCRState.js"], "sourcesContent": ["/**\n * OCR狀態管理Hook\n * 處理OCR識別和智能解析功能\n */\n\nimport { useState, useCallback } from 'react';\nimport { Toast } from 'antd-mobile';\nimport { ocrService } from '../services/api/ocrService';\nimport { OCR_STATUS, ERROR_MESSAGES, SUCCESS_MESSAGES } from '../utils/constants';\n\nexport const useOCRState = () => {\n  const [frontOCR, setFrontOCR] = useState({\n    text: '',\n    parsedFields: {},\n    status: OCR_STATUS.IDLE,\n    error: null\n  });\n  \n  const [backOCR, setBackOCR] = useState({\n    text: '',\n    parsedFields: {},\n    status: OCR_STATUS.IDLE,\n    error: null\n  });\n\n  /**\n   * 更新OCR狀態\n   */\n  const updateOCRState = useCallback((side, updates) => {\n    const setter = side === 'front' ? setFrontOCR : setBackOCR;\n    setter(prev => ({ ...prev, ...updates }));\n  }, []);\n\n  /**\n   * 處理圖片OCR識別\n   */\n  const processImage = useCallback(async (imageFile, side = 'front') => {\n    console.log(`🔍 開始OCR處理 - ${side}面:`, imageFile.name);\n    \n    updateOCRState(side, {\n      status: OCR_STATUS.PROCESSING,\n      error: null\n    });\n\n    try {\n      const result = await ocrService.processImage(imageFile);\n      \n      if (result.success && result.text) {\n        updateOCRState(side, {\n          text: result.text,\n          status: OCR_STATUS.SUCCESS,\n          error: null\n        });\n        \n        console.log(`✅ OCR識別成功 - ${side}面:`, result.text);\n        \n        // 自動進行智能解析\n        await parseFields(result.text, side);\n        \n        return result.text;\n      } else {\n        throw new Error(result.message || 'OCR識別失敗');\n      }\n    } catch (error) {\n      console.error(`❌ OCR識別失敗 - ${side}面:`, error);\n      \n      updateOCRState(side, {\n        status: OCR_STATUS.ERROR,\n        error: error.message || ERROR_MESSAGES.OCR_FAILED\n      });\n      \n      Toast.show({\n        content: error.message || ERROR_MESSAGES.OCR_FAILED,\n        position: 'center',\n      });\n      \n      throw error;\n    }\n  }, [updateOCRState]);\n\n  /**\n   * 智能解析OCR文字到標準化欄位\n   */\n  const parseFields = useCallback(async (ocrText, side = 'front') => {\n    if (!ocrText || !ocrText.trim()) {\n      console.warn('⚠️ OCR文字為空，跳過解析');\n      return {};\n    }\n\n    console.log(`🧠 開始智能解析 - ${side}面:`, ocrText.substring(0, 100) + '...');\n\n    try {\n      const result = await ocrService.parseFields(ocrText, side);\n      \n      if (result.success && result.parsed_fields) {\n        updateOCRState(side, {\n          parsedFields: result.parsed_fields\n        });\n        \n        console.log(`✅ 智能解析成功 - ${side}面:`, result.parsed_fields);\n        \n        Toast.show({\n          content: `${side === 'front' ? '正面' : '反面'}${SUCCESS_MESSAGES.OCR_SUCCESS}`,\n          position: 'center',\n        });\n        \n        return result.parsed_fields;\n      } else {\n        console.warn('⚠️ 智能解析無結果');\n        return {};\n      }\n    } catch (error) {\n      console.error(`❌ 智能解析失敗 - ${side}面:`, error);\n      \n      // 解析失敗不影響OCR結果，只記錄錯誤\n      updateOCRState(side, {\n        parsedFields: {}\n      });\n      \n      return {};\n    }\n  }, [updateOCRState]);\n\n  /**\n   * 重新處理OCR\n   */\n  const retryOCR = useCallback(async (imageFile, side = 'front') => {\n    console.log(`🔄 重新處理OCR - ${side}面`);\n    return await processImage(imageFile, side);\n  }, [processImage]);\n\n  /**\n   * 清空OCR結果\n   */\n  const clearOCR = useCallback((side = 'both') => {\n    const emptyState = {\n      text: '',\n      parsedFields: {},\n      status: OCR_STATUS.IDLE,\n      error: null\n    };\n\n    if (side === 'both') {\n      setFrontOCR(emptyState);\n      setBackOCR(emptyState);\n    } else if (side === 'front') {\n      setFrontOCR(emptyState);\n    } else if (side === 'back') {\n      setBackOCR(emptyState);\n    }\n    \n    console.log(`🧹 清空OCR結果 - ${side}`);\n  }, []);\n\n  /**\n   * 合併正反面解析結果\n   */\n  const getMergedFields = useCallback(() => {\n    const merged = { ...frontOCR.parsedFields };\n    \n    // 反面的欄位可以補充或覆蓋正面的欄位\n    Object.keys(backOCR.parsedFields).forEach(key => {\n      const backValue = backOCR.parsedFields[key];\n      const frontValue = merged[key];\n      \n      // 如果正面沒有該欄位，或者反面的值更完整，則使用反面的值\n      if (!frontValue || (backValue && backValue.length > frontValue.length)) {\n        merged[key] = backValue;\n      }\n    });\n    \n    return merged;\n  }, [frontOCR.parsedFields, backOCR.parsedFields]);\n\n  /**\n   * 獲取OCR處理狀態\n   */\n  const getOCRStatus = useCallback((side = 'both') => {\n    if (side === 'front') {\n      return frontOCR.status;\n    } else if (side === 'back') {\n      return backOCR.status;\n    } else {\n      // 返回整體狀態\n      if (frontOCR.status === OCR_STATUS.PROCESSING || backOCR.status === OCR_STATUS.PROCESSING) {\n        return OCR_STATUS.PROCESSING;\n      } else if (frontOCR.status === OCR_STATUS.ERROR || backOCR.status === OCR_STATUS.ERROR) {\n        return OCR_STATUS.ERROR;\n      } else if (frontOCR.status === OCR_STATUS.SUCCESS || backOCR.status === OCR_STATUS.SUCCESS) {\n        return OCR_STATUS.SUCCESS;\n      } else {\n        return OCR_STATUS.IDLE;\n      }\n    }\n  }, [frontOCR.status, backOCR.status]);\n\n  /**\n   * 檢查是否有OCR結果\n   */\n  const hasOCRResults = useCallback((side = 'both') => {\n    if (side === 'front') {\n      return !!frontOCR.text;\n    } else if (side === 'back') {\n      return !!backOCR.text;\n    } else {\n      return !!(frontOCR.text || backOCR.text);\n    }\n  }, [frontOCR.text, backOCR.text]);\n\n  /**\n   * 檢查是否有解析結果\n   */\n  const hasParsedFields = useCallback((side = 'both') => {\n    if (side === 'front') {\n      return Object.keys(frontOCR.parsedFields).length > 0;\n    } else if (side === 'back') {\n      return Object.keys(backOCR.parsedFields).length > 0;\n    } else {\n      return Object.keys(frontOCR.parsedFields).length > 0 || \n             Object.keys(backOCR.parsedFields).length > 0;\n    }\n  }, [frontOCR.parsedFields, backOCR.parsedFields]);\n\n  return {\n    // OCR狀態\n    frontOCR,\n    backOCR,\n    \n    // 操作方法\n    processImage,\n    parseFields,\n    retryOCR,\n    clearOCR,\n    \n    // 工具方法\n    getMergedFields,\n    getOCRStatus,\n    hasOCRResults,\n    hasParsedFields,\n    \n    // 便捷屬性\n    isProcessing: getOCRStatus() === OCR_STATUS.PROCESSING,\n    hasError: getOCRStatus() === OCR_STATUS.ERROR,\n    hasResults: hasOCRResults(),\n    mergedFields: getMergedFields()\n  };\n};\n\nexport default useOCRState;\n"], "mappings": ";AAAA;AACA;AACA;AACA;;AAEA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAC7C,SAASC,KAAK,QAAQ,aAAa;AACnC,SAASC,UAAU,QAAQ,4BAA4B;AACvD,SAASC,UAAU,EAAEC,cAAc,EAAEC,gBAAgB,QAAQ,oBAAoB;AAEjF,OAAO,MAAMC,WAAW,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGV,QAAQ,CAAC;IACvCW,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,CAAC,CAAC;IAChBC,MAAM,EAAET,UAAU,CAACU,IAAI;IACvBC,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGjB,QAAQ,CAAC;IACrCW,IAAI,EAAE,EAAE;IACRC,YAAY,EAAE,CAAC,CAAC;IAChBC,MAAM,EAAET,UAAU,CAACU,IAAI;IACvBC,KAAK,EAAE;EACT,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMG,cAAc,GAAGjB,WAAW,CAAC,CAACkB,IAAI,EAAEC,OAAO,KAAK;IACpD,MAAMC,MAAM,GAAGF,IAAI,KAAK,OAAO,GAAGT,WAAW,GAAGO,UAAU;IAC1DI,MAAM,CAACC,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,GAAGF;IAAQ,CAAC,CAAC,CAAC;EAC3C,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAMG,YAAY,GAAGtB,WAAW,CAAC,OAAOuB,SAAS,EAAEL,IAAI,GAAG,OAAO,KAAK;IACpEM,OAAO,CAACC,GAAG,CAAC,gBAAgBP,IAAI,IAAI,EAAEK,SAAS,CAACG,IAAI,CAAC;IAErDT,cAAc,CAACC,IAAI,EAAE;MACnBN,MAAM,EAAET,UAAU,CAACwB,UAAU;MAC7Bb,KAAK,EAAE;IACT,CAAC,CAAC;IAEF,IAAI;MACF,MAAMc,MAAM,GAAG,MAAM1B,UAAU,CAACoB,YAAY,CAACC,SAAS,CAAC;MAEvD,IAAIK,MAAM,CAACC,OAAO,IAAID,MAAM,CAAClB,IAAI,EAAE;QACjCO,cAAc,CAACC,IAAI,EAAE;UACnBR,IAAI,EAAEkB,MAAM,CAAClB,IAAI;UACjBE,MAAM,EAAET,UAAU,CAAC2B,OAAO;UAC1BhB,KAAK,EAAE;QACT,CAAC,CAAC;QAEFU,OAAO,CAACC,GAAG,CAAC,eAAeP,IAAI,IAAI,EAAEU,MAAM,CAAClB,IAAI,CAAC;;QAEjD;QACA,MAAMqB,WAAW,CAACH,MAAM,CAAClB,IAAI,EAAEQ,IAAI,CAAC;QAEpC,OAAOU,MAAM,CAAClB,IAAI;MACpB,CAAC,MAAM;QACL,MAAM,IAAIsB,KAAK,CAACJ,MAAM,CAACK,OAAO,IAAI,SAAS,CAAC;MAC9C;IACF,CAAC,CAAC,OAAOnB,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,eAAeI,IAAI,IAAI,EAAEJ,KAAK,CAAC;MAE7CG,cAAc,CAACC,IAAI,EAAE;QACnBN,MAAM,EAAET,UAAU,CAAC+B,KAAK;QACxBpB,KAAK,EAAEA,KAAK,CAACmB,OAAO,IAAI7B,cAAc,CAAC+B;MACzC,CAAC,CAAC;MAEFlC,KAAK,CAACmC,IAAI,CAAC;QACTC,OAAO,EAAEvB,KAAK,CAACmB,OAAO,IAAI7B,cAAc,CAAC+B,UAAU;QACnDG,QAAQ,EAAE;MACZ,CAAC,CAAC;MAEF,MAAMxB,KAAK;IACb;EACF,CAAC,EAAE,CAACG,cAAc,CAAC,CAAC;;EAEpB;AACF;AACA;EACE,MAAMc,WAAW,GAAG/B,WAAW,CAAC,OAAOuC,OAAO,EAAErB,IAAI,GAAG,OAAO,KAAK;IACjE,IAAI,CAACqB,OAAO,IAAI,CAACA,OAAO,CAACC,IAAI,CAAC,CAAC,EAAE;MAC/BhB,OAAO,CAACiB,IAAI,CAAC,iBAAiB,CAAC;MAC/B,OAAO,CAAC,CAAC;IACX;IAEAjB,OAAO,CAACC,GAAG,CAAC,eAAeP,IAAI,IAAI,EAAEqB,OAAO,CAACG,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK,CAAC;IAEvE,IAAI;MACF,MAAMd,MAAM,GAAG,MAAM1B,UAAU,CAAC6B,WAAW,CAACQ,OAAO,EAAErB,IAAI,CAAC;MAE1D,IAAIU,MAAM,CAACC,OAAO,IAAID,MAAM,CAACe,aAAa,EAAE;QAC1C1B,cAAc,CAACC,IAAI,EAAE;UACnBP,YAAY,EAAEiB,MAAM,CAACe;QACvB,CAAC,CAAC;QAEFnB,OAAO,CAACC,GAAG,CAAC,cAAcP,IAAI,IAAI,EAAEU,MAAM,CAACe,aAAa,CAAC;QAEzD1C,KAAK,CAACmC,IAAI,CAAC;UACTC,OAAO,EAAE,GAAGnB,IAAI,KAAK,OAAO,GAAG,IAAI,GAAG,IAAI,GAAGb,gBAAgB,CAACuC,WAAW,EAAE;UAC3EN,QAAQ,EAAE;QACZ,CAAC,CAAC;QAEF,OAAOV,MAAM,CAACe,aAAa;MAC7B,CAAC,MAAM;QACLnB,OAAO,CAACiB,IAAI,CAAC,YAAY,CAAC;QAC1B,OAAO,CAAC,CAAC;MACX;IACF,CAAC,CAAC,OAAO3B,KAAK,EAAE;MACdU,OAAO,CAACV,KAAK,CAAC,cAAcI,IAAI,IAAI,EAAEJ,KAAK,CAAC;;MAE5C;MACAG,cAAc,CAACC,IAAI,EAAE;QACnBP,YAAY,EAAE,CAAC;MACjB,CAAC,CAAC;MAEF,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACM,cAAc,CAAC,CAAC;;EAEpB;AACF;AACA;EACE,MAAM4B,QAAQ,GAAG7C,WAAW,CAAC,OAAOuB,SAAS,EAAEL,IAAI,GAAG,OAAO,KAAK;IAChEM,OAAO,CAACC,GAAG,CAAC,gBAAgBP,IAAI,GAAG,CAAC;IACpC,OAAO,MAAMI,YAAY,CAACC,SAAS,EAAEL,IAAI,CAAC;EAC5C,CAAC,EAAE,CAACI,YAAY,CAAC,CAAC;;EAElB;AACF;AACA;EACE,MAAMwB,QAAQ,GAAG9C,WAAW,CAAC,CAACkB,IAAI,GAAG,MAAM,KAAK;IAC9C,MAAM6B,UAAU,GAAG;MACjBrC,IAAI,EAAE,EAAE;MACRC,YAAY,EAAE,CAAC,CAAC;MAChBC,MAAM,EAAET,UAAU,CAACU,IAAI;MACvBC,KAAK,EAAE;IACT,CAAC;IAED,IAAII,IAAI,KAAK,MAAM,EAAE;MACnBT,WAAW,CAACsC,UAAU,CAAC;MACvB/B,UAAU,CAAC+B,UAAU,CAAC;IACxB,CAAC,MAAM,IAAI7B,IAAI,KAAK,OAAO,EAAE;MAC3BT,WAAW,CAACsC,UAAU,CAAC;IACzB,CAAC,MAAM,IAAI7B,IAAI,KAAK,MAAM,EAAE;MAC1BF,UAAU,CAAC+B,UAAU,CAAC;IACxB;IAEAvB,OAAO,CAACC,GAAG,CAAC,gBAAgBP,IAAI,EAAE,CAAC;EACrC,CAAC,EAAE,EAAE,CAAC;;EAEN;AACF;AACA;EACE,MAAM8B,eAAe,GAAGhD,WAAW,CAAC,MAAM;IACxC,MAAMiD,MAAM,GAAG;MAAE,GAAGzC,QAAQ,CAACG;IAAa,CAAC;;IAE3C;IACAuC,MAAM,CAACC,IAAI,CAACpC,OAAO,CAACJ,YAAY,CAAC,CAACyC,OAAO,CAACC,GAAG,IAAI;MAC/C,MAAMC,SAAS,GAAGvC,OAAO,CAACJ,YAAY,CAAC0C,GAAG,CAAC;MAC3C,MAAME,UAAU,GAAGN,MAAM,CAACI,GAAG,CAAC;;MAE9B;MACA,IAAI,CAACE,UAAU,IAAKD,SAAS,IAAIA,SAAS,CAACE,MAAM,GAAGD,UAAU,CAACC,MAAO,EAAE;QACtEP,MAAM,CAACI,GAAG,CAAC,GAAGC,SAAS;MACzB;IACF,CAAC,CAAC;IAEF,OAAOL,MAAM;EACf,CAAC,EAAE,CAACzC,QAAQ,CAACG,YAAY,EAAEI,OAAO,CAACJ,YAAY,CAAC,CAAC;;EAEjD;AACF;AACA;EACE,MAAM8C,YAAY,GAAGzD,WAAW,CAAC,CAACkB,IAAI,GAAG,MAAM,KAAK;IAClD,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOV,QAAQ,CAACI,MAAM;IACxB,CAAC,MAAM,IAAIM,IAAI,KAAK,MAAM,EAAE;MAC1B,OAAOH,OAAO,CAACH,MAAM;IACvB,CAAC,MAAM;MACL;MACA,IAAIJ,QAAQ,CAACI,MAAM,KAAKT,UAAU,CAACwB,UAAU,IAAIZ,OAAO,CAACH,MAAM,KAAKT,UAAU,CAACwB,UAAU,EAAE;QACzF,OAAOxB,UAAU,CAACwB,UAAU;MAC9B,CAAC,MAAM,IAAInB,QAAQ,CAACI,MAAM,KAAKT,UAAU,CAAC+B,KAAK,IAAInB,OAAO,CAACH,MAAM,KAAKT,UAAU,CAAC+B,KAAK,EAAE;QACtF,OAAO/B,UAAU,CAAC+B,KAAK;MACzB,CAAC,MAAM,IAAI1B,QAAQ,CAACI,MAAM,KAAKT,UAAU,CAAC2B,OAAO,IAAIf,OAAO,CAACH,MAAM,KAAKT,UAAU,CAAC2B,OAAO,EAAE;QAC1F,OAAO3B,UAAU,CAAC2B,OAAO;MAC3B,CAAC,MAAM;QACL,OAAO3B,UAAU,CAACU,IAAI;MACxB;IACF;EACF,CAAC,EAAE,CAACL,QAAQ,CAACI,MAAM,EAAEG,OAAO,CAACH,MAAM,CAAC,CAAC;;EAErC;AACF;AACA;EACE,MAAM8C,aAAa,GAAG1D,WAAW,CAAC,CAACkB,IAAI,GAAG,MAAM,KAAK;IACnD,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAO,CAAC,CAACV,QAAQ,CAACE,IAAI;IACxB,CAAC,MAAM,IAAIQ,IAAI,KAAK,MAAM,EAAE;MAC1B,OAAO,CAAC,CAACH,OAAO,CAACL,IAAI;IACvB,CAAC,MAAM;MACL,OAAO,CAAC,EAAEF,QAAQ,CAACE,IAAI,IAAIK,OAAO,CAACL,IAAI,CAAC;IAC1C;EACF,CAAC,EAAE,CAACF,QAAQ,CAACE,IAAI,EAAEK,OAAO,CAACL,IAAI,CAAC,CAAC;;EAEjC;AACF;AACA;EACE,MAAMiD,eAAe,GAAG3D,WAAW,CAAC,CAACkB,IAAI,GAAG,MAAM,KAAK;IACrD,IAAIA,IAAI,KAAK,OAAO,EAAE;MACpB,OAAOgC,MAAM,CAACC,IAAI,CAAC3C,QAAQ,CAACG,YAAY,CAAC,CAAC6C,MAAM,GAAG,CAAC;IACtD,CAAC,MAAM,IAAItC,IAAI,KAAK,MAAM,EAAE;MAC1B,OAAOgC,MAAM,CAACC,IAAI,CAACpC,OAAO,CAACJ,YAAY,CAAC,CAAC6C,MAAM,GAAG,CAAC;IACrD,CAAC,MAAM;MACL,OAAON,MAAM,CAACC,IAAI,CAAC3C,QAAQ,CAACG,YAAY,CAAC,CAAC6C,MAAM,GAAG,CAAC,IAC7CN,MAAM,CAACC,IAAI,CAACpC,OAAO,CAACJ,YAAY,CAAC,CAAC6C,MAAM,GAAG,CAAC;IACrD;EACF,CAAC,EAAE,CAAChD,QAAQ,CAACG,YAAY,EAAEI,OAAO,CAACJ,YAAY,CAAC,CAAC;EAEjD,OAAO;IACL;IACAH,QAAQ;IACRO,OAAO;IAEP;IACAO,YAAY;IACZS,WAAW;IACXc,QAAQ;IACRC,QAAQ;IAER;IACAE,eAAe;IACfS,YAAY;IACZC,aAAa;IACbC,eAAe;IAEf;IACAC,YAAY,EAAEH,YAAY,CAAC,CAAC,KAAKtD,UAAU,CAACwB,UAAU;IACtDkC,QAAQ,EAAEJ,YAAY,CAAC,CAAC,KAAKtD,UAAU,CAAC+B,KAAK;IAC7C4B,UAAU,EAAEJ,aAAa,CAAC,CAAC;IAC3BK,YAAY,EAAEf,eAAe,CAAC;EAChC,CAAC;AACH,CAAC;AAACzC,EAAA,CA5OWD,WAAW;AA8OxB,eAAeA,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}